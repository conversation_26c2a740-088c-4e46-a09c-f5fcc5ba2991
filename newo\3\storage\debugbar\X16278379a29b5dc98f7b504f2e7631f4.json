{"__meta": {"id": "X16278379a29b5dc98f7b504f2e7631f4", "datetime": "2025-06-17 07:02:50", "utime": **********.130312, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143769.379643, "end": **********.130343, "duration": 0.7506999969482422, "duration_str": "751ms", "measures": [{"label": "Booting", "start": 1750143769.379643, "relative_start": 0, "end": **********.018862, "relative_end": **********.018862, "duration": 0.6392190456390381, "duration_str": "639ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.018875, "relative_start": 0.6392319202423096, "end": **********.130346, "relative_end": 3.0994415283203125e-06, "duration": 0.11147117614746094, "duration_str": "111ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45171016, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0165, "accumulated_duration_str": "16.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.06235, "duration": 0.01366, "duration_str": "13.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 82.788}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.093984, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 82.788, "width_percent": 5.394}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.110338, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.182, "width_percent": 11.818}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-452686867 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-452686867\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-95485439 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-95485439\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-639209948 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-639209948\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=1rkbmmi%7C1750143765825%7C4%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImVCMDBZU0lpZSszWXdEK29ReGdrVVE9PSIsInZhbHVlIjoiaXBUQUVNOHNWajZQTDJaRVdBNGpTSE1iQURUYXI5NlhJZSsweTk4VG1zanFsMDFXWFoweTEyb0VrNll4d3FXZ0VCRmhnTWZwOVJ5UUZPcnAzZkpDTGhlaEVZTUsrNzVIeW5Wc0R1eUt2c3pSR0FDQUJPR0krNDNiLzN6YU40NUpQSXgxRU1QOGxrNmF0WUYwRTR3bm5CWlkzOXBmRzBSWFV2ajUvYS8xVjk3SjVhNkdpOFR3bTFaeStBSDQ0bXY0S1RyalVPdkpEMFdrZXk3dkpwbEMxSzBReVQvVEZrNU8rTXJLb0dDQmtNejNEZFJ1MFR0d3F1UEtwTHNCSkIyUnJtMnFjSHdLdkIxQ0dEZkJLOCsvUVE2cG9WbTNSSlNIOW8xNXExbzMxTDUraEZ4RW82Mlk2T0pTOFlFNWJFcEY2L0NuK0V4eW9JWUQyak9VeEJORDZyVWtmTlZRTnRXTzhXdkZMNHlMTW1OMHpuMnNrTkUxZHUzQ1RoYy9SU0lHY0ZNREptdEFJblpDaUpUU01VQkZPTzJGNzdWbnZlYlpIN01rMk1Qbk80ZU96NXFTMXhqQkJpVDk5NmdoaSt2M0tUUGxENis0QTN3Z0F6QzRsK09OYnl4bVVSL2h2YmNkQUNwc3pIbENTbnMzYlhZbmlFWWNEN0ZJSXhad2tqRGwiLCJtYWMiOiJiNGQwMjQzZGMyYmYyY2EzYjAyNzRiMjI2NzA3NzU4NDVjZjRjY2YxNTg1YTMwMjU5ZjMwZDMyMjU0Mjk4ODE1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkxpNEt0ZjF0YXhmT2RVYi8xYVF2dUE9PSIsInZhbHVlIjoiQzgzczBiZUNsRy9PVGUycXJJbVdnMDI3ZkVKU0F1MXU1M0ZtYWhPUkRiMDh5SEtwK0cxWWovOUFXT05uRHd1OE8ySjVNOFd4aGVBODJMcDlwQ2FZZ0d5b2FubzUyNDRCbjd1RmtGSDNwRWtTVFUvR1FWbmp1SDhDRTFTNzVFZ1pGYXo3TDRodE9UaGt3Si8vbS8ybkVjK0pOT2NpK3NhN0tsMkpvK1BGZnluKys0WVdFSUx2K3ByNy9INEM0Yzh0KzVYRlZSZE8yekRVSjcvM2o2MTNIR1RHMGNrbGVjWG5KYVFvdU81YmxxaDNNNEJSdTZlR3FHdzV0eFJCMWxSK0xjYU1zcEhtLzJPbjdGN0N1SXkxS3FpNHdvRWtSTDZXZHFNcXVkdFBQTG8yVkFYdThPUWZrT2RVcnpZUUhFajZkbjJtM3l2ckt3TzFaTU1FQUpwNDR6UHpGT0l0UGt2QTdqUldaRmFFMGoxODNQVE03cFNsNzk1NUpqeXVSR3RRcVFRT05CMkZyNzJmSG5saVdydXlieVJSM1JBTlYwMmppaHhhdEkrem1SMGJVMG5nVENrR29FZkJwUnQrNFlIbU52alpwdHZvWmE5OFh0ZGxHRXRNaE40VXlHWThTdGNwTmJRdUpEM0FyT2Q4ckozaXJ1WTJnN2g4Mi9tbHpHZTYiLCJtYWMiOiI0ZWRlMmQ3NzMwM2VlMzQ5YzFlZDM1ZjViMmEzZmZmNDY5NWFkMWM2NDhjMmNlNGY3NzA4ZjIyN2U3MzIwZTE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDWN9h6R9gAIdzZaXBzXfq0WGpauVfrUj70kKWA2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1199574568 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:02:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZETmZoSFpVc0k0VE9pT1NJdTBmSmc9PSIsInZhbHVlIjoiWDRzNXBqM2hUMVQyTUt6aE44TUNjRndRWWJ2cWVjSFBmaklqUHdqOEZwVzdJM3BMYW9ETDdCeG5wR0xCOG4rT0RlZE5iRjR2WmNtWWIzVXhiQXdUdlM1K2s0T2VXVy9JYk9QZjRwWW5GRi93b2NyYjZYSU84SFYreUdLZGprdXB0ZWk4bk9XVGNwcC9HUzhGVTlWZXFXYzBDN2dBWGtSRWNjUEFzb29SRzFneVp1eXVWeU9sV3dySXhoQkJ3ajhHdHp0dHVpUlhUcGxNOHgyTWhUQXQ0NzdnRXJUdjRDWXp0MzZPaUJkdWs1WnJZcGJQSGV6SW5ucVc0TFUyckRka2Jxd0R1V3dRUkpFR0t4WGc5VHUwdkVBcjlnYmpHOEk2azNzbHN2RlM1MnNaZzlrVDJWdlZSRWphb3RrTDgvd3RVbzhyMkNsU2dmN2FtaWdkaFpNdGhYM0U5YzhoWGdhUG9UUkNmcW9HanQ1NkY5cnpIODFWWXpJL2MxSlRXaytrbVBLUDl4NnREUERoVG12YVN3YVNRZW9tM1VoNXdKNVB2SXI3VkxGT1E1a3FvN2ZUdDIzU29aOWsvbHQvR2ROb3Z2WS92T3VCNUpnUER4bllyK0RDUm9lY25Nd3ZPc1UvbTgzeU1yb0lxSU1ORTFvQWxzSWxlRUdZYkpBb3BJZjUiLCJtYWMiOiI4YzQ0Mzg4ZDAxNTM4YzcyODNiYzM1MjBiM2QzYmEyNjQ3YTE0NjhlMTE1MjFjN2YxZDM3YTE4ZjIyZTlhMjRjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:02:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjNzb3oxV1dpYXd4SUhDcVpOdWdTa3c9PSIsInZhbHVlIjoiNmpUZzZHTHpOOUNkYXNEbFk1ZGwxRTJ0ZEl6ME9hdTZDdEFyM0czN0RIeUtUc0dLb3pBYzRnM0FzRnYrV2t4SzBzQVRqUmVhY1l1aFQwdCs4dWhDN1YzdGhPQ1QzNzhUd2VIUHR1NTBRaCtTM1FjNlpzMEhickYwSW50SzB3NlA5ZS81WElIMExuYjJ0RS9nRW1PU1VSbkpITE8xRFJHY0Q4TFU2Vm9pUWVhMTh5MStvNVB2OHM3a0ZiVGpjQkNQaWhyd2t0KzNPUzJtU1lwMjYvUFZiV2FoaVQ0Qm9WZ3owc2RkTFRaeVRwTWc5eFQrUmMxOE1oeUZpcmtBM0RvWFpCcVZQckhLejdBbFBONUNiNFVaR2FVY3BmSmp0ZXpDL2lRNHU0LzVQSGdnMXdqL1h5RGNyUDNVUmlzdS9ycVJPRUx3anhCVUQ0NUEzdnVrUm5UdjZ5bnBsb2tVUDdtUVRlVllvc1NzRTV0dGJDSUd1TmxCa1VjRkRza3RFaHRCWExPMmRERUFMdXhaR214Q0NkY1RCaXpwQXVQZ0RBbUtxa3ZlcXdGR2UrQkszdXpubVQxY0R4cmo1UHg3amtyaXppL0hETG1oa0RvTDI5ekJTWHYxRGZhZkQ0Z3pjT0VmeEROQkZnVTRTb3JvM0l0bGpVUWc0Zk1KV001dXYwMlAiLCJtYWMiOiI1OTMwMjZkNzMxOGNlNTY4ZGY0MDkyNTFmY2U0N2QyNzdiZThlMjdkZmUyMDdhYTQ5MmYxN2NhN2QyYmZmZDQ2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:02:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZETmZoSFpVc0k0VE9pT1NJdTBmSmc9PSIsInZhbHVlIjoiWDRzNXBqM2hUMVQyTUt6aE44TUNjRndRWWJ2cWVjSFBmaklqUHdqOEZwVzdJM3BMYW9ETDdCeG5wR0xCOG4rT0RlZE5iRjR2WmNtWWIzVXhiQXdUdlM1K2s0T2VXVy9JYk9QZjRwWW5GRi93b2NyYjZYSU84SFYreUdLZGprdXB0ZWk4bk9XVGNwcC9HUzhGVTlWZXFXYzBDN2dBWGtSRWNjUEFzb29SRzFneVp1eXVWeU9sV3dySXhoQkJ3ajhHdHp0dHVpUlhUcGxNOHgyTWhUQXQ0NzdnRXJUdjRDWXp0MzZPaUJkdWs1WnJZcGJQSGV6SW5ucVc0TFUyckRka2Jxd0R1V3dRUkpFR0t4WGc5VHUwdkVBcjlnYmpHOEk2azNzbHN2RlM1MnNaZzlrVDJWdlZSRWphb3RrTDgvd3RVbzhyMkNsU2dmN2FtaWdkaFpNdGhYM0U5YzhoWGdhUG9UUkNmcW9HanQ1NkY5cnpIODFWWXpJL2MxSlRXaytrbVBLUDl4NnREUERoVG12YVN3YVNRZW9tM1VoNXdKNVB2SXI3VkxGT1E1a3FvN2ZUdDIzU29aOWsvbHQvR2ROb3Z2WS92T3VCNUpnUER4bllyK0RDUm9lY25Nd3ZPc1UvbTgzeU1yb0lxSU1ORTFvQWxzSWxlRUdZYkpBb3BJZjUiLCJtYWMiOiI4YzQ0Mzg4ZDAxNTM4YzcyODNiYzM1MjBiM2QzYmEyNjQ3YTE0NjhlMTE1MjFjN2YxZDM3YTE4ZjIyZTlhMjRjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:02:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjNzb3oxV1dpYXd4SUhDcVpOdWdTa3c9PSIsInZhbHVlIjoiNmpUZzZHTHpOOUNkYXNEbFk1ZGwxRTJ0ZEl6ME9hdTZDdEFyM0czN0RIeUtUc0dLb3pBYzRnM0FzRnYrV2t4SzBzQVRqUmVhY1l1aFQwdCs4dWhDN1YzdGhPQ1QzNzhUd2VIUHR1NTBRaCtTM1FjNlpzMEhickYwSW50SzB3NlA5ZS81WElIMExuYjJ0RS9nRW1PU1VSbkpITE8xRFJHY0Q4TFU2Vm9pUWVhMTh5MStvNVB2OHM3a0ZiVGpjQkNQaWhyd2t0KzNPUzJtU1lwMjYvUFZiV2FoaVQ0Qm9WZ3owc2RkTFRaeVRwTWc5eFQrUmMxOE1oeUZpcmtBM0RvWFpCcVZQckhLejdBbFBONUNiNFVaR2FVY3BmSmp0ZXpDL2lRNHU0LzVQSGdnMXdqL1h5RGNyUDNVUmlzdS9ycVJPRUx3anhCVUQ0NUEzdnVrUm5UdjZ5bnBsb2tVUDdtUVRlVllvc1NzRTV0dGJDSUd1TmxCa1VjRkRza3RFaHRCWExPMmRERUFMdXhaR214Q0NkY1RCaXpwQXVQZ0RBbUtxa3ZlcXdGR2UrQkszdXpubVQxY0R4cmo1UHg3amtyaXppL0hETG1oa0RvTDI5ekJTWHYxRGZhZkQ0Z3pjT0VmeEROQkZnVTRTb3JvM0l0bGpVUWc0Zk1KV001dXYwMlAiLCJtYWMiOiI1OTMwMjZkNzMxOGNlNTY4ZGY0MDkyNTFmY2U0N2QyNzdiZThlMjdkZmUyMDdhYTQ5MmYxN2NhN2QyYmZmZDQ2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:02:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1199574568\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-134107416 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-134107416\", {\"maxDepth\":0})</script>\n"}}