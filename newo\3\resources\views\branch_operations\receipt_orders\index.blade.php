@extends('layouts.admin')
@section('page-title')
    {{ __('Receipt Orders') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('الرئيسية') }}</a></li>
    <li class="breadcrumb-item">{{ __('إدارة عمليات الفروع') }}</li>
    <li class="breadcrumb-item">{{ __('أوامر الاستلام') }}</li>
@endsection

@push('script-page')
<script>
$(document).ready(function() {
    $('.datatable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
        },
        "order": [[ 7, "desc" ]],
        "pageLength": 25,
        "responsive": true,
        "dom": 'Bfrtip',
        "buttons": [
            {
                extend: 'excel',
                text: '<i class="ti ti-file-spreadsheet"></i> تصدير Excel',
                className: 'btn btn-success btn-sm'
            },
            {
                extend: 'pdf',
                text: '<i class="ti ti-file-type-pdf"></i> تصدير PDF',
                className: 'btn btn-danger btn-sm'
            },
            {
                extend: 'print',
                text: '<i class="ti ti-printer"></i> طباعة',
                className: 'btn btn-info btn-sm'
            }
        ]
    });
});
</script>
@endpush

@push('css-page')
<link rel="stylesheet" href="{{ asset('css/datatable/buttons.dataTables.min.css') }}">
<style>
/* تصميم مطابق للصورة - إدارة عمليات الفروع */
.page-header {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.page-title {
    color: #495057;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.stats-row {
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

/* تصميم الجدول مطابق للصورة */
.main-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.card-header-custom {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: between;
    align-items: center;
}

.table {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.table thead th {
    background: #f8f9fa;
    color: #495057;
    border-bottom: 2px solid #e9ecef;
    font-weight: 600;
    padding: 12px 8px;
    text-align: center;
    font-size: 0.85rem;
    border-top: none;
}

.table tbody td {
    padding: 10px 8px;
    vertical-align: middle;
    text-align: center;
    border-color: #e9ecef;
    font-size: 0.85rem;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
    border-radius: 0.375rem;
}

.action-btn {
    display: inline-block;
    margin: 0 2px;
}

.action-btn a {
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: white;
    font-size: 12px;
}

.btn-edit {
    background: #ffc107;
}

.btn-view {
    background: #28a745;
}

.btn-print {
    background: #17a2b8;
}

.btn-pdf {
    background: #dc3545;
}

/* إحصائيات ملونة مطابقة للصورة */
.stat-card.orange {
    border-left: 4px solid #ff9800;
}

.stat-card.blue {
    border-left: 4px solid #2196f3;
}

.stat-card.green {
    border-left: 4px solid #4caf50;
}

.stat-card.purple {
    border-left: 4px solid #9c27b0;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #495057;
    margin: 10px 0 5px 0;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

.create-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 0.9rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

/* فلتر المستودعات */
.warehouse-filter {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.filter-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
}

.warehouse-filter h6 {
    color: #8b4513;
    font-weight: 600;
    margin-bottom: 15px;
}

.form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 10px 15px;
}

.form-select:focus {
    border-color: #11998e;
    box-shadow: 0 0 0 0.2rem rgba(17, 153, 142, 0.25);
}
</style>
@endpush

@section('content')
    <!-- Header مطابق للصورة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-6">
                <h1 class="page-title">Receipt Orders</h1>
            </div>
            <div class="col-6 text-end">
                <a href="{{ route('receipt-order.create') }}" class="create-btn">
                    <i class="ti ti-plus"></i>
                    إنشاء أمر استلام
                </a>
            </div>
        </div>
    </div>

    <!-- فلتر المستودع -->
    <div class="warehouse-filter">
        <div class="filter-title">
            <i class="ti ti-filter me-2"></i>{{ __('فلترة حسب المستودع/الفرع') }}
        </div>
        <div class="row">
            <div class="col-md-4">
                <select class="form-select" id="warehouse_filter">
                    <option value="">{{ __('جميع المستودعات') }}</option>
                    @foreach($warehouses as $warehouse)
                        <option value="{{ $warehouse->id }}" {{ request('warehouse_id') == $warehouse->id ? 'selected' : '' }}>
                            {{ $warehouse->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-primary" onclick="filterByWarehouse()">
                    <i class="ti ti-search me-1"></i>{{ __('تصفية') }}
                </button>
            </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات مطابقة للصورة -->
    <div class="row stats-row">
        <div class="col-md-3">
            <div class="stat-card orange">
                <div class="stat-number">{{ $receiptOrders->count() }}</div>
                <div class="stat-label">إجمالي الأوامر</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card blue">
                <div class="stat-number">{{ $receiptOrders->where('order_type', 'استلام بضاعة')->count() }}</div>
                <div class="stat-label">أوامر الاستلام</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card green">
                <div class="stat-number">{{ $receiptOrders->where('order_type', 'نقل بضاعة')->count() }}</div>
                <div class="stat-label">أوامر النقل</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card purple">
                <div class="stat-number">{{ $receiptOrders->where('order_type', 'أمر إخراج')->count() }}</div>
                <div class="stat-label">أوامر الإخراج</div>
            </div>
        </div>
    </div>

    <!-- الجدول الرئيسي مطابق للصورة -->
    <div class="main-card">
        <div class="card-header-custom">
            <div class="d-flex justify-content-between align-items-center w-100">
                <h6 class="mb-0">Receipt Orders List</h6>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge badge-info">{{ $receiptOrders->count() }} أوامر</span>
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table datatable">
                <thead>
                    <tr>
                        <th>ACTIONS</th>
                        <th>CREATED DATE</th>
                        <th>DATE</th>
                        <th>PRODUCTS COUNT</th>
                        <th>CREATED BY</th>
                        <th>FROM WAREHOUSE</th>
                        <th>WAREHOUSE/BRANCH</th>
                        <th>VENDOR/SOURCE</th>
                        <th>ORDER TYPE</th>
                        <th>ORDER NUMBER</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($receiptOrders as $order)
                        <tr>
                            <td>
                                <div class="action-btn">
                                    @if(Auth::user()->can('manage warehouse') || Auth::user()->can('show financial record') || Auth::user()->hasRole('company'))
                                        <a href="{{ route('receipt-order.edit', $order['id']) }}" class="btn-edit" title="تحرير">
                                            <i class="ti ti-edit"></i>
                                        </a>
                                    @endif
                                    <a href="{{ route('receipt-order.show', $order['id']) }}" class="btn-view" title="عرض">
                                        <i class="ti ti-eye"></i>
                                    </a>
                                    <a href="{{ route('receipt-order.print', $order['id']) }}" class="btn-print" title="طباعة" target="_blank">
                                        <i class="ti ti-printer"></i>
                                    </a>
                                    <a href="{{ route('receipt-order.pdf', $order['id']) }}" class="btn-pdf" title="PDF" target="_blank">
                                        <i class="ti ti-file-type-pdf"></i>
                                    </a>
                                </div>
                            </td>
                            <td>{{ $order['created_at']->format('Y-m-d H:i') }}</td>
                            <td>{{ \App\Models\Utility::getDateFormated($order['date']) }}</td>
                            <td>
                                <strong class="text-info">{{ $order['total_products'] }}</strong>
                            </td>
                            <td>
                                <span class="badge badge-info">{{ $order['creator_name'] }}</span>
                            </td>
                            <td>
                                @if($order['from_warehouse_name'])
                                    <span class="badge badge-info">{{ $order['from_warehouse_name'] }}</span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                <span class="badge badge-info">{{ $order['warehouse_name'] }}</span>
                            </td>
                            <td>{{ $order['vendor_name'] }}</td>
                            <td>
                                @if($order['type'] === 'استلام بضاعة')
                                    <span class="badge badge-success">{{ $order['type'] }}</span>
                                @elseif($order['type'] === 'نقل بضاعة')
                                    <span class="badge badge-info">{{ $order['type'] }}</span>
                                @else
                                    <span class="badge badge-warning">{{ $order['type'] }}</span>
                                @endif
                            </td>
                            <td>
                                <strong class="text-primary">{{ $order['reference_number'] }}</strong>
                            </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    function filterByWarehouse() {
        const warehouseId = document.getElementById('warehouse_filter').value;
        const currentUrl = new URL(window.location.href);
        
        if (warehouseId) {
            currentUrl.searchParams.set('warehouse_id', warehouseId);
        } else {
            currentUrl.searchParams.delete('warehouse_id');
        }
        
        window.location.href = currentUrl.toString();
    }
    </script>
@endsection
