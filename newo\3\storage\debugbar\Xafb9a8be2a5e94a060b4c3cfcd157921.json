{"__meta": {"id": "Xafb9a8be2a5e94a060b4c3cfcd157921", "datetime": "2025-06-17 07:02:34", "utime": **********.970079, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.161886, "end": **********.970116, "duration": 0.808229923248291, "duration_str": "808ms", "measures": [{"label": "Booting", "start": **********.161886, "relative_start": 0, "end": **********.848535, "relative_end": **********.848535, "duration": 0.6866490840911865, "duration_str": "687ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.848552, "relative_start": 0.6866660118103027, "end": **********.97012, "relative_end": 4.0531158447265625e-06, "duration": 0.12156796455383301, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45156232, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00937, "accumulated_duration_str": "9.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.90771, "duration": 0.00731, "duration_str": "7.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 78.015}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.934936, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 78.015, "width_percent": 10.566}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.948864, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.581, "width_percent": 11.419}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-298126669 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-298126669\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1798588560 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1798588560\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1536160451 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1536160451\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1810758239 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1930 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; XSRF-TOKEN=eyJpdiI6IkxkWlJEVjlFckxrYXhoNG1LS2RCY3c9PSIsInZhbHVlIjoiMXRrcE5ZSWdwVDBPT0F1RUo5OVhFV29YMDNqMmhGSzkrYnNPMDk1ckFZK1VFR0hla1FVMEtwc0c1Z0lkdVFqRExnVFozcjZxQUtPS3N1MlhLc04zR2tJcTNWaDkvUTU1Q1Z6WDZLdUxVTC9lNytmdHdYNGdhbVQ4NE9CVTZ1NDhaS0Fud3B6cEM1Z1B0WVhOSndTc2pGaVlnQ1QxUmhTc1FwZm5UWnBhWWs1a29Hckg2ZGYyTUs0YnpWUllEd0I1akRsdldSZGs3WnhtVzV3VWFnQnRoSGU1d3RUcjVwcnp6UURYZzdrQVBqcTY5T2tRL3E5cW01blFHZit3eDNkLzlKTzFwNmoyNmdCK0U5Z1kwUm9wbGxDQzM3WUlqbHE2THp2Z1RlVmd0NzZjUWdFc1RiUWJtUjlSdlk0dTdxWEsyRjBOU3BIN3BrQktOWnMyTFlJbjFWemt0TThONStaY20vVktpOXpxOXd4LzRyZzhsUVY1L3Z1UTlKd0M5OFU5Z2hydklqeW9QTHY1RUQwclduZzJ3TldHcFMwanhVQmxJV3JmclQ1eHdXOTFlb3VxUE9ET243a3VGcG9UZWcxVFd6N2puVWM1ME5wc21xZFpLN1RoZXg5eHVvcnFVb1JKTS9vM1o5bkZKa09meUFpN1Y4UjY4SkVydjVMem45K00iLCJtYWMiOiI1MDgwMDkyNjExZDBjMzhlNmY1Y2QxODc0ZGZkMDNhZmEyZDgzOWM4MGUzMGExMmM3OWY2NGUyNGExNmNlNTBlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IllDS21EL1BNVFBNSGpITkYzL2prQ1E9PSIsInZhbHVlIjoiZ3NkUUprOEZNeS85clV4RmxoenE5UjJSeHFJdkRCTFoySmJVU04yUE91cXpjTTk4VGpGWndlaktPNk5QWE9nbWxUeHhId0pwSU5wM3BXSFVJeWtCK1BrVnY1b0NPa0JZWERObHhuUVRUVGhySDRTU3VGWXBwY1Uva0xEdTlveHRlMVp6b1JpUWlsdlhzS0ZvbkdITFp0UThqTnF2WUN4cFlWVDZjcmtXYVB5THdyNXFVU2RSSmN6cml4dmFibXVnaGRRcnBZTjhtZjRENGd3TTlsRy9LdUc4NERtMk0vR2hYa1JteWpRQXl5c1dmWG5PblVWRFFHb0FGejlhOTRBRzRXbVJEL01aZW13L2J3aXdXZkJ2YkVrNlF1Qk02QUxiYnZtYXo3NkxOTkVyV1lxUDFvNk9IWXpvN2psamFhbW96b2FJQjU4VTd6N2JkRVAwUWNpK01LWXB1Qm5CSFpwQllKcjlQR3ROalgreVNuSmtYOGxoL0ZiMWRhRkROaFpvdjRXSmdQSDZ3d0t2RktJbWFZTndRaEVDVG13NXNBTEpVdklMY3ZPdEJQM21tNG1uRmJyRk84OFlPeDg0YU91NjlGaG9zZGlhKytycms0SUo5aDk1a2owTzJ4Zm9iaFo3S2lMUTNlWmNKbFJPZENPWWxxNFMwRzIvWVdUZU8ySkYiLCJtYWMiOiIzYzQ1OTgxYmY1MjFhYzgzM2NmMDc5MTZhMzczNWQ4NjNiZjJkNDZkNjk4OTlhMTU1MmMxOWRiNDAyYzM3N2YwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1810758239\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2100958791 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2100958791\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1215165104 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:02:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBrdlJQOFVOaE1zKzJIVkNKOWNYNEE9PSIsInZhbHVlIjoiTFdzZjl2OVRpSjlpZS9yOFBDdENJSnptRjF6Wm9CK2JqWEFiUjZQS2FjV1liUFFVSDh5aGQ3c0xkKzhzQ1Q0NGNGY0V4R2F2NnFQYnNsN2tRejdjdEpndDU2S0ErRkg4K0x4K0VMNlptWDhYMTlSMlVtQTZkeFB5bWlNbXpoaFJId1pJRHVGTlZHVWU0Z2pYbzdmV2FBMnFDNTBjbDFVRnYwL3JvTmRHMnNyelNTRGtDcUNMRWJJTWpPalNJK2ZmVjduZW9xdnVwK2k0Y2ZDRDhFTDB1YzhYVTZIaGFZeTQ0WXl1QXZPVnpabHRNTFQyaWM1MGZnTnJhTGZHRFJhUHZVV1lhb25uQnlqRjM3ak5rbjZSaStZb1ZYaEt3SExiZU5yYnBNR21kWGNkcURac2pUM2dOODdKazFTM1pTeTcyRnBjQnlqQm5SYXZFSzZRQTNSVkU0bXZYMDJkM2M2MVlVUWRTdGhjUU10cGQxQk1xTVBmZU95RGVKZTUyNUZ6MzAyYWRpL0ZXRHdHWTBaNGRNWXdhTHY5UnlsS3VWYUlyVXAybm1sYllibG9jR2lkOGJsVWJPNG1FVklVMTl5eVorU3BEdHIyT0F5bTlqY1E4NDJCMjJzY2xDT0tHYUFNbFJDNXB3RWhERklOZXhqaVBsdEdWc1FKbkhuYzZ5aXkiLCJtYWMiOiJjMzQ5MzYzNjc5YmRmNDdmM2E1NDNhNTUzZDBjMTJlZGY5OWU4ZDI0ZmM2OGFmYzQxM2E4NWEyMTRlOWE5YTU3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:02:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im54R0h0RDFNRjlySjg4L0wvZlB3OHc9PSIsInZhbHVlIjoiNnVwVFErdTQrWFVKWjliVUxoRVhlK2l5alJSUHRiUXBEVGJGVnhEYUJmT0oyQ0htdGFPbjg3N3o1YXlrRjRYSS94SFl3TEpJcmhkRGdCWjVoSFcvVCsvNjdLN1QzMnd6UnJZc2IzNkpvMHloYXBSTlEzNTVKTkFpTU9OaFJSR2prZmFFNVdUbFo1cmYzaEJsODNmV29rTit2THp1TTE5Qlc5MmFaa09lY3dSS1p5cGlyYnVLSUNzdm1Kb3JEQ01Eek5VZkVUTHJDY3o2VDVpditvbXVJM0RhRFMrMFdlMDJVeTk0cmJMSEpVZUVFMm5SaUdwOFZjcW8vMGhDRlFyaXllNHhEZ0Q2bzB3SDFna1hOOVNsWnNlTXo2dmdnQk1ycXpPbUVsNDNDK1phbGVjeG9NWUtyay9BckVPc0hpSHVGczJIRTN3Z1VteXh0Vk9MTjRzcHplblV5OW0rdURVQmF6WkRQVk1YcWg3L0xKWDBTZDIyM0k3QS9mcnM4VHVqWlAxdkZrOWExbTEzbWJSVW5XMm82RWVaZ0pYanJVUGpUWlRrSEgyNDlyQ0pMVEVZaVYwb2x2SWlyMkYxazRGSm12eUYvajZuRVBURXZkM0hNZko3dkV2b2JTN3V5K3J0ejI3bUI4eE00WXRLbmVnOTMxbkM1UGxhc0dGL2tUQmgiLCJtYWMiOiI4NWZjOTg5MzA1YzM4MTRjOGU3NGE2M2JhN2M0Y2JhZTk4YmQwNmRkZjEwZTU3YmVmYjQzYzg3Y2ExZjI1YTNiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:02:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBrdlJQOFVOaE1zKzJIVkNKOWNYNEE9PSIsInZhbHVlIjoiTFdzZjl2OVRpSjlpZS9yOFBDdENJSnptRjF6Wm9CK2JqWEFiUjZQS2FjV1liUFFVSDh5aGQ3c0xkKzhzQ1Q0NGNGY0V4R2F2NnFQYnNsN2tRejdjdEpndDU2S0ErRkg4K0x4K0VMNlptWDhYMTlSMlVtQTZkeFB5bWlNbXpoaFJId1pJRHVGTlZHVWU0Z2pYbzdmV2FBMnFDNTBjbDFVRnYwL3JvTmRHMnNyelNTRGtDcUNMRWJJTWpPalNJK2ZmVjduZW9xdnVwK2k0Y2ZDRDhFTDB1YzhYVTZIaGFZeTQ0WXl1QXZPVnpabHRNTFQyaWM1MGZnTnJhTGZHRFJhUHZVV1lhb25uQnlqRjM3ak5rbjZSaStZb1ZYaEt3SExiZU5yYnBNR21kWGNkcURac2pUM2dOODdKazFTM1pTeTcyRnBjQnlqQm5SYXZFSzZRQTNSVkU0bXZYMDJkM2M2MVlVUWRTdGhjUU10cGQxQk1xTVBmZU95RGVKZTUyNUZ6MzAyYWRpL0ZXRHdHWTBaNGRNWXdhTHY5UnlsS3VWYUlyVXAybm1sYllibG9jR2lkOGJsVWJPNG1FVklVMTl5eVorU3BEdHIyT0F5bTlqY1E4NDJCMjJzY2xDT0tHYUFNbFJDNXB3RWhERklOZXhqaVBsdEdWc1FKbkhuYzZ5aXkiLCJtYWMiOiJjMzQ5MzYzNjc5YmRmNDdmM2E1NDNhNTUzZDBjMTJlZGY5OWU4ZDI0ZmM2OGFmYzQxM2E4NWEyMTRlOWE5YTU3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:02:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im54R0h0RDFNRjlySjg4L0wvZlB3OHc9PSIsInZhbHVlIjoiNnVwVFErdTQrWFVKWjliVUxoRVhlK2l5alJSUHRiUXBEVGJGVnhEYUJmT0oyQ0htdGFPbjg3N3o1YXlrRjRYSS94SFl3TEpJcmhkRGdCWjVoSFcvVCsvNjdLN1QzMnd6UnJZc2IzNkpvMHloYXBSTlEzNTVKTkFpTU9OaFJSR2prZmFFNVdUbFo1cmYzaEJsODNmV29rTit2THp1TTE5Qlc5MmFaa09lY3dSS1p5cGlyYnVLSUNzdm1Kb3JEQ01Eek5VZkVUTHJDY3o2VDVpditvbXVJM0RhRFMrMFdlMDJVeTk0cmJMSEpVZUVFMm5SaUdwOFZjcW8vMGhDRlFyaXllNHhEZ0Q2bzB3SDFna1hOOVNsWnNlTXo2dmdnQk1ycXpPbUVsNDNDK1phbGVjeG9NWUtyay9BckVPc0hpSHVGczJIRTN3Z1VteXh0Vk9MTjRzcHplblV5OW0rdURVQmF6WkRQVk1YcWg3L0xKWDBTZDIyM0k3QS9mcnM4VHVqWlAxdkZrOWExbTEzbWJSVW5XMm82RWVaZ0pYanJVUGpUWlRrSEgyNDlyQ0pMVEVZaVYwb2x2SWlyMkYxazRGSm12eUYvajZuRVBURXZkM0hNZko3dkV2b2JTN3V5K3J0ejI3bUI4eE00WXRLbmVnOTMxbkM1UGxhc0dGL2tUQmgiLCJtYWMiOiI4NWZjOTg5MzA1YzM4MTRjOGU3NGE2M2JhN2M0Y2JhZTk4YmQwNmRkZjEwZTU3YmVmYjQzYzg3Y2ExZjI1YTNiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:02:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1215165104\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-577567516 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-577567516\", {\"maxDepth\":0})</script>\n"}}