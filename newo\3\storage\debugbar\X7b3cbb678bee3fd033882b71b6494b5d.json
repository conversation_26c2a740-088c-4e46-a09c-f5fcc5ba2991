{"__meta": {"id": "X7b3cbb678bee3fd033882b71b6494b5d", "datetime": "2025-06-16 15:21:44", "utime": **********.846546, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087303.102428, "end": **********.846589, "duration": 1.7441611289978027, "duration_str": "1.74s", "measures": [{"label": "Booting", "start": 1750087303.102428, "relative_start": 0, "end": **********.550872, "relative_end": **********.550872, "duration": 1.448444128036499, "duration_str": "1.45s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.550907, "relative_start": 1.4484789371490479, "end": **********.846594, "relative_end": 5.0067901611328125e-06, "duration": 0.295687198638916, "duration_str": "296ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45215288, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01495, "accumulated_duration_str": "14.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.68061, "duration": 0.00857, "duration_str": "8.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 57.324}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.727092, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 57.324, "width_percent": 12.776}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.793918, "duration": 0.00265, "duration_str": "2.65ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 70.1, "width_percent": 17.726}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8197188, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.826, "width_percent": 12.174}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1932396339 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1932396339\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-505299699 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-505299699\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1029302554 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1029302554\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-155623277 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087289395%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5hMyszZ20wOUZ6QWRJUncrdkFsL2c9PSIsInZhbHVlIjoiSEszNXJZYTQ1YldKeXZsSms1ZCtHNUhnc3pFSjh3SHR2aUN4RzdmRS8vUkh1NDc1ek9mSTczWEVOY21lQ2lDSHRwdVlGVUJtdDZSM29OT1h5ZHhrNWRBZGdjY055QTNoOHdWajZkQ1VaM3BBcnl3YW9UY0ZQVUdNZzB6VE0zYWVZb0o3SXVhZWF2elBZRVBYTS96OUdNYkpHTlNrWkdyenIwanBGY09VaHY5bW82Yi9zYXVqbW9qYmVSMnFRendTNW1ZcUE5K0FON25FYmZYeUxRVkJpQjBYV29TQTR2dzdCZFhUdFJsNkVLVnNzSlRiMEUrWEhEdW1oZGZrUi93SUIvWlk1dTBqR2R0Y2pWV05WSXNKWFhpK0IxbldOWGdqeWYrNDJzaDJwQmhUelNYWjRWR1pxanlab1REV2pVM0U2dG55L0hXS2JtakZ6OHluRFFzdTd2M0t3RU04QVFIS0xzcXJKUytEZkpzdlVrWWJWV1BrZHNTOE1YZDVRQllQa3BOeFBvelVHdDNkU2dIcytaaWdYR0ZFSzg4K3hQdEIzWnB2V2N0S0FZWW1DcEdnNjQycXFXOEt1UVhidUwzWGpjY2w4clR5cWp0eWpyZ0IzWFpUSWY4QTY3WDhLWWxWclJyMExGRU5RUDR1RzlYV3FKanQ0Rk1VQmJ5TjRhcmkiLCJtYWMiOiIzZTE4MDVhOTA5NDBiOGI5OTg5ZDRmZmU3NzAzNGJiYTcyNzc3NTk3MTI2MzViYjEwNWNkNzEwNWY2Y2Y1OWEzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InNOQmpxVTljMWRMb1ZVTm9IeFBFZXc9PSIsInZhbHVlIjoiZlZQUEZENDRzMnJjWlQxWSszc1dNNFU5NHYzQjBMbmIzRTFLTk5SY21FcGdvU1hOZll2RWZiWUFTTXViQWFMdFlwTFJ4cnN0aHJqdTc5WTc3NE11cFRmT0VxeTRNb2NsaUFEbWhyUUNBVUNBV09tUXVacFB3UGlBNXZ0UnlOR3RleXRHWUMyT1ZhSWg1NTF3MFR1UXcyMnZlMS9aY0JPWkcrSmpFOStEb09MNWk3VlhIS3M0NUZPSC82eW1IcHNGZE8rUC9LNnZTdm1YZUI4RXM5ZkpxOFhlTUlXRU5kWS9lSVpuV2hHa0dIQmJTcy92OVFYNjNyTEkxNTYzYjgxaGFaS0Y0RVo3WEFhZWc1Y1FuOE8yWDVVeU51d1pxRUJmRFdmRFlQTXZTWUNsWDBQTHJJdEdkNVoyUzJhU3VrVmM4clNYRW14cEdWSWV6VXZyQ3BXcURCei84WFNqNEhOY1JORmdJdjM1MllkVHM5UDY1UE93SjgrRTZuOEJDNTFVZ2tPekdXSTI4Q3R0L2s4bGlrSWNxaDZKTlNrSy80VDUweTByT0dHeVl1UHpOQ0R6TmxMcGdPU2JORFFIeG9BdFlFbTNDT20wNXZabzF0Q0Z4Y3d5RWxkWCtpWkFzanN3WjcrQ3JwcGJzS3FKQXdlOGlYVVRFOFV3dGZVcS9kMEsiLCJtYWMiOiJiYTc2OTZiNDIyY2U4Yjk1YzdkODdjNDYxYmEwYTExMDU5NTFjODk3ZmU4YzIwY2VmZDQ5MThkODM3MDMxZTQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-155623277\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-789351815 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WjiNcn9cZJfMB3HvNRV2ADeuSdnp1JlxvgqAKjT1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-789351815\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:21:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlA3QmxmVXVKaEhTL3Jsa2dsK09ET3c9PSIsInZhbHVlIjoiQ1dza3UvMDhXN1o2Q1FOTHVna1JPMGJiL1hLamJ4c01oTk1LYjhUQk9CK3JaeFpDREpubGpmU2NRdS9Dam1rVkpqVi8zdDJJbE1KUkZ5WWp6dVIvMEtCUCtwWjFpV2JDQytCL0VKS2wyS3kzekJqMWRMbUp2VFFHNzR5amV1MWc3a3VNbXBubXJrc1JITjlBNEZvcEJRUE9sVXNTSVoxN09GMW9pU3VnMTNPK2w5QVErb1ZRUFB4VkxnNUwycHh5Y3pQMlVTT01CY0cyVjB3Sll0Z29iakx5NTdTcFBmRmJ0OC9xNktmS3ZoS2FTRmN4T1VJUW93cFdPNlczbTNkMnBWR2xERzdDczkxelZzcmRKQ1ZibVNVYlljV1pLcGxsQWNSdFdPZ0k5ZmhlQ2tlUk4zWlNOcWJvNGZZYi9kN3hRSG50UjRnbVV3NVBLL0FRRU9DbEoyczgrZjJXSmxkWVRnUjI5Y084K3Rnd1NQYnFqR1J2clB1UEE3bFJKcHhnbkJEN0NLL2pucVhDNTdZbGNpM1VJTlREUmhPMDE1b1A1SlNENzFQZXNwYkdETTBlTWN6WmQ4TGRwSjlzSDcxN0xiTXFlTDB0L3BmRkM0d3JZMTBmVU4rSUpEOC9MLzdEUlloTGlmTmVjSjkzQ0x6YmZTQys5U3ZiODBKa0t3RGkiLCJtYWMiOiI5MzVlMTliZGFlYmJiNjE4OGUxZmQ3OTFhMjY0NjU5NTg3YjgzM2RmODAzMWQ4Y2Q5NDNiZjZlYTkwNDViZDczIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:21:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImdvRFljZ3V0QzN3VUNLRGZPd2VWU1E9PSIsInZhbHVlIjoiQWlPUm9RVWwrR3pSNjFmTE85cVdpQ0R2RkJMYlRFelZFVlAvV3VnZWF2TVZSRU1BQlRsOWpUbjIvc0Z2dXNtVlVJMzRnOWNaWnpzRUZGbnlYMDh0Rk1rVnRoNzdQVnZYZnp4UGZKUlFUOUZkRDVodTFmbC9yN2VHVnpTOUhIR1c2ekpBZENHS2lESS92YUlENWNPY1pheU50d04yRkRncVZ6cmRHVkZHa1RWSFZQVE85WHdyK04zbHFtL0t3c1Vqb2NYTThvUDFTeGJvREg1SDZMRkthUXBZWGRmand6QXlSZWdEMzdqSWNyNldMN2JsdE5jcFB5cEtOVFJneDdVb1N1czZnM0pEM0IyK3R2UGN4aEs4OUdMeklrbThhcDVSanRtTHAvSFQwVFFJOS82Tm5GRmU3ZUpwM0VRaGlXTEl2VnJsS2pQN21sRENCTzBVcXdnQk9waGpOc3RhN2ZBd3JLU1V6Wkc5NDE5V3F0c2MvSmErUko4cFFVVFAvMVBBenZ4VDR5d1ZUM0dkeHlQRUhWemRmdXJnWk5QT2NJdW9iTGhMaFNOR3d2MDBPQ29jaTJKS2djdWJkQTdwOWVwTTcxYTdtdnRrSHo3dEVIdWIzU1VNOGJrUTVtdXVKSVVjMkdLWjB1dUFMNldEZ3ltblYwUWVoM1h4c0FYMkp0RnUiLCJtYWMiOiJmZDhlMzg5ZjBjMzMzNTUzYjk5OTU4YzE5OTUyMDJlNjBkYWFlZjdhNzc1NDQzODRiYjFiNzAwZGE3MjRjMjEwIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:21:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlA3QmxmVXVKaEhTL3Jsa2dsK09ET3c9PSIsInZhbHVlIjoiQ1dza3UvMDhXN1o2Q1FOTHVna1JPMGJiL1hLamJ4c01oTk1LYjhUQk9CK3JaeFpDREpubGpmU2NRdS9Dam1rVkpqVi8zdDJJbE1KUkZ5WWp6dVIvMEtCUCtwWjFpV2JDQytCL0VKS2wyS3kzekJqMWRMbUp2VFFHNzR5amV1MWc3a3VNbXBubXJrc1JITjlBNEZvcEJRUE9sVXNTSVoxN09GMW9pU3VnMTNPK2w5QVErb1ZRUFB4VkxnNUwycHh5Y3pQMlVTT01CY0cyVjB3Sll0Z29iakx5NTdTcFBmRmJ0OC9xNktmS3ZoS2FTRmN4T1VJUW93cFdPNlczbTNkMnBWR2xERzdDczkxelZzcmRKQ1ZibVNVYlljV1pLcGxsQWNSdFdPZ0k5ZmhlQ2tlUk4zWlNOcWJvNGZZYi9kN3hRSG50UjRnbVV3NVBLL0FRRU9DbEoyczgrZjJXSmxkWVRnUjI5Y084K3Rnd1NQYnFqR1J2clB1UEE3bFJKcHhnbkJEN0NLL2pucVhDNTdZbGNpM1VJTlREUmhPMDE1b1A1SlNENzFQZXNwYkdETTBlTWN6WmQ4TGRwSjlzSDcxN0xiTXFlTDB0L3BmRkM0d3JZMTBmVU4rSUpEOC9MLzdEUlloTGlmTmVjSjkzQ0x6YmZTQys5U3ZiODBKa0t3RGkiLCJtYWMiOiI5MzVlMTliZGFlYmJiNjE4OGUxZmQ3OTFhMjY0NjU5NTg3YjgzM2RmODAzMWQ4Y2Q5NDNiZjZlYTkwNDViZDczIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:21:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImdvRFljZ3V0QzN3VUNLRGZPd2VWU1E9PSIsInZhbHVlIjoiQWlPUm9RVWwrR3pSNjFmTE85cVdpQ0R2RkJMYlRFelZFVlAvV3VnZWF2TVZSRU1BQlRsOWpUbjIvc0Z2dXNtVlVJMzRnOWNaWnpzRUZGbnlYMDh0Rk1rVnRoNzdQVnZYZnp4UGZKUlFUOUZkRDVodTFmbC9yN2VHVnpTOUhIR1c2ekpBZENHS2lESS92YUlENWNPY1pheU50d04yRkRncVZ6cmRHVkZHa1RWSFZQVE85WHdyK04zbHFtL0t3c1Vqb2NYTThvUDFTeGJvREg1SDZMRkthUXBZWGRmand6QXlSZWdEMzdqSWNyNldMN2JsdE5jcFB5cEtOVFJneDdVb1N1czZnM0pEM0IyK3R2UGN4aEs4OUdMeklrbThhcDVSanRtTHAvSFQwVFFJOS82Tm5GRmU3ZUpwM0VRaGlXTEl2VnJsS2pQN21sRENCTzBVcXdnQk9waGpOc3RhN2ZBd3JLU1V6Wkc5NDE5V3F0c2MvSmErUko4cFFVVFAvMVBBenZ4VDR5d1ZUM0dkeHlQRUhWemRmdXJnWk5QT2NJdW9iTGhMaFNOR3d2MDBPQ29jaTJKS2djdWJkQTdwOWVwTTcxYTdtdnRrSHo3dEVIdWIzU1VNOGJrUTVtdXVKSVVjMkdLWjB1dUFMNldEZ3ltblYwUWVoM1h4c0FYMkp0RnUiLCJtYWMiOiJmZDhlMzg5ZjBjMzMzNTUzYjk5OTU4YzE5OTUyMDJlNjBkYWFlZjdhNzc1NDQzODRiYjFiNzAwZGE3MjRjMjEwIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:21:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}