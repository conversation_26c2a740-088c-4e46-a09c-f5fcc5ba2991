{"__meta": {"id": "Xe7267fac0dcfef2b41da6e8679061b86", "datetime": "2025-06-17 07:13:00", "utime": **********.212131, "method": "POST", "uri": "/pricing/update-inline", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750144379.600412, "end": **********.212155, "duration": 0.6117432117462158, "duration_str": "612ms", "measures": [{"label": "Booting", "start": 1750144379.600412, "relative_start": 0, "end": **********.082028, "relative_end": **********.082028, "duration": 0.4816160202026367, "duration_str": "482ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.082046, "relative_start": 0.48163414001464844, "end": **********.212157, "relative_end": 1.9073486328125e-06, "duration": 0.1301109790802002, "duration_str": "130ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52050736, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pricing/update-inline", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PricingController@updateInline", "namespace": null, "prefix": "", "where": [], "as": "pricing.update.inline", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPricingController.php&line=141\" onclick=\"\">app/Http/Controllers/PricingController.php:141-224</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.019560000000000004, "accumulated_duration_str": "19.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.121464, "duration": 0.01225, "duration_str": "12.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 62.628}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1448941, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 62.628, "width_percent": 3.374}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.168072, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 66.002, "width_percent": 5.266}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.171621, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 71.268, "width_percent": 3.272}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '7'", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.1854649, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 74.54, "width_percent": 3.834}, {"sql": "select * from `product_services` where `id` = '7' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["7", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PricingController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PricingController.php", "line": 169}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1897068, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "PricingController.php:169", "source": "app/Http/Controllers/PricingController.php:169", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPricingController.php&line=169", "ajax": false, "filename": "PricingController.php", "line": "169"}, "connection": "ty", "start_percent": 78.374, "width_percent": 3.067}, {"sql": "update `product_services` set `sale_price` = '3', `product_services`.`updated_at` = '2025-06-17 07:13:00' where `id` = 7", "type": "query", "params": [], "bindings": ["3", "2025-06-17 07:13:00", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/PricingController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PricingController.php", "line": 207}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.196073, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "PricingController.php:207", "source": "app/Http/Controllers/PricingController.php:207", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPricingController.php&line=207", "ajax": false, "filename": "PricingController.php", "line": "207"}, "connection": "ty", "start_percent": 81.442, "width_percent": 18.558}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-718112979 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-718112979\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.177313, "xdebug_link": null}]}, "session": {"_token": "TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]"}, "request": {"path_info": "/pricing/update-inline", "status_code": "<pre class=sf-dump id=sf-dump-430955370 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-430955370\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-254154277 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-254154277\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-80539380 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>field</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n  \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str>3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-80539380\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-603223265 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">77</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=17n9b1e%7C1750144374665%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjcwVjFaTDFwNlpKSVd1a2I2TWM5Tmc9PSIsInZhbHVlIjoiZ3JlNWI1RmRGSXBxVDdIRG8xR05mc0JZZW1DNXBnYmI1eFo2a3JRS0t3eC9BcFBVMmtKU0wvNmdGcGk1S1hYQ2JXTkZzTGl2WDRUeDZiTkJ5OEczOHVPY3NRNGI0LzNPUjNkK1haQXE2cGovc1JYNkErc1FBR1VFS3dLVE5pdzVKbmc2aEZzMEhxVjJkWkorVjdWUFhMZUx2L05Dd1h4K1lEd3JiWVlYY2F6ZFF2cGRrSTBGYjhzU1c0K2ZFUkJiMmMzUXhybTBRM2Y4Y2dkYy9iZ3ZjNGRrbkNiaW5PU3RXTHpvVEFrSE13QXFMN3VBa1g4bVZPc0lkbU55d292UG45Z3NXbytJMlVTaDExSzNGVWNaSkpGa3hBMHNNVGR3YTVGSGJkSnQwZWhsdVNFaTB2RTBHOTZ0OVRQMjRLcDBnWTJsTDZUdE4zYk1pRXMyWE9LR29DS2FzWjV4Tm1TK0I1S3dwc1U4OFZiWFpGUHBkc0xjYlFkMnNmVnVUZG5kOXVVTFZXdlp2bFlkVkJ5T01KdktRL0FUZUJIZXlWLzBSaXpleGlqYUk1RitrZmpLbVNhVmFvWi9BS0ZXb25nK0RiUmt3U2JxdThPN1NJcy95Z0dFdDNJWWFLZmE3a3l2ODdBNVJxZk81OXVsbzBwUW5KOUQ4amxGMHZHa3JwakMiLCJtYWMiOiIxNDIyYTBhZDNhNDNjOWQ2ZGM2ODUxNzhmNGU1MTNmOGZlNTk5N2FkMDU3NWE3NTExZjY2NTQ4ODEyMzA3ZTU5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkU3clJ0MW9JWThiRGwrZ0ZaNzVmaEE9PSIsInZhbHVlIjoiZVFPTEtSZndCbGdQcXg5YnFZT2UrUVpOVEpWc3lMbTVoUE5zdEFhbFhnQkttT0RYTE9zbnpDK0owbVZrd2w0WVo1ZGsvTkVITnVJWVBUMUlDclV3SXhLaU9uUE5CVE5pQ3hmbk9vckRVQkRlbW44UG4xZ0N4TmFaWlBmSjV1SnBIbkNQOXYxakZzR2MyRUd6RnlTSDVqVEl0T0Y3VEd0UjY0TzRqc1EvWmJvMFNCK0xVeHdoSUsrY01mUGJYTEdEeTNvWDhmSW5oNnAzeU1abmJWQWpNUGwzRW9MQkUweWM3aTdDbklsb3hoVHBCc2JiU3Y4MUdTY2tCVWY4RDFwV0drSm9oRlFRS3B3dUZkUWxqbE5FdmhVMHRVVnVLdUtmdVF1UEptS3E4N1JGTGpVZEdaQ0xhdDJNb3M1VGFhM01wdnp0MU4yYjFGczFZemdXc245SnRORCtLTEJUdGE3Y2xTL3ZLNDVxbzNjcVcxK0tvbGFDMnJLazBkbTFERnBXc3pHOGVpKytLWldSckdVRElXQmlKdHVSbWtzQk5iS0Radzg4VGVuUWNxMDVYeXNXK2N0NFpnTzdHakJMZlA2cTB3VE5waldEaTErZWpDRjAxdllRRWRaOGdDWENtRFd2WDNKMVZXNW9kZnkxUU1IY0ZQMVBQdnRRZ2dJQjJTY3giLCJtYWMiOiJlM2I2NDI2NGUzZjY2N2YwNTNjM2ViNGM2M2RjZmJiOGI2MmMxMzYzNWY5NTNiNjAyMTU1ZWNhYzM3ZWZlNzZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-603223265\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1716023941 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDWN9h6R9gAIdzZaXBzXfq0WGpauVfrUj70kKWA2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1716023941\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2009842367 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:13:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijk5NFY4bmNSYlh2RnVKQVAvL2NzVUE9PSIsInZhbHVlIjoiNmdQcmRaRk1QTDNmcGN6cWhEL1hsNEcvOG5PQi93b3BGcEtCOFQxaW42dnAxOFNCaFdFQ2RMZCtWbUFwbndISlQ3Tm9sdXpQTWNlczFVU3JlTTJyTjVoemU0R0gvNUlwekxabG9sMnRObzIxSnA1QmpBSEVUdFNPcktOQ2lPdkdTVGlmK2ZrK29DdWNjb25zV2tGSEtBZWtPd1pZbVdOdzhacVExQVFtNERGOVM4OGFsSzl0UlRaZlhjQjhzREk1K0dPdlM1cjBGd09ybWNBTW1Id3lTOWMzeERVbG5yR08yZ2EyUFcrdUtkTlZRSFV2UG9UbzBHQVhObDVMaWkyQ25YK0FralNac2NjNzZ4VG5QWkZLYWprSC9Oa091T1ptR2F6K3ZQdXh5RXNKZjRVM2VqQlhNRGU5c0RwbmZjSHNLN0kxRnlLT0RFeTdpVmFjQnFSVXBSTmdqamFtLzI4aU5UUENBbkJ0K0dGZGpSSXpjdnUyc1N1MGEzSHFwSm1RQ0pYNnBPWlRtaVV6YkVXcTMrcG1nNVJ2QXMxRmcxbjNtZ1c1aC85YlNyKzI2bmFKNUtxSHdteW8ra1JrbU0rQkd2NGlZYXlqN0dBM082MW4vMUJaQUNRT3BtMjJmVzFuWlhaVVZJeVJ2QkZZL09saXJhRHVNREMwWXdKeXhZUHUiLCJtYWMiOiI2MWUwMjM4NDdmODRlZWY1N2I3MTZmZTg2MTBjN2I3OWJhMzY1OTUxN2NlMjMyNjBmMjEwNGNiNWJlZTI5MTdhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:13:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik91RnZCRDQ1cnErNHhOMjYvc0VnQUE9PSIsInZhbHVlIjoiYWRueis0Z3prOEFvM2M5TldwV2pHYzJTRUtRSm9yVUFNWnB6MkQybkFldG83VlFqbHpCRjk1S0VsQlVOdWNuT0pLdkkvUEU5NFFiQzlhblNIa0tyelZpblNLVnhqVE9Ta08wQmJGWmhEaW51ZFErMWlBSXE3OVNPYlhCcWhxbWJsQkt3bjJPL1lOdFNiMG5GNEVzSlBOam1kR2MzVmxGa1J0OTFHNVUyMk1VRnhBTEtjcGdQd3llYWorY0tpMlMrTVFQdHJHS20wdmVUNGFWNk5VT3Vqby96cU4remU5eTd2a2hkSFlJNHJkWGdmY3pTbWg4MVhvK0Q0RVduNU9lWnJtWml6d2RhTGsrLzc1Y2JUSHVqdzJzT3hYYjZ5TU82OWxvRnBoNXgrZXgzU0JOTUgxcFZQOEhoSFloUU0xelh2TGY5OUgxenN2Q1VyRkdUdm1ETUEzZDZUeUtJYXNOSnlsZGlDeXlRb2tyM0o1aExEeW9RQnlSekNXbng4dW9OUExkd2V0ZERuZnZFcnEzUjNxS2V5ZFo3Tk9iTjl3d05iU09sRkZCLzFFbzRuR2Nwam0vaFhTVm9EZFltM1cxbTVzMHhFanhmbUJnZURoc2FwaitWeWZDRnl4eWl3aDR1Ry9GeWNZOStXK0J3am04NXVYRkJIdFI3MDVUSGIrTEIiLCJtYWMiOiI4ZjJlMmZhYTVkNTk5YzdmYmY3NDcwMGYxMjQ2MDgyZTRlYzc0ZmFmM2EwMmIzMzNiYjRkY2E1NDA2OGRiYTBlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:13:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijk5NFY4bmNSYlh2RnVKQVAvL2NzVUE9PSIsInZhbHVlIjoiNmdQcmRaRk1QTDNmcGN6cWhEL1hsNEcvOG5PQi93b3BGcEtCOFQxaW42dnAxOFNCaFdFQ2RMZCtWbUFwbndISlQ3Tm9sdXpQTWNlczFVU3JlTTJyTjVoemU0R0gvNUlwekxabG9sMnRObzIxSnA1QmpBSEVUdFNPcktOQ2lPdkdTVGlmK2ZrK29DdWNjb25zV2tGSEtBZWtPd1pZbVdOdzhacVExQVFtNERGOVM4OGFsSzl0UlRaZlhjQjhzREk1K0dPdlM1cjBGd09ybWNBTW1Id3lTOWMzeERVbG5yR08yZ2EyUFcrdUtkTlZRSFV2UG9UbzBHQVhObDVMaWkyQ25YK0FralNac2NjNzZ4VG5QWkZLYWprSC9Oa091T1ptR2F6K3ZQdXh5RXNKZjRVM2VqQlhNRGU5c0RwbmZjSHNLN0kxRnlLT0RFeTdpVmFjQnFSVXBSTmdqamFtLzI4aU5UUENBbkJ0K0dGZGpSSXpjdnUyc1N1MGEzSHFwSm1RQ0pYNnBPWlRtaVV6YkVXcTMrcG1nNVJ2QXMxRmcxbjNtZ1c1aC85YlNyKzI2bmFKNUtxSHdteW8ra1JrbU0rQkd2NGlZYXlqN0dBM082MW4vMUJaQUNRT3BtMjJmVzFuWlhaVVZJeVJ2QkZZL09saXJhRHVNREMwWXdKeXhZUHUiLCJtYWMiOiI2MWUwMjM4NDdmODRlZWY1N2I3MTZmZTg2MTBjN2I3OWJhMzY1OTUxN2NlMjMyNjBmMjEwNGNiNWJlZTI5MTdhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:13:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik91RnZCRDQ1cnErNHhOMjYvc0VnQUE9PSIsInZhbHVlIjoiYWRueis0Z3prOEFvM2M5TldwV2pHYzJTRUtRSm9yVUFNWnB6MkQybkFldG83VlFqbHpCRjk1S0VsQlVOdWNuT0pLdkkvUEU5NFFiQzlhblNIa0tyelZpblNLVnhqVE9Ta08wQmJGWmhEaW51ZFErMWlBSXE3OVNPYlhCcWhxbWJsQkt3bjJPL1lOdFNiMG5GNEVzSlBOam1kR2MzVmxGa1J0OTFHNVUyMk1VRnhBTEtjcGdQd3llYWorY0tpMlMrTVFQdHJHS20wdmVUNGFWNk5VT3Vqby96cU4remU5eTd2a2hkSFlJNHJkWGdmY3pTbWg4MVhvK0Q0RVduNU9lWnJtWml6d2RhTGsrLzc1Y2JUSHVqdzJzT3hYYjZ5TU82OWxvRnBoNXgrZXgzU0JOTUgxcFZQOEhoSFloUU0xelh2TGY5OUgxenN2Q1VyRkdUdm1ETUEzZDZUeUtJYXNOSnlsZGlDeXlRb2tyM0o1aExEeW9RQnlSekNXbng4dW9OUExkd2V0ZERuZnZFcnEzUjNxS2V5ZFo3Tk9iTjl3d05iU09sRkZCLzFFbzRuR2Nwam0vaFhTVm9EZFltM1cxbTVzMHhFanhmbUJnZURoc2FwaitWeWZDRnl4eWl3aDR1Ry9GeWNZOStXK0J3am04NXVYRkJIdFI3MDVUSGIrTEIiLCJtYWMiOiI4ZjJlMmZhYTVkNTk5YzdmYmY3NDcwMGYxMjQ2MDgyZTRlYzc0ZmFmM2EwMmIzMzNiYjRkY2E1NDA2OGRiYTBlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:13:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2009842367\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-169299496 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-169299496\", {\"maxDepth\":0})</script>\n"}}