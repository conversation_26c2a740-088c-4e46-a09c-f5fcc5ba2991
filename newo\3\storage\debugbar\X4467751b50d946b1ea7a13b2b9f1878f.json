{"__meta": {"id": "X4467751b50d946b1ea7a13b2b9f1878f", "datetime": "2025-06-17 05:40:49", "utime": 1750138849.0936, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750138847.60303, "end": 1750138849.093633, "duration": 1.490602970123291, "duration_str": "1.49s", "measures": [{"label": "Booting", "start": 1750138847.60303, "relative_start": 0, "end": **********.875194, "relative_end": **********.875194, "duration": 1.2721641063690186, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.875229, "relative_start": 1.2721989154815674, "end": 1750138849.093647, "relative_end": 1.4066696166992188e-05, "duration": 0.21841812133789062, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45219144, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.026609999999999998, "accumulated_duration_str": "26.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.957652, "duration": 0.02121, "duration_str": "21.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 79.707}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1750138849.003135, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 79.707, "width_percent": 5.073}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750138849.053914, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 84.78, "width_percent": 8.418}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750138849.072943, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.198, "width_percent": 6.802}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1211409880 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1211409880\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1862259379 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1862259379\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1458777839 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1458777839\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1755081677 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; XSRF-TOKEN=eyJpdiI6Ik9JRnd3NVZFZFJRTitiejg3aWRsc1E9PSIsInZhbHVlIjoicTlIcTlYR3BxeGRtd1dJOEZOdmFnUVZCbE1IdElnZEF3M3lWTFZPaGN0OGlsUytnMVhiamcySHB3Ynlaa2them5FWEE1S3NRTXdlYXJHcFlpTGNvcFp3aEwzR3ZVR1hiUFlWWVdrMzhlQ2VYakt4eUZob3VjbHl5UGlFK3dDemVvRitHMy8wczZRR1ZsZUtRKzgxZ2xKTHpWVFFYZ3pIMEpWV05iK09yTnNiVmpHZ09KVjZvNFlQK25vaXlZVU1BT1NoWXVrWEdiT3ZRYmNFci9NdFg3S2s0TXBPNWVmKytXTGhYNHdlVk94NngwQzBCZWdtZHB4Ny96YTRtZlBteXJ1TEJjRG54UjR4Q1ZVODNJWVVKY3FhZlpSMlEvTkx6TTVUMEJlcC9UbFpUWlQyYW5NQTJyaS9LQU5zdGRoUEFGcCtwUG55WFMzb1BHUk5RUlE5SWVNMklFUGxlT1NZUnA4TGRlRFBUdURVNW9JR0VZVTRWZVR2MzVWVGhuQ1RsRDk1aTZpemRzRXd1RUZTNFgycjkvcUJhUk1nTUdCZC9ycVF0K1NmVWEvV2hYSmxvRWI5bUdFRXQyVm56QTBsNGQ2SFZYTE1NR0g3dDNrQ3VHVDZDQVE4dk40d0FsWmRLdUpJc05sYi82VmVBSXBuV3hSajFkK1hqdCt4SUxNeUsiLCJtYWMiOiJmZDQ5OGUyYWYzZGRjYmQ1N2M0ODJiNWU4N2QzNjhhZDhmNWY0ODE1ZDNlNTk5ODFiMWRkYTVlY2E5NTVkNjcyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlNKcy9JODdhOEVLSDM4ckNMdk05TUE9PSIsInZhbHVlIjoiNEFnNXdLWXFydVJ5WDRLOXZYRGNCdEp0OVBEVFVCVzFFS01KMndXY2wrY0VJVkpWTG9wUWdzU3pCWFhJU1JITmFwdytnRjkrV0d3N2lWME5OOXFhc2dVZ3NEdjR6Z3ZaS3hySGNUdUxtaHh6UWFReldseEx3N2FZdUtsc1NUM2liVnB3di9JMVFud20xM09hV1R0bng4NTV1cUZhc29ob0Y2L2lUYk5hYklRMlFUb2FnTVdEdXFxQ2cwbFBhQTdtWEJaS1pWR2I3dVpWNGg4ZmR5ZVA1VmZUZitoSlNZMGZCejgyZS9CVXNQYjJKVTNvS2dpNUxkSFFtU3JNR3U1TldXSnZlZWxoalVYNXZVTFZVSXkyZjRVTnJyUTFKcThodEFXc0NXcEhONjdmMUI1Y0NCOExublpuU0FyM2duajljTUNyYXIvdzRzckExMWozMG5ZNlBYTk4vaFhxNDByZE5kYnp4SWc3RWw2RWpvOUZOd1IxZ3VLWGRXWUZSZGNtTkcvM0x1VVlxVldvK3BIbVhrQXE2dEc5bU4xRG1tZFg0Y3ZRbU1xSHJiWHA5bEhBaHhnbmh1clB3dzJOMWFSQU1aQ3N3TS9tbFJWanZmaVZwMmZ0MTF1YWtLWFB6RWJCb2dXallmMUU2YnhqZ2VrNk9qcTJYZTl0QzhOS2Y1cjIiLCJtYWMiOiIzOWNiMDY0NjA4OWJlZjIyODczNjgzZWI1Njc2YjUzNzVkMDFkOWQ3NTViMTM5MmQwMzVkYTA4YjZmZThkY2QwIiwidGFnIjoiIn0%3D; _clsk=1jp2hip%7C1750138846964%7C6%7C1%7Cl.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1755081677\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1267213961 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MsEpVpyAabX9l6kTbr1IUggTy4SSXp1me81GakZC</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1267213961\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1623279960 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:40:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFpWDdaQnBkUE9OMVpYRkN1Rit5TFE9PSIsInZhbHVlIjoiQ1RjT3F0NUlyaDJsZ0ZONklLVmJzR0NyNC9wemwvNzJKVUc1QVVIWW0remN4ek1ieUZoV2lPOE54d3lmL2I4b0JMQVZhc3I0dEVIUldPWUg3L2pWU2hoZHAzcFFSdDZWMnJ2OHZFbXVibXFhaW9sM01Ga1ljc1ZUTEhkVzRKSnM5N21YdE9yMFlxYWZHbm4xbHpYc1AyMGNhWGZPcTJDU0I4ZWY0L3VVUWV1R3doSHFnWGhQYVc5ZVU4VTBGQXdHcmpYK24xRFkvTk43cVBnYVF6SkMwem5MMEdtdzc4WWFWWkdWK2ovV2ZQQjJ4Ui9yUFgwcHltaGF5S3EwNWhYNUpKY3dYRW9YTzBNNHJVN0VUMTFKMVZHL1RtazNVUEM3cFRydkUyaEVLNldNWFJKTlo4djh1K2pvbnYrRm94TTBNditXVTloV0w5TXVuWTFIU2s4ODlaNzlEa2twQWpDbWE4N08yRlJQUmxTRGJQVjlCWWFIamQ3MmdBSERpUFR0alRSWVg5MkxMZStwa3JZQzRPdVpuSWFmZno0clcvZFgrbXg1TWVvS3lWaTBUWjVYUUpXWTdHWUhsODZSR1JqY2RnN2gvWnUybW1nVzBWSHN0NlN3SysyZFpiOHhzdC9XZjZ1Y0RHZlc4UkwwYlllSWpjTFkzVlZ1RFJTREZvMFciLCJtYWMiOiI1M2NhYzMwZThmZWM4ZTZhNmZkODAyMWJkOGUyZGU5MjBmZjllOTU3MWIxZTczYjFhMTcyNDFmYmQzODAyMTdlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IndYQUtrMlZQSDBUajlyVGIyOXNZNkE9PSIsInZhbHVlIjoiWGJ2cFRtaU0zU2sveFFMaysyRlA5dWdiSVNNWFM5MlAxY1h5K0sxdFpJS0tJSitrODNRK3dISUlTL09ac1o4OGp2ZnhMdWdIOHJjYlVadHBzWGtLVUg0M2dWU2ZUNnNhK0FSaGUxaFdqOWdscXVLeHB3c3lobmRMVjlCblNNczlPU2FDdHpETERpb3RMMW5ZS3dYYXd4OVdOdnhmSkVKSzZCMittSWxiaE1KK2pRbXIyeEtNSm0zMkVCZW4vVFdyeFdHYmQxYUFPbFRtOXROY0pxUE5lbnNwUlB1bTZJSVk5aXJKbjc1S2JVODVLZS9UWmJGSC9XTVhySHdNQUpoV2w2SWZNb3p3ZHJrcWdPTHdJcm9GWVN0MHZXd2FQdFRRL1ZoNlNvZ3lXWkI5QzBvYWtrc0JheTU3c09GaFBMMnU4bWdJVHRteFRIN3NjcWdQRUY5blpSY1Yvb1NZV3lxWktPUi9qdTBNWER4VTd1WVZ5YnYyQWtlN2dnaHRqOVd4bVMzQkFkdXZheWtGVzJ4Q01MUkUvSk9yKzVpUTMxTGpvZkVxeFdyV2N4L1NsT1pNeFlvbXdiamEvaWVIV1VYUm9ROGU0Z1VBbll6UWJIOFhXblVLYUc4bjFHY2hpaHFBeDFReXdBdXBONS93cDl2blZJNDV6V1BGRzRYRkNEYlgiLCJtYWMiOiI2OTE0MzQ0Y2JjOGY5MDY2OWM4MTA3NzBmY2YyMzk3MzI2OTZkYWUzMTU4OGI5M2QwNGMwYzhlZTQ4YTUxNjhkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFpWDdaQnBkUE9OMVpYRkN1Rit5TFE9PSIsInZhbHVlIjoiQ1RjT3F0NUlyaDJsZ0ZONklLVmJzR0NyNC9wemwvNzJKVUc1QVVIWW0remN4ek1ieUZoV2lPOE54d3lmL2I4b0JMQVZhc3I0dEVIUldPWUg3L2pWU2hoZHAzcFFSdDZWMnJ2OHZFbXVibXFhaW9sM01Ga1ljc1ZUTEhkVzRKSnM5N21YdE9yMFlxYWZHbm4xbHpYc1AyMGNhWGZPcTJDU0I4ZWY0L3VVUWV1R3doSHFnWGhQYVc5ZVU4VTBGQXdHcmpYK24xRFkvTk43cVBnYVF6SkMwem5MMEdtdzc4WWFWWkdWK2ovV2ZQQjJ4Ui9yUFgwcHltaGF5S3EwNWhYNUpKY3dYRW9YTzBNNHJVN0VUMTFKMVZHL1RtazNVUEM3cFRydkUyaEVLNldNWFJKTlo4djh1K2pvbnYrRm94TTBNditXVTloV0w5TXVuWTFIU2s4ODlaNzlEa2twQWpDbWE4N08yRlJQUmxTRGJQVjlCWWFIamQ3MmdBSERpUFR0alRSWVg5MkxMZStwa3JZQzRPdVpuSWFmZno0clcvZFgrbXg1TWVvS3lWaTBUWjVYUUpXWTdHWUhsODZSR1JqY2RnN2gvWnUybW1nVzBWSHN0NlN3SysyZFpiOHhzdC9XZjZ1Y0RHZlc4UkwwYlllSWpjTFkzVlZ1RFJTREZvMFciLCJtYWMiOiI1M2NhYzMwZThmZWM4ZTZhNmZkODAyMWJkOGUyZGU5MjBmZjllOTU3MWIxZTczYjFhMTcyNDFmYmQzODAyMTdlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IndYQUtrMlZQSDBUajlyVGIyOXNZNkE9PSIsInZhbHVlIjoiWGJ2cFRtaU0zU2sveFFMaysyRlA5dWdiSVNNWFM5MlAxY1h5K0sxdFpJS0tJSitrODNRK3dISUlTL09ac1o4OGp2ZnhMdWdIOHJjYlVadHBzWGtLVUg0M2dWU2ZUNnNhK0FSaGUxaFdqOWdscXVLeHB3c3lobmRMVjlCblNNczlPU2FDdHpETERpb3RMMW5ZS3dYYXd4OVdOdnhmSkVKSzZCMittSWxiaE1KK2pRbXIyeEtNSm0zMkVCZW4vVFdyeFdHYmQxYUFPbFRtOXROY0pxUE5lbnNwUlB1bTZJSVk5aXJKbjc1S2JVODVLZS9UWmJGSC9XTVhySHdNQUpoV2w2SWZNb3p3ZHJrcWdPTHdJcm9GWVN0MHZXd2FQdFRRL1ZoNlNvZ3lXWkI5QzBvYWtrc0JheTU3c09GaFBMMnU4bWdJVHRteFRIN3NjcWdQRUY5blpSY1Yvb1NZV3lxWktPUi9qdTBNWER4VTd1WVZ5YnYyQWtlN2dnaHRqOVd4bVMzQkFkdXZheWtGVzJ4Q01MUkUvSk9yKzVpUTMxTGpvZkVxeFdyV2N4L1NsT1pNeFlvbXdiamEvaWVIV1VYUm9ROGU0Z1VBbll6UWJIOFhXblVLYUc4bjFHY2hpaHFBeDFReXdBdXBONS93cDl2blZJNDV6V1BGRzRYRkNEYlgiLCJtYWMiOiI2OTE0MzQ0Y2JjOGY5MDY2OWM4MTA3NzBmY2YyMzk3MzI2OTZkYWUzMTU4OGI5M2QwNGMwYzhlZTQ4YTUxNjhkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1623279960\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1402766988 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1402766988\", {\"maxDepth\":0})</script>\n"}}