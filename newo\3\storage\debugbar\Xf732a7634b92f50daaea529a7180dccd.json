{"__meta": {"id": "Xf732a7634b92f50daaea529a7180dccd", "datetime": "2025-06-17 05:41:24", "utime": **********.571292, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750138883.221988, "end": **********.571325, "duration": 1.349337100982666, "duration_str": "1.35s", "measures": [{"label": "Booting", "start": 1750138883.221988, "relative_start": 0, "end": **********.392349, "relative_end": **********.392349, "duration": 1.170361042022705, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.392371, "relative_start": 1.1703829765319824, "end": **********.571328, "relative_end": 2.86102294921875e-06, "duration": 0.1789569854736328, "duration_str": "179ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45169128, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.008239999999999999, "accumulated_duration_str": "8.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4834561, "duration": 0.00537, "duration_str": "5.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 65.17}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.521292, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 65.17, "width_percent": 14.806}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.544093, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 79.976, "width_percent": 20.024}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-986884329 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-986884329\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-959343977 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-959343977\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1633196234 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1633196234\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750138863874%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IklFazNlS3FWTVE2TXlIMGpBb2JOenc9PSIsInZhbHVlIjoiUEZNaWNjR1lzVEV0bzM3THI2eXNNVENGRnJVckJYK2ExVVN0a2phRkhpeENLWmxkelprZ3BGMGhnbzVZdU9UbGQySDhFSnJoaWVxcHZxclZRam11WjlxZk81cFJQRFhiNHJlL1VORmJBcGFzUENxTGo3eG5kZUNNRWRaVXEvMktJbW9NRnFOeGRBWnNORFpjam9VVTZjWVZDTjhTYitENzFzcE9SRVN6RUh0OHNzMEtyeVFZVzJuQVkvVEpjYVdZbmh5djlqSVYrRnlIRXJzOGFSa0d1MEJlRnZjWDV2Z3haeSsxdGJvMitNSkI4R1cwczZOQXlkZjNWZFFwNmR0TXRIUVR5TmN0VWpiRzNYOWRiUk9ZdmI0RzBlcFFkMGIxb05VbE90RnZPTHhrZkgwdCtWL212K3Y2NFVPenRTdU1KUWsxdFJ4SC9YTWk2YlArS3NzNUNTOFloeEhnbjBveTV0S3BOelpPUUVDbG5MSUZ2SWRXMU03aDgyc0gxVnR2UUpQOGZYRzRhczBLaTBWQjZCNVdrM0plL1hUbWFSTlBsRFVIaUxnZmlha0VUMFFqRHZGaW90MW1GMEVRa3hDa24yVExsU3drMUFqSDkzOHY5dUFZNmNyRlJpS09TeUFxU2ZLTjFGZCtJc1pzWTV6akJVQzZHaWFobHpzcmdaWVgiLCJtYWMiOiI2MTcwMDVhYzg3Mzk1YmRhOTI0NGQzYjIxMGU0OTQ0N2VjNzllYTY3YzkyZDY3YWQ1MWRiNTFhMDBiYWI0M2UxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImJ3QTJFU0RGTCtNc0wzMWJNNmROWmc9PSIsInZhbHVlIjoiTGZiZjlkbFU3VUhNZmdhM3BBOE10RThEclhSTW84RDVSOXZUZFdscFRMdmNLYWF5bkNKeGY4dFBoVjhyWkJYZnVrN0JKcnUzdGdnampOdHBDb1Zqalh0MzFqL2p2NUpYOTl3MUU1SktsekxBR2d3WCtPRVk0eXJMQU9ZdWV1WFYvSWZvMGd6YkJSTGg2RjcyZ2k2TFpNWldpOTJPaXJxYmdhY2tUTGlqT2xCNmNldkQ0ek9sQTh1NVlHRDI2THJtVExraXpqRzJnc2xlclh4WE12TjY2S3M4NUthMUZSSnZtNkZ0eDJDbmhTQTVTTTBPZTZtS2FHTW1CZDNjUGI3TUJsWGVVZlRhd0IzWUY4WG4xQ1BMSDRHbkptNlBMUmdDK2VaK0Nwd3VUcG0wYjZ3L0VvSUVFUEpCTyt5V29PUU1JaFQ4OTJyN2xqMkdCNldwSTA4RC92bjhBcDVSclhsZ1ZxR0c0bXU2Ui9UUlh0L2xDMUdFNVFpN3gyMFk0NDFmczNKODVYWjRYblZ6Wk9kazVlZEU5NTdiTkRkekNJYlcrSjlkbHdSNDFPMGFxNTVNTlVtKy9VMmlKQ3NFalhnVUQ2QUR3MDZsZTBhdXpqSmNOMkpXSFY3U3B6OTZjQ2JjeWtWSFZtM3lPYUM2Wm92blNXRzU1NXZLQkFyZU1lOE8iLCJtYWMiOiI2NmIyNGVlZDFmODQ4MTBjMTYzOGQ0YmY0NDNmOTk3YzI4NjZlNTViMjVjMmJhNTIyODQ1ZWQ0MGEyZTU4NTY5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1064279960 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:41:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZRa3dySjl3VHpWYXhSTGxsaVBwdkE9PSIsInZhbHVlIjoiamJEYzJYL0dMMko5ZG1BYUNmcUtGWTRWZFpRcGorUVZISjdTaWF3OWZzSGpmdjkyRTJkWUFiR3ozWS91U0xCVVhMMmJsRXZOTmZsTm9QWGd4Sm1sNHdmcERJNHhaK3hMeHNRalB0RE1panRNdGkzbklNZGJsT05TME1scDlQV1VYTmVmWDZyQWtLVlp2Ly9jTG1FZ3JDSHJDdmVnK3pHMU1MTnBCVkRQblJ0YWdZWXJ3NTh5QmVva283TVdQaVVQTGVBZjFYcGRRd2xxOGJuVXluT1ZyQ3VVOGlFVjVPb3p2OFFFY2JQS0VYaWp3aDlZYU5PVzQwTFBFYjNWMzA2WDl3Vytpbk9XSUduYVVHWi94dDhDTnpvTnpTVktONjA5Wmo2RjJ2ODcwWVQzNFJNZitsQ25ySktTSEhRYUl4Wi9BNlhDeGtwUG8vZFFQNXhLajJWM3FHVkNvcFRpeGhZd2lNMG9Za29NNmxKRlpMYlZ5OWIwWW40YlpCdlZadk85WG8vbTdxQW8yL09VZ1VGLzN5cVNvMnNSYjF0d0xuM2VlUmh2WFZMVzZucWdOcURBeVUydzlRSXhNcTVqcXhmOStHQk16TTJ0dy8wVVNTMkQ0TEV3dDRWUkZlajA1R0cySUY2TDVDRkxnUUh4V2w5Nk11YTMvK0NERnZIK05wUU4iLCJtYWMiOiJjNmY1NTZkZTA4ZTllZWIyMzRhOWJjOTcwYWNiNzJmYjY4MGI3YTYwNmUxYmE2ZjAyODY0MGJhMmFlZjdkMDVkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:41:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IitnclZ6SWhQUnN4WnNzYjZjT2FRd0E9PSIsInZhbHVlIjoiL2l5TFY0b2FuQnhuV3hzWEgzSkQrU2MzMitOcEtQRUR6UXZ5bFlBcnJLRE94NjRET1lEdm4walU3OEdiRHQrVm40UG0vOHh5OFdUSExRV3U5NHptdkNFUFlmMkR0Vjdrd3UyUnY0a2hUdjJSVVR5SnNkUTZkK0dlQzc0c3p1QlEveXlBeHIyVm1kV01lc3o0andBUUR5U1E4WGdNeEVUNldNK09yQ01kdjdSTVJNOEpRdzhoNEVIMmdYNnhINHRTRi83OU83bWNhUFBuMVpmYTJJTnY4UmFFUUxuRmptRTdWcnNSYXFpeXE4UU8yKzlVRDRhNE1ERHNmc0pyVHRaVWZiN3pwY3NlZllHamFERHFRRnN5cUhrRDl4cXNOTUt1NWRYaDJvemlrZGRxZnFvN2JvckdtVzF6bm4vQURUcDVmM3p5ekNEbjdMN1FmdHRTY1cyOExYejRlWTZUOHZwZnFKemxJN2JJTytWNkF5M1BHOHNaQ1RaMi9MVVBjUUlEVzZBTS9LWi82WHlTNlRHMmdtTktjb0JUazFMeC8vSU1nMzVGMG5wTGl1S1JWdk12eEZGbDA1cmo1YjRJejJ4WW5LRDhqY0JYMVpEN0RpRUJmdkNsOTlqVXdSQTQ0ZE5sc0wvNVI3UjVFRlJzTVgzcy9FaVNoaFF6ZjduUGVCVUoiLCJtYWMiOiI0NDk5MWQ4YTllNmEzNjZkN2Q2ZDZmNjJiOTQzNTQ5ODYyMjJkOGEyMzcyNTk3NGIxNWE2ZjZiNTliOGI0Nzc0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:41:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZRa3dySjl3VHpWYXhSTGxsaVBwdkE9PSIsInZhbHVlIjoiamJEYzJYL0dMMko5ZG1BYUNmcUtGWTRWZFpRcGorUVZISjdTaWF3OWZzSGpmdjkyRTJkWUFiR3ozWS91U0xCVVhMMmJsRXZOTmZsTm9QWGd4Sm1sNHdmcERJNHhaK3hMeHNRalB0RE1panRNdGkzbklNZGJsT05TME1scDlQV1VYTmVmWDZyQWtLVlp2Ly9jTG1FZ3JDSHJDdmVnK3pHMU1MTnBCVkRQblJ0YWdZWXJ3NTh5QmVva283TVdQaVVQTGVBZjFYcGRRd2xxOGJuVXluT1ZyQ3VVOGlFVjVPb3p2OFFFY2JQS0VYaWp3aDlZYU5PVzQwTFBFYjNWMzA2WDl3Vytpbk9XSUduYVVHWi94dDhDTnpvTnpTVktONjA5Wmo2RjJ2ODcwWVQzNFJNZitsQ25ySktTSEhRYUl4Wi9BNlhDeGtwUG8vZFFQNXhLajJWM3FHVkNvcFRpeGhZd2lNMG9Za29NNmxKRlpMYlZ5OWIwWW40YlpCdlZadk85WG8vbTdxQW8yL09VZ1VGLzN5cVNvMnNSYjF0d0xuM2VlUmh2WFZMVzZucWdOcURBeVUydzlRSXhNcTVqcXhmOStHQk16TTJ0dy8wVVNTMkQ0TEV3dDRWUkZlajA1R0cySUY2TDVDRkxnUUh4V2w5Nk11YTMvK0NERnZIK05wUU4iLCJtYWMiOiJjNmY1NTZkZTA4ZTllZWIyMzRhOWJjOTcwYWNiNzJmYjY4MGI3YTYwNmUxYmE2ZjAyODY0MGJhMmFlZjdkMDVkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:41:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IitnclZ6SWhQUnN4WnNzYjZjT2FRd0E9PSIsInZhbHVlIjoiL2l5TFY0b2FuQnhuV3hzWEgzSkQrU2MzMitOcEtQRUR6UXZ5bFlBcnJLRE94NjRET1lEdm4walU3OEdiRHQrVm40UG0vOHh5OFdUSExRV3U5NHptdkNFUFlmMkR0Vjdrd3UyUnY0a2hUdjJSVVR5SnNkUTZkK0dlQzc0c3p1QlEveXlBeHIyVm1kV01lc3o0andBUUR5U1E4WGdNeEVUNldNK09yQ01kdjdSTVJNOEpRdzhoNEVIMmdYNnhINHRTRi83OU83bWNhUFBuMVpmYTJJTnY4UmFFUUxuRmptRTdWcnNSYXFpeXE4UU8yKzlVRDRhNE1ERHNmc0pyVHRaVWZiN3pwY3NlZllHamFERHFRRnN5cUhrRDl4cXNOTUt1NWRYaDJvemlrZGRxZnFvN2JvckdtVzF6bm4vQURUcDVmM3p5ekNEbjdMN1FmdHRTY1cyOExYejRlWTZUOHZwZnFKemxJN2JJTytWNkF5M1BHOHNaQ1RaMi9MVVBjUUlEVzZBTS9LWi82WHlTNlRHMmdtTktjb0JUazFMeC8vSU1nMzVGMG5wTGl1S1JWdk12eEZGbDA1cmo1YjRJejJ4WW5LRDhqY0JYMVpEN0RpRUJmdkNsOTlqVXdSQTQ0ZE5sc0wvNVI3UjVFRlJzTVgzcy9FaVNoaFF6ZjduUGVCVUoiLCJtYWMiOiI0NDk5MWQ4YTllNmEzNjZkN2Q2ZDZmNjJiOTQzNTQ5ODYyMjJkOGEyMzcyNTk3NGIxNWE2ZjZiNTliOGI0Nzc0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:41:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1064279960\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1879196955 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1879196955\", {\"maxDepth\":0})</script>\n"}}