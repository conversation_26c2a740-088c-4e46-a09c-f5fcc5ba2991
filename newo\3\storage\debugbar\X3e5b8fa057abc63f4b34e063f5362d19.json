{"__meta": {"id": "X3e5b8fa057abc63f4b34e063f5362d19", "datetime": "2025-06-17 06:53:27", "utime": **********.363362, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143205.684744, "end": **********.363404, "duration": 1.6786601543426514, "duration_str": "1.68s", "measures": [{"label": "Booting", "start": 1750143205.684744, "relative_start": 0, "end": **********.152943, "relative_end": **********.152943, "duration": 1.4681990146636963, "duration_str": "1.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.152969, "relative_start": 1.4682250022888184, "end": **********.363408, "relative_end": 4.0531158447265625e-06, "duration": 0.21043920516967773, "duration_str": "210ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45154024, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02093, "accumulated_duration_str": "20.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.25572, "duration": 0.01829, "duration_str": "18.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.387}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.310358, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.387, "width_percent": 6.02}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3329551, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.407, "width_percent": 6.593}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1193108014 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1193108014\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-674599194 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-674599194\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1816362895 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1816362895\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2142458976 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1930 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; XSRF-TOKEN=eyJpdiI6Ikp6b04wck1Tb0JpZUJUeStIWFdkYlE9PSIsInZhbHVlIjoiRDRtWmdhejF3WHJJbXVUbU1zMlZIdnVYR1VUUENnUEw3dWVjd2prK1RzUnBWTmw0aDhpM1BKZDI0M0ZzcCtLTEF5M3R2VVcvUVZ1TkRZZ24zZmdnSG53cjJyOWw3ZlRKUFRrRFJ3Tk5HWWN1Rmp2T2s4WDZkWiswcDJpZGEzS3Q3Y3JZb2pZZWtNT3JYV2pXQ2RwNDdPQWlzMnh3SUMrejRneXMxQ1FPTCtsV3VCUFF4UmFRYzlGM1hjL0JvUTNTREgzeDA4c25OVkMvSkJCNjhYNDNENmRrV2ZkY1hqVC8yZDVVMFowWGtVOVNpK0E4bXFKL0g5WHpmV1ZLdTB5UWt1Sk9JSnNnRU10d1JqcVcyMXFSdlYvUTdjSlNKYUZtR1JwNnFPbmYraXFrNkhBejgvVER3RjRxelF0RUQvbU5qM0tkT2RpRnhWU3JQbHl6bThLTGxKVE05eTVyNEtMT3d0SUJiT3ZabDBnT3VPUU0reWJnbFhSMG5ZYmNFNTliM2p0MU1EYU9ZbGgxUUlBZDg3ZWlsQXZUOG0rNUQ1NWEwdmEwaFBUZnYzRml5QSt0S2lxeVNXbnRkTG8zaVdjRXlwT09VZTdZcG5Jc0c0YU5WL0ZZZlhpME1VbkdrbDR3cDJLemEzTU9pZWdqcjBCQkkxMVVDMFVFcy9jekZkbnciLCJtYWMiOiIxMjBiY2JiNTI4YzNkNTJmYzBkYjY3OWRmNmUwYmVkNzllYjE2YTkwZWEzNzczZjI2MmNlZmEwNTIxMGI2MGNkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZhUnZnaDFaL1o4ZzYyU2k2dElIZUE9PSIsInZhbHVlIjoibEVlQmxXVDdhL2VQdVdaVXlxUExDWktYb0hRUHltV3dQam5XTWxVVWFtL00vWTNVZGJQc2hTdTVSUWlmWDNvU25OU3ZodGwvSXhsRVFjclc1cnFPYUw3bERsWHp5a05PL0ZnWVE1d0hCREE4cUM0S05CMndNcEc5RHdzQ0phVzZRYnd2bEt6N21EQ2hESDRXV1FnSzFXQ3kvVUZwUkNyMFpBbjJPRytLcTNXbFZDMloreFhqMjhCazZMTWVjcEZwekhFQkl6MkQzSWVVWEtla2dHUmJHaUo1ZmFHTmFvbXFvRU5JS2ZzbXNlUVNRQWxFL3oxYmd0TXZiK0JobmdCcWpXY1lqNXJ1T2RBOFZLcDl3R0l3c3BIYXJ4bGVSNm80b3BPSVR1S2l3R2MybGltS2pBYVJpUnhRMXk4QVJyMU5Mc0FGeUdOS2dCUW1wcGZSK3B6Ukhnbm9jWVVXNW80SVVmK1kwNXNkTmpqanA2Z2RaTkJpVGdyZW1SaGpGWVp0SzNlNEJoTnpBdUg4UXE3aW1hQjVxVWNXV1VqUjNVZVNKalRaZmkrZjBxL21iOEIzeit5czRlM3p5cElYbWZxa05TdTRPbE9ycW1HTFdHeWFHVU9NbWFnbHpZUVZiQ1VUWXAzbGI4RXVIUmU4QlVQZEROQXF2b1RnVFBWZm9CbjMiLCJtYWMiOiIzODg1ODRlNTQ2MTIwZDgzZTc3YzIxYzE3YTM2YzgxMGUwNDM3YzIxYjk5NTEzMmJiYmQxOTMxYTFlYTZiYzdhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2142458976\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-977608296 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-977608296\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-900349849 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:53:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImN0SDMrb3FnOGhDdDZ6aitDbGtDQWc9PSIsInZhbHVlIjoiMlZrQk5zVllBc1VWSDFhMFVzbE1COU1MN3ZYQ3dQd0NIRXJlUzQyYWVXUU1paG56SEdZMGJ4VmJOdW10RHNUT0h2ODJZSTJ4N01Ca0RZTG1kWUR4cktpblF2eitUSmNNdnZreldlVXRpbyt2MkI5Z09BVkR6dGhnL3YrT0h3WkxQN1k0N3hsQURiS1dTbTBMVG94UkJiL0xMNkhuYXdwV0pzbSt0ekZRbTdHcklBR0dyR1FBenE0K1ZuTzFXUFRyMXhKeEd6MFVQWndvZzRNQlN3aUlzOFZtbzZjNWxyVU4rYUdjN0RvZmxUaFJGWjdlVUZiamhmSkg5b2djdWhYbkRVTDQ3aHMvRHNlV01jbzVuMTNtTW1xWWlZZXBNNjU5OHdkRjdUZTAzenlwRCttQkFUWmxDK2NLYUM4dWV4aHpDYXJxWVlBd2RlRE8xMUt5amtsMzdMTHRxQ0l6MVRodlo1ZGdXOUdhdUFIMWdodW1vd0VyUnFCUVk0ZjNHaVIrMnJXQUZWdFRSUkVXYVY3Wjc3amtrV2pKWnZSV1FqT3lLVWUrUkFVSUFrTXVTTmdFcDh0YVMrQnlYaXl4T3RWNEZwMFJMYngrT2NmU1pZT0ZNQVlFNDZLRytTbENnNUhKOGNOcis2TDJ5aWtpQ0RteDVxb0tHVFZ6R3V6aEswalMiLCJtYWMiOiJhY2ZhOGY3OWIyOWY3ZDVhNWNlMjU0NGI0YjIwNzgxNjlmZjYzNjllZWVkZTU4NjFmNGEzNjJiMTRkMmQ2MmUyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:53:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlVPNmUvTmlxN21WRTViSVJLbjdnSVE9PSIsInZhbHVlIjoidk1rcm5MRXFKbU5sZFdRUGpqVDltczZJYjVpb3NNb0FiU1RjQTZJeHA5eHZ2dk1kN0UrWUR4RkJVRUxaejA2WUhaWU5qSXF1dk5iMDFhN0VTT1Y4Qlo3Tld5YUdYeTVzMW9vd0ZUdGI1Mm9FMy9rRi91YU9lajJoTzNMNDVZM3h1aW5iYzErMk8yWW5IeGtVd0ZhNmZKS3p3bUhNYzVxbkFKaGU2MlNhYkcwVmxRcmo3QUowcUJFVXZoRi9mZjFkQk1JTHhYazJSNFVQbnhNdS9FblJMbkRlNGlKc3pnNHRPTHJhY0dqQ0JVajNEUVpOcUYvUThGK0V6K3JqdVdtMnRta1JVZ0xNRWhNR0g5SkZYMW1oUVArZUNCM2doaVkySzRBYzhqT2xTSTRFMy9XMndoTTJIZyt1M0ZUTDVVaUlZaVRybnBwVTF5SVI5ZElSdDRyamtlRlh2d3ZQbncxaWtPZFJ1M2ZDeE40RmxBblN2eE45bVkrc1lGcjRUNmNtc3VnSEFLcFlVb0tzTExYMVVlaEJoTEdWSlhUb3NVUUp4bmdFSVpWbk4wQUNKK3BMZ2V5U1pzTW9QYlFlczJabzZWTEpvNSt5OG5la0pFeFIwS0QyTlRuSDNjNUNHdmZKZ08xY0M3YzlpcW9ubVBWN3lTQlNhK0Fwa2ZHb1BodzciLCJtYWMiOiJlZTg3NmJmM2RhNDk0Njc0OWQwNDYyZmM0OGYxYmY2OTY5ZTNkMzFmNTRiZGIzZDhmZWMxMTllMzk3Y2Q5ODU0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:53:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImN0SDMrb3FnOGhDdDZ6aitDbGtDQWc9PSIsInZhbHVlIjoiMlZrQk5zVllBc1VWSDFhMFVzbE1COU1MN3ZYQ3dQd0NIRXJlUzQyYWVXUU1paG56SEdZMGJ4VmJOdW10RHNUT0h2ODJZSTJ4N01Ca0RZTG1kWUR4cktpblF2eitUSmNNdnZreldlVXRpbyt2MkI5Z09BVkR6dGhnL3YrT0h3WkxQN1k0N3hsQURiS1dTbTBMVG94UkJiL0xMNkhuYXdwV0pzbSt0ekZRbTdHcklBR0dyR1FBenE0K1ZuTzFXUFRyMXhKeEd6MFVQWndvZzRNQlN3aUlzOFZtbzZjNWxyVU4rYUdjN0RvZmxUaFJGWjdlVUZiamhmSkg5b2djdWhYbkRVTDQ3aHMvRHNlV01jbzVuMTNtTW1xWWlZZXBNNjU5OHdkRjdUZTAzenlwRCttQkFUWmxDK2NLYUM4dWV4aHpDYXJxWVlBd2RlRE8xMUt5amtsMzdMTHRxQ0l6MVRodlo1ZGdXOUdhdUFIMWdodW1vd0VyUnFCUVk0ZjNHaVIrMnJXQUZWdFRSUkVXYVY3Wjc3amtrV2pKWnZSV1FqT3lLVWUrUkFVSUFrTXVTTmdFcDh0YVMrQnlYaXl4T3RWNEZwMFJMYngrT2NmU1pZT0ZNQVlFNDZLRytTbENnNUhKOGNOcis2TDJ5aWtpQ0RteDVxb0tHVFZ6R3V6aEswalMiLCJtYWMiOiJhY2ZhOGY3OWIyOWY3ZDVhNWNlMjU0NGI0YjIwNzgxNjlmZjYzNjllZWVkZTU4NjFmNGEzNjJiMTRkMmQ2MmUyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:53:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlVPNmUvTmlxN21WRTViSVJLbjdnSVE9PSIsInZhbHVlIjoidk1rcm5MRXFKbU5sZFdRUGpqVDltczZJYjVpb3NNb0FiU1RjQTZJeHA5eHZ2dk1kN0UrWUR4RkJVRUxaejA2WUhaWU5qSXF1dk5iMDFhN0VTT1Y4Qlo3Tld5YUdYeTVzMW9vd0ZUdGI1Mm9FMy9rRi91YU9lajJoTzNMNDVZM3h1aW5iYzErMk8yWW5IeGtVd0ZhNmZKS3p3bUhNYzVxbkFKaGU2MlNhYkcwVmxRcmo3QUowcUJFVXZoRi9mZjFkQk1JTHhYazJSNFVQbnhNdS9FblJMbkRlNGlKc3pnNHRPTHJhY0dqQ0JVajNEUVpOcUYvUThGK0V6K3JqdVdtMnRta1JVZ0xNRWhNR0g5SkZYMW1oUVArZUNCM2doaVkySzRBYzhqT2xTSTRFMy9XMndoTTJIZyt1M0ZUTDVVaUlZaVRybnBwVTF5SVI5ZElSdDRyamtlRlh2d3ZQbncxaWtPZFJ1M2ZDeE40RmxBblN2eE45bVkrc1lGcjRUNmNtc3VnSEFLcFlVb0tzTExYMVVlaEJoTEdWSlhUb3NVUUp4bmdFSVpWbk4wQUNKK3BMZ2V5U1pzTW9QYlFlczJabzZWTEpvNSt5OG5la0pFeFIwS0QyTlRuSDNjNUNHdmZKZ08xY0M3YzlpcW9ubVBWN3lTQlNhK0Fwa2ZHb1BodzciLCJtYWMiOiJlZTg3NmJmM2RhNDk0Njc0OWQwNDYyZmM0OGYxYmY2OTY5ZTNkMzFmNTRiZGIzZDhmZWMxMTllMzk3Y2Q5ODU0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:53:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-900349849\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-52739394 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-52739394\", {\"maxDepth\":0})</script>\n"}}