{"__meta": {"id": "Xb380c6214762bc55d6653469be25b4c5", "datetime": "2025-06-17 05:42:03", "utime": **********.435829, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750138922.06584, "end": **********.435871, "duration": 1.3700308799743652, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1750138922.06584, "relative_start": 0, "end": **********.231055, "relative_end": **********.231055, "duration": 1.165215015411377, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.231118, "relative_start": 1.1652779579162598, "end": **********.435876, "relative_end": 5.0067901611328125e-06, "duration": 0.2047579288482666, "duration_str": "205ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45156376, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02754, "accumulated_duration_str": "27.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.328275, "duration": 0.02453, "duration_str": "24.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.07}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.382089, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.07, "width_percent": 4.466}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.405613, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.537, "width_percent": 6.463}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1070745684 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1070745684\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1551719578 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1551719578\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1919942690 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1919942690\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-436498746 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750138904236%7C11%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Iit0eUdtM202Nk1abjR5QWRaYXN0TXc9PSIsInZhbHVlIjoic3B0V2F3cG5QTm85bUZ2U1RENGpUZ0ZkU0grTEtSWFIxTGo0Y3ZiRXJBb2dJVnI5VXFOcFdTRFNaT2hhNXRERlhKR2FDbUJJWXN5ZG5hWVFBa29qTWZwT2VhM1I4U3QyaGpNOVMyWlhQTWFodWtRdjZGMTdvY2QrNWxTbUtMS2pVZVNZT3pCemRocEE0YklwcS9xbWR0WTBwSG5RaUt1RFMra3hJTUtQOHpOcjlYVG1TaWdUcDM0bFZBc3IyanJYcUFFZGVzeU01S2E3NzFlczR2eEZ4d25wRG5Pa0kwS3NPMUxtTC9xd0RlWFRCVDFhK1Vmb2tEZll3VFl1dXVZTXhWTytCaHp5Qy9PYjJrZUVRdGZqdGVCU3ZTYzk1YzJFVVRqd3oyaU9LUUcvRlBzbW5JNm8yeG5oTWNSU1BlSVo5L3VNa281Vkx3SFkwY2h2ajAvZ0N6ekZRNEYyUUVMc1V3SVhQY1k1cElGNHBZTEx3ZUI0SEE4QnNDaFIvMjluR0RoRTloUUdDWCtpV0NQbVgzUDJmZ0gybXY5UzdidEJNbi9YcFNGYzYwZllDbnAxU0RqMnE4RkdlVG92SGVYMG5aU1ROa3JScTc3bFk0aXowd1VrY2oxaVVHdkNTNHpaRmxBa3FRN0g4dGhrbkM4RkdpbkxPOXhJVVlUVEI1L0kiLCJtYWMiOiJiMGI1MTVkZTg0ZjM3MTExZjdiNjZmNjM3MDVmYzYyMDBhMmJlNzA0NWYzMTg4OGI1NjRhMWQzMDQ0MmM0Y2FlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBnSndDeDQ0TTFlcGNVOUFmMHMwaXc9PSIsInZhbHVlIjoiUkd3WmU5UEV1WFJmNjF3bTFFSzB2M1hnSmJjZzMwZHlhWW9OMVdRU0dGT3JnZnlyZ2NLeGdacVF5V3dBZDM2d3YxQ043QmhUMk93TXVzVXV6U09ya3NFUThrWXlKcnJxQWVwSnhWSFlrTy9wZkl2MVdQMmNid0hqSkpjTmRYZGFRb2xObUpFYjUrUHlUbTE0QXUxSlgrd1JxaWVyaXdud3FtblBoOFZFSERrUmhtTFplVHoxb1lLcFp5R0d0VDFoSnRTWVM1YXBXRkwyejJNQmlvejB3TTRrOEswcStMVlRoL0EyRmt2SXluak0vVjBMYU10cUxSUk1pNlRtcXdGbGVrTThMVkVqcUxFNHlTK2YxUDRhUm8xdkM1WUJMT213aGE3ejNnTWlkTWJ5V21nTC9JcmRlQ0dJVlA3eGtUSWV5K3N2YlZlNUZPTFJjdU9aZEYxNDFueHQxeHRQcEpxdDBaSnQ1UUl5WDd0OURMRmtFWDJOd0lZM29ZWnJGcWdUbHpVcFJpLzZWL0Y4NEpxL1RuQVVqcWtVM1hQQWptZjlLM1hlY1UxcDlDQ3RBdmZSNU0xU0IySjNPL1pieW1mNERXWm12MzBLUFpNbFZnT3ZNMUN0U0V2N1c4cGh6QzlDSHdpWXBUVy9Hd1ZuWHJrTVAvL1oxa2pNSkNWNnhITHkiLCJtYWMiOiI1NTI5MTk0MmE5ZDc0YzNhOTNkMjhhNTA2ZGM1YzRiZGIwZmMwZWExOWY2ZDBjOWVlODY1NTY3ZjE1MWY1MmRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-436498746\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1124537263 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1124537263\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-359643178 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:42:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhzWDZGei9hK1Rpd3BkbmVCYUVSSlE9PSIsInZhbHVlIjoicUJwZ2hmNzJZZm80cms3R29FdDlRQlJ4MVpid3NTKzNkU1FmSlRXSE9uRXJpdnJlNGU1N1FERXJXV1hwajVFUnB6ZjY5Y2xCRDF6dFZpUUtQT241djk4WFl0dDF0cERrK0RMWm0wajJ5OTl5cFNEWDVabTNRampsMERJTkk4YWtiUWlpT1BkMHMzd1c5OWpiR1pKdzZrRGptaW9qRVRXSmQwSXdhUEJud2M5ZlgyTEZoTkYzN3AzM2RuOStYVnVNckxucS9TMjlSdFhDVENsZTRxN3NiVVFmT1hkZTExWWJ6UGNEa1NVRmNtTGh6MThsMW9hZ0dURE9WOFRWSWtzQWdOTllHRDFNblBvMHFhNDNwUlJIRDZ5S0k1VmkwSy9tcU95VHNtM1ZIUFhHbHFDeWdhL3ovN0ZHaWMvc1VXNlBlNGZaeTlFQkRpbklTd0xPWmZHclNaekxDL0twc0REcVZPT2Q3ZFEyRWgzb2UwWDltcW53UlVWZkhnK3h1UzZ2VEh3eTVGUERGN1VDTU5EWEMyVmNWSnJjclJIMWhLeXpUR2dLZnhhSE84S2pHUVJaVkdtZmVWYm9Bd0dDMG51UHBjeGpwQ1dLQ0hObG54M1hOc0g3Y3pkUGJWWE9sZkZJSUFWbmN0QnNKZUo3azZqelBZR21PTkVHam5pYWd6MkkiLCJtYWMiOiI2ZTc4ZTcwYzFiMjBmZjhjNmI4N2I2NjU3MjRjMmI5MjFkZDJmOTc5MjgwMmZmYjI2MDAyNjA4YjY4MTIzYzhjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:42:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZJOHc5Z3phejdUT3VsTk11bXArK0E9PSIsInZhbHVlIjoiL016cEpVM21mWm9tV290YUZObEltYjZNZHVIcVhCVHpYdDQzRGpGaXRSUTNmZ1pta2FlYjByWUZKMnBML2VzZjQ3Z0FBU2xZelhwTXpUQUovSDYyNTAvTGxBb1lHZjlVOUdPbTFWOVBiZjR2NXN2bmZ1U0F1dE9vS0diTmdrL21pUkpGUGljWktDQm1qbERyR2s0aHJYS2l5akFyYXhtaHM2ek8wRVdDbDZ5dGdPemRCT3VHVncrU0t1cWlxSlYzNTNjMlVGUzdFTHVtZWR5dHRIbjBvWko5S3g2TDRNSU51M2hxRlhQL3ZGZVhVL2g3MGZkZkxCZzRPVWlxZ2ZOWUZOLy96bjRMMUVaZ0NWd1AxdXE0aUMwa3FFZE1ybFJ6aGlNUUlYdWtHUlNyTFJuTS9zdklLSkw4VmtIYURWQU9pbkRTQXNMS2RDNWlySVh5WVRWa0JTeUYrd2RHcjdyOGNEREE2Uy9kZEJCZjJmamxsS3Z3ZEFocGJ1VTcxNzB6YzZ2VmxNMGhPaU5xNWNqSkJSZThEQWVPZnVZMnBvV1EwNHJhZ0d3OC9tNU1PSFRQZU13TFlIQysxdHdnQjh1VmR5MEJiWnNoaDYzZjdIc3dTOWlWME92R1QxMjBBcWxkYjk3YTlPcmFNVExoWXg5eVFiYmVzMlZVd2xyOVJxbk8iLCJtYWMiOiJlYjQzNTYwYzZjNTFlY2FiY2MwN2M5YWU1NDRlNDc2Yzg5MDk2NzQ4ZDY2OWNjYTg5YjlkMjc2NGY3YTgyOWIyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:42:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhzWDZGei9hK1Rpd3BkbmVCYUVSSlE9PSIsInZhbHVlIjoicUJwZ2hmNzJZZm80cms3R29FdDlRQlJ4MVpid3NTKzNkU1FmSlRXSE9uRXJpdnJlNGU1N1FERXJXV1hwajVFUnB6ZjY5Y2xCRDF6dFZpUUtQT241djk4WFl0dDF0cERrK0RMWm0wajJ5OTl5cFNEWDVabTNRampsMERJTkk4YWtiUWlpT1BkMHMzd1c5OWpiR1pKdzZrRGptaW9qRVRXSmQwSXdhUEJud2M5ZlgyTEZoTkYzN3AzM2RuOStYVnVNckxucS9TMjlSdFhDVENsZTRxN3NiVVFmT1hkZTExWWJ6UGNEa1NVRmNtTGh6MThsMW9hZ0dURE9WOFRWSWtzQWdOTllHRDFNblBvMHFhNDNwUlJIRDZ5S0k1VmkwSy9tcU95VHNtM1ZIUFhHbHFDeWdhL3ovN0ZHaWMvc1VXNlBlNGZaeTlFQkRpbklTd0xPWmZHclNaekxDL0twc0REcVZPT2Q3ZFEyRWgzb2UwWDltcW53UlVWZkhnK3h1UzZ2VEh3eTVGUERGN1VDTU5EWEMyVmNWSnJjclJIMWhLeXpUR2dLZnhhSE84S2pHUVJaVkdtZmVWYm9Bd0dDMG51UHBjeGpwQ1dLQ0hObG54M1hOc0g3Y3pkUGJWWE9sZkZJSUFWbmN0QnNKZUo3azZqelBZR21PTkVHam5pYWd6MkkiLCJtYWMiOiI2ZTc4ZTcwYzFiMjBmZjhjNmI4N2I2NjU3MjRjMmI5MjFkZDJmOTc5MjgwMmZmYjI2MDAyNjA4YjY4MTIzYzhjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:42:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZJOHc5Z3phejdUT3VsTk11bXArK0E9PSIsInZhbHVlIjoiL016cEpVM21mWm9tV290YUZObEltYjZNZHVIcVhCVHpYdDQzRGpGaXRSUTNmZ1pta2FlYjByWUZKMnBML2VzZjQ3Z0FBU2xZelhwTXpUQUovSDYyNTAvTGxBb1lHZjlVOUdPbTFWOVBiZjR2NXN2bmZ1U0F1dE9vS0diTmdrL21pUkpGUGljWktDQm1qbERyR2s0aHJYS2l5akFyYXhtaHM2ek8wRVdDbDZ5dGdPemRCT3VHVncrU0t1cWlxSlYzNTNjMlVGUzdFTHVtZWR5dHRIbjBvWko5S3g2TDRNSU51M2hxRlhQL3ZGZVhVL2g3MGZkZkxCZzRPVWlxZ2ZOWUZOLy96bjRMMUVaZ0NWd1AxdXE0aUMwa3FFZE1ybFJ6aGlNUUlYdWtHUlNyTFJuTS9zdklLSkw4VmtIYURWQU9pbkRTQXNMS2RDNWlySVh5WVRWa0JTeUYrd2RHcjdyOGNEREE2Uy9kZEJCZjJmamxsS3Z3ZEFocGJ1VTcxNzB6YzZ2VmxNMGhPaU5xNWNqSkJSZThEQWVPZnVZMnBvV1EwNHJhZ0d3OC9tNU1PSFRQZU13TFlIQysxdHdnQjh1VmR5MEJiWnNoaDYzZjdIc3dTOWlWME92R1QxMjBBcWxkYjk3YTlPcmFNVExoWXg5eVFiYmVzMlZVd2xyOVJxbk8iLCJtYWMiOiJlYjQzNTYwYzZjNTFlY2FiY2MwN2M5YWU1NDRlNDc2Yzg5MDk2NzQ4ZDY2OWNjYTg5YjlkMjc2NGY3YTgyOWIyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:42:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-359643178\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1372473826 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1372473826\", {\"maxDepth\":0})</script>\n"}}