{"__meta": {"id": "X3b5ff7498eb60a245d7cf96a503fec9e", "datetime": "2025-06-17 07:13:26", "utime": **********.880745, "method": "GET", "uri": "/search-products?search=ww&cat_id=0&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[07:13:26] LOG.info: searchProducts debug {\n    \"warehouse_id\": \"8\",\n    \"products_in_warehouse\": 3,\n    \"products_found\": 0,\n    \"search_term\": \"ww\",\n    \"category_id\": \"0\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.872966, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.295076, "end": **********.88077, "duration": 0.5856940746307373, "duration_str": "586ms", "measures": [{"label": "Booting", "start": **********.295076, "relative_start": 0, "end": **********.749375, "relative_end": **********.749375, "duration": 0.4542992115020752, "duration_str": "454ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.749386, "relative_start": 0.45431017875671387, "end": **********.880772, "relative_end": 2.1457672119140625e-06, "duration": 0.13138604164123535, "duration_str": "131ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48479184, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1336</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02795, "accumulated_duration_str": "27.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.795374, "duration": 0.016149999999999998, "duration_str": "16.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 57.782}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.823298, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 57.782, "width_percent": 3.184}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.847053, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 60.966, "width_percent": 3.22}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.850445, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 64.186, "width_percent": 2.97}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1236}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8569849, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1236", "source": "app/Http/Controllers/ProductServiceController.php:1236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1236", "ajax": false, "filename": "ProductServiceController.php", "line": "1236"}, "connection": "ty", "start_percent": 67.156, "width_percent": 2.612}, {"sql": "select `product_services`.*, `c`.`name` as `categoryname` from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15 and `product_services`.`id` in (3, 5, 6) and `product_services`.`sku` LIKE '%ww%' order by `product_services`.`id` desc", "type": "query", "params": [], "bindings": ["product", "15", "3", "5", "6", "%ww%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1260}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.861539, "duration": 0.00845, "duration_str": "8.45ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1260", "source": "app/Http/Controllers/ProductServiceController.php:1260", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1260", "ajax": false, "filename": "ProductServiceController.php", "line": "1260"}, "connection": "ty", "start_percent": 69.767, "width_percent": 30.233}]}, "models": {"data": {"App\\Models\\WarehouseProduct": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-342240501 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-342240501\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.856141, "xdebug_link": null}]}, "session": {"_token": "TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-874670060 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-874670060\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1292816917 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ww</span>\"\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1292816917\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1961812663 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1961812663\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=17n9b1e%7C1750144374665%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InNOaG1ZK3dzUEFOOENEdVFpUHc0eHc9PSIsInZhbHVlIjoiUFhvdlVjOW92L2hQd3d2dDdmZEpCVE5SbFFFcEMzdTlnRHJzZ29QZkxuOFZLVlorOVRzc3EvQVJlaXJhSTRFcCtFQmVJdWZDRXlVc0hSS1puVnlzUzRqaEoyMzBzZ24yMnNSSHkxNk5rR05TS0d3WUxNNjN6aCtwTU1PS01HaG1QczZQd2RSdGI0VW9EZHVOZnY4T0ViTmtqdkZ0eVlKVm1JWDBHYkZ5SFJ0dVRIdG92YTV6cC8vUWFRTHNzaWhabC94aUx1WXdKSnVOb2pGdkw1Vm5EdkRVQXFJVWhlSU9LUkFCOEFyd1lzemNlVEp6clN5R1l2SWFZdlBNdHVneUdEcjZkT3lYK0E3Yk5EQjg0c2tqWHB5UDI4WkNQdmVNRXJVNzQxdUVZTmhRc2RhOE4zOWYvc0IrR2lraksrZXR5MTFIT3I2a0JlKzdLMVl1ZHFCdGwxMGdmVjVscXlEVUNpUGxZN25iUkl6T2N0NkNhY0tEaUExWnZ6Z2J6Y09BZ054RnYwWHArVnlMNUcwaVhDRi9Td2ZEQ1l4bzBIKzF0bEErMTVWLzdVZUEzOEQrdkROVXk1OHh5bE5qR0dyR0d2eXR5V1JzbWt6b3J0aTByNGVKaEVvS045UXJPaTNPazNOQ0NhaE4xNHZzN1dTK2dOcFdQUDF5SVltTm9kajAiLCJtYWMiOiIxZGVkMDc5Y2VkMWM5OGIwYWY1YWMwYjkwZTAyODdlNmQzNjUyYmI3MWZlNzBjYzFkMWIzMjk2NzA5ZGIzM2U0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImNSdStFM2FRYThkNlZ4SnZ4b200L0E9PSIsInZhbHVlIjoiM0M3emc3cTFqVzg0TXlxdWVQRE1Dd0l4enJvUllIaklVa2xOekxNaFc4UTJMMWlIbFZsVnJpWU9HZjhjWHZ6UVN4Q2Z6cHJHbktmSldoOHpPOTdTNUNPdXhaZTZMNTd6QXh3VUNpa0dRajU2SjdWVVdMQjhPSjNCa2ozaWk4ek9CcVFaMmNUKzRKSUpEMWV2ZTQyVmh0RExlZkc2Ym00dVczNmZFeDY2WGxQbVltQ1JkNnJVN3lKRlk0dW1EcjlhaWtEVTRyRWZORklNNWx5TVhDRHFaYzYrdGtCTmVQMnc5bFdIZUlTWnlSVng4VUw0N003QTVrTjkvOTM3a0hraDNGWlNvWXVUdk4wUmVXUHkrNVlhYjVVTDdUdWJtaXZXVjNjeTJuSmZWUGRwYnBPbSt3dFRzWVJVUGRkL1FpSkRaejhGVjErQXBpbDdzTXNteXVIUHdwY25TNDBYZkNrNVo2VE84VUNuTTZib0ppc1lEakxnY0liN3h5ajh2cEx0TlpxL2M1UER5UDBTdkx4bTY5bThmenNQYmV3UWUzVTljT2pnUUloTDZLZ1J4N0FFY2M1N0twYVhERFZvbGd1d2Z6ZWw5M0M2dVFiZUQ3aFBXWE9XQVN0ekpBcGJRVWxTT0ZCOTJCZ2wrUys4TUM3VUpINEtoZFNxU1FmSTdyYVEiLCJtYWMiOiI5ZmE4OTc3MzYyMmVlODUyNDgwNGE4NWU3NzQ2Njc3NjM5ODZkMzk3OTJkN2FjNDkwYWFkMGUyY2YzYWZjOWExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDWN9h6R9gAIdzZaXBzXfq0WGpauVfrUj70kKWA2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1763571892 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:13:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxyRkpUdllsdXhoOVRzRHZxVGNoRGc9PSIsInZhbHVlIjoiWHV5R21zcldtN2lJclh6RU5JTFRnM2xCcTdvYVBLVHg0b2FWRDVnOEJhSm1vczB2c0FpV2Qra3pJSVpVbzVPZkpBMGZjbU5Oa21BVmxSNVpHWm5lVVVZTjFRakVONFFRZSttTjkxM1lIMS9zVWdGdFh1UnlyREw1eVgrU1QwMndzZmhYZGdZdDhJcUVmRzEwNk1jbXB3ZjI2Z0JIZnJvTzA3cE0vQmg0ZExIQWxjbXpDTG9VVHZNcDRwZTl0M3FPbzRnRC9IeVU4MzZ4RGdvSzB1U3lpczJtQWR6K1ZUdE4rSld2bk5CbHNiM0pVbEJSVG04Tnd4L3NnWjlDRXgzS1ZCNjlKREFSZDI3YmNGVWt1QlRTdEVjcUd3dHlHaTA1d2V5bFdvSUE0RTFpeWdpU1VraFBvdi96U2tjSEhidlRwd3I0NHZ1ZlJSMFg3dkxkYzN3a1FYT3Z4NmlDNG04ZHlVRmRVN1NPaGhzZ0tEa1g2anN3MW5TdzZLZG11T29oWld6QmlaOGtCdmgwWlVSZXhSTnNMWmt4YUhVNnp0OXhYNml5aGJmVXU4ZnFxMVdyaWxYbVR3RzRmVG8xTStFVWhNUmRNSUZZVjlEMkFvMWlxTEgyNkN4UnEyb2tqSWY3SHB4dDhOYm9MZmZuSENNZ1AvQy8zemlIOERzeHBZU3giLCJtYWMiOiJhMjM0Yjg0MDBhZGQ4ZmQ1MTQ5YWFiNzE2ZGI0ZWYxZmU5ODM5YmE3MWRiNWRiMThhYmM4ZjZkYTM5YzUxOWEwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:13:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjNPeTdZTmZNRVg5ZHdRamdLdG9WRkE9PSIsInZhbHVlIjoiYUR1b3krdWYxOFpRQnhicU90S01yMVRMOGRydFBIMjljWFkrbVA1U1orWmlLUWVuTVdUbHRwSkJpVkkxM1AyMUhUc0svejg5QVlHb2c4Nk5iblRrYTRWWDRjaFR4ejBKRkVjRUdraFY2SW4zbGVuYzhvWjkzSjltQ2pFUUR0ZzlzRjd4amtzUkt3Qm1TbGV6cjFTYkxFcVg0YmxNVWppaDVIY1RQeE1sbSs2TGxLVVdsOGdHMXNDMUhvaXlYQzVqZGlIdWhybzNEODdDTGVpaG9GVmdqYmNsOFlWY00xSEd6YjNWS3g3Q3l0bDcxVkNmcnZYMitqeFIzbm5WckRpcm5SSTVDYWY1bjdXSldRdGlIaFNMc244allOM0hoWXNzdGNQeW1HYXlMdHUwS1c4UGNwWUJUa2pmSUxnMmhTUkFua2p6SzdISEdJTmtoWlVOc1dENFpIdXA3R2JsNUVQS2tiNGpCMXl3NWdmaGdwcU5mUjZBbTNzaUVscEJRdmZWdjRCbkw2K3Zha0ZJRFlmclhSZ0ltVUo1RE4rT0h4Wld1V05FbzJXS0x1aWVCUllmcGY0UlJnZ3VXaXFnOUdySVNKQlFTNGxwT2lKRUtUbStrWVpmdU1GbFd4emRCNU1qVXoxZU1DaTRUTkN1d1kvRU51ektHSHNmM2IzSU5zdUwiLCJtYWMiOiIxZTg1YTRmMjc1ZDdkZmNlMDQyZjllM2QyOGMxNzcwNDQ0ZjQyYjkyYjBkZDI2NzE1YjE1MGM0ZTA2MGY1ODljIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:13:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxyRkpUdllsdXhoOVRzRHZxVGNoRGc9PSIsInZhbHVlIjoiWHV5R21zcldtN2lJclh6RU5JTFRnM2xCcTdvYVBLVHg0b2FWRDVnOEJhSm1vczB2c0FpV2Qra3pJSVpVbzVPZkpBMGZjbU5Oa21BVmxSNVpHWm5lVVVZTjFRakVONFFRZSttTjkxM1lIMS9zVWdGdFh1UnlyREw1eVgrU1QwMndzZmhYZGdZdDhJcUVmRzEwNk1jbXB3ZjI2Z0JIZnJvTzA3cE0vQmg0ZExIQWxjbXpDTG9VVHZNcDRwZTl0M3FPbzRnRC9IeVU4MzZ4RGdvSzB1U3lpczJtQWR6K1ZUdE4rSld2bk5CbHNiM0pVbEJSVG04Tnd4L3NnWjlDRXgzS1ZCNjlKREFSZDI3YmNGVWt1QlRTdEVjcUd3dHlHaTA1d2V5bFdvSUE0RTFpeWdpU1VraFBvdi96U2tjSEhidlRwd3I0NHZ1ZlJSMFg3dkxkYzN3a1FYT3Z4NmlDNG04ZHlVRmRVN1NPaGhzZ0tEa1g2anN3MW5TdzZLZG11T29oWld6QmlaOGtCdmgwWlVSZXhSTnNMWmt4YUhVNnp0OXhYNml5aGJmVXU4ZnFxMVdyaWxYbVR3RzRmVG8xTStFVWhNUmRNSUZZVjlEMkFvMWlxTEgyNkN4UnEyb2tqSWY3SHB4dDhOYm9MZmZuSENNZ1AvQy8zemlIOERzeHBZU3giLCJtYWMiOiJhMjM0Yjg0MDBhZGQ4ZmQ1MTQ5YWFiNzE2ZGI0ZWYxZmU5ODM5YmE3MWRiNWRiMThhYmM4ZjZkYTM5YzUxOWEwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:13:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjNPeTdZTmZNRVg5ZHdRamdLdG9WRkE9PSIsInZhbHVlIjoiYUR1b3krdWYxOFpRQnhicU90S01yMVRMOGRydFBIMjljWFkrbVA1U1orWmlLUWVuTVdUbHRwSkJpVkkxM1AyMUhUc0svejg5QVlHb2c4Nk5iblRrYTRWWDRjaFR4ejBKRkVjRUdraFY2SW4zbGVuYzhvWjkzSjltQ2pFUUR0ZzlzRjd4amtzUkt3Qm1TbGV6cjFTYkxFcVg0YmxNVWppaDVIY1RQeE1sbSs2TGxLVVdsOGdHMXNDMUhvaXlYQzVqZGlIdWhybzNEODdDTGVpaG9GVmdqYmNsOFlWY00xSEd6YjNWS3g3Q3l0bDcxVkNmcnZYMitqeFIzbm5WckRpcm5SSTVDYWY1bjdXSldRdGlIaFNMc244allOM0hoWXNzdGNQeW1HYXlMdHUwS1c4UGNwWUJUa2pmSUxnMmhTUkFua2p6SzdISEdJTmtoWlVOc1dENFpIdXA3R2JsNUVQS2tiNGpCMXl3NWdmaGdwcU5mUjZBbTNzaUVscEJRdmZWdjRCbkw2K3Zha0ZJRFlmclhSZ0ltVUo1RE4rT0h4Wld1V05FbzJXS0x1aWVCUllmcGY0UlJnZ3VXaXFnOUdySVNKQlFTNGxwT2lKRUtUbStrWVpmdU1GbFd4emRCNU1qVXoxZU1DaTRUTkN1d1kvRU51ektHSHNmM2IzSU5zdUwiLCJtYWMiOiIxZTg1YTRmMjc1ZDdkZmNlMDQyZjllM2QyOGMxNzcwNDQ0ZjQyYjkyYjBkZDI2NzE1YjE1MGM0ZTA2MGY1ODljIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:13:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1763571892\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1805548491 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1805548491\", {\"maxDepth\":0})</script>\n"}}