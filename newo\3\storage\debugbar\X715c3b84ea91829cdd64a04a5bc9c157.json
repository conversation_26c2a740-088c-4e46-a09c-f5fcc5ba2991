{"__meta": {"id": "X715c3b84ea91829cdd64a04a5bc9c157", "datetime": "2025-06-16 15:23:53", "utime": **********.402463, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087431.719901, "end": **********.402509, "duration": 1.682607889175415, "duration_str": "1.68s", "measures": [{"label": "Booting", "start": 1750087431.719901, "relative_start": 0, "end": **********.169324, "relative_end": **********.169324, "duration": 1.449422836303711, "duration_str": "1.45s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.169347, "relative_start": 1.4494459629058838, "end": **********.402514, "relative_end": 5.0067901611328125e-06, "duration": 0.23316693305969238, "duration_str": "233ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45152544, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02981, "accumulated_duration_str": "29.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.274829, "duration": 0.02729, "duration_str": "27.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.546}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3371491, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.546, "width_percent": 4.462}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3741481, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.008, "width_percent": 3.992}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order/1/edit\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-551539479 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-551539479\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-952981870 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-952981870\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1171216397 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1171216397\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1979601600 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost/receipt-order/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087422981%7C11%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikh5M2pkTFdGT0YyQVU5NHF0MkxkcGc9PSIsInZhbHVlIjoiMkRlMFFkR1lPaXRpS05peTFQL0R4UzVrVE5Kc2hoVHY4djVJQTNad0xML3dDNk92RUJpWGJURkZzaWg1RE1EckJTZTVZRnptQVhQTGtXTlBRTkFydTRLT3hCektwcUJXTkVSNDFnZHJtdmNML3VyckZGeGhyNWZWaWsxV00xbTNHbmsrRG5rcTZ1YWlwZTZKMkhPTjRiTnc0eGVNQk9USzUyTWZsaXVBYkgzRHV1R0szNXpqelJHV0NtWmtvTUtaUjFicUFtZGIxTThQSDFkZXlWdGZ2RnVwcm5UL05tMXVLbkM1cExlbzQ5cXo1MmdOZFRhY2hab1UvOGpaNi9ackNSN3JqenVDMmxUTXF4MWJEd3dkNkNtOHNURmdtM3hRUXNUemZrWkNDK240SDNPQUIzY3oyQWppalBRTWJrMm1RdUNINjlGYXp6ZVp4QTZNem5xWFBHRlJnTlZ0Qk5YNFl2N0lZUHZPNHUzVlRYaUF4cTdUYmJTKzRTNXUxZ21PMktJd3dHNExKY1JjRXJJMFEvYVNYV3c1T3lxTnFucFR1Z1pqZHJESG10dlBBRWtaQmd6ZDQ1L2VPaFNOWVU0T0tIUHVUY1B2SG5QUWpuUWl1ZzMvU1hZS2hXblRJdVNpTjZWdU5jenhaQTFaWUlNSnh3dmNWV0FLNm9tQk1FYXUiLCJtYWMiOiI0YzBhMzkzZWM3ZTQwODJjYzJhYzkxYjA2MjI0MTA2ZWIyYTU1Y2UyOTM3MjU3YTBiMDhkZjE3NjE5N2RiZGJlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IklMWmROYUVxN1pDcVArTVl5eEhLYlE9PSIsInZhbHVlIjoiUEU4anVQMXhCYndaVGlFMzBQc3dtZTJsalZzdllmYkhjdE56YlRJMy9YMzFMUG1xUjdLekZBVW9mTjJFVVg1cVlQZDJBMWdMZTkxUU1sdXpDVTAyeWxzVXplOTFRaVo1ZktsY29iWThiaVAyZWhKQmw3d1ZJQXVzMlV1K3lldU9zek4zdnlLcisxa3l5YW1xMnFCdWdrb2NQSU42YXNPT0NrSjJiNVFnWHR6UXBLTFF1d0N1cTJLL0Y5aXJCVzBUSXVQTk5vWGwvcHVCdEVDNm5nYUlGT05IZnhOVGJJM1YrWmlvanJ0TnFuZkJSbnQyOVdweEJTUzRSekQwOWdUb0tOdUZpbWVDcTlPYzZSTFcwbzVyTzFUWGRLeEhMQVlXQ2NkUG5QSDBhajJhRjBnMUJqNzVNT0FDYmZ0MzN2WHNGTEdaeDFXcURDOHBiU091UVNpTmIzVnJrOGZSd0wyQU9qYUVxdjlyR3F1UmNIcTdlclZHSXdvOFpSTjIrb1J6eHYvS2tFUGRLTHZsY3BTbDN4TmdHOE56NTBhRzJSUWJFVXo2ZlZHL3FxRHNUVTB4VXFESTlxaGhLZVhVWENTTGpsUFFVM0JyVkcyRVEvL3ZVRHFadmo3dTkwSGgrcGZnYllGWWlmcHZmckNiNkY5OVZiNDI4UlE3aHJ2VHlmRS8iLCJtYWMiOiIwZTMyMDEwNjM3NTY0NGFkOTliODc0ZTVhMGU5ZGFkZTQ1ZGQ0N2EzZmU3NDY5NmVlMjQwMDAxMDljNTNkNzFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1979601600\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-108225415 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzcvmOKIgVMCCvEHD9HLB5trqF9yJ9GcIs8Pndo9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-108225415\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:23:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5LbUNrc2oyS1JNK1gzcHhwQURzMnc9PSIsInZhbHVlIjoiTXBVZ1BKa0FJMjVLaUNRa3BnWWp5emxnQzk2R1Z6N2VTNSs1ekZQT3Q0eFVFSkpPdXg3bElvSkphT0RVYXF0NE52dlVLU3U5TUg4WWpXZ0ZtUVhOVkJhTWZYcTJYdW16WDc3QmwzVVp3WTBySnRIWFVxamRFek5zSG5iZlZ2eGVlZktVZG02VkFGTHJ5YXRzWEZudEx1Uk9JdG9GeG1WcUc0ZkFTTmpxNXVzWUNicWlKWmozMHRCaTcwcXB3eXRGOGJUanltbTZZUnh1aE9NamtyYm10KzhqQnZpZDYzSVgrV0w5Y2JNUys3NzVna1BJMkMvVDhHVjRPelZVbzVpYmxWUXdrUWRYNnJQc1FzZ2ZSRHRKSWl4cUdYenQ1NXNtQTAvMHl4NzZZeGlkdXBOMGUxdFBWWjhXdkw5M1MzZlBTM0NKNERHNHVNWTdtVU9JamZQNW13ZW4yMmpTK011YWdqQWUzTCtId0gwVWJTRUJ6SEd1Q2UzUFdtZG1WUjVJU1czd3NGalNacDgzeG1GYUw1OUc2ZG9DMDBQb2Ivakc5TTd4bEpBN2xBVTJvQThxZWU4SmNvMWQ1elhkOU9vZnIzZlc1bStJZFFGTnlwem12dFlJejY4YTh4dUlwU3RqeGlucHBTbU9UelI2cEZyYWdGcDUyOVEyTEIyK0pZcW4iLCJtYWMiOiI0NmU3YmZlZDcyMTVkNTQwZjlhM2IwZjE5ZjBkZGY3NjY0Zjk4Y2ZjOWJlMjI4NTgwN2Q2ODdkNDhhZWRlYTU1IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InNWVVM4LzBiaUtDV08wY1JvVVEyTlE9PSIsInZhbHVlIjoiVy9VYzZXOWhnV1FqY3lyNlVVS2hwVGZFN2dscDJiblpYR2UzZmFibmNVN0R2UTVIL2xrVEhDblJMUjdueW5CSDFKcnpUclFIOWFpcjFUeVdOYkVYeXJVU0graTU4cEJ2eEJwUElGelR0Um9PSVJoaEN5elhxbDZsSWw5OTNIMmdkSjJpWDkvNjVCOFBFU1FxWm5NaFp5bW9CZTlsQVlPYjQrbzdEcVV5SXlydnZYM1hOcndMMEszMjZMZXZULytlc3MwS1E2Q2ZWVFg1WjBicWxoam9lZFNDVUlnRitFczE1SDNMVzBacDZyZVBZUk1meVUzcDhPS3pZM29HejFUQkxyS1dXWHNoWlJoVGFqOTFNMnJEbS9mcDFzOTgwZ1NsRno3bis5aFk3WWtSMFlFU2FmZ3FDWCtTS1dtL2hzdk9hWGkzV24wQ3NXWFFoVVJKZGVDekFLWnhZUThpcEZtUW4yNkFXZ3ltMDB2VWl0NGtuM0ozM2JpNlc3clppTnNJOHhPUVp2ZU91N3FTbVdXRGQyaUtxYlJVbEdCTlJuUE9mek80Unl0eVBkKzJydFh5SHh1ejhFTXRpVUxMdWZmbk9qUnhYVFl4VGtFck0rc2tReklYNitRTis3b0twa0Qza2cybkRyclhYTkJrWUR2QmxqQ3dZZC9EdEJMMG1VNHciLCJtYWMiOiI4MTJmMTc5ZWEzOTQwMTY4MTE3YjgyZjZmMDhhMzAzODlmNDZkNDliODRiMjQ4NWJhOGI1NWU3YzA1M2VmMTRkIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5LbUNrc2oyS1JNK1gzcHhwQURzMnc9PSIsInZhbHVlIjoiTXBVZ1BKa0FJMjVLaUNRa3BnWWp5emxnQzk2R1Z6N2VTNSs1ekZQT3Q0eFVFSkpPdXg3bElvSkphT0RVYXF0NE52dlVLU3U5TUg4WWpXZ0ZtUVhOVkJhTWZYcTJYdW16WDc3QmwzVVp3WTBySnRIWFVxamRFek5zSG5iZlZ2eGVlZktVZG02VkFGTHJ5YXRzWEZudEx1Uk9JdG9GeG1WcUc0ZkFTTmpxNXVzWUNicWlKWmozMHRCaTcwcXB3eXRGOGJUanltbTZZUnh1aE9NamtyYm10KzhqQnZpZDYzSVgrV0w5Y2JNUys3NzVna1BJMkMvVDhHVjRPelZVbzVpYmxWUXdrUWRYNnJQc1FzZ2ZSRHRKSWl4cUdYenQ1NXNtQTAvMHl4NzZZeGlkdXBOMGUxdFBWWjhXdkw5M1MzZlBTM0NKNERHNHVNWTdtVU9JamZQNW13ZW4yMmpTK011YWdqQWUzTCtId0gwVWJTRUJ6SEd1Q2UzUFdtZG1WUjVJU1czd3NGalNacDgzeG1GYUw1OUc2ZG9DMDBQb2Ivakc5TTd4bEpBN2xBVTJvQThxZWU4SmNvMWQ1elhkOU9vZnIzZlc1bStJZFFGTnlwem12dFlJejY4YTh4dUlwU3RqeGlucHBTbU9UelI2cEZyYWdGcDUyOVEyTEIyK0pZcW4iLCJtYWMiOiI0NmU3YmZlZDcyMTVkNTQwZjlhM2IwZjE5ZjBkZGY3NjY0Zjk4Y2ZjOWJlMjI4NTgwN2Q2ODdkNDhhZWRlYTU1IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InNWVVM4LzBiaUtDV08wY1JvVVEyTlE9PSIsInZhbHVlIjoiVy9VYzZXOWhnV1FqY3lyNlVVS2hwVGZFN2dscDJiblpYR2UzZmFibmNVN0R2UTVIL2xrVEhDblJMUjdueW5CSDFKcnpUclFIOWFpcjFUeVdOYkVYeXJVU0graTU4cEJ2eEJwUElGelR0Um9PSVJoaEN5elhxbDZsSWw5OTNIMmdkSjJpWDkvNjVCOFBFU1FxWm5NaFp5bW9CZTlsQVlPYjQrbzdEcVV5SXlydnZYM1hOcndMMEszMjZMZXZULytlc3MwS1E2Q2ZWVFg1WjBicWxoam9lZFNDVUlnRitFczE1SDNMVzBacDZyZVBZUk1meVUzcDhPS3pZM29HejFUQkxyS1dXWHNoWlJoVGFqOTFNMnJEbS9mcDFzOTgwZ1NsRno3bis5aFk3WWtSMFlFU2FmZ3FDWCtTS1dtL2hzdk9hWGkzV24wQ3NXWFFoVVJKZGVDekFLWnhZUThpcEZtUW4yNkFXZ3ltMDB2VWl0NGtuM0ozM2JpNlc3clppTnNJOHhPUVp2ZU91N3FTbVdXRGQyaUtxYlJVbEdCTlJuUE9mek80Unl0eVBkKzJydFh5SHh1ejhFTXRpVUxMdWZmbk9qUnhYVFl4VGtFck0rc2tReklYNitRTis3b0twa0Qza2cybkRyclhYTkJrWUR2QmxqQ3dZZC9EdEJMMG1VNHciLCJtYWMiOiI4MTJmMTc5ZWEzOTQwMTY4MTE3YjgyZjZmMDhhMzAzODlmNDZkNDliODRiMjQ4NWJhOGI1NWU3YzA1M2VmMTRkIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1328528618 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1328528618\", {\"maxDepth\":0})</script>\n"}}