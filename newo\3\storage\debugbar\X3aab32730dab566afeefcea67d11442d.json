{"__meta": {"id": "X3aab32730dab566afeefcea67d11442d", "datetime": "2025-06-16 15:21:27", "utime": **********.640102, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087285.960188, "end": **********.640147, "duration": 1.6799590587615967, "duration_str": "1.68s", "measures": [{"label": "Booting", "start": 1750087285.960188, "relative_start": 0, "end": **********.313004, "relative_end": **********.313004, "duration": 1.352816104888916, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.313135, "relative_start": 1.3529469966888428, "end": **********.640152, "relative_end": 5.0067901611328125e-06, "duration": 0.32701706886291504, "duration_str": "327ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46093824, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.49579, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.519317, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.591512, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.605554, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.06102, "accumulated_duration_str": "61.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.39644, "duration": 0.031010000000000003, "duration_str": "31.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 50.819}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.435683, "duration": 0.016300000000000002, "duration_str": "16.3ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 50.819, "width_percent": 26.713}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4607282, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 77.532, "width_percent": 1.917}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.498409, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 79.449, "width_percent": 1.917}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.522821, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 81.367, "width_percent": 1.868}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.554018, "duration": 0.0044, "duration_str": "4.4ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 83.235, "width_percent": 7.211}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.568547, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 90.446, "width_percent": 2.032}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.575863, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 92.478, "width_percent": 1.819}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.595407, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 94.297, "width_percent": 5.703}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aXNlwC2u7eBVVmf76Srodlte78QD6PcvbxRIevxR", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1304601290 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1304601290\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-838458346 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-838458346\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1998444874 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1998444874\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-808255823 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=1x3i2fa%7C1750077280918%7C10%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxEUDBvQ2JXVTlsekx2cGxuQzYvSXc9PSIsInZhbHVlIjoiM3JHcUdtQWlwUGlvOXVXV3RtRkhXcVJycWVoSTlxRWtTRWx6QUJYSUhPZkZzZFR2WUppbEY3Z1JBcHdIVVcraXBHUGVWbWEraXRHbWNvaTVpYXQxUE1YcThMZkYrT25ZOUhna0U1UEdjbXBxQkdwSjd1RFBjTGFLZFdlWmtnbkRhNU9wVU9OZGlaelpwQS9lVnJlQmlYaHZiSG5FZW0wMFkyMTB2c05vR0ZOS0gyeHlYMVRzTldibEU3RGZydjl2dEdnSFZVdGszVjlUU2sxdEJXSW5JZSt5NEFkKzJ1NjJwcmRnM3lzWHVQSGZ3dkZ6cHVCdTRUSm56OFgyeXdFUjF5aG1iN2prUlZpa3hCc2RsQ0tpYkRIbHRQN01HRGcyS1NUUWpXaC83Mmtva1hOL0FNUzllT056dk5kWm1aMkNhcnFDWUdQNmVpWUZiQ1dCU2xHdnlleWFjRWlsWjNVVTdVUDhuTmJGNHVXSmJSOVdkdXZNNmtVVGRYcXgrcVRMVUE4bnBNdDhpb2gxN051UTFla2VMZE1ZWGYxd3RjVXlyT2JRTzBoQ0F6WXQwVDNVOFBuRlpzbU5XZDNUVC9keWY0SHBwQkJVLzRiaVBOQkQ4WGZFLzNQMStKTk1WWm1OU0ZMeVVINDNhMFVnWlByM0ZXaFE1cE1sSEhBZVFXV1kiLCJtYWMiOiIyNTRhNjU5YzMzMDA2MWU2NDgxODg5Y2E5ZDg2ZTA0MGNhNWEzZDI0NzM1N2Q3MGRjODI4NGNjMDM4OTE0NzFlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZDelpmUUJyU1REbnhMUmR5b2dwaEE9PSIsInZhbHVlIjoieUYwVVcrK1lOazVMdDliampaaFVUN08wbFBvTGVvZXJYL29jTDhnVDE3anM4ZVR6MjdaemcvNFUvaFRCVGxhWXZPMzRnWmJEM1BUTGQ4Z09MQ3BFUnhWNVRTZzVjbmM5OS9nQXMxSnhuS2ZhMmJVVjd0R3pZTm5WTnI3eUdDRzFwa01xcTNLVHcwTnhXaGV2TkR5aS8waXl0T2hxLytXNzZ6QUdRWkhhV2hZNUlYMTlIUjU3alVqSHpoMG9jeEwwTEQ1NTZYMlo1UzBBSCtkTUY1dkl2Sk1TSXB4S0VSZW1BZVJPY0VqZG1hTUk0VU9rUzJzZGF2SzJnWnhQSUhBRVM2TFJPL1pKZFVvRUszZW5LTjNEMTYrM290TEMrMFlHL2xIMkFBU3NheFRVc2dWL2x6NHQvZldnUHN3K0tkNnAyRzV1S0lCeWE2MzRKSVhEdnBBOCtnZTcvUTVGM09nNjNYZ0o4UFlRNlJNdFdQVjVjQzZWMExJSG54ZUVBeVZPT25qa3lSalJCKzZuaWU5NG9MTE9NQTJLYWlDUkZXMUhlakRlQ3NKOGR2K3JZZmNpZE9yWWg2OFh0dEV4WWJDUlc2eGk2U3JOYVRlblRoMDhKN3pkbk5wZ2hsbVp6eHZaTkM1RG4xTG0zYkZMbHEvWUdza2tTSWhBR25iVmYxd0MiLCJtYWMiOiI2MTg1N2FkZGJjNDU1NGYzYWRiMzdjNmNiNzI3MzhjNjJlYmIzNDg0NTE5NDJlZTYwMzM1ZjMzOWQwZjczZDNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-808255823\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-657602193 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aXNlwC2u7eBVVmf76Srodlte78QD6PcvbxRIevxR</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WYBnQ8g3Mj9WIHdt5GFuuRqs2bU7AqKfY7oPOQHm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-657602193\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-911525717 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:21:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFqaG80dkRYM3JvTTFPVDhHY21Va1E9PSIsInZhbHVlIjoiazFwZ3VEWGNtN2RCeC9uYXVPM2h3OUNrazlnSy9Dd0JQKzM0d0VRcXE5d0ZKQnFIVS9CNEt2WGJ1OE5KT2hvUndLaEZka1d1SlMxaEw2QVQ5TW1pa0s2YkdmeXRqQ1JORXdyUkVnaS9KQ3ptTVlmSEtsdU1SUG1VQVRZd1hMOU9IckRCVlBQWFVDalRuVDhaSlZlUkFkWUtOY1VwWElTK2hYcnZoZTZxZ0JpdjdKMFJkTFBQS3M5RURHV0tVWTFuT0w0LzczL2xaOHU0Z0tXVzlVNG8zZmRIYTJYWmdvUzh6OXZ3Q2lneTRXSXF5L1FzNEVlOXZwb0MzZnBUYStmNXhHSEZuK0VIaHNhcElZMzFleVFVQTZ2NFFmN0V0eWVHR1k0NkRYYThQZG1LL21LYldNVy9mcW5jLzBzckdHTDZUd0w2Vyttb2h5UmZEZnhralBIWU1BcGNmZjFVUENGZkgybHhONG5BSS96ZmdvcUxhQ0ZKR2o0ditkZGlxMGtydzg4UU5kZS9pS1dYL0orbzBlLzhmTEJXV0YrUEgzNnJoK2NNaUFTS2lNSHJuMFVQejFRNE5MVTZpU1FkWHZHem0zcDdxa0JrZHp1VHVBZTJvUWEvVkIxc3ZoeGluRm5neUFEYmZFNDlxQ0hYdnFqWTZrcWxsdU9ZNm1ZNFBZcm0iLCJtYWMiOiIxMTY2Y2E3YjI0MDBhZWQ5NDI5ZmYzOTE3MGE0YWZmNzVlZDI1YTIwY2Q4ZGE0ZTcxNThjNTFiZmUxOWJmNTEzIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:21:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImFmdWlKRUZVcXZvS2QyRmtvenI3dFE9PSIsInZhbHVlIjoiNFBKREtGd3hhUkNWTXVJaFZNUGVrSVNNTWh0NndHcXVRK0pMRURFZW5qYkZ4TGlKYkdCQWhLbkVRbXBGUGNvQnVqV2R4QWFNTnI1a05Kd3pRbVloQVQrWXZ0Qm9KZ0ZPMDZ5dmdETnJoSU1FZlFOb250dWtPMWdudWlpSFlrWkZ4R1N6ZEJCcmh3MlUwQi9WSWZndFpKTERtZWkybk1vcHdZaEdsTVpMRjJwNXBNck5nUFBWekNGZUQ4UUtsaUJrbU0wNGFpaW5PNEk5ODhkajBMMmhKbGloT2dVSjVtQmtSamozcTdPTVJtVFJrdDVKRHhBVUp5TFVqaFB4SUZXU0tmU0hVenZ1RXFXMW0rY0pUMzR4TDB4dVFHMkIrbU1GQkswY1BjZDJaa3dYVXBJcThUT0h0VDRmNGVLV1pKVCtjczA2RVh2RnQwZ0lrYWJ2MWZINithR2tuWnFqZXJhanRUM1h3Q0E2enc4WTkvMUpmVzB3Z1IzL3g4OEZsdml0KzJFdzlCNHR4S2szQUtwUWhrL0JDQmdGWFE4eWZPWTVnZnViTW9GSGhEOVBqMThGZ2xEQ0ZJc0NpUGthRU1NS0VNOVp2UDFTZnZzejE5SjRjVTlHVDFnRllQcHY3ekFCQWQrRytIbG5aUGxRWitSVjIybk1pSzNvWEdtVWZ1cDMiLCJtYWMiOiJjOGQzOGNjYWY1NjhhYWZkOWU5NWNlMDNmMWQ1NzI4NmFmN2I0ODYxMGQ4ZGE3MTAyOTdmYTZiZjkzMzM5NmEwIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:21:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFqaG80dkRYM3JvTTFPVDhHY21Va1E9PSIsInZhbHVlIjoiazFwZ3VEWGNtN2RCeC9uYXVPM2h3OUNrazlnSy9Dd0JQKzM0d0VRcXE5d0ZKQnFIVS9CNEt2WGJ1OE5KT2hvUndLaEZka1d1SlMxaEw2QVQ5TW1pa0s2YkdmeXRqQ1JORXdyUkVnaS9KQ3ptTVlmSEtsdU1SUG1VQVRZd1hMOU9IckRCVlBQWFVDalRuVDhaSlZlUkFkWUtOY1VwWElTK2hYcnZoZTZxZ0JpdjdKMFJkTFBQS3M5RURHV0tVWTFuT0w0LzczL2xaOHU0Z0tXVzlVNG8zZmRIYTJYWmdvUzh6OXZ3Q2lneTRXSXF5L1FzNEVlOXZwb0MzZnBUYStmNXhHSEZuK0VIaHNhcElZMzFleVFVQTZ2NFFmN0V0eWVHR1k0NkRYYThQZG1LL21LYldNVy9mcW5jLzBzckdHTDZUd0w2Vyttb2h5UmZEZnhralBIWU1BcGNmZjFVUENGZkgybHhONG5BSS96ZmdvcUxhQ0ZKR2o0ditkZGlxMGtydzg4UU5kZS9pS1dYL0orbzBlLzhmTEJXV0YrUEgzNnJoK2NNaUFTS2lNSHJuMFVQejFRNE5MVTZpU1FkWHZHem0zcDdxa0JrZHp1VHVBZTJvUWEvVkIxc3ZoeGluRm5neUFEYmZFNDlxQ0hYdnFqWTZrcWxsdU9ZNm1ZNFBZcm0iLCJtYWMiOiIxMTY2Y2E3YjI0MDBhZWQ5NDI5ZmYzOTE3MGE0YWZmNzVlZDI1YTIwY2Q4ZGE0ZTcxNThjNTFiZmUxOWJmNTEzIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:21:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImFmdWlKRUZVcXZvS2QyRmtvenI3dFE9PSIsInZhbHVlIjoiNFBKREtGd3hhUkNWTXVJaFZNUGVrSVNNTWh0NndHcXVRK0pMRURFZW5qYkZ4TGlKYkdCQWhLbkVRbXBGUGNvQnVqV2R4QWFNTnI1a05Kd3pRbVloQVQrWXZ0Qm9KZ0ZPMDZ5dmdETnJoSU1FZlFOb250dWtPMWdudWlpSFlrWkZ4R1N6ZEJCcmh3MlUwQi9WSWZndFpKTERtZWkybk1vcHdZaEdsTVpMRjJwNXBNck5nUFBWekNGZUQ4UUtsaUJrbU0wNGFpaW5PNEk5ODhkajBMMmhKbGloT2dVSjVtQmtSamozcTdPTVJtVFJrdDVKRHhBVUp5TFVqaFB4SUZXU0tmU0hVenZ1RXFXMW0rY0pUMzR4TDB4dVFHMkIrbU1GQkswY1BjZDJaa3dYVXBJcThUT0h0VDRmNGVLV1pKVCtjczA2RVh2RnQwZ0lrYWJ2MWZINithR2tuWnFqZXJhanRUM1h3Q0E2enc4WTkvMUpmVzB3Z1IzL3g4OEZsdml0KzJFdzlCNHR4S2szQUtwUWhrL0JDQmdGWFE4eWZPWTVnZnViTW9GSGhEOVBqMThGZ2xEQ0ZJc0NpUGthRU1NS0VNOVp2UDFTZnZzejE5SjRjVTlHVDFnRllQcHY3ekFCQWQrRytIbG5aUGxRWitSVjIybk1pSzNvWEdtVWZ1cDMiLCJtYWMiOiJjOGQzOGNjYWY1NjhhYWZkOWU5NWNlMDNmMWQ1NzI4NmFmN2I0ODYxMGQ4ZGE3MTAyOTdmYTZiZjkzMzM5NmEwIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:21:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-911525717\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-827943754 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aXNlwC2u7eBVVmf76Srodlte78QD6PcvbxRIevxR</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-827943754\", {\"maxDepth\":0})</script>\n"}}