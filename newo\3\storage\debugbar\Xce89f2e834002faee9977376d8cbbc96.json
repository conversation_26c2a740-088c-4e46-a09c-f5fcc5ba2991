{"__meta": {"id": "Xce89f2e834002faee9977376d8cbbc96", "datetime": "2025-06-17 05:48:41", "utime": **********.32781, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750139319.731538, "end": **********.327847, "duration": 1.596308946609497, "duration_str": "1.6s", "measures": [{"label": "Booting", "start": 1750139319.731538, "relative_start": 0, "end": **********.114711, "relative_end": **********.114711, "duration": 1.3831729888916016, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.114732, "relative_start": 1.3831939697265625, "end": **********.327851, "relative_end": 4.0531158447265625e-06, "duration": 0.2131190299987793, "duration_str": "213ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45156184, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.024220000000000002, "accumulated_duration_str": "24.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2197878, "duration": 0.02095, "duration_str": "20.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.499}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.283447, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.499, "width_percent": 5.533}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.302997, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.031, "width_percent": 7.969}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1861481915 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1861481915\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1402988853 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1402988853\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-139484810 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-139484810\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1622372362 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750139302605%7C16%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Iis3TWl6SCtFbk82WTVMb0lnSWlGSnc9PSIsInZhbHVlIjoiNzIxaUVuQmJUV0UwOHJpRU9TcXVVU3pYTTl4a2dZcHdMSTNUbDMrSUVvcG1RcFR3K0tXalJGVWxhL2w5aStGNGR6RDE4dmJvSVkrMHVUY1o5QUNRU1loMllra1A2elBzT1E5UVlYRDZ3T1lpRTcrbzNHVlJWMzRuUFV3STJSWjUrMllnSE43Tk1YUmJ2Y1diajh6dmUxZnROTlMvWnhjTmxQMDhmMW9vTEpEdkdOdS9laWN6QzdQUk9MSjJZY0puK0ZXb0xDOFFoUkw1ZkhEd0JMT08rR3JiNVdQUnEwQXpTTXRFNkhHK1hjWXN3bVQ1MnMxdTZqSEtrT3ZEZkpLYXUxb3NPZlczZy9WUG5yTk5iaGlPWHpNMkJLcWlDcFVrWC9MTXAvRVVWenAyZG43UGxtN1RDYitUS2tsSS9YTWlVU2xOUUdqMXZLbW1hb2ZjZVRSTlJDdjVXcVl5Z0xKSzJTL1hIMmc1S3NUa0xYZ0ZEVUFlSi9JT3VlY2tYNHF4YjRUN3BjWlRTMXZMK0dVeHczMmcrZXVzR0hJNTg5MWFraVdKQXNXUlFkSDlqMlNzMFp3UjdUQk1XRXlqbnAzUk5JczBlNHNPSFh1Z1FJN0tCUVpZM3JmNXNDeEd5R05MdUlZY3k2M2Uyd1NKUnFrVFFoZVVNMUdTNkVHT0xhbTQiLCJtYWMiOiJhMjFjNDM4OTY4MTkyNzNlMzMzMzUwNmI1MzkwYTEzYjFkODE1Nzk3ODQ1YmJhM2VkM2VhN2FjNTIzYmRjYzgxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkRjRElrUlFRSFRYNUp4OXVJTm5WVlE9PSIsInZhbHVlIjoiTlNzbkp1WExRL1Jrd3FTOWluMDl5ZXBwdUdXRmtUODh0VUVMM0FpOWMvV0R4S2x0NmF2VkNWSjV5cHJOLzVTNHlTZ2c2SWxrcHZvOWZNQmhFOEFFUkc2aXA0Y2ZwUVhVclhWbjNYWnhuYUdCZVlXV1plMU9UTEZkSGx0NVlUMi96VU1tdDdweUJuMHdCeEtXVEtSeVJkTy9kNk45TWNiZ243Vy9Zci9EbFJvY016c0xkVCs2MXR4dWxmWFdLdnYrSmw5eWxoMDNMNmlNVW1vNkY2YVp1bW1BT3hCVERpY0w4RVBCTXcvL2I5V1h3ampEL1hrR1JJUUFNMnQrMms0S1h5ZUlaSXFOTkpKRWN3aGJPVHNCNTVDZ3FMN2FvOG9IcnhQcCtBSEJETlNvb1FPSUF0REh1VnYvcFF1UDRQQmU0Q0gxOEl5Y0xicHhRLzRpc2xPWGpzbmlQbmt6NGg3WVc1QWZnRWRIODJxNWFPSE83c0EvOC9sY3dXZzhJMjRTYW9iTHp1bjhuZlVQZkhrdEpzWFk0eVpjOGdwOGxmYlB1NW1XSDRhWWw3dXVVWGdOUVFOdEJJdjNpZkNQVWUyL0U1SWtDWCsrQ2l5VTdpT1E4VzVsNlRpMzdTTnVMd3N5UWZyWUhxcWM4S3hqRk1qZjR5eHc2elREVlhNc2Y5N1YiLCJtYWMiOiJlNWI2YmYzMmJhMzFmNTUyMzc3OWQ0NjFjZDc5M2ZiOTUzN2UxZGFkYWVlNWVhNTQ3ZjlmOWZhMTk4N2VjMjY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1622372362\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2128169577 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2128169577\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-882378831 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:48:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVjbTBJdDFXN0hFYUVIQ0dkdURJTmc9PSIsInZhbHVlIjoiWXhseGhyVFFDbS9mem5MdmtlR2ZQYVk1elNWd3Zwbm1SUXpkUUZDNEVxS0dPMVdyWFFlV3E2UFhCdE00dTdyNkdhRFpERXUvNnlDSGx2NitxbVFTQ2VjQ2pFOVhNV200YlByQ0Y5SmkvYjdXbEJkNDVVTEo4K3FmaXdOcVBJM2xjSXNPc2JwR3BhUW95YllDWEQ3aExDdTZOenBIWS9EVFhaR0JCNjVyWjZqRkZTVFJwZms0bWhnRGlrSjMrTWFoQW5iZU1uSWJsQTluTGlVcDV6REVVNjNXbld5UDhoSjduQUt1bzN2d3NuWnRpY043UCtnOVd6RVN1UzRBdG50bUVENUgrSXVkSy95V3ZmS2ZLek5xZTZhSVB4aUhneHB1bW05VDRCWjd0UVF4OXI4N2JwUXNrdlVneXUxazZYYis3QjgwcUo2djA0ZjNSQ1VlbDIwNlRnOTdBUDBHWkNPQS8vQTltMWhvcnBxNnAreitYdTdEaXBoVkZsc3hGSmkxSVVjK0VuYVJnSWpnN0l2bDBZdXpkeVVJZHMyQ1pjcENiVTVsaEJmNC8rRk5tWWl1eTRES3JvUzRSMUV0ZEdMNXRTREJGUlZPM0MzSmdub0wvUnBLT3JMeXZUNWZSalhUTm9zNzNpbUNUeW1tQXR1b3JwcG5KR2ZHR20zcEN2VFgiLCJtYWMiOiJjYWVmMTBhOWFmYzFlNzdhZjQ1NWUzODUyMzE0ZjBlOWYzNmE2NWVlYTcxNjM1OWFiZGYwN2Q1NzZjZjQ2N2Q5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:48:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im93YXovUFdnRW93OVl5a1NnMEpPUWc9PSIsInZhbHVlIjoiRXQwTEhhYTFzRE0zb085c0FNOEFXNXRiSWtLSTI1MVNyWm5YYnZTK252QURXSmxPeFUveldpK29PMzNkZ3NQUkt1MCtzc2RoeFlBMnhYc0h1RGgrNzdPRDlIRDR6T3k4OE1meURLTklkS0tlQlB4MFdrdHBFZjR3czlRUEQ2YytqR1Z3UE5FaEZCdC9ZdFpMeHkxL1dwWUljdDdPK1pnNGJVS1ZFcGJGc2EvUGtDZTBuLzVYenFUWkV6T25kL0ZIamwwak1qT20yb1V0SUR1U3dUcXpnMjM2NlV4RmM4cnB1T1ZrNURPYW1aMDNnakhnTC9oSlZOVnhFcHZGWWFua2JXcDNHTGhvQ2xndnFBRnhWVEtodnlNNkM0UDRVVFE3aWxXOUc0VjJsUjErMC80STVyalYzL1VyV2p5bjloQTBKYWE2TVZ6aWpmNnJCSHlKVmJCODBCUTRwTlYyVUxXMmtkUmxxa3M3MWZGeXZISmpwSWRVNGV5TnpGQWxhNCs0S3c0eTNqeE1keHR6REY1WVpmU0tkZUNQWldPc0xPOVBhZ1hRR1dPUTYzTWZ6dUhtNzlCLzNkQlJTa3lSdGdNVk9sOEw0bG11MkF1MFpnTTQ3TmZBYisrakdzUG11WG1rSWE5aUhLWjByMzhFMldoRDAvbUlISUJjUHVLWkNhcGwiLCJtYWMiOiIzMjc4ZmQ3YzJlYTUzZGEzMzhjZWJkNmFiMDRlOTdlMmZmNTAwYmU0YTMyMzMyZTc2NjNhMjE3NDRjMzU0ZTdmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:48:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVjbTBJdDFXN0hFYUVIQ0dkdURJTmc9PSIsInZhbHVlIjoiWXhseGhyVFFDbS9mem5MdmtlR2ZQYVk1elNWd3Zwbm1SUXpkUUZDNEVxS0dPMVdyWFFlV3E2UFhCdE00dTdyNkdhRFpERXUvNnlDSGx2NitxbVFTQ2VjQ2pFOVhNV200YlByQ0Y5SmkvYjdXbEJkNDVVTEo4K3FmaXdOcVBJM2xjSXNPc2JwR3BhUW95YllDWEQ3aExDdTZOenBIWS9EVFhaR0JCNjVyWjZqRkZTVFJwZms0bWhnRGlrSjMrTWFoQW5iZU1uSWJsQTluTGlVcDV6REVVNjNXbld5UDhoSjduQUt1bzN2d3NuWnRpY043UCtnOVd6RVN1UzRBdG50bUVENUgrSXVkSy95V3ZmS2ZLek5xZTZhSVB4aUhneHB1bW05VDRCWjd0UVF4OXI4N2JwUXNrdlVneXUxazZYYis3QjgwcUo2djA0ZjNSQ1VlbDIwNlRnOTdBUDBHWkNPQS8vQTltMWhvcnBxNnAreitYdTdEaXBoVkZsc3hGSmkxSVVjK0VuYVJnSWpnN0l2bDBZdXpkeVVJZHMyQ1pjcENiVTVsaEJmNC8rRk5tWWl1eTRES3JvUzRSMUV0ZEdMNXRTREJGUlZPM0MzSmdub0wvUnBLT3JMeXZUNWZSalhUTm9zNzNpbUNUeW1tQXR1b3JwcG5KR2ZHR20zcEN2VFgiLCJtYWMiOiJjYWVmMTBhOWFmYzFlNzdhZjQ1NWUzODUyMzE0ZjBlOWYzNmE2NWVlYTcxNjM1OWFiZGYwN2Q1NzZjZjQ2N2Q5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:48:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im93YXovUFdnRW93OVl5a1NnMEpPUWc9PSIsInZhbHVlIjoiRXQwTEhhYTFzRE0zb085c0FNOEFXNXRiSWtLSTI1MVNyWm5YYnZTK252QURXSmxPeFUveldpK29PMzNkZ3NQUkt1MCtzc2RoeFlBMnhYc0h1RGgrNzdPRDlIRDR6T3k4OE1meURLTklkS0tlQlB4MFdrdHBFZjR3czlRUEQ2YytqR1Z3UE5FaEZCdC9ZdFpMeHkxL1dwWUljdDdPK1pnNGJVS1ZFcGJGc2EvUGtDZTBuLzVYenFUWkV6T25kL0ZIamwwak1qT20yb1V0SUR1U3dUcXpnMjM2NlV4RmM4cnB1T1ZrNURPYW1aMDNnakhnTC9oSlZOVnhFcHZGWWFua2JXcDNHTGhvQ2xndnFBRnhWVEtodnlNNkM0UDRVVFE3aWxXOUc0VjJsUjErMC80STVyalYzL1VyV2p5bjloQTBKYWE2TVZ6aWpmNnJCSHlKVmJCODBCUTRwTlYyVUxXMmtkUmxxa3M3MWZGeXZISmpwSWRVNGV5TnpGQWxhNCs0S3c0eTNqeE1keHR6REY1WVpmU0tkZUNQWldPc0xPOVBhZ1hRR1dPUTYzTWZ6dUhtNzlCLzNkQlJTa3lSdGdNVk9sOEw0bG11MkF1MFpnTTQ3TmZBYisrakdzUG11WG1rSWE5aUhLWjByMzhFMldoRDAvbUlISUJjUHVLWkNhcGwiLCJtYWMiOiIzMjc4ZmQ3YzJlYTUzZGEzMzhjZWJkNmFiMDRlOTdlMmZmNTAwYmU0YTMyMzMyZTc2NjNhMjE3NDRjMzU0ZTdmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:48:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-882378831\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1780098123 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1780098123\", {\"maxDepth\":0})</script>\n"}}