{"__meta": {"id": "X23809c024abc8538e1fd1cba0f180333", "datetime": "2025-06-17 06:52:00", "utime": **********.35858, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143118.36271, "end": **********.358626, "duration": 1.9959158897399902, "duration_str": "2s", "measures": [{"label": "Booting", "start": 1750143118.36271, "relative_start": 0, "end": **********.1307, "relative_end": **********.1307, "duration": 1.7679901123046875, "duration_str": "1.77s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.130728, "relative_start": 1.7680180072784424, "end": **********.358631, "relative_end": 5.0067901611328125e-06, "duration": 0.22790288925170898, "duration_str": "228ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45156424, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.018439999999999998, "accumulated_duration_str": "18.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.253411, "duration": 0.01594, "duration_str": "15.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.443}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.30442, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.443, "width_percent": 5.315}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.327898, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.757, "width_percent": 8.243}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1060063246 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1060063246\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-138314286 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-138314286\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2085304164 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2085304164\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1714419094 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1c3qljs%7C1750143113800%7C4%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZPbzdpYUZNUGc1dlhXMS95UWF1c3c9PSIsInZhbHVlIjoid1VhNTN3Rkl6UHhqVDFoUzFoWXZLT3pFR20yZExsdFQxR3BUUHNnSWtER096V1h2V1VYaXI4UW9FNTlSRG5obHVGN0tKSGRFOVhiaG91NlgvcHV0VVM3RTBMdUFUR3VYNUh3WnQrd0prZjN0RzRVelNtMzl5SThERmdqNG95WE9Hd2h6blFhRUU5T09PRm9oc20rV0JVYlRZNld6V2NVeDRHM0pCbGIxUjRKSUpaYUJSR0Fxa1dJUHZmS1VOZnhoVk9RMVJ0UG9mTWpYNWEydnZ6eHdRMUpwaEpxMTFHa0pjRkFLdHBuWkZ3THVrUHlyUDZpb3NKT3RsUzg2STAvcWVyNk5YL0VYRk0xZnZUYjAvbkE5WGljSGVKNXRRUjlMcWZtTTNxUU9VYXhJallHdHdwWEhDdUhGc2NrV3habnRldGhFeUxqRW9vZGtVQmtYV2ZaRXU2ZENmZXI0R1hkL2dudzNnTm9sL3dJeDBrT0pjWS9kZGMvN1VHN2t6TS9xb3ZyemxIUTFpS1pBYmEra3hrRnE3c1MzbjRQNnp1RVhwUHBtYkRySCtvbWRSbmVTS1hoY1hRL2FybFhObU93Yjk0V1hvS0UzK3RaLzRWZVJIOFJBbVI0dUFUdnpGNUZwNTA5ZWNEY0F6MkQ0bUNJMWlKVS9WZjJFWUVsZnVtUDAiLCJtYWMiOiI3M2ZmMTcyYjhlZTA0ODY5NGExZjhmZDEzZTJlZjgwMTU0NjMzODI1OTQ2NWI2Y2NiOGRkMTYyOTY4ODZhYzk5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImJITDF0YmdiZlJuRDNpZVllU0t5eHc9PSIsInZhbHVlIjoiL0czc2o3S09BcmljZ0gwSkF2U3hKZEJBdzdUaiswakMwNHRleVRWNDdqVE1uVUpmeklsZFZEVlNPZk5xeDJXa3RDT2VrNzhuSEJZYmVIUFN1NU9ILzlCMzJTMm14MHM5M3hpMTZhQ1VsN3Q1OWV4Tnh4MlBFZlZtblRxNGZxdG9HT1BnbUZwOFR3TkZtQ0MzblFOMmFwNUhhcmJRRzJPb1gwczZuVWp3YzNOUWxUSTNySEp4eTNBaEdLMk1mR1FGUzhxOFZ5SVZGMng0YUZEdHRZUDB4bmwxUml0LytMUWYvd21qaTNWczRzNDgvQTJmemRjYzRsT2liSXV4Unp3Z1JDOVcycnhUWFZocFpJNzB0WHBIZEVuOUZBdmk1bWtwcXJqS0ZlV1dFYTh1SmxUc29HZC9KeDdXbHBNbnFqanBtYk9lNHgvMGk0UC9tSStQUlBXbW1hc0M4ZEo2K3VIMEFicmltUm9ObjhSZE9aTW1Ra1h1VEt2SlFydlFLS1JWYWkrQnJhWUtTY0RuMldKNkdSVWlUODY2R1h5QjhVOVBrTWE5RDIvZmdBLzRvTEZNTU1oSzZaaGxONzJ5dGs4bWsyMDh2WmMxc1Zzd0krZE5uRGhmNWhwWUdDQ3I1bWRLWGdjY1ZWZ2JVSVliYjI3UHB6MS9aQ2FoYUN6S01UR1YiLCJtYWMiOiJhZGU5OWE4MzE1Y2NkZGZlNTliZDEzYTc5ZTNhYjFhOWE0ZGQ2ODJjNmNmODEzYzUzN2QwYTJiYjg2YmVlNDA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1714419094\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-556056571 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-556056571\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-955548568 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:52:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdQVlludmM1NitHaUlKcXY3SzJ3NWc9PSIsInZhbHVlIjoiL2UwOXhTMjR0U0h4TkhMbFBFQTFDRDV5VGRDTE9yVEhwQWladm5NWVd0clJpYWVDMU45Myt3TmUxOXlJVDBLMzloc21XOU1IMUxnbFd5NGwza0xRcXFBaDc2ckU1VEFRbUdKL01NVi9aYnJWVllUZ0lHSi9abGJTQ3FiRFg3ZFpzM0R2UDlPbHcwTkw4THRKSmZNY3hQL2t6emxHN0RQYisyTXp2cy9adnltZkxQSEExUmtmS2tySFNDeFR4b1BPdEk3dXF6NUlqQ1VkbWtMeDluYkhiLzRDRk9xaU5kWHkvT1dFYjBuNHgvRDNqU1lyOVRvNXlKM3hvaitPVHFqWURpeTFLamtKVnJNVldia2VmUzl6WG1WSjlLL2JvRnVwNnpWV3VIcDJxYnQwTy9PVFhFa0lVbUFpeXVsTFpKZHpTRmN3YnNVS1JUWFhDRlBhMGRmeHIzNlk3TW5LL05FVTUyMTJub2FETXpvbjcyK3UxNzNPdmVmbDV5ejc3S1NuQkNpbjM3R2ZvNFZvT3I0UlpKdmgycUlHTDRyVVdRWHp1VVRzcWJuUE1wWmtSOVRyS0pINVhYd1BwWklkZWRhenBPSU8vOWlGQ3FWdEFBZWRBS2c3UklsdDZCQmZnUlAvUTQ3Mlc1OEJyUGZxcTdOZWEwWlNNMXRCN1dpdlREV24iLCJtYWMiOiIxY2RlOWUzYjE4ZDYyMzY4ODBmZDhjYTBiZWVjODA5MTU5MTU5MTI4MzQ1NjgwZWVjZDJhZDA2ZjBlMWY3OTJjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:52:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImpobnU4dVdqU1NoUmFYSFJBU2JRY2c9PSIsInZhbHVlIjoiNXJ4VmpPM0Y2QmRyUjgrNmd1a3FOSzhIUUVrcE5waEpHcnNxeVV6enRENUhsWXA3aWFlUkNqTUdvanRURXpUYjBBcU9OdzJEcElKSWs0ZDZ6QWhmeFNybjlyVkx0eXJKVjlva2ZpNE1TZm90bEphK1pEd2l0NExRRXRGQVZkSm5neGxnYmZSNWltYXVvSk5HUzdxR0h5aUk4UUxTM0lSZDZjUFR6ei9CVUU5OUE0WThwVGIwdTJHY1NTVGlqOERwTHlpRnJVUEs1NUpvNGdSNldaU1p4ek90NGhzUlhvK1FuRjI1cE53bXlsZVJXSEFxWlNsMUNSVmlEcXRYR0M3WHFreS9pdnRWSlQxK0hEcTdZSjVLQUxURHJNaXY4U01lMnR0aUVpRFhqc0tZcjgrVUgvUmt1RGVOM1haY1hnTDB3SXM2eE85SENWRkd5Nm5FbFo2dlRhQ2pKRU5NNmtRd0lIOUIwRnlZaFVQY0E1TGhKU05Uc1JzOGpHZUVNYnBzVCtnaFdwWnQvS1RocVA0bG5qcENpOElFamloSCtyT1F1cjlscjZzeU1VQlQzMmdnQ3B3VHBCYVRKQlplcncyaE5vMCtSbzVYdWpHMll4NlNHdkJna0tmeU92VjRuVlIzb2Y0eW1OSHh5WFZMQkJJSXVtczJsczhZOERzYXpKOWIiLCJtYWMiOiI0MWM0MTYyNzRkYTUxNDM3ZjE4NGE3YjY1MjIzNTJlOGUxOWNiMjE2ZmI1MWFmNjAzMjZiZTYzMjE5MTQ1OTMyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:52:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdQVlludmM1NitHaUlKcXY3SzJ3NWc9PSIsInZhbHVlIjoiL2UwOXhTMjR0U0h4TkhMbFBFQTFDRDV5VGRDTE9yVEhwQWladm5NWVd0clJpYWVDMU45Myt3TmUxOXlJVDBLMzloc21XOU1IMUxnbFd5NGwza0xRcXFBaDc2ckU1VEFRbUdKL01NVi9aYnJWVllUZ0lHSi9abGJTQ3FiRFg3ZFpzM0R2UDlPbHcwTkw4THRKSmZNY3hQL2t6emxHN0RQYisyTXp2cy9adnltZkxQSEExUmtmS2tySFNDeFR4b1BPdEk3dXF6NUlqQ1VkbWtMeDluYkhiLzRDRk9xaU5kWHkvT1dFYjBuNHgvRDNqU1lyOVRvNXlKM3hvaitPVHFqWURpeTFLamtKVnJNVldia2VmUzl6WG1WSjlLL2JvRnVwNnpWV3VIcDJxYnQwTy9PVFhFa0lVbUFpeXVsTFpKZHpTRmN3YnNVS1JUWFhDRlBhMGRmeHIzNlk3TW5LL05FVTUyMTJub2FETXpvbjcyK3UxNzNPdmVmbDV5ejc3S1NuQkNpbjM3R2ZvNFZvT3I0UlpKdmgycUlHTDRyVVdRWHp1VVRzcWJuUE1wWmtSOVRyS0pINVhYd1BwWklkZWRhenBPSU8vOWlGQ3FWdEFBZWRBS2c3UklsdDZCQmZnUlAvUTQ3Mlc1OEJyUGZxcTdOZWEwWlNNMXRCN1dpdlREV24iLCJtYWMiOiIxY2RlOWUzYjE4ZDYyMzY4ODBmZDhjYTBiZWVjODA5MTU5MTU5MTI4MzQ1NjgwZWVjZDJhZDA2ZjBlMWY3OTJjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:52:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImpobnU4dVdqU1NoUmFYSFJBU2JRY2c9PSIsInZhbHVlIjoiNXJ4VmpPM0Y2QmRyUjgrNmd1a3FOSzhIUUVrcE5waEpHcnNxeVV6enRENUhsWXA3aWFlUkNqTUdvanRURXpUYjBBcU9OdzJEcElKSWs0ZDZ6QWhmeFNybjlyVkx0eXJKVjlva2ZpNE1TZm90bEphK1pEd2l0NExRRXRGQVZkSm5neGxnYmZSNWltYXVvSk5HUzdxR0h5aUk4UUxTM0lSZDZjUFR6ei9CVUU5OUE0WThwVGIwdTJHY1NTVGlqOERwTHlpRnJVUEs1NUpvNGdSNldaU1p4ek90NGhzUlhvK1FuRjI1cE53bXlsZVJXSEFxWlNsMUNSVmlEcXRYR0M3WHFreS9pdnRWSlQxK0hEcTdZSjVLQUxURHJNaXY4U01lMnR0aUVpRFhqc0tZcjgrVUgvUmt1RGVOM1haY1hnTDB3SXM2eE85SENWRkd5Nm5FbFo2dlRhQ2pKRU5NNmtRd0lIOUIwRnlZaFVQY0E1TGhKU05Uc1JzOGpHZUVNYnBzVCtnaFdwWnQvS1RocVA0bG5qcENpOElFamloSCtyT1F1cjlscjZzeU1VQlQzMmdnQ3B3VHBCYVRKQlplcncyaE5vMCtSbzVYdWpHMll4NlNHdkJna0tmeU92VjRuVlIzb2Y0eW1OSHh5WFZMQkJJSXVtczJsczhZOERzYXpKOWIiLCJtYWMiOiI0MWM0MTYyNzRkYTUxNDM3ZjE4NGE3YjY1MjIzNTJlOGUxOWNiMjE2ZmI1MWFmNjAzMjZiZTYzMjE5MTQ1OTMyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:52:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-955548568\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-769426279 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-769426279\", {\"maxDepth\":0})</script>\n"}}