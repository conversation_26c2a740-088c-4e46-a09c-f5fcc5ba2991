{"__meta": {"id": "X1e6973dfad9ecf80e52e69de37dd9242", "datetime": "2025-06-16 15:22:11", "utime": **********.702455, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087330.022483, "end": **********.702488, "duration": 1.6800048351287842, "duration_str": "1.68s", "measures": [{"label": "Booting", "start": 1750087330.022483, "relative_start": 0, "end": **********.497132, "relative_end": **********.497132, "duration": 1.4746489524841309, "duration_str": "1.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.497154, "relative_start": 1.4746708869934082, "end": **********.702492, "relative_end": 4.0531158447265625e-06, "duration": 0.2053380012512207, "duration_str": "205ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45152544, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02193, "accumulated_duration_str": "21.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.602704, "duration": 0.0183, "duration_str": "18.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.447}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.6513078, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.447, "width_percent": 5.837}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.675416, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 89.284, "width_percent": 10.716}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087320647%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFsYiswQ09QSjlyNC9zVDUyOHBmbEE9PSIsInZhbHVlIjoiYi9uRGhZc0NHRUZBZ1JkeXdLSFk1Qisvd2xjUW51MXA2aHZxRXh6T29ZRzZ5UzNMNUU1Rk9MUFRLdHNSZFJ4b01QckU0Y3AvaDhoRHRBY3h3UnV1KytIS3RSSWFvaDZWcWF2SGdud0hSYjN5V2tIbkxvWGZqSjlxeTZ1ZHZMSWVoYTcyRkt2Q0paYWEyR3dSaUhOakJkcjRSeGNvWXRTeWFsak5zMGhZS1ZuVE1FcHp6R1dhek9SNEJPVTFwUkRraGQ2c1ExNFlwY3lhem5WdnVaQkhxQXFLU2JiV2NJczNLWDFsWlpnbEwxNHVlRXN6ZWpTWHBuaXA2R1hmZ1NTcWRFMEVZWkVtYmVoVFBTaHNDT0ppUXFvNEZTbU9sSGNrek40b09OMS80a0cvNkpyRXlPemtjR3E2Z3NTdXVJbXhpSXpqRDJNKzUzQS9FNkMrSTdJNDFKQ1hhczJCSEtVNGJxY0prZGFKMUduZGlwd0poRjJOOG1mV2VjcVJZK3dNWFhEczZWd3N4dldEajlMZk1FbTVyVGRKQ2YxWEliSHY4WWFCQ0ZTa0JyZXpxaElzellkSlBBSjd4YkVGZ25YRUEzT085SnU5ekpCQ2ZZWXlhdzVXeE80RnFPejQ0QVYrN0NvaFl2V05LTHdGUmpJaEtHeDBpYXY4RTJGUWUrRXIiLCJtYWMiOiJhZjdlYWRmYWYzNjM2YmRkMTNiMmZhNmU4YmZkYmJhYTZkMWEwMzY1MDUyYTZiMTM5YmQ3YTMwODA2Njc2ODFjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjhPMm5lSWFsODdpVnpaRmE3R1JiUVE9PSIsInZhbHVlIjoiS09ySG9ZNVMxdjR3Z05WWmQzb2J5QXNTVXkwQ1FDVmxNQ2VzZFhvZjVOSDZoRDV2OEgwSm9JeVJKTXMrSmhBaGcvVUxGK0s2NHRLOVVibGRhbEJuWnQ3MnpwZ3d3VGRzckxBWUVXQlZ6U1BVNFhBcmhQek0zOEZ0Ym1HMURJVEg3bnpEZmR2YU9iYWFCTEUyaVRNb2RXaStIcFR1azVrTXNvcVNaNGJ6ZVYwby8zdVg3YllMY3NSRmtqNEhkVXgrdG9CVUF4WS8yYVZiOUJDb0Q1VGNQbEkzckM3ZllUbVZNZWdqbHdSelVxSTJRUzgzK2dJMVZzaUZJNkV6WmJ1QUdPZCtYZ0IxTnJSUU1UcWdGbXk4Ny9kZTVpQytzTGpFL3U0OC9YTnNkNHF5bGYrT2kyblpvbnlnSkFQUTBHYlZXclhtM0IxbldCLzlyUDZaWk5LMkRMWGZQQkJHWmZvalN1b2x3d2dKYU9SaGtnVU9oRlhTV25wcXZPWlpSU05sZkNUREUvTzFqRnJSR21IV3draGphUmk0NGNXVWtmM292STFGMGRIbkhTVkNmdmQ0WGlKbHdkajhxcDVSdVpHN2xoUkdzUFRBYklFenZzOHRBRURQa0JWT1BZajNyU041TTRpVWRPNWJIZ2JMR1M0QmNjTFdVbWlHK2taTjBqNEMiLCJtYWMiOiI2ZTQ2OGE4OGQ0MWY4MWYyNzllZTdjMWMwNWQxZjgyZTcwNjI2MTAzNmIwMjU1YTlhNWVjNGY4MGVhZDg4MDcxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzcvmOKIgVMCCvEHD9HLB5trqF9yJ9GcIs8Pndo9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-31191561 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:22:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9BSHhyL2ovcy9VYkh1U0ZneU5WU1E9PSIsInZhbHVlIjoibkZzYnlSTldYb0VvTnJyVDdnc2FKbnRkbWplWVVHdy9TdGhmcWZBR3grdlZ2ZzhPR01pemVBSDdTTlRiSGtNcjk2Wi92WWI0VHN5OXBpaGFGTjZDaWJjaGhRVVJ3cUNuM096Mkx5aG1McWQ4NDVOMnJlRHZBWDJZMlQwelRrK2phUndad2tkbzhjQjdFU25Ib3p0S2tVYWk5dndmaUhQaGdTVXc4N3QwWVJmZnZ4QURtZnVrRjh2aFdndTJuNTk4c0llZFN6NDVwQWxGb1RLUXRnTERudnIxZE1QaHU0eUNESFpLaHNWNHorQWlyNjQzcXN6Zk0zanlYL1haYW9VMTNUMjVFSlBsaEVLRzlEaVlhc3prdDB2VVZ3TXZvMEtzZzVkRmEwNEFqWUxXTXdtUVREaXdadEtnTmNka2hyZ3JRdU1pOHhsNXZHanF3QlRJQ0xwbnJ1SGdBdlozYnBRSGpWVEZFNTUyMzhvTHltbFRDWXY5M1ZQK3BTSUE2S29MbkhSVmdxdGVQc1ljc2VwZ1lWZ1luem8vYUVwc2c3WWtJZ2Q4RUNFa25OaG83NU5QVTVQRFpTbHNMajNpNERqbFlxTExsTU1jOXFKK3BFRkVHOHgyODdoL3ozME40enNMb3Z6TTdvUSszaXYxTjM5bkR5RjhnUGRncWdObTN2QUUiLCJtYWMiOiJkYWM4MmYyZjlmNWQ2N2M1M2M3MTExZDkzNGY4OTIzMTZiZGUwYjY4NzUwZjc3NTdhYmZlYmVhMWM4NzcyYTM1IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:22:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InY5VFhaZWtFMmpkbGNobk9DeDRNN3c9PSIsInZhbHVlIjoicVdDK1B0bW5ja3A0L2N2U294eS9iMFJXYVZ1TDRJV3ZueGRSY1RqeTlXWDNpS29lRHlOYkpIem5qdVBLNDBvaGRKWm1qVjBCYlZHL0hLL1QwUGNWU01ZTDJIVWFMM0NyOEZjUGQ1SmRXUThLK0VuMFgrdDF3NWFpZjVMVUFoWUtKS2p1OGVmN1FTOWpuYmlmNmNFRjFpdjlCQXlDS0JYTlRYL3FFQVN6MWw0MExSYlExeHUwUXJhYm05dFZmUzdPNndHTC9ZNHA0bFpoWmNNZ0NrZ2JsM250eXVDM3JQUEY2UGJFQTMxUDVrRHc0U2YxdDJHMGx3V090VS9LUFVRTEdIeFhNZTFZa1lvTzZoZlZqaDZCaitiQTRiVFpqM01PT3VVamZKRWc2Vy9sWk9RdjZrZldkVkcrWUtnVlNzRkZhZlNOM2Vwb2FWYUwycHpTQlZpWHlFSWc0SEhSL1NsdXJQdVByRFhoWDlHcmZ1ZTBCQ3pQTnFtUFBOL1o5Yi93akR6Q0FqbElPZndhUmNtT0hVcFllbUt4REZlY3k0bDJxcDcxdmYyZjR1TzhiNy9UUGFrMitZanJwNnFTSjNQQjh2RmxPTXBpMElWaEM2aFk5WDlPV0lwUHhyamtiVGJtbXRTaXNsWFhkQjVLdjhMVWxVTkRZT0xpbVFOUkpQNzMiLCJtYWMiOiI1MTQyYWJiYWQyMDYzZjRmYmNhNTVmOWRlZmYyOGI4YzRhZjhiZDBlMzEzZDY3MWIyOWNjMGU5YjAzNTJhNGIwIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:22:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9BSHhyL2ovcy9VYkh1U0ZneU5WU1E9PSIsInZhbHVlIjoibkZzYnlSTldYb0VvTnJyVDdnc2FKbnRkbWplWVVHdy9TdGhmcWZBR3grdlZ2ZzhPR01pemVBSDdTTlRiSGtNcjk2Wi92WWI0VHN5OXBpaGFGTjZDaWJjaGhRVVJ3cUNuM096Mkx5aG1McWQ4NDVOMnJlRHZBWDJZMlQwelRrK2phUndad2tkbzhjQjdFU25Ib3p0S2tVYWk5dndmaUhQaGdTVXc4N3QwWVJmZnZ4QURtZnVrRjh2aFdndTJuNTk4c0llZFN6NDVwQWxGb1RLUXRnTERudnIxZE1QaHU0eUNESFpLaHNWNHorQWlyNjQzcXN6Zk0zanlYL1haYW9VMTNUMjVFSlBsaEVLRzlEaVlhc3prdDB2VVZ3TXZvMEtzZzVkRmEwNEFqWUxXTXdtUVREaXdadEtnTmNka2hyZ3JRdU1pOHhsNXZHanF3QlRJQ0xwbnJ1SGdBdlozYnBRSGpWVEZFNTUyMzhvTHltbFRDWXY5M1ZQK3BTSUE2S29MbkhSVmdxdGVQc1ljc2VwZ1lWZ1luem8vYUVwc2c3WWtJZ2Q4RUNFa25OaG83NU5QVTVQRFpTbHNMajNpNERqbFlxTExsTU1jOXFKK3BFRkVHOHgyODdoL3ozME40enNMb3Z6TTdvUSszaXYxTjM5bkR5RjhnUGRncWdObTN2QUUiLCJtYWMiOiJkYWM4MmYyZjlmNWQ2N2M1M2M3MTExZDkzNGY4OTIzMTZiZGUwYjY4NzUwZjc3NTdhYmZlYmVhMWM4NzcyYTM1IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:22:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InY5VFhaZWtFMmpkbGNobk9DeDRNN3c9PSIsInZhbHVlIjoicVdDK1B0bW5ja3A0L2N2U294eS9iMFJXYVZ1TDRJV3ZueGRSY1RqeTlXWDNpS29lRHlOYkpIem5qdVBLNDBvaGRKWm1qVjBCYlZHL0hLL1QwUGNWU01ZTDJIVWFMM0NyOEZjUGQ1SmRXUThLK0VuMFgrdDF3NWFpZjVMVUFoWUtKS2p1OGVmN1FTOWpuYmlmNmNFRjFpdjlCQXlDS0JYTlRYL3FFQVN6MWw0MExSYlExeHUwUXJhYm05dFZmUzdPNndHTC9ZNHA0bFpoWmNNZ0NrZ2JsM250eXVDM3JQUEY2UGJFQTMxUDVrRHc0U2YxdDJHMGx3V090VS9LUFVRTEdIeFhNZTFZa1lvTzZoZlZqaDZCaitiQTRiVFpqM01PT3VVamZKRWc2Vy9sWk9RdjZrZldkVkcrWUtnVlNzRkZhZlNOM2Vwb2FWYUwycHpTQlZpWHlFSWc0SEhSL1NsdXJQdVByRFhoWDlHcmZ1ZTBCQ3pQTnFtUFBOL1o5Yi93akR6Q0FqbElPZndhUmNtT0hVcFllbUt4REZlY3k0bDJxcDcxdmYyZjR1TzhiNy9UUGFrMitZanJwNnFTSjNQQjh2RmxPTXBpMElWaEM2aFk5WDlPV0lwUHhyamtiVGJtbXRTaXNsWFhkQjVLdjhMVWxVTkRZT0xpbVFOUkpQNzMiLCJtYWMiOiI1MTQyYWJiYWQyMDYzZjRmYmNhNTVmOWRlZmYyOGI4YzRhZjhiZDBlMzEzZDY3MWIyOWNjMGU5YjAzNTJhNGIwIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:22:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-31191561\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}