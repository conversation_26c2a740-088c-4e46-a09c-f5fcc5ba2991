{"__meta": {"id": "Xb61d6f30b0f4feccaf72a87635670328", "datetime": "2025-06-17 06:27:13", "utime": **********.771624, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750141631.924204, "end": **********.771671, "duration": 1.8474669456481934, "duration_str": "1.85s", "measures": [{"label": "Booting", "start": 1750141631.924204, "relative_start": 0, "end": **********.513662, "relative_end": **********.513662, "duration": 1.5894579887390137, "duration_str": "1.59s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.513689, "relative_start": 1.5894849300384521, "end": **********.771677, "relative_end": 5.9604644775390625e-06, "duration": 0.25798797607421875, "duration_str": "258ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45156232, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.024120000000000003, "accumulated_duration_str": "24.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.633808, "duration": 0.020030000000000003, "duration_str": "20.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.043}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.696393, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.043, "width_percent": 5.763}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.727556, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.806, "width_percent": 11.194}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1445188281 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1445188281\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-736071677 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-736071677\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-920316033 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-920316033\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750141457263%7C21%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndkM1p3c0F1UmlTVk91cXEyWW1BQ0E9PSIsInZhbHVlIjoiZHJPMnNiN1VWaldFTWdwNnFZTXN4VUtMMTZlRHhSZ1laK09hZXZNcFE4dXNmMXdLOGVtbVdLT0VWRGxjNXdWSTNxTmpWMHcyYmRnOFVxdUF0UjdXRHpPNmdRUkNxaHFwdGN0d0g3RDdHRzRFMTd2dERRNGhWcXF1T2VnQ25MaXMwcldPOWtXVEV1S1I2cE1TTTZSNzhQbkZabWF3SzFheWh5aHpLc1F4V3Qvd0luTWp2U1JuWjZGMjkwMGk1WFZueFAvd09LSHBiTDZhbFJ4S0Y2MkdmcCt4RmVoak5QUUt0SVJjaUxpdjEyZGtIajY1OXYzSnFpRi9kVUVVREpoK0RmS1dGQkUyWExKMGhEb2c3OWNhSXZjZ083aFAwR1RIWGsrRUdONmF0SjVxazhtdkVYTUpUenltcDRXZndDQTMyQzEvUnBjT3BtZEd3dEIrczNQOUZaamtoSjJ5N2RlUzMrcEp3Mm93cGdraHZHN2E2emE3M1B2YkFUdWc2aFVSR0JCYXlxR0lNd1dmQ3F6ZFhBdE9mQTFxRWRZeGEzb1cyZUlyMzZrMmNLWENOR2p4VVZieHQxVG1peEsyS1Zsanlka0lrNTFSUE9jWUdJYnRuVys0czc0VjVrV0pMeWZYTFFob0MwaEpRSDV3N0hpUy90Sjc0TTZLaDBydU4xTjUiLCJtYWMiOiI3ODQ1MDY4NTMzZjNlOGQxYTk3YmE2M2Y3MWE5MTRjMTAwNWNlODY3ZTQ5MmQwMTcyMGFjYjc1Zjg1MWJkODA2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IndNQTVqVERqcHBIUXZ5c0hhV28zc0E9PSIsInZhbHVlIjoiRTliRDQzbm83QXdyOHN0cmlhNVltUDJndzlhaWkwNnY4RVlPUmczbmFmZ3p3SlZiZ1oyejF2VURuYUZ6MDJ0eEZtbng3WXplcmozUU9LR1grTlVYR2pCTU1rMXcraEtDSVFOVFE0Z0ErMlJEY0NiTVQ5Qk9RbHJ1eTlLUVhmOWJFZzZ5a3ZjelQxcXU2Yk0wSUxXS1hOTXpjampwRDVtQ3dPNGR6ZnR0NjMxNUtnM1ZVN3BVVWJFNGVYdGZOeTNFUUhESGxjVFVJKy9zK3o3UHhBWHJnRTFRNDFJUm1YRUtTV1Rqbm9ORThELzFzcHFhZ1ovU1dlVXlRaVFVNWFlYTQ2Mzd5OG83VXJFaENvMVlSZjBWeUdtZzNPM0hBckNJZmlUZndzR1BtL2FReUNueFlGNFBoSmhuVUdBVnFKSVhicUNLbFJhVFpkZllsTm40NlVwSFpZOWR5Tkg1UVJBRHg4c3RTTVZ5emYvZk5BeUxseGIzQTRtQVZJemIybVBPZEVrVWtBSHhsQ2s1VTdnRzJNWjZXN3BnZ3c3cTd5TnFKT2Z4S01ZRzV1U29FQ1dNS0Y0WTI1VHJUM3grNmszYnA0YmdoaHNDOGd6cy9uWi8xQzViSkR2akJDMkc3SzZ1UmNRRHcxK0VHN0V1Uy9lZ2VxMjZPWlBoS3lLcG54YUciLCJtYWMiOiJiZDMyZDQyMTMwN2IzMTRhNDI3NjQ1YTA2ZTE0YWFiYTYzNzM2M2YyMWVlZGZhMDMxNDgxYjE3OWYzMTQ3MzdmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-6336018 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-6336018\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-271305631 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:27:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRGeGkrYmx4VHlQQWMvMXdvZDZpTWc9PSIsInZhbHVlIjoiR2VUbmcvb1ZZL2NPUmk1VVdHeUttNFZxVnlCSkZ0T25PTzVSdTBuci9DYlIyWk9UOUZzQmpIVXFqajAvVitKYm9yclNzZHJIUFNtNFZTblZwb2JQM3JvdkNuT2xkZE9QSUpzaVBNdzRaZ09qQVFReXNEeGdaZXhmZmpBTlZzcUU0NUdFUkpOMmk2KzBxNUgvb25sMEI0VFQyV0E0Q0dmbHpqb1lNaG1wK1hZWklyNXc4Sjc2WGNtTTdkbFdkQ3N4dmhqRVFjdG0wdFFvN3lzSDJ2WDVGTFhSc3A5VXFCZUhLdTB2UTZRY1hkMUFvdlI5Lzh3TnE2TEpIcStPczVQbUlSVHFNc2FIMkdFQ3J5N2hJYVQ1ZkxDOExNYnZxTlc5U3FTSUtydXhMWGowMDBHbThyRnhUNnk5cGMrUWJDdXdBWnIwTjNESE13eHVHSER1a3NuTmlGeFVIcFNaSHNpR3JHRzZ0SEdUY3dyb3NyRitCSFFIZ1NBUlBGMDBueW02ajMrYnJDbXRGei9uRjBIeU9LNVJnRUV6WFYyYTJZUTRubDRjMnlDQkFURmkwa3RBMVA2S3lXaTh1WmpNUFQ3WGtRSGgyNnFlanNtWnFkMFJWUXZvQzdVKzZXMFVRM0FZYzJRVEdjQWdWdEtjV1BTbStCM2tGbGhxQjY0dlh4T0UiLCJtYWMiOiJjMzMwMzUxZDZhOGQ4MjRmOWE2ZjZiOGNkMmNmMmE3ZjJlYTljZDY0ZTY0ZDQ4MzY5ZDQwMDIzOWI3YmJiOWUyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:27:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjFycTVXdUsvOFp6d1czaEFNays2RFE9PSIsInZhbHVlIjoicmpMbEhPOUcyRDQ3SHkwMkNrUHp2ZEVJYW14TnJxS0UyS0F2OUZoZ3RtOHNhQ3M5RWlKc3k2UXdSMnJyM3hvMnZkUXJuSFNJRHBNK2dkS2hnRTVwYStGdG1wS3FZdDJpdnlGMWNEVnFnVzdpL1EvMFVHdnVNSHBxWWMxTDQreDdOVStIbm1zNzNwWS8xaWY5SkNmSXVaRDFSbHV1Z0sxUHZDa2RNVlVXM1FDclZYamlUcEluOTRZRC9JblRwZ3RhMkpyeUQ2OTFQYVR3czI2bGNBNXNpYURTRWE3M1JKZllVanAvenpUdmM3OFB2d2twQnJZazFqWU9KVjc0emg4YWZjK25DRUNXUEhKdmZmaVgyMUhCMWpsMTNYaDNpSC9oRlJESWhyU0hRYWtKckhxMFZmQThXTGI2RzhBNEhRdlAxQUk3OElzZUNkYjY5a0VOSmJKeWMrc0h5UFFaK1IydW1tLy9KWlhPandVdUVEU2d5TmhNa0g5UG4zNklwZlhZV3p0bGFtM2tJMUVtOE5zVmRuSDloRGZJZEVVbExwTVNDSHIxYlZNK3FoZ2h4Wk1MK1ZaVmxrV1J0MU9obVhDL2NEbGhaeEJxSjVUelVpZnpibSt4bWpCN0hhQ0lmendkc1QrbTliY1VwWCtneDAxYjhjbXFsakJtV2VPNEV3aFYiLCJtYWMiOiIxNTdhODZjNzdhMjE3ZTNkZmU3Y2MzNjZlYzUwYmYyOTc2ZjUyN2JkNGUwNzMxNmQzOTNlYWE1YjJlYzE1MTdlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:27:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRGeGkrYmx4VHlQQWMvMXdvZDZpTWc9PSIsInZhbHVlIjoiR2VUbmcvb1ZZL2NPUmk1VVdHeUttNFZxVnlCSkZ0T25PTzVSdTBuci9DYlIyWk9UOUZzQmpIVXFqajAvVitKYm9yclNzZHJIUFNtNFZTblZwb2JQM3JvdkNuT2xkZE9QSUpzaVBNdzRaZ09qQVFReXNEeGdaZXhmZmpBTlZzcUU0NUdFUkpOMmk2KzBxNUgvb25sMEI0VFQyV0E0Q0dmbHpqb1lNaG1wK1hZWklyNXc4Sjc2WGNtTTdkbFdkQ3N4dmhqRVFjdG0wdFFvN3lzSDJ2WDVGTFhSc3A5VXFCZUhLdTB2UTZRY1hkMUFvdlI5Lzh3TnE2TEpIcStPczVQbUlSVHFNc2FIMkdFQ3J5N2hJYVQ1ZkxDOExNYnZxTlc5U3FTSUtydXhMWGowMDBHbThyRnhUNnk5cGMrUWJDdXdBWnIwTjNESE13eHVHSER1a3NuTmlGeFVIcFNaSHNpR3JHRzZ0SEdUY3dyb3NyRitCSFFIZ1NBUlBGMDBueW02ajMrYnJDbXRGei9uRjBIeU9LNVJnRUV6WFYyYTJZUTRubDRjMnlDQkFURmkwa3RBMVA2S3lXaTh1WmpNUFQ3WGtRSGgyNnFlanNtWnFkMFJWUXZvQzdVKzZXMFVRM0FZYzJRVEdjQWdWdEtjV1BTbStCM2tGbGhxQjY0dlh4T0UiLCJtYWMiOiJjMzMwMzUxZDZhOGQ4MjRmOWE2ZjZiOGNkMmNmMmE3ZjJlYTljZDY0ZTY0ZDQ4MzY5ZDQwMDIzOWI3YmJiOWUyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:27:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjFycTVXdUsvOFp6d1czaEFNays2RFE9PSIsInZhbHVlIjoicmpMbEhPOUcyRDQ3SHkwMkNrUHp2ZEVJYW14TnJxS0UyS0F2OUZoZ3RtOHNhQ3M5RWlKc3k2UXdSMnJyM3hvMnZkUXJuSFNJRHBNK2dkS2hnRTVwYStGdG1wS3FZdDJpdnlGMWNEVnFnVzdpL1EvMFVHdnVNSHBxWWMxTDQreDdOVStIbm1zNzNwWS8xaWY5SkNmSXVaRDFSbHV1Z0sxUHZDa2RNVlVXM1FDclZYamlUcEluOTRZRC9JblRwZ3RhMkpyeUQ2OTFQYVR3czI2bGNBNXNpYURTRWE3M1JKZllVanAvenpUdmM3OFB2d2twQnJZazFqWU9KVjc0emg4YWZjK25DRUNXUEhKdmZmaVgyMUhCMWpsMTNYaDNpSC9oRlJESWhyU0hRYWtKckhxMFZmQThXTGI2RzhBNEhRdlAxQUk3OElzZUNkYjY5a0VOSmJKeWMrc0h5UFFaK1IydW1tLy9KWlhPandVdUVEU2d5TmhNa0g5UG4zNklwZlhZV3p0bGFtM2tJMUVtOE5zVmRuSDloRGZJZEVVbExwTVNDSHIxYlZNK3FoZ2h4Wk1MK1ZaVmxrV1J0MU9obVhDL2NEbGhaeEJxSjVUelVpZnpibSt4bWpCN0hhQ0lmendkc1QrbTliY1VwWCtneDAxYjhjbXFsakJtV2VPNEV3aFYiLCJtYWMiOiIxNTdhODZjNzdhMjE3ZTNkZmU3Y2MzNjZlYzUwYmYyOTc2ZjUyN2JkNGUwNzMxNmQzOTNlYWE1YjJlYzE1MTdlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:27:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-271305631\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-955723368 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-955723368\", {\"maxDepth\":0})</script>\n"}}