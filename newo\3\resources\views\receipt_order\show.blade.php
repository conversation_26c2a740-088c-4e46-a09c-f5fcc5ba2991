@extends('layouts.admin')

@section('page-title')
    {{ __('عرض أمر الاستلام') }} - {{ $receiptOrder->order_number }}
@endsection

@push('css-page')
<style>
    @media print {
        .no-print { display: none !important; }
        .print-only { display: block !important; }
        body { font-size: 12px; }
        .card { border: none; box-shadow: none; }
        .invoice-header { border-bottom: 2px solid #000; }
        .invoice-footer { border-top: 1px solid #000; }
        .table th, .table td { border: 1px solid #000 !important; }
        .company-logo { max-height: 80px; }
    }
    
    .print-only { display: none; }
    .invoice-header { padding: 20px 0; }
    .company-info { text-align: center; }
    .company-logo { max-height: 100px; margin-bottom: 10px; }
    .order-details { background: #f8f9fa; padding: 15px; border-radius: 5px; }
    .table-products th { background: #e9ecef; }
    .signature-section { margin-top: 50px; }
    .signature-box { border-top: 1px solid #000; text-align: center; padding-top: 5px; margin-top: 40px; }
</style>
@endpush

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('receipt-order.index') }}">{{ __('أوامر الاستلام') }}</a></li>
    <li class="breadcrumb-item">{{ __('عرض الأمر') }}</li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header no-print">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0">{{ __('تفاصيل أمر الاستلام') }}</h5>
                    </div>
                    <div class="col-auto">
                        @if(Auth::user()->can('manage warehouse') || Auth::user()->can('show financial record') || Auth::user()->hasRole('company'))
                            <a href="{{ route('receipt-order.edit', $receiptOrder->id) }}" class="btn btn-warning">
                                <i class="ti ti-edit"></i> {{ __('تحرير') }}
                            </a>
                        @endif
                        <button onclick="window.print()" class="btn btn-primary">
                            <i class="ti ti-printer"></i> {{ __('طباعة') }}
                        </button>
                        <a href="{{ route('receipt-order.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left"></i> {{ __('العودة') }}
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <!-- رأس الفاتورة -->
                <div class="invoice-header">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="company-info">
                                @php
                                    $logo = \App\Models\Utility::get_file('uploads/logo/');
                                    $company_logo = \App\Models\Utility::getValByName('company_logo_dark');
                                    $img = $logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png');
                                @endphp
                                <img src="{{ $img }}" alt="شعار الشركة" class="company-logo">
                                <h4>{{ \App\Models\Utility::getValByName('company_name') ?: 'اسم الشركة' }}</h4>
                                <p class="mb-0">{{ \App\Models\Utility::getValByName('company_address') ?: 'عنوان الشركة' }}</p>
                                <p class="mb-0">{{ \App\Models\Utility::getValByName('company_phone') ?: 'هاتف الشركة' }}</p>
                                <p class="mb-0">{{ \App\Models\Utility::getValByName('company_email') ?: 'بريد الشركة' }}</p>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <h2 class="text-primary">
                                @if($receiptOrder->order_type === 'استلام بضاعة')
                                    {{ __('فاتورة استلام بضاعة') }}
                                @elseif($receiptOrder->order_type === 'نقل بضاعة')
                                    {{ __('أمر نقل بضاعة') }}
                                @else
                                    {{ __('أمر إخراج بضاعة') }}
                                @endif
                            </h2>
                            <p class="text-muted">{{ __('رقم الأمر') }}: <strong>{{ $receiptOrder->order_number }}</strong></p>
                        </div>
                        <div class="col-md-4 text-end">
                            <p><strong>{{ __('التاريخ') }}:</strong> {{ \App\Models\Utility::getDateFormated($receiptOrder->invoice_date ?: $receiptOrder->created_at) }}</p>
                            <p><strong>{{ __('الوقت') }}:</strong> {{ $receiptOrder->created_at->format('H:i') }}</p>
                            @if($receiptOrder->invoice_number)
                                <p><strong>{{ __('رقم الفاتورة') }}:</strong> {{ $receiptOrder->invoice_number }}</p>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الأمر -->
                <div class="order-details mt-4">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>{{ __('معلومات الأمر') }}</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>{{ __('نوع الأمر') }}:</strong></td>
                                    <td>
                                        @if($receiptOrder->order_type === 'استلام بضاعة')
                                            <span class="badge bg-success">{{ $receiptOrder->order_type }}</span>
                                        @elseif($receiptOrder->order_type === 'نقل بضاعة')
                                            <span class="badge bg-info">{{ $receiptOrder->order_type }}</span>
                                        @else
                                            <span class="badge bg-warning">{{ $receiptOrder->order_type }}</span>
                                        @endif
                                    </td>
                                </tr>
                                @if($receiptOrder->vendor)
                                    <tr>
                                        <td><strong>{{ __('المورد') }}:</strong></td>
                                        <td>{{ $receiptOrder->vendor->name }}</td>
                                    </tr>
                                @endif
                                <tr>
                                    <td><strong>{{ __('المستودع') }}:</strong></td>
                                    <td>{{ $receiptOrder->warehouse->name ?? 'غير محدد' }}</td>
                                </tr>
                                @if($receiptOrder->fromWarehouse)
                                    <tr>
                                        <td><strong>{{ __('من مستودع') }}:</strong></td>
                                        <td>{{ $receiptOrder->fromWarehouse->name }}</td>
                                    </tr>
                                @endif
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>{{ __('معلومات إضافية') }}</h6>
                            <table class="table table-sm">
                                @if($receiptOrder->exit_reason)
                                    <tr>
                                        <td><strong>{{ __('سبب الإخراج') }}:</strong></td>
                                        <td>{{ $receiptOrder->exit_reason }}</td>
                                    </tr>
                                @endif
                                @if($receiptOrder->exit_date)
                                    <tr>
                                        <td><strong>{{ __('تاريخ الإخراج') }}:</strong></td>
                                        <td>{{ \App\Models\Utility::getDateFormated($receiptOrder->exit_date) }}</td>
                                    </tr>
                                @endif
                                @if($receiptOrder->responsible_person)
                                    <tr>
                                        <td><strong>{{ __('الشخص المسؤول') }}:</strong></td>
                                        <td>{{ $receiptOrder->responsible_person }}</td>
                                    </tr>
                                @endif
                                <tr>
                                    <td><strong>{{ __('المنشئ') }}:</strong></td>
                                    <td>{{ isset($creator) && $creator ? $creator->name : 'غير محدد' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- جدول المنتجات -->
                <div class="mt-4">
                    <h6>{{ __('المنتجات') }}</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered table-products">
                            <thead>
                                <tr>
                                    <th width="5%">#</th>
                                    <th width="15%">{{ __('رمز المنتج') }}</th>
                                    <th width="30%">{{ __('اسم المنتج') }}</th>
                                    <th width="10%">{{ __('الكمية') }}</th>
                                    @if($receiptOrder->order_type === 'استلام بضاعة')
                                        <th width="12%">{{ __('سعر الوحدة') }}</th>
                                        <th width="12%">{{ __('الإجمالي') }}</th>
                                    @endif
                                    @if($receiptOrder->products->where('expiry_date', '!=', null)->count() > 0)
                                        <th width="12%">{{ __('تاريخ الصلاحية') }}</th>
                                    @endif
                                    <th width="20%">{{ __('ملاحظات') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php $totalAmount = 0; @endphp
                                @foreach($receiptOrder->products as $index => $item)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>{{ $item->product->sku ?? 'غير محدد' }}</td>
                                        <td>{{ $item->product->name ?? 'غير محدد' }}</td>
                                        <td>{{ number_format($item->quantity, 2) }}</td>
                                        @if($receiptOrder->order_type === 'استلام بضاعة')
                                            <td>{{ number_format($item->unit_cost, 2) }}</td>
                                            <td>{{ number_format($item->total_cost, 2) }}</td>
                                            @php $totalAmount += $item->total_cost; @endphp
                                        @endif
                                        @if($receiptOrder->products->where('expiry_date', '!=', null)->count() > 0)
                                            <td>
                                                @if($item->expiry_date)
                                                    {{ \App\Models\Utility::getDateFormated($item->expiry_date) }}
                                                @else
                                                    -
                                                @endif
                                            </td>
                                        @endif
                                        <td>
                                            @if($item->is_return)
                                                <span class="badge bg-danger">{{ __('مرتجع') }}</span>
                                            @endif
                                            {{ $item->notes }}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                            @if($receiptOrder->order_type === 'استلام بضاعة' && $totalAmount > 0)
                                <tfoot>
                                    <tr class="table-active">
                                        <td colspan="{{ $receiptOrder->products->where('expiry_date', '!=', null)->count() > 0 ? '5' : '4' }}" class="text-end">
                                            <strong>{{ __('الإجمالي') }}:</strong>
                                        </td>
                                        <td><strong>{{ number_format($totalAmount, 2) }}</strong></td>
                                        <td></td>
                                    </tr>
                                </tfoot>
                            @endif
                        </table>
                    </div>
                </div>

                <!-- الملاحظات -->
                @if($receiptOrder->notes)
                    <div class="mt-4">
                        <h6>{{ __('ملاحظات') }}</h6>
                        <div class="alert alert-info">
                            {{ $receiptOrder->notes }}
                        </div>
                    </div>
                @endif

                <!-- ملخص الأمر -->
                <div class="mt-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>{{ __('ملخص الأمر') }}</h6>
                                    <p class="mb-1"><strong>{{ __('إجمالي المنتجات') }}:</strong> {{ $receiptOrder->total_products }}</p>
                                    @if($receiptOrder->order_type === 'استلام بضاعة')
                                        <p class="mb-1"><strong>{{ __('إجمالي المبلغ') }}:</strong> {{ number_format($receiptOrder->total_amount, 2) }}</p>
                                    @endif
                                    <p class="mb-0"><strong>{{ __('الحالة') }}:</strong> 
                                        <span class="badge bg-{{ $receiptOrder->status_color }}">{{ $receiptOrder->status }}</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التوقيعات -->
                <div class="signature-section no-print">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="signature-box">
                                {{ __('توقيع المستلم') }}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="signature-box">
                                {{ __('توقيع المسؤول') }}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="signature-box">
                                {{ __('ختم الشركة') }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فوتر الطباعة -->
                <div class="print-only invoice-footer mt-5 pt-3">
                    <div class="row">
                        <div class="col-6">
                            <small>{{ __('تاريخ الطباعة') }}: {{ now()->format('Y-m-d H:i') }}</small>
                        </div>
                        <div class="col-6 text-end">
                            <small>{{ __('طُبع بواسطة') }}: {{ Auth::user()->name }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@if(request()->get('print') == '1')
    <script>
        window.onload = function() {
            window.print();
        }
    </script>
@endif
@endsection
