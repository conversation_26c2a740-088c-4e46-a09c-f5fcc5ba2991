{"__meta": {"id": "X0134dcb751ad66ebc61589e7f7f97245", "datetime": "2025-06-17 05:48:27", "utime": **********.266708, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750139305.856206, "end": **********.266747, "duration": 1.41054105758667, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1750139305.856206, "relative_start": 0, "end": **********.124882, "relative_end": **********.124882, "duration": 1.2686760425567627, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.124929, "relative_start": 1.2687230110168457, "end": **********.26675, "relative_end": 3.0994415283203125e-06, "duration": 0.14182114601135254, "duration_str": "142ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43508224, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.01889, "accumulated_duration_str": "18.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.220344, "duration": 0.01889, "duration_str": "18.89ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XTqfILO20ESOyUrbO2PEByvY2Oxsmxq0H0QyDSRn", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1804537226 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1804537226\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1977791950 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1977791950\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1612593014 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"202 characters\">_clck=1pubyvl%7C2%7Cfwt%7C0%7C1993; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1s0mt6n%7C1750063901378%7C2%7C1%7Cq.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1612593014\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1358111939 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1358111939\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-215616307 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:48:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNsOXl3WXAwK3YyRjd2UTEzVDdJNEE9PSIsInZhbHVlIjoibVJFdHBNUFd6NkZxekZwdDJ6SE5Sa0Q5MTFPSnRzVjAvcW0wSXllaU9iVWp2MmRVanJldDd2dVFnZXlha3kxK1lkQjEvZEJndytOY0JDRjA0NWkrTlBOYkgwcXd1Yk5RZmlPWllSSXNSUklXYWQxZm1tR2JFTkUyOENFYkFKdDhIbzZzWUJheTF0d0xreThJOGJ3V0VacldRSHAvQ0xNTzRPRjRTdUNXWDRka0lzV245eGIxZzNhUGd5Rm9lZ29idVFQS0xGVWtGVnNPRVJlN3JUTGpzSGxsaGt2aUZCaFB1Q0ozQWt6ZFEzQmdtTzlSQ1ZtZnFmZU5ucURhaEhtSi9KTS9KSGpOV0JtMDZGZlJtM3JYT1A4b2dhZDVMajY0QThpbWp0SlZsblFGMkZpbU1zWlBCNFdYUDFaYkd4WXRyNmo5NXlrK1hZbTl3eUhOdTZuSU1aSFlvNnRhOTMwRzcvbS9tdHdtU0xQQklGSmZsR28zd0Zjc3pKOCtHN016NlpYY2dGMWhvdDF6MmFDZjlBMjdKKzBQcFYySXlKWFIxZGRrNGhPU21EK1JtamxpZ0NhZUFrYzJBck1PRjg1NlRyUmxVNUdxK0syZDFhYnpuSFRIcFl3c0RaSEQwZElod2JtSDdCZWJDRUdUQ3lhNkY2UGRqbmdUVDJEREFnQS8iLCJtYWMiOiI1NmQ5MjQ3NThjOWMxYTNmZGU2OTQ2ZTg0OGVhMWFkMDliMTg1ODljNzZkNTk0YTFmZjM1NzY2MjFjZmZlYWRlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:48:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkJWT3JodjZiUHNoM1k4ZmVsQm8vNFE9PSIsInZhbHVlIjoiWUU4QWhaWmorL3BabnBJU2xZZzNGdUxDclpjSjFLR0dHZkN5b2hDb1hTTUhKT0xJVkV1NERPSDN4ZE55SGlsWWdYM3M4cFNhNGx1b3ZwTE9NRTBsZEx5SUFHY2FtNlVLamRrcVBLTGZuTjFpd3RuVWlBR1lFdVJqcWVZOFFoQlRuZ1FVd3ZoanhCaUt2MEVCOTliY2psYXNKd0lJZXpjT3BQZGZhSUFlWXpWTlZ0eFJhUHN1dWFUQlJWeVF1QkZENXdmQUhpa2JSR2dLUnNKcUFTWkM3NWF3TjJqSnVnU3BmSDE2R1pZVHBtZ0t1ZzNPN0ZSWldSM3E5K2xCNmNLTHJHajNuTDhiVklXQVVMZkpYZTRnc2dNeFU4cUVBcWE0T2gwRDVlMVMzalU2Y05QMURBeXRKR3FHd01QRXk1VlpKYnBoNHdvNGZsa2VxdkJqNWRDaW1kVHRaVS8wWE1Ycmh6WkkwSWR0eGduczBjSVRJZXorMWl6WTMxeFg4WEF3YkNTU2dPLytwdUVZSW8rVXc3NEQvVCttK0JTUitiQ1RqSWNPelA5WUhOUWo1UlpHVVJWMEhNK00wVW5ZMzZ0b21iYmVvY2dsbDFuWUc3ZjY5VVFzTFBLRDhlTmR3OEtsYUhEQkNvVU5VcVhvb2dubFR2MTdWaWRWeDJrMzYvQUoiLCJtYWMiOiIzYWM2YWNkMjQ2M2FhNTA3ZTg2OWEyZWU3YWFiMWFmZWQ2NTIzNjg5NDg1Yzk1OWU0ZjU3YTY4NjcwMzNiNzU5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:48:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNsOXl3WXAwK3YyRjd2UTEzVDdJNEE9PSIsInZhbHVlIjoibVJFdHBNUFd6NkZxekZwdDJ6SE5Sa0Q5MTFPSnRzVjAvcW0wSXllaU9iVWp2MmRVanJldDd2dVFnZXlha3kxK1lkQjEvZEJndytOY0JDRjA0NWkrTlBOYkgwcXd1Yk5RZmlPWllSSXNSUklXYWQxZm1tR2JFTkUyOENFYkFKdDhIbzZzWUJheTF0d0xreThJOGJ3V0VacldRSHAvQ0xNTzRPRjRTdUNXWDRka0lzV245eGIxZzNhUGd5Rm9lZ29idVFQS0xGVWtGVnNPRVJlN3JUTGpzSGxsaGt2aUZCaFB1Q0ozQWt6ZFEzQmdtTzlSQ1ZtZnFmZU5ucURhaEhtSi9KTS9KSGpOV0JtMDZGZlJtM3JYT1A4b2dhZDVMajY0QThpbWp0SlZsblFGMkZpbU1zWlBCNFdYUDFaYkd4WXRyNmo5NXlrK1hZbTl3eUhOdTZuSU1aSFlvNnRhOTMwRzcvbS9tdHdtU0xQQklGSmZsR28zd0Zjc3pKOCtHN016NlpYY2dGMWhvdDF6MmFDZjlBMjdKKzBQcFYySXlKWFIxZGRrNGhPU21EK1JtamxpZ0NhZUFrYzJBck1PRjg1NlRyUmxVNUdxK0syZDFhYnpuSFRIcFl3c0RaSEQwZElod2JtSDdCZWJDRUdUQ3lhNkY2UGRqbmdUVDJEREFnQS8iLCJtYWMiOiI1NmQ5MjQ3NThjOWMxYTNmZGU2OTQ2ZTg0OGVhMWFkMDliMTg1ODljNzZkNTk0YTFmZjM1NzY2MjFjZmZlYWRlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:48:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkJWT3JodjZiUHNoM1k4ZmVsQm8vNFE9PSIsInZhbHVlIjoiWUU4QWhaWmorL3BabnBJU2xZZzNGdUxDclpjSjFLR0dHZkN5b2hDb1hTTUhKT0xJVkV1NERPSDN4ZE55SGlsWWdYM3M4cFNhNGx1b3ZwTE9NRTBsZEx5SUFHY2FtNlVLamRrcVBLTGZuTjFpd3RuVWlBR1lFdVJqcWVZOFFoQlRuZ1FVd3ZoanhCaUt2MEVCOTliY2psYXNKd0lJZXpjT3BQZGZhSUFlWXpWTlZ0eFJhUHN1dWFUQlJWeVF1QkZENXdmQUhpa2JSR2dLUnNKcUFTWkM3NWF3TjJqSnVnU3BmSDE2R1pZVHBtZ0t1ZzNPN0ZSWldSM3E5K2xCNmNLTHJHajNuTDhiVklXQVVMZkpYZTRnc2dNeFU4cUVBcWE0T2gwRDVlMVMzalU2Y05QMURBeXRKR3FHd01QRXk1VlpKYnBoNHdvNGZsa2VxdkJqNWRDaW1kVHRaVS8wWE1Ycmh6WkkwSWR0eGduczBjSVRJZXorMWl6WTMxeFg4WEF3YkNTU2dPLytwdUVZSW8rVXc3NEQvVCttK0JTUitiQ1RqSWNPelA5WUhOUWo1UlpHVVJWMEhNK00wVW5ZMzZ0b21iYmVvY2dsbDFuWUc3ZjY5VVFzTFBLRDhlTmR3OEtsYUhEQkNvVU5VcVhvb2dubFR2MTdWaWRWeDJrMzYvQUoiLCJtYWMiOiIzYWM2YWNkMjQ2M2FhNTA3ZTg2OWEyZWU3YWFiMWFmZWQ2NTIzNjg5NDg1Yzk1OWU0ZjU3YTY4NjcwMzNiNzU5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:48:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-215616307\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-49869893 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XTqfILO20ESOyUrbO2PEByvY2Oxsmxq0H0QyDSRn</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-49869893\", {\"maxDepth\":0})</script>\n"}}