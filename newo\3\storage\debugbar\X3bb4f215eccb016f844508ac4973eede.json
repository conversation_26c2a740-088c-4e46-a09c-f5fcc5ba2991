{"__meta": {"id": "X3bb4f215eccb016f844508ac4973eede", "datetime": "2025-06-17 06:53:27", "utime": **********.273668, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143205.690575, "end": **********.273712, "duration": 1.583137035369873, "duration_str": "1.58s", "measures": [{"label": "Booting", "start": 1750143205.690575, "relative_start": 0, "end": **********.091481, "relative_end": **********.091481, "duration": 1.4009060859680176, "duration_str": "1.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.09151, "relative_start": 1.400935173034668, "end": **********.273717, "relative_end": 5.0067901611328125e-06, "duration": 0.1822068691253662, "duration_str": "182ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45171048, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00989, "accumulated_duration_str": "9.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.188329, "duration": 0.00735, "duration_str": "7.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 74.317}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2228389, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 74.317, "width_percent": 12.841}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.243078, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.159, "width_percent": 12.841}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2019505636 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2019505636\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1220105975 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1220105975\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-549277705 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-549277705\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1243967143 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1930 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; XSRF-TOKEN=eyJpdiI6Ikp6b04wck1Tb0JpZUJUeStIWFdkYlE9PSIsInZhbHVlIjoiRDRtWmdhejF3WHJJbXVUbU1zMlZIdnVYR1VUUENnUEw3dWVjd2prK1RzUnBWTmw0aDhpM1BKZDI0M0ZzcCtLTEF5M3R2VVcvUVZ1TkRZZ24zZmdnSG53cjJyOWw3ZlRKUFRrRFJ3Tk5HWWN1Rmp2T2s4WDZkWiswcDJpZGEzS3Q3Y3JZb2pZZWtNT3JYV2pXQ2RwNDdPQWlzMnh3SUMrejRneXMxQ1FPTCtsV3VCUFF4UmFRYzlGM1hjL0JvUTNTREgzeDA4c25OVkMvSkJCNjhYNDNENmRrV2ZkY1hqVC8yZDVVMFowWGtVOVNpK0E4bXFKL0g5WHpmV1ZLdTB5UWt1Sk9JSnNnRU10d1JqcVcyMXFSdlYvUTdjSlNKYUZtR1JwNnFPbmYraXFrNkhBejgvVER3RjRxelF0RUQvbU5qM0tkT2RpRnhWU3JQbHl6bThLTGxKVE05eTVyNEtMT3d0SUJiT3ZabDBnT3VPUU0reWJnbFhSMG5ZYmNFNTliM2p0MU1EYU9ZbGgxUUlBZDg3ZWlsQXZUOG0rNUQ1NWEwdmEwaFBUZnYzRml5QSt0S2lxeVNXbnRkTG8zaVdjRXlwT09VZTdZcG5Jc0c0YU5WL0ZZZlhpME1VbkdrbDR3cDJLemEzTU9pZWdqcjBCQkkxMVVDMFVFcy9jekZkbnciLCJtYWMiOiIxMjBiY2JiNTI4YzNkNTJmYzBkYjY3OWRmNmUwYmVkNzllYjE2YTkwZWEzNzczZjI2MmNlZmEwNTIxMGI2MGNkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZhUnZnaDFaL1o4ZzYyU2k2dElIZUE9PSIsInZhbHVlIjoibEVlQmxXVDdhL2VQdVdaVXlxUExDWktYb0hRUHltV3dQam5XTWxVVWFtL00vWTNVZGJQc2hTdTVSUWlmWDNvU25OU3ZodGwvSXhsRVFjclc1cnFPYUw3bERsWHp5a05PL0ZnWVE1d0hCREE4cUM0S05CMndNcEc5RHdzQ0phVzZRYnd2bEt6N21EQ2hESDRXV1FnSzFXQ3kvVUZwUkNyMFpBbjJPRytLcTNXbFZDMloreFhqMjhCazZMTWVjcEZwekhFQkl6MkQzSWVVWEtla2dHUmJHaUo1ZmFHTmFvbXFvRU5JS2ZzbXNlUVNRQWxFL3oxYmd0TXZiK0JobmdCcWpXY1lqNXJ1T2RBOFZLcDl3R0l3c3BIYXJ4bGVSNm80b3BPSVR1S2l3R2MybGltS2pBYVJpUnhRMXk4QVJyMU5Mc0FGeUdOS2dCUW1wcGZSK3B6Ukhnbm9jWVVXNW80SVVmK1kwNXNkTmpqanA2Z2RaTkJpVGdyZW1SaGpGWVp0SzNlNEJoTnpBdUg4UXE3aW1hQjVxVWNXV1VqUjNVZVNKalRaZmkrZjBxL21iOEIzeit5czRlM3p5cElYbWZxa05TdTRPbE9ycW1HTFdHeWFHVU9NbWFnbHpZUVZiQ1VUWXAzbGI4RXVIUmU4QlVQZEROQXF2b1RnVFBWZm9CbjMiLCJtYWMiOiIzODg1ODRlNTQ2MTIwZDgzZTc3YzIxYzE3YTM2YzgxMGUwNDM3YzIxYjk5NTEzMmJiYmQxOTMxYTFlYTZiYzdhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1243967143\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-467546527 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-467546527\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:53:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJaNFFyQ1c4dUd1ZEd2RVpHSkVZUUE9PSIsInZhbHVlIjoiZEw0OG1WaG9UVEhNdG5tTm40OXZZZTI5MEhQbCtKcDNaZTBmNy80RkphaDE3Qkt5Q3lpZS9NMDZTamhvS0ZHZmJ2MXV6VUQ3aVRJYlZsYVJJS3AyNjZQak8xZmFIMnFkdWpRbnJCOWpnMStheXF4RkJxRzZ4RGYyMnlnd3FOdEpCajYxOU84QUNObEFCSmtpTnR2WXlUUm1jZzVLQVpsVWRoSlFQQzZ6OUtXdmJRMUNIUklHN0ZlUEpaS0h1U3RKRGxDMkozaVBHdWdDV1U5RGw0OVFVRWVBSU5yQmJyK3B2MGUxUjRvTVVVd25RNDBUdGVVY2lndFZNdjB3b0t5Q2F6cXdYT1pBV0lFY2U4a0t4d0NiYjJrYzZ5RkgwRHprRXFHbUhaNWVJUVFnSGprcFkxc0VhRFc0WDloM2xOLzFtcjZEbmpGakIrS0JZUHA4bHNBSStkWGRkbHl1Y0tFTlRJaGpwOEpHNUhpN1luaVl4b0JaVWYxelZRVGtib2NLdTd6dHFDUEhFMXV3OEV4MmkwL05xU1Q1bDRYY3k4d0gvMXljaXZraUZ1Tml5QzhBWmhLZjIzKzdGQW1yQUVnQ2Rpdk1lTjFwQlZxUDZjQkNlZGJ1ZlFOSDJjdXphTGw4aWpUQzFDNUVXWnFYeVRQYld1U0o4UnByTVo4NmtJOFAiLCJtYWMiOiI1ZTBlYzgxYzkxYWFiZTdjYjNhMGRmYzYwZGY5MjM0OGQ3MmQ0MjMzMjBmNWNiYmViOWQ4NTBhZGVmYjU4NTk5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:53:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikh4RkQ4OFowUUZvcUtMeWx5anJtY0E9PSIsInZhbHVlIjoiREdVSEh5NjFxUXp6c2VDdzdkTXVqREQwcnVhaFZOZmdWRzBKZ1kyNVpoY3A3WTBDTGh3K25XYVl4eHkzQUh1K1hudVNxSHZhT3VwKzZZK0duMkRrOW82NUpXeXNVbUx4NCtJenYrL0RNUmhxTDdpRENNMy9tSzFNdWZCYnJSa2NtOFZMOXNib3dNR1hCN2FUM1h2dUZ1WEJtVG45T2JOOUdtbkJ6SUpmMmY4MVF2V1ZaQXlwck9FL0JkLzgrNlhnR3NoUXVLeGM3VGp2cmRrTFVYdmI3eFk3emxueUFleHU0WkJwMUtvSEpyRkk5dFFJTUFVdytMR0I0M055aDQ5Q2JjZ0dqaFlOc3RDZHcxTWlvVW5XNFN6a2JQa0VLUVArY3MwRy82aXJPalEvd3poajBHa0RmUUtvWmZVZkpUQS9nUGdTd3c1Ni9vMnhzTGs5T2Y5YWF3V2hzSXNDTkhtT1VYaERQcFcrRzNxVXh6VEttWTlRbjViUkpBckJCN05QdkgzUXlhK2FvMmdRNkxLaFJZWDVxbjlHeXJoK3c4WHdzcU9kSC8rUFBrdTRheGhwbTlmbWF2eXBXbnRrWCtoR1RzOEtiZlFqVGVHVzZFRllQdTNHSHMzQXlGbFFJREtxN1RZSEFrNksyV1hFcHdxZGlCeWcvWHBnSllIbnRva0IiLCJtYWMiOiIwNWM1ZWRhNmNjYjhmNzk0NzMyOGRmY2FlZDFlNzdjYTVjNGQ5YmZhOWY0NTNhMmNhMzY1ZDc5OTc4MTNmZmMzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:53:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJaNFFyQ1c4dUd1ZEd2RVpHSkVZUUE9PSIsInZhbHVlIjoiZEw0OG1WaG9UVEhNdG5tTm40OXZZZTI5MEhQbCtKcDNaZTBmNy80RkphaDE3Qkt5Q3lpZS9NMDZTamhvS0ZHZmJ2MXV6VUQ3aVRJYlZsYVJJS3AyNjZQak8xZmFIMnFkdWpRbnJCOWpnMStheXF4RkJxRzZ4RGYyMnlnd3FOdEpCajYxOU84QUNObEFCSmtpTnR2WXlUUm1jZzVLQVpsVWRoSlFQQzZ6OUtXdmJRMUNIUklHN0ZlUEpaS0h1U3RKRGxDMkozaVBHdWdDV1U5RGw0OVFVRWVBSU5yQmJyK3B2MGUxUjRvTVVVd25RNDBUdGVVY2lndFZNdjB3b0t5Q2F6cXdYT1pBV0lFY2U4a0t4d0NiYjJrYzZ5RkgwRHprRXFHbUhaNWVJUVFnSGprcFkxc0VhRFc0WDloM2xOLzFtcjZEbmpGakIrS0JZUHA4bHNBSStkWGRkbHl1Y0tFTlRJaGpwOEpHNUhpN1luaVl4b0JaVWYxelZRVGtib2NLdTd6dHFDUEhFMXV3OEV4MmkwL05xU1Q1bDRYY3k4d0gvMXljaXZraUZ1Tml5QzhBWmhLZjIzKzdGQW1yQUVnQ2Rpdk1lTjFwQlZxUDZjQkNlZGJ1ZlFOSDJjdXphTGw4aWpUQzFDNUVXWnFYeVRQYld1U0o4UnByTVo4NmtJOFAiLCJtYWMiOiI1ZTBlYzgxYzkxYWFiZTdjYjNhMGRmYzYwZGY5MjM0OGQ3MmQ0MjMzMjBmNWNiYmViOWQ4NTBhZGVmYjU4NTk5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:53:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikh4RkQ4OFowUUZvcUtMeWx5anJtY0E9PSIsInZhbHVlIjoiREdVSEh5NjFxUXp6c2VDdzdkTXVqREQwcnVhaFZOZmdWRzBKZ1kyNVpoY3A3WTBDTGh3K25XYVl4eHkzQUh1K1hudVNxSHZhT3VwKzZZK0duMkRrOW82NUpXeXNVbUx4NCtJenYrL0RNUmhxTDdpRENNMy9tSzFNdWZCYnJSa2NtOFZMOXNib3dNR1hCN2FUM1h2dUZ1WEJtVG45T2JOOUdtbkJ6SUpmMmY4MVF2V1ZaQXlwck9FL0JkLzgrNlhnR3NoUXVLeGM3VGp2cmRrTFVYdmI3eFk3emxueUFleHU0WkJwMUtvSEpyRkk5dFFJTUFVdytMR0I0M055aDQ5Q2JjZ0dqaFlOc3RDZHcxTWlvVW5XNFN6a2JQa0VLUVArY3MwRy82aXJPalEvd3poajBHa0RmUUtvWmZVZkpUQS9nUGdTd3c1Ni9vMnhzTGs5T2Y5YWF3V2hzSXNDTkhtT1VYaERQcFcrRzNxVXh6VEttWTlRbjViUkpBckJCN05QdkgzUXlhK2FvMmdRNkxLaFJZWDVxbjlHeXJoK3c4WHdzcU9kSC8rUFBrdTRheGhwbTlmbWF2eXBXbnRrWCtoR1RzOEtiZlFqVGVHVzZFRllQdTNHSHMzQXlGbFFJREtxN1RZSEFrNksyV1hFcHdxZGlCeWcvWHBnSllIbnRva0IiLCJtYWMiOiIwNWM1ZWRhNmNjYjhmNzk0NzMyOGRmY2FlZDFlNzdjYTVjNGQ5YmZhOWY0NTNhMmNhMzY1ZDc5OTc4MTNmZmMzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:53:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-450765200 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-450765200\", {\"maxDepth\":0})</script>\n"}}