{"__meta": {"id": "Xbc9a381ebb64f35bf74dca83c13e0b1a", "datetime": "2025-06-17 06:53:53", "utime": **********.860195, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143232.24642, "end": **********.860241, "duration": 1.613821029663086, "duration_str": "1.61s", "measures": [{"label": "Booting", "start": 1750143232.24642, "relative_start": 0, "end": **********.632537, "relative_end": **********.632537, "duration": 1.3861169815063477, "duration_str": "1.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.632573, "relative_start": 1.386152982711792, "end": **********.860247, "relative_end": 5.9604644775390625e-06, "duration": 0.22767400741577148, "duration_str": "228ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45154216, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02485, "accumulated_duration_str": "24.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.745805, "duration": 0.02213, "duration_str": "22.13ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.054}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8080928, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.054, "width_percent": 5.835}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8287609, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.889, "width_percent": 5.111}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-230688606 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-230688606\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1351960067 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1351960067\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1346290011 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1346290011\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-151449168 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1v0jvob%7C1750143208122%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtZY2hVb013bWJwYVUzSjJuZmVyOUE9PSIsInZhbHVlIjoiVHFJakQ3Rm5XaEtGVDNIZWl0cmlXL0tDMEVpcGY1UXVKL1NiUVpEV3ZkUTg5T1Jsa1RVOXRJZ1kwUGsvRmVyeEhmdHR5aHdrVndXQTQvNklzWWIxM0ovUExZZS93Nll5MEU2a29kVXFQY001ejd0dXFQR0hyMlFVdXFwUEIxQ2tFOXVSbVJvZ3VFV0dxWGJpTkw1cXEvK29qVmtDSGY3b3JUTGF3M2d2dkRVZHc1OFF0ZVE5VWs0V1AzRVdwQ29nNG42M2FueFYrTWVuUE5zYkRkR3J2WCthellkU2lvMEJqZlhoTUUwa1JxOE5ibFRkbm4zTGR6SUxwMWNkcFpTV1ZLY2xuWFBnWDlzc0RnbGF6TVVuU0JRVElVL3g1d1RaWDFZU205dC96eWZyZU04NDBBNTFZUmlqU296YlhNdXhHN1FWbW1HYkdLM3I0ajh5VzRpWUZMR3hkQ0tPdFhJSDNKSENXNzhrMnhVR3lNUzBVa21aZ1VUS2NFTk5OT0ZDZ2R4MWQ1UHBTbjNaUGFvUjhLbk9sUkdwUUdmOXFVTjI5MHl5SS9USUJTcnBJdUpoN0p0anFvNWlZK01EUW9IR0RxUDhjMUlnM3NXckJVbUFDODFJK0NvU05QQW5xWG8vcXlxcFdoL2xJRXhBR3RHY2cxZUEyamZBQVppc3JYVksiLCJtYWMiOiI5NDNmZDdmZDI3MjJjM2Y5ZmI1NjMxYzFkZGMyYWE0MTEyM2RhZDFhZTkwMjI5ZDc5ZjE1NTY4NGQwOGNhYTM5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImhGVWMyNlFVb3l0UDA2UXhzaGNaYUE9PSIsInZhbHVlIjoiTitrSWZRMUZzeDBMeHZtVTVlRHFuQm1sMkt3THZ0U0t2VnNYbnNoOFdCNW1wK29CTy9DSDJpMUNSRUl1UmdPVFc0MmVBVC9mU3NIOGtvelVWejRsU3VkOExpVkdmSitWWHZlRXJBcFNNMUFYV3o2ME5ud2Z6NE0xMjJwTnNUUVhHRDV3QktEUmkwYkdWZFpFRTJBZjQ2dDJOOHBwSjI5VE1mY1pLeE1aOW1vcXJvNG5NVU45SGxJanRTK1d4ZWZBNW5zZlQ1a3Fqby81aVIzczNDZC85Sm1FRnFuTUNhbkNWOVJlckoxQ1k1bm1paTFMdW1UVlZaWFlxbk1aYm9Xb29BNVVBRUtWVm9VZWpLRDE1dTkrWlZ4SG5DRTNhR0tLVXp5VE5ZcE1SYnJYb0s2eWpKNXIxQis2d24yRHYrNVgrWHcwN1g1QjBFUmhzd1VrbGFNYUNhRExGa2dwdmUwVE1UU1RVZHp3VEtsZjRsVUd3WW9LMnNnZXRaMTNMSC8zQlcrM2hoWXRaUEdHaGJGaUhMSkR4ckRYWVRyQ2E2YTl6clJUR0ZRTTM0WkR3MElsOEpUQTZJTEFibXhjc0lXTmdVSnVGQTU2ZnRXc2t5ZmJVMDZtODZmWmtmd1NTRWFvd3doUXlYZ3h2UUxxU21yQy9QYlJGLzNUVVg2NXJMVHAiLCJtYWMiOiI4ZWMxNTBkYWU4YmZmYjYwZDcxYjNiOWU2YjdiZjRkM2RiNWFlN2JkOWFlM2U0Y2YyOTYwOGE2YWM4NDY5MjM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-151449168\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-562643550 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-562643550\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2088893737 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:53:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZMQ3Y1SUp2NnNZdzZaMzF1bHltSmc9PSIsInZhbHVlIjoiZEF1NVMvdTJld1l2dytmUU80c0NnNVFwVEZqWnd4aE82Sjl0OVUyTi9ZVGlTS3FGdUxiczl1NjBqc1REekRmY2MyNjhhS2pFOCtMaEM0KzZNZURRYzNxbEwwYkR4cUdRSy8xa1hKYXBhclVwK3RPR1puRCtWc0s1YmdTU2FlTG13aVFvZUVqY2kxNWNDOG8wejVMSGVZRk81SWVoVUdod0U4ZG04ek13WFBHSFB3NlRuYkcvN0wvU1dDUUFWd1UvNnV0WXpFV09OT0VwWDJLa0w2OFQzZzVuUjNmMXVCOWdRaDZ4U1NPRXUxYjQ1eWFnU2RKN2IyMXRNaFNkWkZ5M05Jc3NodWxOWEc5c3ROcmdqYzFzSVQwcUlsZlRkYzA0bU91OUltNlpnTVN6QjYyTDByZ29QNE52bWE1T08wUDFiRUxjaSthdUJzd0FSR1ZMZE80VVZHZVBuUlZmVGhBYU0zOVRLcjVVeGxSUGtlN3JoOVNvQ29WU29ZTmFEVGtSWWNqVXRmUkNrMGMxVXNoT0x2TmJxVXZyRWpDTjMxalh5cTI3alJ3SXVsbzg0ZXVuMFJ3Ny9zeFFFVGFYa1l6bWdCcFF6Zzh3akpydUl0UGFYQ2tGTmZ2Nks0RjM2SWdkb05RYVcvOHBMQUZoVkhxUTN3aE5BQTVLUmZHYTRIcEoiLCJtYWMiOiI3YTM5NmNmODZkZDcxNWMyYzlkN2I4ODRlYzVjNWMzZTRhZDI4MTFiN2NlMzJiMWI4YTFmOTk1ZGI2MTJkYmVjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:53:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InF2WTg5dW9DUjJ4NFJJeDVvUG5XVUE9PSIsInZhbHVlIjoiaUV6U1E3dFhKZzQyY1pXZXJtb0RySkp2OGhzcW9YV0RDVTZ0N2R2Z2lCSXNvU01HbFYxOHYvVDY3dXd5OVFNMDdOVndkOUpLVFRLTUZyNGtTWWRMK1BNV3MzbFdMQ1c5UU9LWEo3V0FvV2ZoL2dpRklsWEhRRWNqV1RlMUJvZFdoUzZHL2VSVm11UFF2VXI3K0dmRm0wVGdQQkxqK1QxYzk0eEwzRHFzaDhCNkN1enQvSlBRdkM1cW03MytYOHJyWXNrYWVmcWxQNnJsL3JUSFhtVTZwbWR0ajFkSDFOZ043SkxDMXZxVnVnNjZoYjMxaGpWd09tUFVIc3FDL1pLV2ZEVk9Xd1hoZ3h5Q3grMzFhdTR3aFpBanQ0U25vTlh4R2ZOYkJSRzZma2JmODJRMDFFQ2ZJV3YxazNQbjRVVjhpVlNRbXRKbUdFRndoeVF6dmJZTUVWMC9xVlB2bGk3RURVSndEMlhiaTRHNTN4NnJEbFNzRTF0TU1sWnIwanpscHFMUnFuU3l5VkhGWTg5dDJoSnprbSsvRllCSXV1VjdDaXNrZERRYTFIT3VFUFhHcGRsNUZFNHdhSUVLM3FMZEpMdGdiZ1o0ZUszUE1JcDVGNjBRYk11V0JLaHhGdHl5R1RBRExEa2RBVEwwN2FGanZSeGt1QUI0aVJRSzJ0ZmUiLCJtYWMiOiI5OWU5MTNiMDZmYzliMWVlNWExMzMwZGYwMDAyYjU3NWRhOWE4YmY1ZTkwY2FjMDg3NTdjMTk1NTA5ZTQ0ODg3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:53:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZMQ3Y1SUp2NnNZdzZaMzF1bHltSmc9PSIsInZhbHVlIjoiZEF1NVMvdTJld1l2dytmUU80c0NnNVFwVEZqWnd4aE82Sjl0OVUyTi9ZVGlTS3FGdUxiczl1NjBqc1REekRmY2MyNjhhS2pFOCtMaEM0KzZNZURRYzNxbEwwYkR4cUdRSy8xa1hKYXBhclVwK3RPR1puRCtWc0s1YmdTU2FlTG13aVFvZUVqY2kxNWNDOG8wejVMSGVZRk81SWVoVUdod0U4ZG04ek13WFBHSFB3NlRuYkcvN0wvU1dDUUFWd1UvNnV0WXpFV09OT0VwWDJLa0w2OFQzZzVuUjNmMXVCOWdRaDZ4U1NPRXUxYjQ1eWFnU2RKN2IyMXRNaFNkWkZ5M05Jc3NodWxOWEc5c3ROcmdqYzFzSVQwcUlsZlRkYzA0bU91OUltNlpnTVN6QjYyTDByZ29QNE52bWE1T08wUDFiRUxjaSthdUJzd0FSR1ZMZE80VVZHZVBuUlZmVGhBYU0zOVRLcjVVeGxSUGtlN3JoOVNvQ29WU29ZTmFEVGtSWWNqVXRmUkNrMGMxVXNoT0x2TmJxVXZyRWpDTjMxalh5cTI3alJ3SXVsbzg0ZXVuMFJ3Ny9zeFFFVGFYa1l6bWdCcFF6Zzh3akpydUl0UGFYQ2tGTmZ2Nks0RjM2SWdkb05RYVcvOHBMQUZoVkhxUTN3aE5BQTVLUmZHYTRIcEoiLCJtYWMiOiI3YTM5NmNmODZkZDcxNWMyYzlkN2I4ODRlYzVjNWMzZTRhZDI4MTFiN2NlMzJiMWI4YTFmOTk1ZGI2MTJkYmVjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:53:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InF2WTg5dW9DUjJ4NFJJeDVvUG5XVUE9PSIsInZhbHVlIjoiaUV6U1E3dFhKZzQyY1pXZXJtb0RySkp2OGhzcW9YV0RDVTZ0N2R2Z2lCSXNvU01HbFYxOHYvVDY3dXd5OVFNMDdOVndkOUpLVFRLTUZyNGtTWWRMK1BNV3MzbFdMQ1c5UU9LWEo3V0FvV2ZoL2dpRklsWEhRRWNqV1RlMUJvZFdoUzZHL2VSVm11UFF2VXI3K0dmRm0wVGdQQkxqK1QxYzk0eEwzRHFzaDhCNkN1enQvSlBRdkM1cW03MytYOHJyWXNrYWVmcWxQNnJsL3JUSFhtVTZwbWR0ajFkSDFOZ043SkxDMXZxVnVnNjZoYjMxaGpWd09tUFVIc3FDL1pLV2ZEVk9Xd1hoZ3h5Q3grMzFhdTR3aFpBanQ0U25vTlh4R2ZOYkJSRzZma2JmODJRMDFFQ2ZJV3YxazNQbjRVVjhpVlNRbXRKbUdFRndoeVF6dmJZTUVWMC9xVlB2bGk3RURVSndEMlhiaTRHNTN4NnJEbFNzRTF0TU1sWnIwanpscHFMUnFuU3l5VkhGWTg5dDJoSnprbSsvRllCSXV1VjdDaXNrZERRYTFIT3VFUFhHcGRsNUZFNHdhSUVLM3FMZEpMdGdiZ1o0ZUszUE1JcDVGNjBRYk11V0JLaHhGdHl5R1RBRExEa2RBVEwwN2FGanZSeGt1QUI0aVJRSzJ0ZmUiLCJtYWMiOiI5OWU5MTNiMDZmYzliMWVlNWExMzMwZGYwMDAyYjU3NWRhOWE4YmY1ZTkwY2FjMDg3NTdjMTk1NTA5ZTQ0ODg3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:53:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2088893737\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1202672029 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1202672029\", {\"maxDepth\":0})</script>\n"}}