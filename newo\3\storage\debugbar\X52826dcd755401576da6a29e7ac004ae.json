{"__meta": {"id": "X52826dcd755401576da6a29e7ac004ae", "datetime": "2025-06-16 15:23:03", "utime": **********.595834, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087381.931833, "end": **********.595879, "duration": 1.664046049118042, "duration_str": "1.66s", "measures": [{"label": "Booting", "start": 1750087381.931833, "relative_start": 0, "end": **********.381757, "relative_end": **********.381757, "duration": 1.4499239921569824, "duration_str": "1.45s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.381788, "relative_start": 1.4499549865722656, "end": **********.595885, "relative_end": 5.9604644775390625e-06, "duration": 0.2140970230102539, "duration_str": "214ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45162552, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03166, "accumulated_duration_str": "31.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.479455, "duration": 0.02847, "duration_str": "28.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.924}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.542835, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.924, "width_percent": 3.822}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5678868, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.746, "width_percent": 6.254}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1046089667 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1046089667\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1806054530 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1806054530\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-972692093 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-972692093\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1512944158 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087369942%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJSK1lJME5oSEoyT0FJTThwZlh4b0E9PSIsInZhbHVlIjoiNE1XSEFsVTNpVnE4bms4YWZjTnJpWnUyc3g1d1IyN2NEdzNWVG05ekxzSUFacWRnKzB6YWZZb0N3cUxlY1pjTFFvTWJUWlZ3U2FnRkpWcnJQa1QrWFBVd2dyTmJUblVBZ1U2WFNzTlM0SnY5V2dFaE9TaUFHeHhjUTQxVFZqc1VRMmVDU2w2WVZmay9DVzN0RndnT0M4cldqUXdRUDBRUHE5VlFnVmZvYUxmQ0lHWDMzWVBEc0RBdDVLTlF5Ykh0SUUycmpqbERBZ295d1pwc0tkTWVIU1MrcTVxMmovRVN5ZVpQRkJzWlFlMzdEVndXVnJtZlZvaEdPUzAvMTdmejhSSU5jMm5veFo2c2E0L1dpQjNtN2lwUTRpcmk5SW1qMGx6aWsweEtLWDYzdXpqVWJTMHQ1RDR1Zm5Nb3VGYWp1bzNvWlUyVkNETHpCb3BGUTl6NzB0S0I0TUduVzVyTDBpMUJZcUFUekhqWjZhWGQ2OUIvTnp2byt6ODJUUERwcER5amZPQ29hQUxWcUlKQy9KcVZwRWpxQThmUkozZndNVmlvS2h1Nk5TUGdrSWdnbmRtVjc2aDZZU1o4ZjNncjhDZHVkeE9oNXNtQ0FmSjdFWk9uMzdHcENjQlNEY2djd2ZpNlZPUG5iVytKSUU0VHUrMFBDa21PR3o2emV4c3giLCJtYWMiOiJmNmNlOGIxZDhhYjVmZGExODcwZDg3NWM2ZDEwODNmZTA0MzRhYjBhNTQ3ZjYwMDE4Y2FlZjZhY2IwZDZjY2Q2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjNEajFzclVJSFpkTm9FM0JvRWN4WFE9PSIsInZhbHVlIjoib1RYQm95WC9FbUtqU2xDb0owdjhVOFBaelhibEJqKzNnU1p5T1JDYlprbXFjNEpaeU1lbWxPWlpUUFpGZzhTdlpsbVBHNGxWWWowbEg3OWJ5S0FSU3pZR1pyakk1WWF4dkJraTQ5a0dBb0VPY2FHUmI1MmE1OEgwMHlsNWtnK1duUUozQk1CQ1VRRVA1aHhMbllnYWhwU0EybDFhMW15Ym1PZUsrbGhhdGRzMFQrZm5DMnA4aFdBekUrZVpxWTVBZS9FTG81cnh5N0dYMHFFY3Y2d2xVYXRINEJibDdaZGU3Z2FPVkJYdjBPaUVlYUZhQWwvK3Z6QWtFZmJueUhZeEZHN2laTHBqZnU3RmRDRHdYUUtqcXNZazZjUHZGa2lLSStkdHoxYkdKT0lWZDBkcll6RHdHQnRFL09jKzJEa3dsV0t5QncxemthenZCYmMrQkxPNE5iYTdZeU9UOGhMK3IvdS9NOXEwV0RJa3JDZFhwMlp0dW5DMGd4elVnREsra2l4SmFDMFUrb0pxREJCdmM2aCt0K21JT3BhaWV0OGdYckFDS2hCMUxGOWpLeXpkaUlnbTVxVHpEQ21mYXBNQ3BmVStVVzVnQ2JMOEtjSVlQZ29iNkIxUGR5WHFibHB2dHY5c0R0RktWZEYxdDNtZHU0RnVsZ1h1M04vei9YQUsiLCJtYWMiOiJhNzg4ZWEzMzRhZDNkNDdhZWMxZTk5YTM0ZDY1OTMzMjM5ZDE0YWI0OTQ2MWI2YzMwYzk2NmNkZDc4YjFkYzc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1512944158\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-127232 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzcvmOKIgVMCCvEHD9HLB5trqF9yJ9GcIs8Pndo9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-127232\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1014839388 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:23:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImMzS0l6aCtDTXlybmp5K1hkcGsvQVE9PSIsInZhbHVlIjoiMXBHYm5HUDRmcW9wRmxWMTgyYTBhbm96WVdpOFg2QkppVDI4RjY5Y21jekd2WkVIdWRJR1hzaDlmdFVlTE9WN05OYVlGWFNONkNHVG9XWDJUZy80VENsaGNycG84TlhvVXV0N01OTFZjeklCVmk4cUd1TkxIMGxRR01kdFh3SHU4bzFPTjRHSnlzVGJpZjdqVkJybFlPc3VVVTlEeXB0bisvREV2Ukxvc2RNRzhzc2hZdFU0aHgraGJXQUhKNXA4SmZSdUtaNGZxYTFUd3lGOUhXc1Avd09taHk5ZVNhL3ZVeVJJams4UDl0LytWYzdKOTBzdUs2Nmh6eFZsVnFTQ1IzZ2Jjem9SUkFiQ3hlVnp4ZkRuSWE1VSt3MlI3czNFeXlBOHFobDNscEt5RzNDcnZXQ2RibGNqeVVpdTdCeXI5UVVPTFRMaFgvSTJnY2NXMFk4SUxSNWVybzc1OGNvZUZ5YkthSDhTQ2tQRkhtdlEvYVBmaW5GdmppUEgxRXZFOWJpSjhOYzdSVVFUNVQyMUxVd0ordG5HMGo2bE1tTHVBRFNYT3BzbEh5a3VhemVEVFh6NlpiK3BiQ1ZHSTJZOENnS1FtQUI2dUZNTjA3bTFUNUdjL3dtSlJhZEwzS016a0xDYnk1YlVwODIzeHNVdEtLT0M5WGRaUkQrNk0zaW8iLCJtYWMiOiIyOWUzNGUwYWJkOTZhMDhhNjBmNDI2NjNkMzlkZjMyMTFkZmNjZjdjNjVmYWFjNDI5ZTFmNDk4MmUzYTEyNzE3IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImowUS9JSmtIQVFDUXNIcXhNOGYxZ0E9PSIsInZhbHVlIjoicUNqSVlqQ3ZVbVR5cUNhMHZzUTlReVUwSi9pOFlxalh4R1M4Rm9ndEVTZkZWbGFOTXBudDAvcU9TMVFtZXptUG90MjFCbTZsWS9mS255b2ttT0lFMU5QbmllYzZhYkVDeGRIYzd2RWJPRG80R1BnNG1GdER4cXRpUkFHclZod0FmcDJad3N4TjBxVXNrMVFMNTVZNWlqOE5LeGVsVUJrc2loQlRTdHd4dWZBaG1PZnB2MUNhWlkrWW9RV3lqVFp2NVQ3cEduNUM5Z28rTUdXOUhCOXpReHJwbmU5NDVrNmdYNG12M251dzhWcEcwUEV1Q29lc3lObzNTcDRPT0hZUWxFcitLdithWEZSWW9WanMzeTYrdVc0a0NuWm9lRmRiTjlvLzhSdXBucDVWODhtZU9TVU95eFB5V3JTcHhnRGZDSjRQdnJUMU11eFhYWnhnU0txRHZDeGJzcHdlcTEwNGVXRGVZdTFlV0FhZ01XZ2hiTXJsYnB0YXJ4aUYwUlhmTEdCWnpUVExzeTRuVEhqRzVCOFpGOHV5QzZTekovYzZuYlB1TDZYMlIxeVYrY0NZZjFpQ2xCTGxyYTNaQy92V3grU2lnQ1B5N21Ia3I0VFhlZm5jZmQ4dzFsRjBKMHJtWW5Uc1A1TXdOUVVvNkd2Sm1TVkJzblRNUFArMGEwTG0iLCJtYWMiOiJmNjNkNmRlYjk1OTg5MzU5ODA5NGU2M2JmMGI5YTAyN2FiNjZlZTQzMTQyNWVjOTY2NzA2MmNlYWVhNTEzMGU4IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImMzS0l6aCtDTXlybmp5K1hkcGsvQVE9PSIsInZhbHVlIjoiMXBHYm5HUDRmcW9wRmxWMTgyYTBhbm96WVdpOFg2QkppVDI4RjY5Y21jekd2WkVIdWRJR1hzaDlmdFVlTE9WN05OYVlGWFNONkNHVG9XWDJUZy80VENsaGNycG84TlhvVXV0N01OTFZjeklCVmk4cUd1TkxIMGxRR01kdFh3SHU4bzFPTjRHSnlzVGJpZjdqVkJybFlPc3VVVTlEeXB0bisvREV2Ukxvc2RNRzhzc2hZdFU0aHgraGJXQUhKNXA4SmZSdUtaNGZxYTFUd3lGOUhXc1Avd09taHk5ZVNhL3ZVeVJJams4UDl0LytWYzdKOTBzdUs2Nmh6eFZsVnFTQ1IzZ2Jjem9SUkFiQ3hlVnp4ZkRuSWE1VSt3MlI3czNFeXlBOHFobDNscEt5RzNDcnZXQ2RibGNqeVVpdTdCeXI5UVVPTFRMaFgvSTJnY2NXMFk4SUxSNWVybzc1OGNvZUZ5YkthSDhTQ2tQRkhtdlEvYVBmaW5GdmppUEgxRXZFOWJpSjhOYzdSVVFUNVQyMUxVd0ordG5HMGo2bE1tTHVBRFNYT3BzbEh5a3VhemVEVFh6NlpiK3BiQ1ZHSTJZOENnS1FtQUI2dUZNTjA3bTFUNUdjL3dtSlJhZEwzS016a0xDYnk1YlVwODIzeHNVdEtLT0M5WGRaUkQrNk0zaW8iLCJtYWMiOiIyOWUzNGUwYWJkOTZhMDhhNjBmNDI2NjNkMzlkZjMyMTFkZmNjZjdjNjVmYWFjNDI5ZTFmNDk4MmUzYTEyNzE3IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImowUS9JSmtIQVFDUXNIcXhNOGYxZ0E9PSIsInZhbHVlIjoicUNqSVlqQ3ZVbVR5cUNhMHZzUTlReVUwSi9pOFlxalh4R1M4Rm9ndEVTZkZWbGFOTXBudDAvcU9TMVFtZXptUG90MjFCbTZsWS9mS255b2ttT0lFMU5QbmllYzZhYkVDeGRIYzd2RWJPRG80R1BnNG1GdER4cXRpUkFHclZod0FmcDJad3N4TjBxVXNrMVFMNTVZNWlqOE5LeGVsVUJrc2loQlRTdHd4dWZBaG1PZnB2MUNhWlkrWW9RV3lqVFp2NVQ3cEduNUM5Z28rTUdXOUhCOXpReHJwbmU5NDVrNmdYNG12M251dzhWcEcwUEV1Q29lc3lObzNTcDRPT0hZUWxFcitLdithWEZSWW9WanMzeTYrdVc0a0NuWm9lRmRiTjlvLzhSdXBucDVWODhtZU9TVU95eFB5V3JTcHhnRGZDSjRQdnJUMU11eFhYWnhnU0txRHZDeGJzcHdlcTEwNGVXRGVZdTFlV0FhZ01XZ2hiTXJsYnB0YXJ4aUYwUlhmTEdCWnpUVExzeTRuVEhqRzVCOFpGOHV5QzZTekovYzZuYlB1TDZYMlIxeVYrY0NZZjFpQ2xCTGxyYTNaQy92V3grU2lnQ1B5N21Ia3I0VFhlZm5jZmQ4dzFsRjBKMHJtWW5Uc1A1TXdOUVVvNkd2Sm1TVkJzblRNUFArMGEwTG0iLCJtYWMiOiJmNjNkNmRlYjk1OTg5MzU5ODA5NGU2M2JmMGI5YTAyN2FiNjZlZTQzMTQyNWVjOTY2NzA2MmNlYWVhNTEzMGU4IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014839388\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-806506697 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-806506697\", {\"maxDepth\":0})</script>\n"}}