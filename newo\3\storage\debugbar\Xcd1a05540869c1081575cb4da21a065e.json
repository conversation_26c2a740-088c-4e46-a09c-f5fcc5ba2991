{"__meta": {"id": "Xcd1a05540869c1081575cb4da21a065e", "datetime": "2025-06-17 05:40:38", "utime": **********.481226, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750138836.812011, "end": **********.481273, "duration": 1.6692619323730469, "duration_str": "1.67s", "measures": [{"label": "Booting", "start": 1750138836.812011, "relative_start": 0, "end": **********.166969, "relative_end": **********.166969, "duration": 1.3549580574035645, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.167002, "relative_start": 1.3549909591674805, "end": **********.481279, "relative_end": 5.9604644775390625e-06, "duration": 0.31427693367004395, "duration_str": "314ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45216808, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.034710000000000005, "accumulated_duration_str": "34.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.285779, "duration": 0.028820000000000002, "duration_str": "28.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.031}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.34644, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.031, "width_percent": 4.408}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4213212, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 87.439, "width_percent": 7.375}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4489849, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.814, "width_percent": 5.186}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-59654782 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-59654782\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-650605302 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-650605302\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1271836030 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1271836030\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2006128517 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750138821284%7C4%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNFYVVMaUd4Y0xURmVoakNDVkVWaFE9PSIsInZhbHVlIjoibGFIM3BwZVkrRHVtSEJwNXR2THJWeGRFNUY1aUxFQ21vK1R0L2RtSHdTWFhqM01peTh5RHZBaTJkZ0o4TGJUeCtSNUhNZjdTSlBVSDU1Z1BRTGZpWFh5dVIzeVVPSmZtbm1FQm5PVEk4REZ0ck9FNm52UmhXYmwyNWlGTlZWL2t5Vyt6RytTb044UENxU0dzQ0pOYSs1OE44amJQRHpuOHAwelNhT3B0RTNJM21oRkFYY1RTdW8xNUdCN1VPaWNMTS8wdWduWVdkNEgzYm9UZDRYZHkzL0I4bjYwdksvV3IydmI1Y3BrVUtSbG4wUTZtM2hSM0Y3Z1hHY0tNNW5JVEdUaUk4YjhjZlI1aVplM0l6NWJkTDBTemQvNjllZHAvbTFjU2czd28wYzY5RmlRbHNDQUtzWDJkcktuMXJvaVphcytkNHBqd25jd1czTXpEZk1rdnFSKzU3Wlh6SWtOQ09xYVRzRXZZYVk0cHp3Rm15R1Nub0k3a3hNMG51UHIvQmwyWmZnc1J1QlcwbzcxRjQreTVEU3ZRVmRtVXR0SmFHRGZUdDBoU2p0RDdDeFN3aHpjNFMwQUYyQkxxY0lIaTNEUkVrUmhHRU5obHlhajEwV3FOcU05NmUwMHl6MGs3RWlzRFBrRGYzUFpRb2tXRzR3eGdvSDAyY0pCelZWaXIiLCJtYWMiOiI3MWM0YmEwMzM4NWY5Njg0NDA0NmQ5MjNkM2VmZmRkZjU5NzIxYjU5ZDFiOTc0NDgxODE2ZjhiMWE2YjI5YWY0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ii9jZjA5U3ZqWHp2Q1NpS0FJQktObFE9PSIsInZhbHVlIjoibFBET1g1ZzBSRDZ2WEdlVDhMV3RPUDVZVk4wL04zZjRvbkZJL2VSR3FBbjdTZDVZK2VHS1dWZkF5djNZL0drd29NZy95cnpUa1pjQTNvZFBuWkZKL0szcExFWjQvdHFaQjVWYzM3N21zTEFxZGpKdFFMdTJDOHl6aTVVZENya3ZjVDVkZjBrYkU4QlBGSlNSY1Yrb0tzZFF4bGVneUtaTWtVMTdKUjFCSVdTZ0g3U0R4Y3pJNklWQmVtbWxHSjRXMHJ0Nkw0U2QyRlRqOStoamx0eEZpcElBQnFLQldUQjQ5Qk1XcUExOVg4UVE2cm1NZW5PcGdOaHdJczE4c3BJOFVWbERsYWdka09nZGZxS1gxYjhXNThuUklCaHYxc0xIV1ZLSVY1bmZoajZMalBES1pNYmZBY0F6YzNtTkpsazhOWXBBUXVnek9EbWpteWlrbjE0Q0VqdHJocG0zOU9CM0xTWm5CZFMyb0JrU1NPZzdEVk9iNWRyZ014MWlqZDFNdmFod05kNmNJc1dkM1ZpZnc2OStJQ2lzVXEyV2wxK2JTRFp5aGlpYnY3djlGN0Zuc3pKWWcrbUl5YnV1Y3YyOElvYmxjM0VyY0lyOHZoVi9sVUVwRzkrNDU1ZG5FcTdjRGdCMHFxRWozMncySFBRRS9rOVdrOFRWMkF5R0IvbDkiLCJtYWMiOiI3NDE4YTM0ZDVjNjMxM2ZiMjYyMmMwYjE3ZDJhNTAyOTMzNmY4ZjhiM2NhM2I5NmRhYTBiOWY1OGRlODNmM2U5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2006128517\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1731996517 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MsEpVpyAabX9l6kTbr1IUggTy4SSXp1me81GakZC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1731996517\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:40:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkIvWStwTm1weUEyTC92aTcvVFZSUHc9PSIsInZhbHVlIjoiZ2ZXbWgzU1R3d2kxbVpFVUtuL3BkTitQMlNvM1JBQ0xJcXF4YUVRZStvUHlmdi8rQm1WenBNeWR0QkZSeEtXZnZBU3FDWkxrZmRtQXkwQ0JXcWdqT2RtV21HQWZFbFBENC9MOEd1NHArQ1MvWCs2cjVoTm5vRUxBMjkxeVBHaWRxaXBjN2FQdU9CZjNNU2RCNmk4VEN2aWRZZVRkUXBtYU1zZ1ZwUFlxQ2JPQzlRNm5DSEJ3QnNzYTNrVW1WcnA0QUtyNURoYW1Gd2l0UnNuV2I5MnBjdUxXeXd0TW1LZ0svTGFSS2EvRUZxWnkvWUFQTWFYYmo0MkFGblBXRUs1alFxN3RrVG02MDgxWUJsWi9sVEROUWZQRzFrbi94TFhTQ29PbGtqSElYUUxlclU3QjRLYmdXRXhyOGEvVkZBaG9ndk9iY082WUwwbWN0WVF1N2JodlUzMGl6Mmk2ejFKb2NuZmozNE5IVFpKbUsyaEszeDNCejhBM2tURDE0amxtK2FqNityNTVxNjFDVFpGWWhQMGx2OUZlQUpzb0FOdm5MYllISldlMHo5MEN6UEtDeGNVbXp6NWNpVHJnYzloZDhVNWpwUEVJLytJWURnQStkdk9ISUNKZVMzc0lQSXp6ZzIvQVVyTjdqdDZWcjZEdE5VVkYxSnFEOE9WbkNpQkwiLCJtYWMiOiJhNzRhZTQ4OGJmOWZjMTYwMzAwZGRhOWZkYzVjYmYzMTljYjI3MDlhNzYwZTkzZjM4MTNhMTAxY2MwOGM3ZGQyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjdYZVhhc1dBb3N6bGJaM3NzLzhtZVE9PSIsInZhbHVlIjoiWFNFVDd2anU2d1ZSM29tcTRPbkh1MUVjUnh3anhraXlrTEhrWk5ja3VBMHFTWDNCZHI3RWZQL0RHa214NmExckFTVWJEaXkrLzVsZmxYNHFiaTZOZmZYTUVabWxURVJFSTFnaU9YNDdiUGdNaWFLREVLd1BrdE11T2ltcHlFclJTeVNKQnczVnJBalRpTVlJSjRyZ1J1TmQrYkdMSGZoV3NnY2RkRS9sUUxBb29uZjlaaUxZOVNnakxLT1R4ZU95dWxra0dlRUQrSTV5djBURjNwMFlpQksrOHIwUmFVdmVBRGwzc0FuMHQ3VnVzYWc4eEpjVFI1K2k2cStKUmEvdE5VT25CTGhZTE16TE9tSmdGdFJ6eVBDblFubFc5bHdFOFZhMGlldURDZUIxL0dMRm9PdmI2Tkl6dmsvQ1hOR0xKUU04SWRPbXROdTFocTdTY3M2ZUVsTHRjZnZwVlRibUVBck9TbFZMWmZINjl0a2ZaeEdGSDVoZThNK3ZVcG9Xd01CL3l1a0ZHdVlCaHRabFhUZVNhZ0lJNkRRTTFEc0xmWll1S2Jab2dKdzN0bFBoT29US2x2THNURGhwYmUzZXJIdHFWbG9FaXVPNSttQTB2dDZKNWVsSmlsRlloV1NPeVpvOXU4b0tEN1U1dnc4VXRNTDhTRnJtSUxDTURXZmgiLCJtYWMiOiI2YmM2MWUxYTNhNzM1NDBiZjM1OGI0MDYzZTU2Zjc5Y2QzZmIxYjk4ZmNkODFhZmJkNWFkNzJjM2VlMjEwMGFhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkIvWStwTm1weUEyTC92aTcvVFZSUHc9PSIsInZhbHVlIjoiZ2ZXbWgzU1R3d2kxbVpFVUtuL3BkTitQMlNvM1JBQ0xJcXF4YUVRZStvUHlmdi8rQm1WenBNeWR0QkZSeEtXZnZBU3FDWkxrZmRtQXkwQ0JXcWdqT2RtV21HQWZFbFBENC9MOEd1NHArQ1MvWCs2cjVoTm5vRUxBMjkxeVBHaWRxaXBjN2FQdU9CZjNNU2RCNmk4VEN2aWRZZVRkUXBtYU1zZ1ZwUFlxQ2JPQzlRNm5DSEJ3QnNzYTNrVW1WcnA0QUtyNURoYW1Gd2l0UnNuV2I5MnBjdUxXeXd0TW1LZ0svTGFSS2EvRUZxWnkvWUFQTWFYYmo0MkFGblBXRUs1alFxN3RrVG02MDgxWUJsWi9sVEROUWZQRzFrbi94TFhTQ29PbGtqSElYUUxlclU3QjRLYmdXRXhyOGEvVkZBaG9ndk9iY082WUwwbWN0WVF1N2JodlUzMGl6Mmk2ejFKb2NuZmozNE5IVFpKbUsyaEszeDNCejhBM2tURDE0amxtK2FqNityNTVxNjFDVFpGWWhQMGx2OUZlQUpzb0FOdm5MYllISldlMHo5MEN6UEtDeGNVbXp6NWNpVHJnYzloZDhVNWpwUEVJLytJWURnQStkdk9ISUNKZVMzc0lQSXp6ZzIvQVVyTjdqdDZWcjZEdE5VVkYxSnFEOE9WbkNpQkwiLCJtYWMiOiJhNzRhZTQ4OGJmOWZjMTYwMzAwZGRhOWZkYzVjYmYzMTljYjI3MDlhNzYwZTkzZjM4MTNhMTAxY2MwOGM3ZGQyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjdYZVhhc1dBb3N6bGJaM3NzLzhtZVE9PSIsInZhbHVlIjoiWFNFVDd2anU2d1ZSM29tcTRPbkh1MUVjUnh3anhraXlrTEhrWk5ja3VBMHFTWDNCZHI3RWZQL0RHa214NmExckFTVWJEaXkrLzVsZmxYNHFiaTZOZmZYTUVabWxURVJFSTFnaU9YNDdiUGdNaWFLREVLd1BrdE11T2ltcHlFclJTeVNKQnczVnJBalRpTVlJSjRyZ1J1TmQrYkdMSGZoV3NnY2RkRS9sUUxBb29uZjlaaUxZOVNnakxLT1R4ZU95dWxra0dlRUQrSTV5djBURjNwMFlpQksrOHIwUmFVdmVBRGwzc0FuMHQ3VnVzYWc4eEpjVFI1K2k2cStKUmEvdE5VT25CTGhZTE16TE9tSmdGdFJ6eVBDblFubFc5bHdFOFZhMGlldURDZUIxL0dMRm9PdmI2Tkl6dmsvQ1hOR0xKUU04SWRPbXROdTFocTdTY3M2ZUVsTHRjZnZwVlRibUVBck9TbFZMWmZINjl0a2ZaeEdGSDVoZThNK3ZVcG9Xd01CL3l1a0ZHdVlCaHRabFhUZVNhZ0lJNkRRTTFEc0xmWll1S2Jab2dKdzN0bFBoT29US2x2THNURGhwYmUzZXJIdHFWbG9FaXVPNSttQTB2dDZKNWVsSmlsRlloV1NPeVpvOXU4b0tEN1U1dnc4VXRNTDhTRnJtSUxDTURXZmgiLCJtYWMiOiI2YmM2MWUxYTNhNzM1NDBiZjM1OGI0MDYzZTU2Zjc5Y2QzZmIxYjk4ZmNkODFhZmJkNWFkNzJjM2VlMjEwMGFhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1356857995 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1356857995\", {\"maxDepth\":0})</script>\n"}}