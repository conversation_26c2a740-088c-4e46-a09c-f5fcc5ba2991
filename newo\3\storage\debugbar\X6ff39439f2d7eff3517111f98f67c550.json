{"__meta": {"id": "X6ff39439f2d7eff3517111f98f67c550", "datetime": "2025-06-17 06:55:00", "utime": **********.949062, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143299.155351, "end": **********.949101, "duration": 1.7937500476837158, "duration_str": "1.79s", "measures": [{"label": "Booting", "start": 1750143299.155351, "relative_start": 0, "end": **********.699042, "relative_end": **********.699042, "duration": 1.5436911582946777, "duration_str": "1.54s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.699063, "relative_start": 1.5437121391296387, "end": **********.949106, "relative_end": 5.0067901611328125e-06, "duration": 0.2500429153442383, "duration_str": "250ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45156128, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.026890000000000004, "accumulated_duration_str": "26.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8255382, "duration": 0.0233, "duration_str": "23.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.649}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.889035, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.649, "width_percent": 5.615}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.913902, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.265, "width_percent": 7.735}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-705424327 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-705424327\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1289458744 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1289458744\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-328651530 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-328651530\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1923541892 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=1rkbmmi%7C1750143267722%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IngrV2VnWEFaMlozeE93dVNJY0ZRMnc9PSIsInZhbHVlIjoiUXJDSmttOXVXK0FYYzgxTks3R3hFYi9wUmVpQXh5RUFUNVlZckJodVl5L0FtWWl3MUZMM0k1Z1hvSHpXNmswUFYrQWRIc1RuZzBLMjZQTUFvT014dVlBYnJ0MDExbjNQQU9idmxJbjNacCsrZzJlYTIzVVI3VjIweFI4VWlTZzIxbWVHc2l4UWY3KzlKajZKdEd0WFNJRjBFblozejVtTFVjTDZVTFhKOUgzR0lSOFRZS045VEYxbktES0RLWEtFM2FXbEplMzlJKzhWM0F0L0hwVTQ0OGUvOEFEMDZnQ3hPNFhMcmNIZFp6clhMYWRzL0p5YWxvUWZvb0xTMWM0ZE5DQWNDQUlNVDlvRnQwMjFIUWZCendRMjRqb3VpdTJmTzUyaXZzWDYyVGhRb2VOcXNUYWtCd2hMb1FmUzRIV1kzR3MvbEdHRkROcnRxQm12MkpSbmVrYlVtU0k3ZnRkQzBmRW4rdjU0QkZEZ0xVWVBFTldaY0JJbVY4U28wWjd3RU1FSENSaS9tWmp1aDVuOHNrcmNadXFPMEdRRWRYRlc0MTUyNHNXWHV1bWZ2d3ZENDEzMlg1WmZzSS9KOUNYSXlINHhOUllkL3hXcG9TOStqWVBrQVl6Nk1sMDd1QUk2YjFwNWhvQkxqZFdERGR4T3VSSDhJR2VuR2EvSTZPOXoiLCJtYWMiOiI0YWJlMGIxMzMxOTlmMjg3NTliNjY0YjEzMDEyYjFkMzBhZGNhYmJiODQ2ODI1ODU0Y2RkOWZlYWY2MGU3NjkxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjdOUkl5RDdpUmZjYVU0TnVjaGlQbUE9PSIsInZhbHVlIjoiZmpEaExZa0pBdFBOdzRKNit5dW5wZkVVY1dwOWlnVktZV1gxTUxvT1crN3AwelBLSCsxOTJiY1Q0a1lURkw3bkRjTVI0UFdqaHNFazB5L1JoUStNdHk0eThkek5WbDdWNFNmWkE3UEw5amtFTThmaVVRWW1ZME9Ob2hZRXJMalBuNGJxU1VsYi9DNlFvSWZvZ1g3NUtxbG83WmtyODlRbFAyTDdWV1VRbGV5blpsaWpPZlNEQWxhTU91UVIvaXB2TWJuc3NWU3F2citHQzNDVTZRWUhoVjl5U3o1TTZLd1BQSXFaQ25SRHlTai9QTmJ0bkNqa3AvQzFESVM4V2NsaFlmd1dnUkc5dE15Tk1YUGtla01ZaitpK0Vlem5lRDhpamtGdkpkbU5HR0JRTVVSYXhkTWwyUDJmR2o1TVJMbjVyL2FVbVhjTlpQY3lGeGhQNEFkMkJwRkFUeG03TXBFancrYmNHOW45R1BPZzFPbzRHcGplMVBRaFNqM3NEZnhUN0hHTTBKU3I3MXQ5N1RrenJrZkJoTVpGaWNTMTJyUE5jYXVoaDBsTmVaTithWDJqUUR4VjdVSklFYksyeURTb1UyMHZHbWwvUmFNTkZ6VldRb3EvckhOSEhjejE0Ykt0REZxYVhFS054b21lM0ZleWJOaW5iNUZ4MXVvMW9JcTQiLCJtYWMiOiJiMWNiNmJiYmQxODcwNTM4YzRmYjNjYTI1NmNiYzgxYmI0MzNiMTIzYzgzNjQ5Zjk1YTc0OGRkOWQ4YWE5ZmZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1923541892\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1385510562 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDWN9h6R9gAIdzZaXBzXfq0WGpauVfrUj70kKWA2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1385510562\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-311749183 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:55:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFxSGRQSlpidHVxWWoySWRycjRyY0E9PSIsInZhbHVlIjoidEtQNElYZXVETmlsWGdHbGY5dUNoNDhoeTlLNmo5a1Viem1nUWNpVnZoVkhyWDRMY0YwUVpUYURMaW83TStKMkRIdjdwWFptc3oxWDVhZ0xaM1dhaEJQOEF0TjMzTmVBRHU0Mk9YYWlWNlRhK3FqcFJtZWk4bXNUT0sxanZiOC9jLzFzN1dmQlRDMUdNSUhkaVY1eDhUY0UvYUdONHcwMXJIMkVLZVRBT2ZPNWxuMWk5ZElTT0dmM1h1VFZhOHk3MzVROWZjWjc3ZEc5ZDNxSEJSUUZaWFRhOVBhUE1VT2k3WktCek9hLy9TOWYvak9HWU80Vy8yR2Z2T0Z3Nm0zRXd6TkN3OTV2TzhRa1hSV3dFUXMvbzF3V0FJaldwQllldTFUUStrclNmSW5jaUZLazA5ZjVIaCtoTGJGQzRVOGdHSUk4UDVya2lqc3lxSjcrMFdMS3J0U0hZV3E3STRNcWpDK2tUUzNGZlNmalo2SHJrQ3dhd1RqVXVGeU5xWjJrUlh1dU1LcUx2U0p3OFFldUU3bGV6U3FrdGZiSVZqeTBRbndrWEFSS2VJMWJ3NE5hVlRmMW1JczdSYmpwa1ZST1J6NG1OQi8zc05XMDlUYUJFd3V6UEhqbnZrOS93UE1yUnlhd1dPTkpMY3RDY01ac2VST1p1OGNNdC8walZ2MlUiLCJtYWMiOiI3ZGQzZjc1MWJmNmMzMDk4ZjRmODJiNzlkYmEzY2Q3NmYzZDdjMDNmNWFmZmI5ZjAzNjFmMzVlNjA3NjhlNDllIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:55:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InkyeXFlaXhMSitaMmVaSHZhM2hDQ2c9PSIsInZhbHVlIjoiRFlwM3NYQzAzY1N5VEduMFMreWlpUDZIQ2FoVjF0Q21nbXlJU1NZR0NaaVNMZk0wTXZDV2NHNTE4Y3BNem9PSGR4M1VqUGUzV0JOUzlWbHlrWk5hSDhCZGxHWndtakVjT0J1dnhvbm96cFd6YWxLQm5ZRWhtaTZuQS9LRDE4c0R2RE1LSXMrRFVvR21hbENLckhQTHRLbGNUbWxCSjlKaWNjaGJHcmZrVWJkZUEwN3dYMXpVVUIxNkN1am9iNzJUUEpDMk1MV01HaDNQdjkyS2pJd1drYkVhT2FkZFdMS0UyVUVFZnhxQ0d1SXZ3ZHNBZjcxeGNpZnBBQ2Nza2k2N0E3dmR2bkZVY09OU3BOdmRTNDFielNpY24zUTc2U29QQXhnRk10VWx1NGRDcDdXbW1TcTNtTVRnek1yQ2hUSWwydkFoTjdpYU02S0F6S2JFYjIxME1QZjBWODJUdE8rcHFwdHhYaWV0RUNhcFg4eXJOeWswTFZycGRxSWZSb0hkbFFjLzQzNGtxTFI1dUZsL2JFdTFDZkU2TlRqSlBoNGh5OU1iVk9mTzVUVW1scmJlalJYKy9BUDlRQ2FUZmlUcE5DNHdoM1cxVFdubkxtWUt4QzdZaGxGdTU1aTI0TFlkNC9WSXdScnZsOEV5K3d3RWtjdnBzMEY5TTNSbmpkTDMiLCJtYWMiOiJiOTZhNDhlYzVhYTBkMTY3Y2Q0NDc1NGJlYWRiYWY4YmNmMGMyODY5NDNlZTNiYjBmYmRjYzFmZWUyMjlhZThlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:55:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFxSGRQSlpidHVxWWoySWRycjRyY0E9PSIsInZhbHVlIjoidEtQNElYZXVETmlsWGdHbGY5dUNoNDhoeTlLNmo5a1Viem1nUWNpVnZoVkhyWDRMY0YwUVpUYURMaW83TStKMkRIdjdwWFptc3oxWDVhZ0xaM1dhaEJQOEF0TjMzTmVBRHU0Mk9YYWlWNlRhK3FqcFJtZWk4bXNUT0sxanZiOC9jLzFzN1dmQlRDMUdNSUhkaVY1eDhUY0UvYUdONHcwMXJIMkVLZVRBT2ZPNWxuMWk5ZElTT0dmM1h1VFZhOHk3MzVROWZjWjc3ZEc5ZDNxSEJSUUZaWFRhOVBhUE1VT2k3WktCek9hLy9TOWYvak9HWU80Vy8yR2Z2T0Z3Nm0zRXd6TkN3OTV2TzhRa1hSV3dFUXMvbzF3V0FJaldwQllldTFUUStrclNmSW5jaUZLazA5ZjVIaCtoTGJGQzRVOGdHSUk4UDVya2lqc3lxSjcrMFdMS3J0U0hZV3E3STRNcWpDK2tUUzNGZlNmalo2SHJrQ3dhd1RqVXVGeU5xWjJrUlh1dU1LcUx2U0p3OFFldUU3bGV6U3FrdGZiSVZqeTBRbndrWEFSS2VJMWJ3NE5hVlRmMW1JczdSYmpwa1ZST1J6NG1OQi8zc05XMDlUYUJFd3V6UEhqbnZrOS93UE1yUnlhd1dPTkpMY3RDY01ac2VST1p1OGNNdC8walZ2MlUiLCJtYWMiOiI3ZGQzZjc1MWJmNmMzMDk4ZjRmODJiNzlkYmEzY2Q3NmYzZDdjMDNmNWFmZmI5ZjAzNjFmMzVlNjA3NjhlNDllIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:55:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InkyeXFlaXhMSitaMmVaSHZhM2hDQ2c9PSIsInZhbHVlIjoiRFlwM3NYQzAzY1N5VEduMFMreWlpUDZIQ2FoVjF0Q21nbXlJU1NZR0NaaVNMZk0wTXZDV2NHNTE4Y3BNem9PSGR4M1VqUGUzV0JOUzlWbHlrWk5hSDhCZGxHWndtakVjT0J1dnhvbm96cFd6YWxLQm5ZRWhtaTZuQS9LRDE4c0R2RE1LSXMrRFVvR21hbENLckhQTHRLbGNUbWxCSjlKaWNjaGJHcmZrVWJkZUEwN3dYMXpVVUIxNkN1am9iNzJUUEpDMk1MV01HaDNQdjkyS2pJd1drYkVhT2FkZFdMS0UyVUVFZnhxQ0d1SXZ3ZHNBZjcxeGNpZnBBQ2Nza2k2N0E3dmR2bkZVY09OU3BOdmRTNDFielNpY24zUTc2U29QQXhnRk10VWx1NGRDcDdXbW1TcTNtTVRnek1yQ2hUSWwydkFoTjdpYU02S0F6S2JFYjIxME1QZjBWODJUdE8rcHFwdHhYaWV0RUNhcFg4eXJOeWswTFZycGRxSWZSb0hkbFFjLzQzNGtxTFI1dUZsL2JFdTFDZkU2TlRqSlBoNGh5OU1iVk9mTzVUVW1scmJlalJYKy9BUDlRQ2FUZmlUcE5DNHdoM1cxVFdubkxtWUt4QzdZaGxGdTU1aTI0TFlkNC9WSXdScnZsOEV5K3d3RWtjdnBzMEY5TTNSbmpkTDMiLCJtYWMiOiJiOTZhNDhlYzVhYTBkMTY3Y2Q0NDc1NGJlYWRiYWY4YmNmMGMyODY5NDNlZTNiYjBmYmRjYzFmZWUyMjlhZThlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:55:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-311749183\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-487005940 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-487005940\", {\"maxDepth\":0})</script>\n"}}