{"__meta": {"id": "Xa8d3b695a7c045332f7e2528e2b209d9", "datetime": "2025-06-17 05:42:36", "utime": **********.585251, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750138955.18549, "end": **********.585283, "duration": 1.3997931480407715, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1750138955.18549, "relative_start": 0, "end": **********.414355, "relative_end": **********.414355, "duration": 1.228865146636963, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.414377, "relative_start": 1.2288870811462402, "end": **********.585287, "relative_end": 4.0531158447265625e-06, "duration": 0.17091012001037598, "duration_str": "171ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45156376, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021330000000000002, "accumulated_duration_str": "21.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4986858, "duration": 0.01794, "duration_str": "17.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.107}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.542635, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.107, "width_percent": 7.173}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5606742, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.28, "width_percent": 8.72}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2055427698 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2055427698\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-780506305 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-780506305\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1266173096 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1266173096\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1577396836 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750138931737%7C14%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImN4b1hGamFWYU9vd1cxMFI2VmsrYkE9PSIsInZhbHVlIjoieDZ4QTkwV1lHaDNUbzllRDBRRHRqUm14L3NHN1FGbFNpdE9kZnRDL0N0cXpmRk94NDZ6WjQxRHRvWTBmU0hvT3ZIYlZITTBOL0JaSGpnWGtLT3lKNW9ORUNuUlJiZzhSc0NLSTcxTzh6TnBucFpidHl5ZmRQaEgvUERzVVgvYUgybGhLWXNZTWJ2a1NsVGVXa1dxZ0Zlc3lCRTFmeDkrelhZZGZLTFRSUVYyWm14VkJveTBremxwWHhoRlNWTjFTVHRKNmpNd2ZhaUFlNWY3bTlIaHR5NWJJdU5FREFLaFpXZGVDQnRYNmxob1phazlSSlRUSGtoNE1RbE11SkFTOWk0WGJ4TnlKS1Z3UERFM2UxUjhrU1c3ODFxLzBudDBoNktJUzJRVit4eGkxTWF2b3BkVklDTzFtQ1Arc2lNY3J0R0xjV2xjcFZIay9Qa2FhbEZpSjJYZnVZWC9yQkZlUDRPcFB6MU5HVm0xNnVsMG5jWmRReHN0Z3htanlZcW1TQU03c1VKejVqVXhqQ21KVzN2VHE1TjZpa2s3aStiWVBuSUNQdnlDU0JpcjZoM3ZYT3BJbzllSFc0N3Y1YnoyeDhpMG9FbFoyU3JTTGFBMUNBQ1hYYVNJaU9xbi82VitWVHVON2dEOUZTNjVGcEp5VHpMUDdVdklLY0FHT0lDaUQiLCJtYWMiOiI4MmFiMjZhMzAzZDZhMTM5NDY5ZjdkYmQwZGRhODczNTBiNDkwNDdiMDk3OTQ3YTY2MDY2ZDQ3ZGMxOTJhZWZlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlNvaDQwR2dpdkFqK1hwMjZoQTVlMWc9PSIsInZhbHVlIjoiVWRPNEpLaXNzNGZjWFo0YmJ1NHFlN0RGUnJQQWU3T25lRW5paE53QnBkV1l0c2wyaFphWFUxSlJmeENGSUg2c1BBM2xjaUluNnRsalZraEQwcVJuTHJsL0JZaEVNRC9OWExWYVV2MU83RXhqNVlVNnZTR0psNjhLNFU2Q2pES3hFWXJlQ2ZVa0xvY0tVU2dncVVNZWhuTnVTQktvaXcvcHRVTkw0QTZ0M1I2K2FIakVGNmRTMUMvNDZvb0Nqeml3NzBEMm5Ja2dKMlpPTTlTbGk3Q3gzWStGeWt6VXdQWmw1TndBcy9OMUlnbThQUyt2cysyWkdBKy9RZEgzbzVyNlFTQVVNODBuamRETzRQdGVLMDI3d3FmOEd6RHhZYm5pS29JSDgrb3l4azlIT1pqcTloNk1GaUxvSGhDYWhMdDR4QkY4RWgxZUloZWFJZjZkN3M0YzJMV0FYcUczRGRvL2JNRVpadW9ualRVWlZXZ3Y5dUV5TDI0U29RYU4vMXNtbUt5KzBMMXJIaWJNZ3E3dm0wd2RXekgyTTdlUlZVRmxZZk52Lzd0UVVjWUgva0FqWnBWVUdxZGNadUdnUE8zWHhRY1VPRVZ6WkFabUxueXYzU3dPODZ4bFRTWHg4MmVzVnRZMGJNeDJCU21JNG5hOGJPWTNUUVkySkw3dlA3R2IiLCJtYWMiOiJjYjNkNzZjYTllOTZlNDkxZmQ4OTBlYjc2YjI0NWJkODNjZDQ4YTBjMzlmNTg1ZTI2YmNkMmU2OGU2MTc3OTBjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1577396836\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1550632696 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1550632696\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-368658445 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:42:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjM1QWRJei9qUGhBcmgybmxzSVhHcGc9PSIsInZhbHVlIjoiQ3BNcDhxREw0a2dDcDdKTlpGeGgvb2lzWElLci8ya3ZGc0xiUDFUUEo5N2Y1ZVJGSTVrUDZlN1F2VlRSTmRQOWl4RjYvbTRSY1hDWjB2T2NTZFRCZjd2VjVKNlpGTXUxQ3ZDYmVMakE0YjN4SDlTbHZJbDhSaDdrK2kvVHpyQTlsQlpSYUFTL3hTZi91RjlqVmV3UnBVbVJhbFJCRWovSCsyMjRQU3lnblRLTkFaOWFqTU9uZXFPUEhsNS9TQkhiZ01HQnpTMUg5WjJyMFU5Rk0yVi9DalJock9IZjZTK080cDl4cjk3alJYdVdLV1ZNL0w4OVU4VnZ2cXJXbk5rSXFpZGZ0NFRxWDRCSnBUWGkzSFpwZGNXelpkZlBHcXk3dmdkbmRCK2lqSlFqZDBSQmM1ejh0ZnliRGl1TExXeFZsbDFjelZsTUZ3TFpyRmRMZWV2MklVS21UZXU2TUFhVm0xWlFxS0VxNE0zRUJGWTE3N2xPWnU0TEpWQjJiZnhDb3VxUTNEcStSSC9IY3lYRFppYVFRUkluY3BLaFRXRWQ4UU5HQlh4TFJLOS9RTEQ5RDUrZExjR0hZSWFsKzMyNUxjWSsrZFVMdHNXQ3o4Nm82NUVwcEE5bVhHbFF1THR6bUFTQ1VhQzhzOUUxOXRVdzE0akN2QXZmQmRxaDdhajEiLCJtYWMiOiI4Zjg3ZDI5ZjMwOGEyZmEyOTgwZjgwM2RiMDg5YjBkZmZmZjg1ZTFhODVhNjM2ZjM3MjMyMDRjNzI4MzQ4YWRlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:42:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImpkNG1tNWd5d0Y2Tjk3NzVxVTd0Z3c9PSIsInZhbHVlIjoiQ1R4OVpZNEltUWIwbGpvWldUS1RLaWhhSlppZkJiZWhYUC9QZEM1Z3cweUZUbjFzZXFFamx2Y1UyWDdNWFBUMXdTY0FNMjNCNDZyOEQ0MFpTU2QyN2N0cFQyRDFGUWlleDlGNjZZV0lOYytGUHYrMzYwd3pCZTkvbGQ0dTZCbEYrK0dYRkJVanBnam1OUFRxbFpSU0tSYlYxOUFqbzhmMEdrdFFPZHBNdk9SVktaMWVwMzRvanFKSEd6Y3pNZ2hFUWhJdkxPSnA3cUsvMEZONlJuUDF4dUdwOWVmbEgvVVZGaU1qZjVZTjZQRlRyempoVHF4UFNKSG9CazdWRTlNUm15d2Z5MlcxUnB4eEtIN0dERnZZVnlRejM0ODVPck4yRHd1NUcxRE80MkpuaXR5bDJVSFE3VGV3NTQ4SG9JMlR0anZvT0VKbkUyMnlEU0tYL3UrMmtTcHdndjNIZURnZWlqNHN0UTgwY3pvZG5sWldJTzYrNTFEK01zVmtwL3o1bk55SFVPQ1pVT0t4dFlPVWg4SkR4Y3FIWDFyUmpLMzhwc3RrTTBlR0tUTm96SEZNYUYxaTVEclN6TXFBL1VzYVplY3d5b1U0U1ZFL2F2cHJrM21DQTA3MlNOcXlLSkZiMUJHNXZNWXJiYlJxNHpyeUU3ekJzN2RXZEZySDYxMlEiLCJtYWMiOiJhYjhhZDljMmM2OWRhNmYwMTY0ZWE5YmM1YTg4NmQyZDBlZTIzZDA0MDI4MzZiMThhNWQ3MDI3ZWM3MzIxNGI5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:42:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjM1QWRJei9qUGhBcmgybmxzSVhHcGc9PSIsInZhbHVlIjoiQ3BNcDhxREw0a2dDcDdKTlpGeGgvb2lzWElLci8ya3ZGc0xiUDFUUEo5N2Y1ZVJGSTVrUDZlN1F2VlRSTmRQOWl4RjYvbTRSY1hDWjB2T2NTZFRCZjd2VjVKNlpGTXUxQ3ZDYmVMakE0YjN4SDlTbHZJbDhSaDdrK2kvVHpyQTlsQlpSYUFTL3hTZi91RjlqVmV3UnBVbVJhbFJCRWovSCsyMjRQU3lnblRLTkFaOWFqTU9uZXFPUEhsNS9TQkhiZ01HQnpTMUg5WjJyMFU5Rk0yVi9DalJock9IZjZTK080cDl4cjk3alJYdVdLV1ZNL0w4OVU4VnZ2cXJXbk5rSXFpZGZ0NFRxWDRCSnBUWGkzSFpwZGNXelpkZlBHcXk3dmdkbmRCK2lqSlFqZDBSQmM1ejh0ZnliRGl1TExXeFZsbDFjelZsTUZ3TFpyRmRMZWV2MklVS21UZXU2TUFhVm0xWlFxS0VxNE0zRUJGWTE3N2xPWnU0TEpWQjJiZnhDb3VxUTNEcStSSC9IY3lYRFppYVFRUkluY3BLaFRXRWQ4UU5HQlh4TFJLOS9RTEQ5RDUrZExjR0hZSWFsKzMyNUxjWSsrZFVMdHNXQ3o4Nm82NUVwcEE5bVhHbFF1THR6bUFTQ1VhQzhzOUUxOXRVdzE0akN2QXZmQmRxaDdhajEiLCJtYWMiOiI4Zjg3ZDI5ZjMwOGEyZmEyOTgwZjgwM2RiMDg5YjBkZmZmZjg1ZTFhODVhNjM2ZjM3MjMyMDRjNzI4MzQ4YWRlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:42:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImpkNG1tNWd5d0Y2Tjk3NzVxVTd0Z3c9PSIsInZhbHVlIjoiQ1R4OVpZNEltUWIwbGpvWldUS1RLaWhhSlppZkJiZWhYUC9QZEM1Z3cweUZUbjFzZXFFamx2Y1UyWDdNWFBUMXdTY0FNMjNCNDZyOEQ0MFpTU2QyN2N0cFQyRDFGUWlleDlGNjZZV0lOYytGUHYrMzYwd3pCZTkvbGQ0dTZCbEYrK0dYRkJVanBnam1OUFRxbFpSU0tSYlYxOUFqbzhmMEdrdFFPZHBNdk9SVktaMWVwMzRvanFKSEd6Y3pNZ2hFUWhJdkxPSnA3cUsvMEZONlJuUDF4dUdwOWVmbEgvVVZGaU1qZjVZTjZQRlRyempoVHF4UFNKSG9CazdWRTlNUm15d2Z5MlcxUnB4eEtIN0dERnZZVnlRejM0ODVPck4yRHd1NUcxRE80MkpuaXR5bDJVSFE3VGV3NTQ4SG9JMlR0anZvT0VKbkUyMnlEU0tYL3UrMmtTcHdndjNIZURnZWlqNHN0UTgwY3pvZG5sWldJTzYrNTFEK01zVmtwL3o1bk55SFVPQ1pVT0t4dFlPVWg4SkR4Y3FIWDFyUmpLMzhwc3RrTTBlR0tUTm96SEZNYUYxaTVEclN6TXFBL1VzYVplY3d5b1U0U1ZFL2F2cHJrM21DQTA3MlNOcXlLSkZiMUJHNXZNWXJiYlJxNHpyeUU3ekJzN2RXZEZySDYxMlEiLCJtYWMiOiJhYjhhZDljMmM2OWRhNmYwMTY0ZWE5YmM1YTg4NmQyZDBlZTIzZDA0MDI4MzZiMThhNWQ3MDI3ZWM3MzIxNGI5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:42:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-368658445\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-98700770 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-98700770\", {\"maxDepth\":0})</script>\n"}}