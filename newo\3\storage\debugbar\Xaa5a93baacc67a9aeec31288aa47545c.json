{"__meta": {"id": "Xaa5a93baacc67a9aeec31288aa47545c", "datetime": "2025-06-16 15:23:44", "utime": **********.306405, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087422.620212, "end": **********.306446, "duration": 1.6862339973449707, "duration_str": "1.69s", "measures": [{"label": "Booting", "start": 1750087422.620212, "relative_start": 0, "end": **********.086263, "relative_end": **********.086263, "duration": 1.4660508632659912, "duration_str": "1.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.086285, "relative_start": 1.4660730361938477, "end": **********.306451, "relative_end": 5.0067901611328125e-06, "duration": 0.22016596794128418, "duration_str": "220ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45160944, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03289, "accumulated_duration_str": "32.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.194482, "duration": 0.02994, "duration_str": "29.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.031}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.25778, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.031, "width_percent": 2.767}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2806962, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.798, "width_percent": 6.202}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order/1/edit\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-973059444 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-973059444\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1501325224 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1501325224\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1213786822 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1213786822\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-911990829 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087411438%7C10%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkkvQnVTUFE2TitqRDBBeG84ZGwzbFE9PSIsInZhbHVlIjoialE4MjV3d2FoTnU5bVhlTTd0U1UzOUFkTUJaODNLazdjNE1YbklhWkpNNCtPQmsrS2pyV0NqZkpRSFpJUTE1SEpHZitkam9HeHcxR1RVU1FpUG8wRVFJZlNOcGp4azd5cUhmSXpQRmhSZWJqMCtIa3gyeUhEMC9sT0MyNUpHZlRCZncyMnI0S1IwMW9jY3pVWUY4bU5oM2NyTHNOWGh0S2lMczROTGw1T29LdXlLUnNoa0t3L2xRaHMwQWRPOS9CS1ZZcHh5bzJaVEpBMlVPRlB4bThGZVdHS29nOTVSNkRWanJMUlMzKzAzR1RaOFNJVkJGQ0o5YmgvWDdFNy9WOXRFNzFzNGRNdWozT2tLMnkrUGwvVUZ2Qm80eFpTRVBXamJOOUlodW9YNU52bnBmdHpaMFlMK0xORmQ0U29jemxldENBci9HeExtWXFiWHNPdTcxZVlrYjdCODNSRFZRT09sLzUvRUZ2V0ZQa3VKWmR1dUg0Uld2aUsxT1RjSlhubGdUWWFOVXpyVkJ0amJveSszMjhLaGlrdG5mRzg1c3BrbFV3KytyU0tCM2FaVUdrTjlPSHF6OFIrbXRqMlJZRUcwd3E5cWNud3FvcklSRVgxK2I2RE82YW9BWUE1SGQ1blFURis4ZWhBSkFaOUlSMUh2L1QzcCsrYURvVU5uY2UiLCJtYWMiOiI2MmQxNzM0NTYxZGFmNjJjYjA3NDFlMmZmNTg3ZTNmMzYzYWFjOGVjZDdiOWM3ZWQ0NWQ1YzcyN2JjYTRkMGU1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IldtWlIxeGxOMGZnTTJDbkVXcEE2cnc9PSIsInZhbHVlIjoicnZoMUZCQmNYOE5WTG1CWlRYUVRnSlpySDhwdWdqb1NBRVF5QlByeTlKeTIrT1NRYlpNU1l3Q2ZCelloZFdMZ2dWbytnemFNNXpUdEtMclpIQlJobnkwV3hYZUpuZWlwbUtJT2owQWdvU1RsUHdXYUZJdHJiVGRDRkpBbUxCSXI2aXhSanF5Nmw4SkNnSU9BNm16aXdWRVZuL01mL2JiS05ubmIycTcxZVdyaWJDY0EwZEY5b3MzTVcvZXl6L0QvejUvUHFVOGZ3bTBvdGhIdTdiRTFaL2pEMmNKQi9Uc0pNMThoSTFZOWRtbmFabXlrNjR1TlFXRW9YM0tSQVJmVTMrRlh2Sk0zellIRUdZWWt3VkkrNHdUbU5PMkxGanhrazZvTDY3eFNuMnFQc3ZRTmFFdUl2VFUrZW1ON2U3MDBmbXRlb2hCdXpMRkE4NUJKN0d2OFI0c2VENlBDenJCdFVKd1c5cS80RzVxSEFONzhsTjlZNE5PNE9BZWJDVmxJQ01WbWhMK09RbDFvQ0ZDNnNwR3c4RDRjRldOaXA4bDNualhSNXZEeXNuZmc3RTR5dGVvWlN3SURLbXpZUWFnWGFaV0l1b1Zncy9qalpXZVE4U0xiYTRTTlJRZmJ1bGF0UjBidXBLVy9LT1JCdmFIL3E0bFZIaE03S1QyVVpaWDciLCJtYWMiOiIxOTI0NzIwYmNiNjJkODZiOTEwYzdkNWVlMDIyOGFmNWQyMTU5NTQxY2VmYjRlMWI4MmM4YTFlMzY2NWQyMDllIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-911990829\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1480033579 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzcvmOKIgVMCCvEHD9HLB5trqF9yJ9GcIs8Pndo9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1480033579\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1796812075 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:23:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ino1NDNFL2Q4Qit6bmRxbTZnc0NuNGc9PSIsInZhbHVlIjoibXFtd3Z1dk5TSnVDR0lKZ1JkRTM2S2xmR2NLZmNhdlVXejh3S1ErVTN1OGJDRlVPUGNDZURhTlZhNWkxUG55YWEyVFNobEx4c1Ntd2dMVzhIM0c2YjBQcmd5TERYRWlBU2NSLzRNU3ZNT2VEMlA4Z1ZnUFNQRXhNdi8yNDNlZ3VtMytPT0FLUVFlamNRU3BDNDZJR3QyMEpESzFWL0txZG9YaHgrZGhmc2FUendSRXBIM3FPNHdOalkxZmFGUi9kUHMxT0NPa2xwZ3FtelE1bnl5ZmpwTmc1ZkZFTjFVVUgyRCtxRmlXZGJCeExyaUZxd3lFQ0tWYUw2TnhwcWtUd1BiMFRnMlI0czNRdTJGa0RpOUZWZnNyVEdKR2ZLSzlLUFJsSHM2YkdhY0grYVZJeGRTYkdwS2svZlMvOFFVZUdUb0IrUFJLWkF4M0xqSnpsUDlHYkJwODgrZWFoQUU2ZlhBT0lMai9SZy9RVHl5YUxVTkc3VWdqTlE5SE82Sk9VOW5kK1BPdk1vL1JBRHFRRTF6a20rYlM2dmNhVkRXYTV2NTVFNmxTS0Q5Z3NCWXA3ZUU3d1BnYy9JNE9uYXo0L1VydjVacmE0WDBLVzlYbXlHUVZuUmtSNkRoQ1g4bHI2V3dCeUtJRmc1M1diOU1pTytjU2lWOVVkakZpTXNlb0UiLCJtYWMiOiIyNGNkMTE2OGNkNWIyZDlkNDJkMWFhM2UwMzUzYzM5YjFiZmU0OTg0MWQ5Yzc5MDI4MDhjNThjYjk2Y2MwNTZlIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkdOd2JxS2gyMldjRC9RMkt1bGFlc2c9PSIsInZhbHVlIjoiNVZ6R2lUZDBERFA2eVRrOUFIeUZYMG45bDV5Q1FxMEZTV1BnKy9UNGJsZFNFMk1JWm1hVTM0N3FsU0NpaytQL0xMRlpyMzRRT0VKbFVMamFQOVZsVHUrSHk4cFJMV2I5RUp2dHN4c0dZSkVEci8rN3dNQ3ZlUGNwRjZPa0EwUTdkUnZqdHdPTXNZTmhDR2g4S3QzbzM2UWw1NUtobHpQZFFRSytPSkJnanpVVFR4bVlYRGkraExhTStOeHc4SmF3akwwS2RqZzNqR0VOdFJhdTdwK3ZLcFVOUUNFWjBtbDdER3N4YnQ3MXE3Mk5qbWM0ejdnTjMrMHJ4aTN3cFYvVVNMYnRTbzFQdCt0UzFreU9UTDc0TGdIWVlGSGZWanpSUG1lS1RFbHF4TTRWUG15Qm9Ma3pvQmhnY0hmUVhLT1NpTEF3OURFblo4NE4xZVE5YlRJYUZYV3lpK3Zoc1JvQ3BqSnB6OVgzYStHQXJpaHB5S1NkdU9nVWJNbXhvUFBiRDV3L0lXbldsRms5R21hRnd1WERZZ3NtUkR2clhwTCtTUEVVcURLRzcrakZWSkNWR1ZtRlVCdUcxdEhaTkdrdVBLbVVjTWpqVHRBYmtTMGFpSHV4MVphMGZyN0pwZ0oyaFZrODFKT0RXbDZxamloMGF2dU9NS3FNU1lpQ2ROZGwiLCJtYWMiOiIzMzMzMzQ4MDhjMWRhNGVlZGUwNDIzOWYyYzc3Y2Y1ZmZlOTdkZjUyZjZkNjRlZGIxZjczMmZiNzY2MDAyYTM3IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ino1NDNFL2Q4Qit6bmRxbTZnc0NuNGc9PSIsInZhbHVlIjoibXFtd3Z1dk5TSnVDR0lKZ1JkRTM2S2xmR2NLZmNhdlVXejh3S1ErVTN1OGJDRlVPUGNDZURhTlZhNWkxUG55YWEyVFNobEx4c1Ntd2dMVzhIM0c2YjBQcmd5TERYRWlBU2NSLzRNU3ZNT2VEMlA4Z1ZnUFNQRXhNdi8yNDNlZ3VtMytPT0FLUVFlamNRU3BDNDZJR3QyMEpESzFWL0txZG9YaHgrZGhmc2FUendSRXBIM3FPNHdOalkxZmFGUi9kUHMxT0NPa2xwZ3FtelE1bnl5ZmpwTmc1ZkZFTjFVVUgyRCtxRmlXZGJCeExyaUZxd3lFQ0tWYUw2TnhwcWtUd1BiMFRnMlI0czNRdTJGa0RpOUZWZnNyVEdKR2ZLSzlLUFJsSHM2YkdhY0grYVZJeGRTYkdwS2svZlMvOFFVZUdUb0IrUFJLWkF4M0xqSnpsUDlHYkJwODgrZWFoQUU2ZlhBT0lMai9SZy9RVHl5YUxVTkc3VWdqTlE5SE82Sk9VOW5kK1BPdk1vL1JBRHFRRTF6a20rYlM2dmNhVkRXYTV2NTVFNmxTS0Q5Z3NCWXA3ZUU3d1BnYy9JNE9uYXo0L1VydjVacmE0WDBLVzlYbXlHUVZuUmtSNkRoQ1g4bHI2V3dCeUtJRmc1M1diOU1pTytjU2lWOVVkakZpTXNlb0UiLCJtYWMiOiIyNGNkMTE2OGNkNWIyZDlkNDJkMWFhM2UwMzUzYzM5YjFiZmU0OTg0MWQ5Yzc5MDI4MDhjNThjYjk2Y2MwNTZlIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkdOd2JxS2gyMldjRC9RMkt1bGFlc2c9PSIsInZhbHVlIjoiNVZ6R2lUZDBERFA2eVRrOUFIeUZYMG45bDV5Q1FxMEZTV1BnKy9UNGJsZFNFMk1JWm1hVTM0N3FsU0NpaytQL0xMRlpyMzRRT0VKbFVMamFQOVZsVHUrSHk4cFJMV2I5RUp2dHN4c0dZSkVEci8rN3dNQ3ZlUGNwRjZPa0EwUTdkUnZqdHdPTXNZTmhDR2g4S3QzbzM2UWw1NUtobHpQZFFRSytPSkJnanpVVFR4bVlYRGkraExhTStOeHc4SmF3akwwS2RqZzNqR0VOdFJhdTdwK3ZLcFVOUUNFWjBtbDdER3N4YnQ3MXE3Mk5qbWM0ejdnTjMrMHJ4aTN3cFYvVVNMYnRTbzFQdCt0UzFreU9UTDc0TGdIWVlGSGZWanpSUG1lS1RFbHF4TTRWUG15Qm9Ma3pvQmhnY0hmUVhLT1NpTEF3OURFblo4NE4xZVE5YlRJYUZYV3lpK3Zoc1JvQ3BqSnB6OVgzYStHQXJpaHB5S1NkdU9nVWJNbXhvUFBiRDV3L0lXbldsRms5R21hRnd1WERZZ3NtUkR2clhwTCtTUEVVcURLRzcrakZWSkNWR1ZtRlVCdUcxdEhaTkdrdVBLbVVjTWpqVHRBYmtTMGFpSHV4MVphMGZyN0pwZ0oyaFZrODFKT0RXbDZxamloMGF2dU9NS3FNU1lpQ2ROZGwiLCJtYWMiOiIzMzMzMzQ4MDhjMWRhNGVlZGUwNDIzOWYyYzc3Y2Y1ZmZlOTdkZjUyZjZkNjRlZGIxZjczMmZiNzY2MDAyYTM3IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1796812075\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2126379955 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2126379955\", {\"maxDepth\":0})</script>\n"}}