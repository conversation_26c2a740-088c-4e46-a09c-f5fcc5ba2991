{"__meta": {"id": "X0488eab396742172ea4b0b429d1f53e6", "datetime": "2025-06-16 15:23:39", "utime": **********.441365, "method": "POST", "uri": "/inventory-management/update-min-quantity", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087417.597002, "end": **********.441406, "duration": 1.8444039821624756, "duration_str": "1.84s", "measures": [{"label": "Booting", "start": 1750087417.597002, "relative_start": 0, "end": **********.142053, "relative_end": **********.142053, "duration": 1.545050859451294, "duration_str": "1.55s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.142087, "relative_start": 1.5450849533081055, "end": **********.441412, "relative_end": 5.9604644775390625e-06, "duration": 0.29932498931884766, "duration_str": "299ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50982464, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST inventory-management/update-min-quantity", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\InventoryManagementController@updateMinQuantity", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "inventory.management.update.min.quantity", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=203\" onclick=\"\">app/Http/Controllers/InventoryManagementController.php:203-240</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01427, "accumulated_duration_str": "14.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2679162, "duration": 0.00625, "duration_str": "6.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 43.798}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3081062, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 43.798, "width_percent": 10.582}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '5'", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.3429031, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 54.38, "width_percent": 13.034}, {"sql": "select count(*) as aggregate from `warehouses` where `id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.355475, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 67.414, "width_percent": 13.735}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 212}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.38396, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:212", "source": "app/Http/Controllers/InventoryManagementController.php:212", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=212", "ajax": false, "filename": "InventoryManagementController.php", "line": "212"}, "connection": "ty", "start_percent": 81.149, "width_percent": 0}, {"sql": "select * from `warehouse_product_limits` where (`product_id` = '5' and `warehouse_id` = '8') limit 1", "type": "query", "params": [], "bindings": ["5", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3852398, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:215", "source": "app/Http/Controllers/InventoryManagementController.php:215", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=215", "ajax": false, "filename": "InventoryManagementController.php", "line": "215"}, "connection": "ty", "start_percent": 81.149, "width_percent": 10.371}, {"sql": "update `warehouse_product_limits` set `min_quantity` = '6', `warehouse_product_limits`.`updated_at` = '2025-06-16 15:23:39' where `id` = 4", "type": "query", "params": [], "bindings": ["6", "2025-06-16 15:23:39", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 215}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.393724, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:215", "source": "app/Http/Controllers/InventoryManagementController.php:215", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=215", "ajax": false, "filename": "InventoryManagementController.php", "line": "215"}, "connection": "ty", "start_percent": 91.521, "width_percent": 8.479}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 226}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.415527, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:226", "source": "app/Http/Controllers/InventoryManagementController.php:226", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=226", "ajax": false, "filename": "InventoryManagementController.php", "line": "226"}, "connection": "ty", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\WarehouseProductLimit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FWarehouseProductLimit.php&line=1", "ajax": false, "filename": "WarehouseProductLimit.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order/1\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/inventory-management/update-min-quantity", "status_code": "<pre class=sf-dump id=sf-dump-1724992619 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1724992619\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-738283784 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-738283784\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-81477328 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str>5</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>min_quantity</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-81477328\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-174720237 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">90</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087411438%7C10%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhvQTFWeFNxOVlZVjEyOGhmNU5xQlE9PSIsInZhbHVlIjoiblFQRG1hNmZkYTBCVjZFLzF6MnpZT3VGUVZlUUpQZWNwMEJYbHdEVWhYSG1sbElMY3VTaWVETmdYdlZ3OWFwZlkvWUlwV0JIZFNlYlIvWGQ1VXo2UmtZR1lTZGdMMEFFOFRwVTJZeW84a1RZTXBLcGhmT1d5bVhYTHNwNCtEbnlUUnNWcVRxTzB0Z0gybkZTcWllekR0dE1EN2lJUDNURnd3Ny9JUjhCK3ZzZXc2ZGdHSGVXWHBMaFF4ZlZUbmF2VGtsV29ieVVoODY3YjBhemw2Q1dSVHdNb1d5cnpBeFFoTElOUC9Sc1VJcFZ5NTc5YnYzNnQxOHBGaVR0SDJFTkx1cHVUU1FVYUl3R0tDMElJNGhYQUZTWVlVWGtrQnBESFpwc3BYNUJzVy85V3lLYXR1cjkyU0RjWTdPNGNTQ1gyV1JRakR0WlQrRUoveG5iM3h4c3RKYkEzanMyMWFJVUZPVTNCY3FSa3FrMDNQRHBoRUpOQVMrODArWFhRdW1OZ2IzQjRqMk1uQ3FJc2h1ejR0OFpKck52RHZoSVdBOUR0MncyazJ0WDR5UlE0c1hUTUpRK2d1Q3lnbEhtOG02TEJlSFlqY0NkdURKd2xWV25EYzk2SFFnNXovRkpBeER2Zm9UbmFTZ1VWdEVDcFY4Nk1lVW5UTVhmOW5JcXlyamkiLCJtYWMiOiJjZmM4YjcwZWU2NmU2MTRjNDJiNTI4NjBhOWFjOTI1ZmY3ZGVkNzE0YTljMWU0MzU5ZjUzOTMyYmFhYjg5NDQ2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlROdU9BSUxkamZ0clpOZlRGYWcyd0E9PSIsInZhbHVlIjoiakNVNnl4WG1TOW9jRjduT0dpL1IvZStwWmsydHNoay9sNklad3B1MWZ1TDVuVUFBZ2Jsa2dBclZrVTlQdnNQWnpNNTg5ZzVXMk9oZjNkODREN3BUQU1FTzg3VityL3BoMVdUcVI4WUFEYVJEblF5NWl5cDkzN1ZMazBCTUZXcm1nRzVzekZtSER5ZWdvaCtGeDlTdEhyc3dEUGtqeFpHSFQrUU1YVXljVUJlWFFOWXRBQTVvY1BrUFRuY2l0bFdIbHlGRC9uVjdOVGlmSTVQL2JESm9UWERjYmkrcjBNck0wdlBlN0RlcWJ3QVMzdy9wSGJhL3R4V2c2d0pScGpyZFVUdFd1VGtWbHhvQm41cnJRbkhhakVmaDRnbGJOUzNsT3ByOEJRQ0FMTjN3TjVLeDFnRFNvdkhHVEdlYWxRNmN6eTN0MXRQUlhWaGVVNEQrTFFIRGdja3dGRjJUdmx2VmdZVHczZHMyOXFtSUcxVzQ0NEtTYW9BdnlGNWdaYUVLT0NxOERqYWx1V0ZObWlQT3FhL3dZQXJVZ0hjN3lCQURpa3RNSWhCV2JXVEJxdXhKKzlRd3FBWmo1SFM4Q3BzcGxNakJrcmZhRDYxNkZnSE1VNDJCd3NhbEV3OHlWTll5K0hTeUZCcFVXWEJYakZCZ0JqOXNCNTZ4ZnF4RXdxOTUiLCJtYWMiOiIzMDgyMzExYWYwMWMzZmU1OTU5YTA2NjY0YjQ5MDQwMDM0YzQyNGIzZTdiNWI1NzQ0MzM2YTVkNTMyODViZjVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-174720237\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1640182882 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzcvmOKIgVMCCvEHD9HLB5trqF9yJ9GcIs8Pndo9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1640182882\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1804076562 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:23:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlNL09iNlgzYXlkVk5kL3kxd0QxMWc9PSIsInZhbHVlIjoiWlYvSEhVd1VZa3Fmdjk5bjlXVHBSYlBJNGdMT0lnYmpEUUdoL2V3ZXN5aDZqM3BReXZhSTFhUXkrWEJWVW5IV1Q1QnZRZ2F2bVdJeGNSdlZ3bzJjMVFoem43a3VqcFlYWUtJRSt3OWsybWRYQzVyZ0poOFUrUUVxSlhLOGRPYXIxcjZSMld2ejFCbFRPR1IvRkxqTjZNN1NmMGhvdHYwNjRnUk1ENk05Ni9uT1NlenVGSFlGS1hCd2haTEc0ZGh0b0FlT3RFNkpNZ0c5UnlYd3JhZ3Z4eXNZUzlERkFqQzczZFUwV04xaWtLQTRJOGpCNkhWaktQZWpMZVJObWYrTG9QN3RDcjZIQ0FFTFNWbjdPdHI3bENYRDNkS0tETUJYa0I0RkZKVEdnTmJuSmxFdmJjTG1DM24yc1dnMExuWElKdkJ3KzBUZWFmYUxWSDQyU3Ayd0dHKy91ZG1MWlZiVFo3MEhKaVdPajdiRmh3ZzA4TjVhNmIxbkNOa2dFdkxYUmZJQVMycmYvakdyaVZKMHp4T0dNa0I0SStDaTFNWi8wTUkxKzk3bGUza3JmNDU2MzNpUkRURERWMFdwbTZBU1c0RHNobmo4L1UwTmpScGZHbFBycUd1bi9XcGhpUHlEZFlnZDh1UmpSbm43eGEyVDBTVkozUlRUVzF0U2RJMS8iLCJtYWMiOiIzNWIyOGIxMzIwNmEyMTIzNTc1MzUwMDZhYWIwNTQyNTI5YWIyMGVmMGU4YTJjMzE5NTU3MjgwYTI1MTYyNzNhIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik1iQmlYV1NzbmFaN050UGd3UU1yR1E9PSIsInZhbHVlIjoiNkt0a1hoZERzcnNiTUNsdzNNbXpJWGx0U1RwVVZ5Ym1DNXFEa2VETXFkR29jMjhMOFZrbjBpa1FJdFY5K2hGOCt2N0Y4SHNZTzhDdXhKdTZybHRPWXNHM3huSTZYajk2UjRUVnhCMGlDellIaDNYYzJyUGhwVzFrbjc2Zk1raUNodFl3Z3FiK3kwK0o5eG1tNTFINUxtcGlPQnJubTRPcTBDcjZEa3RIZ2RCSGJOYktsTXF2ZTJaSlAwTURBQ2VlTzdkMFhIU2JnK3VRb3hsQVkva2IveTNlV3lWQTZOUWtIenJ6akJzVXFxemltRC9RV3JZMlIweGVOK1F1SWkyY21RRk9JY2lKK3FzUkxqZDRuQnZMajQyUWcydVJSRHQ4L0EwSnZkTFJtcGYzZzYvUXg2SzU4aEltNm5SeTd5enluME1HeEZOMmc5emNwNUtJZkdmZ0JFeGcyRm5Fd0M0OXpaNFQ5WTNEdEhuOTF5Q1N0STBoaEEvcHM4clBWUXFtS0VnM0RFdGRQNUYyTmNjUnh5QWw4MTdEWTJJazN6d2t4TzlnYU9FdVM2Q2VFRmV4MjJKSEwvOENHUVF4WU5zdjREUHdMNkVuVkhyVjQyRDNhb2Q2bzh3TzVEMVZWaEdsdi8rdFN0NmxlSlh5QjlsOGgzZVpXbVZvR01PWWlkWHUiLCJtYWMiOiJmNTZlZmQ5ZGI2YThhNDQzYzliN2RjMDU2Yzg0MWYyZTAxOWFhYjA1NzI5ZmMzNWE1OTgzY2YzODYxNWEyZjcyIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlNL09iNlgzYXlkVk5kL3kxd0QxMWc9PSIsInZhbHVlIjoiWlYvSEhVd1VZa3Fmdjk5bjlXVHBSYlBJNGdMT0lnYmpEUUdoL2V3ZXN5aDZqM3BReXZhSTFhUXkrWEJWVW5IV1Q1QnZRZ2F2bVdJeGNSdlZ3bzJjMVFoem43a3VqcFlYWUtJRSt3OWsybWRYQzVyZ0poOFUrUUVxSlhLOGRPYXIxcjZSMld2ejFCbFRPR1IvRkxqTjZNN1NmMGhvdHYwNjRnUk1ENk05Ni9uT1NlenVGSFlGS1hCd2haTEc0ZGh0b0FlT3RFNkpNZ0c5UnlYd3JhZ3Z4eXNZUzlERkFqQzczZFUwV04xaWtLQTRJOGpCNkhWaktQZWpMZVJObWYrTG9QN3RDcjZIQ0FFTFNWbjdPdHI3bENYRDNkS0tETUJYa0I0RkZKVEdnTmJuSmxFdmJjTG1DM24yc1dnMExuWElKdkJ3KzBUZWFmYUxWSDQyU3Ayd0dHKy91ZG1MWlZiVFo3MEhKaVdPajdiRmh3ZzA4TjVhNmIxbkNOa2dFdkxYUmZJQVMycmYvakdyaVZKMHp4T0dNa0I0SStDaTFNWi8wTUkxKzk3bGUza3JmNDU2MzNpUkRURERWMFdwbTZBU1c0RHNobmo4L1UwTmpScGZHbFBycUd1bi9XcGhpUHlEZFlnZDh1UmpSbm43eGEyVDBTVkozUlRUVzF0U2RJMS8iLCJtYWMiOiIzNWIyOGIxMzIwNmEyMTIzNTc1MzUwMDZhYWIwNTQyNTI5YWIyMGVmMGU4YTJjMzE5NTU3MjgwYTI1MTYyNzNhIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik1iQmlYV1NzbmFaN050UGd3UU1yR1E9PSIsInZhbHVlIjoiNkt0a1hoZERzcnNiTUNsdzNNbXpJWGx0U1RwVVZ5Ym1DNXFEa2VETXFkR29jMjhMOFZrbjBpa1FJdFY5K2hGOCt2N0Y4SHNZTzhDdXhKdTZybHRPWXNHM3huSTZYajk2UjRUVnhCMGlDellIaDNYYzJyUGhwVzFrbjc2Zk1raUNodFl3Z3FiK3kwK0o5eG1tNTFINUxtcGlPQnJubTRPcTBDcjZEa3RIZ2RCSGJOYktsTXF2ZTJaSlAwTURBQ2VlTzdkMFhIU2JnK3VRb3hsQVkva2IveTNlV3lWQTZOUWtIenJ6akJzVXFxemltRC9RV3JZMlIweGVOK1F1SWkyY21RRk9JY2lKK3FzUkxqZDRuQnZMajQyUWcydVJSRHQ4L0EwSnZkTFJtcGYzZzYvUXg2SzU4aEltNm5SeTd5enluME1HeEZOMmc5emNwNUtJZkdmZ0JFeGcyRm5Fd0M0OXpaNFQ5WTNEdEhuOTF5Q1N0STBoaEEvcHM4clBWUXFtS0VnM0RFdGRQNUYyTmNjUnh5QWw4MTdEWTJJazN6d2t4TzlnYU9FdVM2Q2VFRmV4MjJKSEwvOENHUVF4WU5zdjREUHdMNkVuVkhyVjQyRDNhb2Q2bzh3TzVEMVZWaEdsdi8rdFN0NmxlSlh5QjlsOGgzZVpXbVZvR01PWWlkWHUiLCJtYWMiOiJmNTZlZmQ5ZGI2YThhNDQzYzliN2RjMDU2Yzg0MWYyZTAxOWFhYjA1NzI5ZmMzNWE1OTgzY2YzODYxNWEyZjcyIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1804076562\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://localhost/receipt-order/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}