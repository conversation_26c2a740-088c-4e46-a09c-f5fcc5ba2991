{"__meta": {"id": "X7a035a15e3a099475dc40b8a2d1a628e", "datetime": "2025-06-16 15:22:24", "utime": 1750087344.060187, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087342.441737, "end": 1750087344.060233, "duration": 1.6184961795806885, "duration_str": "1.62s", "measures": [{"label": "Booting", "start": 1750087342.441737, "relative_start": 0, "end": **********.887116, "relative_end": **********.887116, "duration": 1.4453790187835693, "duration_str": "1.45s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.887134, "relative_start": 1.445397138595581, "end": 1750087344.060238, "relative_end": 4.76837158203125e-06, "duration": 0.17310380935668945, "duration_str": "173ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45152520, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01939, "accumulated_duration_str": "19.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.973194, "duration": 0.01654, "duration_str": "16.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.302}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1750087344.0165179, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.302, "width_percent": 7.375}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750087344.0359359, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.677, "width_percent": 7.323}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1173074149 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1173074149\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2066194553 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2066194553\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1344530751 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1344530751\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1452350371 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087330436%7C4%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IklXd01DbWFoM1NITDh3RkNuRHJoSEE9PSIsInZhbHVlIjoiR05qenQwMEhwSXlpU0UvZnp6SVExRWdhRWhBTHN4L0E0M0tpWmZ2L0J6NHpNUjVIQW9GYkh1RWlzazRPMnZDbnFiV2g5K1VCK2xocG5nTnZvbUNJVHI1aVRjY2xBNXgxWFVDblhtaDlGNmRrTWdyRExqalJnblJlbEFPZTNPTHQyLzVabVQ1alpiZ1I5R2VWbGdxaG9qWW5wMDNOSndmTDdYRkVvTE1EZDFFTU9MTVZUd1YyQU1MdWFMUUxXaFljWWZxYnhlT2N6elY2czNUNmdHVCttL3hUNW5tQzBWaDhQaDR5RjJ5UGZJeEljOHNkSmNKU3RZS0k0eEp4bHBkSzgwWHBRYkRiMHFLRG1WbjZyZWtlZEJTNTIzRSt2Z1l0N1AzckpQeElYQzlaczNobVg3Z2dhV29lNFVnM2MvTHl2VzZKamxudTUreXZ1RnN0TDlVMzRITW5sL0NHV0YzY1VrbjRPc2YzbWJ4VHoyMURkNysvWjQ1SkFDNFJOczQ2OGFqRzdUenVKa3FZVHlXRDJVTk1rRzhmeXRPTzB2ZG5lZGd1MVlvWjJFcGt5aDMyNjFMUEZlbE5PMnVFc2Nna1FKdjdETU5uYm10VTVYY0t3azNoUTVWTXhVenJGUlJyajVoZjF5SVFYREZNN0VNTmVRVm5wd25saElmWS9zRlEiLCJtYWMiOiI1ODk3Nzg2NDA4ZDU2ZmE2OWQ2NzI4ZWY2NjUwMTU0YjJiOTY0Y2Q2YTI4ZWE0NzIzMzM1N2MwZGU1MWI2ZmJjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InVBVk55MGxXd0hGV2hwZDcrRTNyV0E9PSIsInZhbHVlIjoiTEZscWZxc2VLV2NPZnZFZmlpbW9PYjRCVVFCZnZjRFhtbW1TYWY1bUV0cFhZRllXK1A3QXUzVEFxK2RjZW1uRzhpcW5YcXBjUWtoTDBua0pKNDdtcmp5djRsVmNPYXBFOXU4VU9Ua2pjN3FCSnI3WUZxYWNCdE11N293c1B4c29YRWpnZ0hFSDlHdGk1cSttSlZwRjA1TXBDemFwcUcwNndrN3RuUW80VjBXZUwrR0sxdWY4SUVwZStza2EwMXR6OFVPcGdsKzdPR3RVWXRraVFqeGhoUC8xNnpSSXlld0xFOFcvckJvVEVOSHUwWnVHQnNLcE1hdnFqU0VXK3hmS2lmYUIvKzdzZUJBbWRkMVlJMUhwandpVkY3ZzZPdXEzRUEwbDJuaGtGTWxocEd2a1NjOWUweUNWR0gxRkNaa3MzSVhyNmZrY1NkWWJ0azJHS1dML0l6Z251QTJQa3hwVVBRM1V1dEQrRy8wUUozVFhrMGsvN0krUzg4dHB4aWRxYzk4YXpsZkV5U29sRGRPd3cwVmxPSGZFbUNQZ0sxTFFXY3Y1T2F3ajNGa3BIRzU4MFZXQ2Z5aExHOUI5am5OaTFVMVBzSWsxVEgxUWpYQUJoQzVKQW56SXIrWWxiTHBETzhPNmFCelJaMmx6VWpTRS83bEpCdzkxMkZ1dTNUZjQiLCJtYWMiOiIyYzJiMzI2YmE0MjcxZTA4ZmEzNDVjMGNlNzNhMDc2NTBmNTcxYWNhYTQ4NmQwNGRjY2MzOGQ3OTRlNDdmYTFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1452350371\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1867464636 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzcvmOKIgVMCCvEHD9HLB5trqF9yJ9GcIs8Pndo9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1867464636\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-732091332 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:22:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNMM1lCR2hLV3ZRVW1zUTlQcDE0cnc9PSIsInZhbHVlIjoiWjhSQmZveWZqVjl4aU10b2xOU1JUVEVqS0UxSEZmNUJZRG85QVJBVnhXUW9GM2NaZjdmS2dSQW1PdXBWcDB0cE1SaklHNHdlY2JnWVRSRlVIczRIN0I2Z0NjYWE3VjJwcHpxNEpUMEYyd2ZDM1QrNDhRMWsyOHpTUW1lNzFJbXRNalp2YmlqeWZkRW1YalNXZGNnZ2hMZ21TN2tuT3pKOXdtY1QyZmkxcnNOMVp1TmJnQ01jVFZQTXhmQWlhNUc4cXFPMnBtYXdpNm9CMVl4M25LV0tsR0RxdFRMd1JYM09hL0p3RWUwVlJ6bER1QWJQNXRmVTdKNy9zajZmaFpDZHEwMXhJN2hyMXMwTDg2MG9FNE1KV0JxRlgyRkN2WkkwZUR3Mm9MN05pd3NHcThoSGgrbkNpWHNRaDZMSW1vaDBNTVQwajJSMTVTSlRNcElSdC9zQ3lldG0rNmM0Qi94RXl5UWR1c01ZOGF6aHFOVUFoN2czN0FSYW1FQkkwS3QzTUhScStRU3R5Z3FYUXFlZFg0K1RwN3N3dXQ0dFpVWkhiU0tRS28zYS9nLzMrRGxFRGhXWDNlL0l6TlFIcXZjWFdScVZwNDd0ZXlCT2QycTRYZWl5WU1QNy9LbHEzcmtnY2cvdTM3MTl3YmlENW9lR1o4bCtrNW5mVTEvM2FDR0oiLCJtYWMiOiI0NDljMjRlMDhkNTFlNzZiMTZkNTFlNGE3NDE2MGIzOTVjMTUzZjU4NGFiYjkwZTg2NDVmODRhNGE0YWNkMmQ3IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:22:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFHTWgxWmZFM1l1aTk1NXdIVmI2ZkE9PSIsInZhbHVlIjoiNUlRMDE0aWJYYys3ZThxMzBMQUdpTksyRURscnl1aHhxRVhEWWVOWVF3NjVHVTZ2YW8rZ0w1aExtWVYwdVRlWGpEK0s2VHk2UktlK24rc05EOUhoc3p1UGtCbm9xSnkwUWxuTTBURnJjaGRHUVNEVlRnZ01aRGVMQ2R6K3d3Nm9qVFJ6QjdFMWpveDZRN2FzR1lQSGhqall6U2J0Y2s3YmE0alh6bjZVekI1eFRJTU16ZDgrU3BtbWxFTmNrem5LajRYblRXdCtzdHhVY1cwK0M0bkVvWE1XVHdpVE5ycWVvM0ZsalN2MDBIdDkrSDJEMUpCSHpnT2tZNURIMmQ0RHQvUUhxS2VrTWhiWk5ibEZPdE1XWmFPNnpOeCtzSnZiUHVzVGVJYWpFWVdVbTVFN1BTZ0VPK3lOR0xuT3dsZ1N4TGZMdTN3REw0TkViSFNidGM2ZU1sTzkwZSs3MkhnSHFPemF0YkxUZWU2V1ArZVd3SWxBcHg3UDIxdVJobzM2Z08zcEF5bmQ3UXA4U0JTWDdBVnUvZXV5dUo0YS9kejY1LzZvNmd1ME1UdlFlWkhEWkFoUkM2dEovYnR6QThqbStlTkVheHpPYnJjYlo3WUx3WWZzVVZjRC9EZ2ZtcDNFdDkwZEVPUDE3VkpKK2hWaHJvMkRsZWdtWkh0Vndid0siLCJtYWMiOiIzNGE2Zjk1MjE1ZmIyNTVjNDE4OWJiMWFjNzE4ZWEzNDM1MTNjMTVmMjBmYjQ1MWU2MDNjZDk0ZDYxYjczOWViIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:22:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNMM1lCR2hLV3ZRVW1zUTlQcDE0cnc9PSIsInZhbHVlIjoiWjhSQmZveWZqVjl4aU10b2xOU1JUVEVqS0UxSEZmNUJZRG85QVJBVnhXUW9GM2NaZjdmS2dSQW1PdXBWcDB0cE1SaklHNHdlY2JnWVRSRlVIczRIN0I2Z0NjYWE3VjJwcHpxNEpUMEYyd2ZDM1QrNDhRMWsyOHpTUW1lNzFJbXRNalp2YmlqeWZkRW1YalNXZGNnZ2hMZ21TN2tuT3pKOXdtY1QyZmkxcnNOMVp1TmJnQ01jVFZQTXhmQWlhNUc4cXFPMnBtYXdpNm9CMVl4M25LV0tsR0RxdFRMd1JYM09hL0p3RWUwVlJ6bER1QWJQNXRmVTdKNy9zajZmaFpDZHEwMXhJN2hyMXMwTDg2MG9FNE1KV0JxRlgyRkN2WkkwZUR3Mm9MN05pd3NHcThoSGgrbkNpWHNRaDZMSW1vaDBNTVQwajJSMTVTSlRNcElSdC9zQ3lldG0rNmM0Qi94RXl5UWR1c01ZOGF6aHFOVUFoN2czN0FSYW1FQkkwS3QzTUhScStRU3R5Z3FYUXFlZFg0K1RwN3N3dXQ0dFpVWkhiU0tRS28zYS9nLzMrRGxFRGhXWDNlL0l6TlFIcXZjWFdScVZwNDd0ZXlCT2QycTRYZWl5WU1QNy9LbHEzcmtnY2cvdTM3MTl3YmlENW9lR1o4bCtrNW5mVTEvM2FDR0oiLCJtYWMiOiI0NDljMjRlMDhkNTFlNzZiMTZkNTFlNGE3NDE2MGIzOTVjMTUzZjU4NGFiYjkwZTg2NDVmODRhNGE0YWNkMmQ3IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:22:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFHTWgxWmZFM1l1aTk1NXdIVmI2ZkE9PSIsInZhbHVlIjoiNUlRMDE0aWJYYys3ZThxMzBMQUdpTksyRURscnl1aHhxRVhEWWVOWVF3NjVHVTZ2YW8rZ0w1aExtWVYwdVRlWGpEK0s2VHk2UktlK24rc05EOUhoc3p1UGtCbm9xSnkwUWxuTTBURnJjaGRHUVNEVlRnZ01aRGVMQ2R6K3d3Nm9qVFJ6QjdFMWpveDZRN2FzR1lQSGhqall6U2J0Y2s3YmE0alh6bjZVekI1eFRJTU16ZDgrU3BtbWxFTmNrem5LajRYblRXdCtzdHhVY1cwK0M0bkVvWE1XVHdpVE5ycWVvM0ZsalN2MDBIdDkrSDJEMUpCSHpnT2tZNURIMmQ0RHQvUUhxS2VrTWhiWk5ibEZPdE1XWmFPNnpOeCtzSnZiUHVzVGVJYWpFWVdVbTVFN1BTZ0VPK3lOR0xuT3dsZ1N4TGZMdTN3REw0TkViSFNidGM2ZU1sTzkwZSs3MkhnSHFPemF0YkxUZWU2V1ArZVd3SWxBcHg3UDIxdVJobzM2Z08zcEF5bmQ3UXA4U0JTWDdBVnUvZXV5dUo0YS9kejY1LzZvNmd1ME1UdlFlWkhEWkFoUkM2dEovYnR6QThqbStlTkVheHpPYnJjYlo3WUx3WWZzVVZjRC9EZ2ZtcDNFdDkwZEVPUDE3VkpKK2hWaHJvMkRsZWdtWkh0Vndid0siLCJtYWMiOiIzNGE2Zjk1MjE1ZmIyNTVjNDE4OWJiMWFjNzE4ZWEzNDM1MTNjMTVmMjBmYjQ1MWU2MDNjZDk0ZDYxYjczOWViIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:22:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-732091332\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-622444280 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-622444280\", {\"maxDepth\":0})</script>\n"}}