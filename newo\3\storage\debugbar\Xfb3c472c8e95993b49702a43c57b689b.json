{"__meta": {"id": "Xfb3c472c8e95993b49702a43c57b689b", "datetime": "2025-06-17 07:12:56", "utime": **********.960678, "method": "POST", "uri": "/pricing/update-inline", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.360946, "end": **********.9607, "duration": 0.5997540950775146, "duration_str": "600ms", "measures": [{"label": "Booting", "start": **********.360946, "relative_start": 0, "end": **********.823982, "relative_end": **********.823982, "duration": 0.46303606033325195, "duration_str": "463ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.823993, "relative_start": 0.4630470275878906, "end": **********.960703, "relative_end": 2.86102294921875e-06, "duration": 0.13670992851257324, "duration_str": "137ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52050752, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pricing/update-inline", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PricingController@updateInline", "namespace": null, "prefix": "", "where": [], "as": "pricing.update.inline", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPricingController.php&line=141\" onclick=\"\">app/Http/Controllers/PricingController.php:141-224</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.01942, "accumulated_duration_str": "19.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.863511, "duration": 0.01263, "duration_str": "12.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 65.036}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.887261, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 65.036, "width_percent": 3.553}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.909703, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 68.589, "width_percent": 4.428}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.913182, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 73.018, "width_percent": 4.428}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '5'", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.926544, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 77.446, "width_percent": 3.759}, {"sql": "select * from `product_services` where `id` = '5' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["5", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PricingController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PricingController.php", "line": 169}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9311159, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "PricingController.php:169", "source": "app/Http/Controllers/PricingController.php:169", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPricingController.php&line=169", "ajax": false, "filename": "PricingController.php", "line": "169"}, "connection": "ty", "start_percent": 81.205, "width_percent": 3.038}, {"sql": "update `product_services` set `sale_price` = '10', `product_services`.`updated_at` = '2025-06-17 07:12:56' where `id` = 5", "type": "query", "params": [], "bindings": ["10", "2025-06-17 07:12:56", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/PricingController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PricingController.php", "line": 207}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9454508, "duration": 0.0030600000000000002, "duration_str": "3.06ms", "memory": 0, "memory_str": null, "filename": "PricingController.php:207", "source": "app/Http/Controllers/PricingController.php:207", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPricingController.php&line=207", "ajax": false, "filename": "PricingController.php", "line": "207"}, "connection": "ty", "start_percent": 84.243, "width_percent": 15.757}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-837640825 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-837640825\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.918804, "xdebug_link": null}]}, "session": {"_token": "TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]"}, "request": {"path_info": "/pricing/update-inline", "status_code": "<pre class=sf-dump id=sf-dump-803299665 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-803299665\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1158287309 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1158287309\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1555502505 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n  \"<span class=sf-dump-key>field</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n  \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1555502505\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1549553089 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">78</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=17n9b1e%7C1750144374665%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkU4Ym55NkY2L3pvdXNrUVpCTzMxK1E9PSIsInZhbHVlIjoiTDZETjhUY1krQjhaMk9Bd2l0ZEJoTVR3OFd1ci8vd0RVTXoza2hKbUJ2TldPeGpidFdNSUMydkpWTXdpMHp2UGhHc3FWdXdjS01TTWdrN1BQZHR1bEkySGpPdFBrWG41VkN1ZWkxWFArQUZGTnpXbTl6QndkcDExWHhDbUZMbnJnOTBHcGdLOVJJcHBrZ09BR2F5cG5kY3VZR0dHa0RZbTg5Ykk3WjNIaWFzNFNOUFZGdnNxeEY4eUgySDNoZXFFVWZ3cGVUeEgybkhoRlRDR215cG8wV09YY2xCcEo1VFQ5QlhaWXdyV1FHYnFwakdua3VoV05GWEFGS0VyQ0Y3cFpQRDF4VC82N2N4b1plZ2c1N05WdDhYUHdXYU5rYUU3VU0zMTFhdVpVdFU3aUhxUXQzTTFvS0pRUXdUMDJzOGpGSCtqV1pDd3BFdmZwZFZtdVFCbmltZU5jcDk4dDRqQ3I5K1BXVDRqcWFFMUl2ME4zeVRBNjRLNDhOS09sTHdnVkZsQTZhdEtmWXViNlpGeUc1ZjVUUWF3VGVNTmFJZkpaL21CLzhGdDBGMnppWGQwUEgvUTBGU0Vvd1drMUMvUE1uWUlvZ3RueC94T1pGWUtkOGJXYWFaMXNlc1pVb1BiSXdnSXpwYUFaL25pV3o5bVFybEs5TnJQTW1vU2dqcDUiLCJtYWMiOiI5Zjc0NjZmYzJhMzc2ZGRkYzZjMGYwNTM5NzIwMDMxNWY3NmY5ZWQ0MTQ5MWZhODVmZDUxMGQ1MDliMWQ2M2I0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImtwdkRhSFd6NmlGMHdCVFZkcUlwYUE9PSIsInZhbHVlIjoiMFdtUXpKQ0pFViswZ1V5YVVWRWlwZGRpOVdiVzMwRTJDZ1dxWmZJZ1ZNbXFXblpBeFo1SitrWlJaRHdCK1l6ZmRxSU9HOS9aTVc0clc0MElySjZFT21uRkJpYVhxamVIWWo4ckNoNXRQSVZRTVZqRFRvSE1oNHUxTWkxM3UzMWdHcWNXUXgzN0lYVGtXRERLZHoramNOc2NyclVxVjk2NG9MQmZjMzhXL1dFSjRWRkRBelFlODl5NVZiMUIvRlFLQ0R0SEUybHg3bnpKQTNVdzE4cy80SVNsK1RwZlhVeVpvUnMwemFFWTF0KzJrY3VDaktPdUZxRjBxVGRYZDYvbC9nTjJRc3EzY2NGU2twbUpLL3lnTExvTXZmc3k5V28yTmZaTzZiTjlrV3M3T3podWdUR1c1eHlUYmV4ZW12YU80M3BrYUVwM28rRFFtd05xWnljQmd1b25yOHRVR21xdTNyUGNEVkd0dDhCUUFGeEplcEZyd2pUZ1hRYnMxTi8vSVhUeHhhV1Z0MlFHR1llSEFMbnBGaFQxKzBJWGtsQkJ3dVVBZkNkTTlqUU1FNGd6WjQ5N1BsNzZIRUwvVmJLdlZTSzVjczMvYVliRU5LSk1KNjVhOXR4V0ZZWTFQL3dVazZMZ09xQmQzcXFiclYyRElERlZFalZEWHdkZEd2THciLCJtYWMiOiI5M2RlZGNmM2EwYTM5ZGRjZTFhMjQ2YmMyMWI5MGJjNDg5MDlhMDgyNmIwMDA0OTBmODAwYWNiYzYyYTg3MTZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1549553089\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1415558736 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDWN9h6R9gAIdzZaXBzXfq0WGpauVfrUj70kKWA2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1415558736\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-797653284 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:12:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjcwVjFaTDFwNlpKSVd1a2I2TWM5Tmc9PSIsInZhbHVlIjoiZ3JlNWI1RmRGSXBxVDdIRG8xR05mc0JZZW1DNXBnYmI1eFo2a3JRS0t3eC9BcFBVMmtKU0wvNmdGcGk1S1hYQ2JXTkZzTGl2WDRUeDZiTkJ5OEczOHVPY3NRNGI0LzNPUjNkK1haQXE2cGovc1JYNkErc1FBR1VFS3dLVE5pdzVKbmc2aEZzMEhxVjJkWkorVjdWUFhMZUx2L05Dd1h4K1lEd3JiWVlYY2F6ZFF2cGRrSTBGYjhzU1c0K2ZFUkJiMmMzUXhybTBRM2Y4Y2dkYy9iZ3ZjNGRrbkNiaW5PU3RXTHpvVEFrSE13QXFMN3VBa1g4bVZPc0lkbU55d292UG45Z3NXbytJMlVTaDExSzNGVWNaSkpGa3hBMHNNVGR3YTVGSGJkSnQwZWhsdVNFaTB2RTBHOTZ0OVRQMjRLcDBnWTJsTDZUdE4zYk1pRXMyWE9LR29DS2FzWjV4Tm1TK0I1S3dwc1U4OFZiWFpGUHBkc0xjYlFkMnNmVnVUZG5kOXVVTFZXdlp2bFlkVkJ5T01KdktRL0FUZUJIZXlWLzBSaXpleGlqYUk1RitrZmpLbVNhVmFvWi9BS0ZXb25nK0RiUmt3U2JxdThPN1NJcy95Z0dFdDNJWWFLZmE3a3l2ODdBNVJxZk81OXVsbzBwUW5KOUQ4amxGMHZHa3JwakMiLCJtYWMiOiIxNDIyYTBhZDNhNDNjOWQ2ZGM2ODUxNzhmNGU1MTNmOGZlNTk5N2FkMDU3NWE3NTExZjY2NTQ4ODEyMzA3ZTU5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:12:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkU3clJ0MW9JWThiRGwrZ0ZaNzVmaEE9PSIsInZhbHVlIjoiZVFPTEtSZndCbGdQcXg5YnFZT2UrUVpOVEpWc3lMbTVoUE5zdEFhbFhnQkttT0RYTE9zbnpDK0owbVZrd2w0WVo1ZGsvTkVITnVJWVBUMUlDclV3SXhLaU9uUE5CVE5pQ3hmbk9vckRVQkRlbW44UG4xZ0N4TmFaWlBmSjV1SnBIbkNQOXYxakZzR2MyRUd6RnlTSDVqVEl0T0Y3VEd0UjY0TzRqc1EvWmJvMFNCK0xVeHdoSUsrY01mUGJYTEdEeTNvWDhmSW5oNnAzeU1abmJWQWpNUGwzRW9MQkUweWM3aTdDbklsb3hoVHBCc2JiU3Y4MUdTY2tCVWY4RDFwV0drSm9oRlFRS3B3dUZkUWxqbE5FdmhVMHRVVnVLdUtmdVF1UEptS3E4N1JGTGpVZEdaQ0xhdDJNb3M1VGFhM01wdnp0MU4yYjFGczFZemdXc245SnRORCtLTEJUdGE3Y2xTL3ZLNDVxbzNjcVcxK0tvbGFDMnJLazBkbTFERnBXc3pHOGVpKytLWldSckdVRElXQmlKdHVSbWtzQk5iS0Radzg4VGVuUWNxMDVYeXNXK2N0NFpnTzdHakJMZlA2cTB3VE5waldEaTErZWpDRjAxdllRRWRaOGdDWENtRFd2WDNKMVZXNW9kZnkxUU1IY0ZQMVBQdnRRZ2dJQjJTY3giLCJtYWMiOiJlM2I2NDI2NGUzZjY2N2YwNTNjM2ViNGM2M2RjZmJiOGI2MmMxMzYzNWY5NTNiNjAyMTU1ZWNhYzM3ZWZlNzZjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:12:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjcwVjFaTDFwNlpKSVd1a2I2TWM5Tmc9PSIsInZhbHVlIjoiZ3JlNWI1RmRGSXBxVDdIRG8xR05mc0JZZW1DNXBnYmI1eFo2a3JRS0t3eC9BcFBVMmtKU0wvNmdGcGk1S1hYQ2JXTkZzTGl2WDRUeDZiTkJ5OEczOHVPY3NRNGI0LzNPUjNkK1haQXE2cGovc1JYNkErc1FBR1VFS3dLVE5pdzVKbmc2aEZzMEhxVjJkWkorVjdWUFhMZUx2L05Dd1h4K1lEd3JiWVlYY2F6ZFF2cGRrSTBGYjhzU1c0K2ZFUkJiMmMzUXhybTBRM2Y4Y2dkYy9iZ3ZjNGRrbkNiaW5PU3RXTHpvVEFrSE13QXFMN3VBa1g4bVZPc0lkbU55d292UG45Z3NXbytJMlVTaDExSzNGVWNaSkpGa3hBMHNNVGR3YTVGSGJkSnQwZWhsdVNFaTB2RTBHOTZ0OVRQMjRLcDBnWTJsTDZUdE4zYk1pRXMyWE9LR29DS2FzWjV4Tm1TK0I1S3dwc1U4OFZiWFpGUHBkc0xjYlFkMnNmVnVUZG5kOXVVTFZXdlp2bFlkVkJ5T01KdktRL0FUZUJIZXlWLzBSaXpleGlqYUk1RitrZmpLbVNhVmFvWi9BS0ZXb25nK0RiUmt3U2JxdThPN1NJcy95Z0dFdDNJWWFLZmE3a3l2ODdBNVJxZk81OXVsbzBwUW5KOUQ4amxGMHZHa3JwakMiLCJtYWMiOiIxNDIyYTBhZDNhNDNjOWQ2ZGM2ODUxNzhmNGU1MTNmOGZlNTk5N2FkMDU3NWE3NTExZjY2NTQ4ODEyMzA3ZTU5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:12:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkU3clJ0MW9JWThiRGwrZ0ZaNzVmaEE9PSIsInZhbHVlIjoiZVFPTEtSZndCbGdQcXg5YnFZT2UrUVpOVEpWc3lMbTVoUE5zdEFhbFhnQkttT0RYTE9zbnpDK0owbVZrd2w0WVo1ZGsvTkVITnVJWVBUMUlDclV3SXhLaU9uUE5CVE5pQ3hmbk9vckRVQkRlbW44UG4xZ0N4TmFaWlBmSjV1SnBIbkNQOXYxakZzR2MyRUd6RnlTSDVqVEl0T0Y3VEd0UjY0TzRqc1EvWmJvMFNCK0xVeHdoSUsrY01mUGJYTEdEeTNvWDhmSW5oNnAzeU1abmJWQWpNUGwzRW9MQkUweWM3aTdDbklsb3hoVHBCc2JiU3Y4MUdTY2tCVWY4RDFwV0drSm9oRlFRS3B3dUZkUWxqbE5FdmhVMHRVVnVLdUtmdVF1UEptS3E4N1JGTGpVZEdaQ0xhdDJNb3M1VGFhM01wdnp0MU4yYjFGczFZemdXc245SnRORCtLTEJUdGE3Y2xTL3ZLNDVxbzNjcVcxK0tvbGFDMnJLazBkbTFERnBXc3pHOGVpKytLWldSckdVRElXQmlKdHVSbWtzQk5iS0Radzg4VGVuUWNxMDVYeXNXK2N0NFpnTzdHakJMZlA2cTB3VE5waldEaTErZWpDRjAxdllRRWRaOGdDWENtRFd2WDNKMVZXNW9kZnkxUU1IY0ZQMVBQdnRRZ2dJQjJTY3giLCJtYWMiOiJlM2I2NDI2NGUzZjY2N2YwNTNjM2ViNGM2M2RjZmJiOGI2MmMxMzYzNWY5NTNiNjAyMTU1ZWNhYzM3ZWZlNzZjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:12:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-797653284\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1836888658 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1836888658\", {\"maxDepth\":0})</script>\n"}}