{"__meta": {"id": "X37bc0aae43293c7544b3d5c9b67962a7", "datetime": "2025-06-17 07:13:53", "utime": 1750144433.051629, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.208649, "end": 1750144433.051653, "duration": 0.8430039882659912, "duration_str": "843ms", "measures": [{"label": "Booting", "start": **********.208649, "relative_start": 0, "end": **********.932328, "relative_end": **********.932328, "duration": 0.7236790657043457, "duration_str": "724ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.93235, "relative_start": 0.723701000213623, "end": 1750144433.051655, "relative_end": 2.1457672119140625e-06, "duration": 0.11930513381958008, "duration_str": "119ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45156424, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02584, "accumulated_duration_str": "25.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.985563, "duration": 0.02436, "duration_str": "24.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.272}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1750144433.027071, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 94.272, "width_percent": 2.941}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750144433.038051, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 97.214, "width_percent": 2.786}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-681435603 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-681435603\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1901293145 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1901293145\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-980860752 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-980860752\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1685417964 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=jfj0s4%7C1750144421803%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJlT09IRU95aTJuUkVhcTZjTHBybVE9PSIsInZhbHVlIjoiTUZLaktOQWpqRmdzRXRxZ2N5UnFCeGJjUVVCWEltUFQ2UVBxb1F0elpkYjdFL2VuYzhnQ3Njck5Za2YzSXlqMUdhWGErNlZHOWR5TDJvbU1meHU4aW14c2xOdmFXWWJyR1RVQ3RPSEZlOUxFeFBUMTZ3OGpBZTdweENiS01JdnVHYSsvRmZxSGlJUk9OKzB4OW5RMzVudUMyUEkxdE9lSXg2SndmUjB5V0lJSmo3emx5enN4eE1MbVQ0OGNOaXl1elpPclc5UWVrR3JBdHhsRy9TdTI2Z1lmZkwxWTFaWFlnbHI1WDZpc3pIbmRkMC84eW5DdFVMSy8rTDNQS080UEJMTEFJYjRMcVAzaXhPZHNVamtxb0hGMWhMVU5wNWJSd1oxQy8vZDBtN1llbXlrbDVpZ2tFTmRQSU5ZZDJwQ1JDMk5ncXE1SFVQNVFpTGJkaGVXVVppU2UwRkRkNzhPQjU2cmJ6UE9zVFF0OUZ3S1daVW9Pd2pJTHVKUG5ybDJ6aElNeWRmNTM3Y29KNnI4T1VKV3YrZ0xJemx3dEZPblNZUzNoV09QTW9CdE54YTVaa2JudFVhbzhXb2JsNGRNV0lTcWl1UHVSZU9wcTV5bG5CQStSYnZnVFNLNGxCYUVUU2ZwNEp4c0t5alhaRTNLenIwU0w1aklyMjNIeWNkeTgiLCJtYWMiOiJjYmJkOWM3OWNiODJkNmRiYWJlNzgzODFlOTMxYTBjMTE2N2JmMWI1MTQ3YzVkNzc0ZGIxZWYwYjMwNDM0MWI0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImxhU3NnS1NYbjEwb3BQdGhDNVpHUnc9PSIsInZhbHVlIjoiYTE4b2pBSHdtelZaK04rL2VtRmU0S2J4dHpWTmgwdVJRUVpJSi94REhHUnZqWkhWL0ZaOEdwYjVrNXREN0pjRkJlUXQzaTlKeUwvc0xlTmdsL1NzNkQ4MGlrNURGOHBLbVRmUkVSUEFOVlozWHZLd3JsYVA4RWQ0aWZTS2N0OGdBUEE4Z0d3OHl3Ni9ERmVINnZLT3RCLzQxSVM1ZUd3MWZJUndzbjdXd1d5bGVyeWxyUTc4Y0xUZzRzZ2hYUENlcDNUNUxSZW9qOEtLVHVEditFZDY5V0hwcXMvSzhKNnlCcUtTNnBTMkl6U3YvaWFOdm5PUHlBemp1QnZiczlWYVZQV285SWhCSnpuTE11UFZmOFViOW5Mdm9TUnc0NU96QlQya3JCQWtySk9yRnpacXFXaG8wRXg3TE82ejdVbzFhMXRMcjNsTUxoclQ2L0QwSTNkaGE4NlpJeWRpZWJNSnhKRE1kbTVaeXdEdjJLK0dOSHdOb2haOVlLN2ZPeEVoTzVUMzNpS3FsaEFmZ25hNVF0VlErdUhEVDBwVEUvS3JwdmR3VnltRjVMbjdERlpvQkJZVWlzdXRxR2s3ZVJZc2dKcFFobDAxR2FnWUVTMjhyK29tTHBNdDA4VzJQNkMvSlJ0OEtKNGFvL1F1cjhGdnhqRDhpcDYyUGtSOG4wSU8iLCJtYWMiOiIzMzQxODQyZTM0MTI3NmRjZTY3NTQ1MTRhNjBjMzUxYTBhZWY1NjBjNzNiNjQyZDhmMGQ3ODQyZmFmNGExZTNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1685417964\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-36607965 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-36607965\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1885218763 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:13:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkorNFozc2JHYURCVnNSMXIydlM2L0E9PSIsInZhbHVlIjoic1RNRjlSQ2liRFFDOWM4ZkdRVkhCZkx4dEpqYTVPVUh4NWs1c1NFNno1c0pYNGlCQ2Q5cVVQc2RtWDAyWTZoMzZ3K2Z2V3kxaTN0SE5adTh0QUZpQmtIaDNvaG13N3k0SjkzTW5oR0ZXNGRPM1Z1WFZ5MnRxVjgrKzQ3T2hNWEcvZTdQejU1ZmZzbEluRUZHaTZnVzQxS0o5RjJjM2l0VkppZ01sTnJjNHJ5RHl5WDBGc2hZTHpOS1BFbGM3c05JRVpGdFhMdG9YSFN0c1dja2tXcmR5YTQ1eEFna3dlWThFVkFYVURCc3dRWTVtK1ZJYzhBVDJGNzBsVndaR3FTbjcxZmdENDdPOEVtZWVrWEovZHY5b21WaWY3bFlueGxubklNSXhNdUpsaE43K1YzdHc1cEprcTZIWk1qanRiNzRpVys5aW8ydUdTL3NyVDRnZXRlQTFuejZjczl5Ukhzb3BkNEh6Yko4aEVHV1VnUzlRVTI0aFIyRHpiQ2xINFBxVkNNcEhkOGNLWmNJaGtuZEdLSGJCK3NrdG00NlUvUXlvMG1GZkpxME93RHczRlhkYjJvUnpTWHJuSzY0Z2hIY0VXTENsUDZEeGlMTDZsdDA0U3RMUjFQQ0Y3REJOK2xxR1BrRy95LzJUL0VxZFdJUFhWc0xwOEtaRytsSmZIWkwiLCJtYWMiOiJjNDU0NjJhZjE5NWMzYTBjYjY2MDczMmFiMGM2ZDA1NWU1M2VjODJmOGEyMTcyNGYzZGY5M2JjZjNlMGY3YTc5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:13:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik9MdmRBdFo3REM0MHQremJFWkFNaEE9PSIsInZhbHVlIjoiL2FkaGNScmo5ZWZPMFVHYUVCZFcycEZIVWlSTjN6V3RuS0cvcHhhNi8rZ2tsVHRWaUg3NzhaM3dRcXdHWmZRUGw3N1cxRjd3ZGpjY05hT2hHUlRYaTlUTXRGRHAxaE9SV0hjVVVlejM5MCt2YU04NGs1QU0xNlo1b2VQRFJ5elhOdEVOMjUvWVZ0SDc4YXN5S3BCVkVyd0JYa0NpTWZKcGhwUXpxc05NSWluTDFLV0ljMjVBTk84cjdQUjRPSVpFbjVJbG9BTWg3c241c1MxUFpYbVRaclh2ZjdQVUgzQWFkdEg4dHgxbTA5N0tyeG9Nc3pYK3c5c0l4cW5XeVRSV1ZFMEt0TktZZVk1cEJzR0Zrd21NdzJMSEJmeklMNmltTC9RakRuejVJaHJEV1F1N2pJRWt4Z0tab21aL1lrOEJkS1BscXZhM1U3T3RxRWUyOTJ0eDVJN250bEhtM2Q5c3VVcHk2MTE4d3NJbFJCdDhFc1NzWGxZNzJLdFZub0R3eTJtSytMaDZXeWd3OXdkTEJkQkpmczg0Rmg5TThNSkJKdWljZUpQcUcwajUvaGRxRXRRbDBudkdRcUJCZCtVVVk3YWVKMUw0UXVIaG9uZjV4b1kraFFlTDhGOFBHbmRnT1d5RDhzWFVHMDRxdGZ4YVZVNzczdDVzZUFRZlBIZXYiLCJtYWMiOiJlMGM1MmE4OGE5ZDc1MDVlMjUyMTNhYTI2M2Y1ZDQ1NDVjMzMyOTcxMjg3ODQzMDFlM2IyMWMyOTBkNTE0MmRjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:13:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkorNFozc2JHYURCVnNSMXIydlM2L0E9PSIsInZhbHVlIjoic1RNRjlSQ2liRFFDOWM4ZkdRVkhCZkx4dEpqYTVPVUh4NWs1c1NFNno1c0pYNGlCQ2Q5cVVQc2RtWDAyWTZoMzZ3K2Z2V3kxaTN0SE5adTh0QUZpQmtIaDNvaG13N3k0SjkzTW5oR0ZXNGRPM1Z1WFZ5MnRxVjgrKzQ3T2hNWEcvZTdQejU1ZmZzbEluRUZHaTZnVzQxS0o5RjJjM2l0VkppZ01sTnJjNHJ5RHl5WDBGc2hZTHpOS1BFbGM3c05JRVpGdFhMdG9YSFN0c1dja2tXcmR5YTQ1eEFna3dlWThFVkFYVURCc3dRWTVtK1ZJYzhBVDJGNzBsVndaR3FTbjcxZmdENDdPOEVtZWVrWEovZHY5b21WaWY3bFlueGxubklNSXhNdUpsaE43K1YzdHc1cEprcTZIWk1qanRiNzRpVys5aW8ydUdTL3NyVDRnZXRlQTFuejZjczl5Ukhzb3BkNEh6Yko4aEVHV1VnUzlRVTI0aFIyRHpiQ2xINFBxVkNNcEhkOGNLWmNJaGtuZEdLSGJCK3NrdG00NlUvUXlvMG1GZkpxME93RHczRlhkYjJvUnpTWHJuSzY0Z2hIY0VXTENsUDZEeGlMTDZsdDA0U3RMUjFQQ0Y3REJOK2xxR1BrRy95LzJUL0VxZFdJUFhWc0xwOEtaRytsSmZIWkwiLCJtYWMiOiJjNDU0NjJhZjE5NWMzYTBjYjY2MDczMmFiMGM2ZDA1NWU1M2VjODJmOGEyMTcyNGYzZGY5M2JjZjNlMGY3YTc5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:13:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik9MdmRBdFo3REM0MHQremJFWkFNaEE9PSIsInZhbHVlIjoiL2FkaGNScmo5ZWZPMFVHYUVCZFcycEZIVWlSTjN6V3RuS0cvcHhhNi8rZ2tsVHRWaUg3NzhaM3dRcXdHWmZRUGw3N1cxRjd3ZGpjY05hT2hHUlRYaTlUTXRGRHAxaE9SV0hjVVVlejM5MCt2YU04NGs1QU0xNlo1b2VQRFJ5elhOdEVOMjUvWVZ0SDc4YXN5S3BCVkVyd0JYa0NpTWZKcGhwUXpxc05NSWluTDFLV0ljMjVBTk84cjdQUjRPSVpFbjVJbG9BTWg3c241c1MxUFpYbVRaclh2ZjdQVUgzQWFkdEg4dHgxbTA5N0tyeG9Nc3pYK3c5c0l4cW5XeVRSV1ZFMEt0TktZZVk1cEJzR0Zrd21NdzJMSEJmeklMNmltTC9RakRuejVJaHJEV1F1N2pJRWt4Z0tab21aL1lrOEJkS1BscXZhM1U3T3RxRWUyOTJ0eDVJN250bEhtM2Q5c3VVcHk2MTE4d3NJbFJCdDhFc1NzWGxZNzJLdFZub0R3eTJtSytMaDZXeWd3OXdkTEJkQkpmczg0Rmg5TThNSkJKdWljZUpQcUcwajUvaGRxRXRRbDBudkdRcUJCZCtVVVk3YWVKMUw0UXVIaG9uZjV4b1kraFFlTDhGOFBHbmRnT1d5RDhzWFVHMDRxdGZ4YVZVNzczdDVzZUFRZlBIZXYiLCJtYWMiOiJlMGM1MmE4OGE5ZDc1MDVlMjUyMTNhYTI2M2Y1ZDQ1NDVjMzMyOTcxMjg3ODQzMDFlM2IyMWMyOTBkNTE0MmRjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:13:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1885218763\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1741897931 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1741897931\", {\"maxDepth\":0})</script>\n"}}