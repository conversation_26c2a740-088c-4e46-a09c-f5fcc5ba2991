{"__meta": {"id": "X6891883993450d9499deb90345fcb896", "datetime": "2025-06-17 07:05:16", "utime": **********.691671, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143915.829225, "end": **********.691697, "duration": 0.8624718189239502, "duration_str": "862ms", "measures": [{"label": "Booting", "start": 1750143915.829225, "relative_start": 0, "end": **********.565393, "relative_end": **********.565393, "duration": 0.7361679077148438, "duration_str": "736ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.565407, "relative_start": 0.7361819744110107, "end": **********.691699, "relative_end": 2.1457672119140625e-06, "duration": 0.12629199028015137, "duration_str": "126ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45156400, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02673, "accumulated_duration_str": "26.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.624611, "duration": 0.02476, "duration_str": "24.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.63}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.663488, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.63, "width_percent": 3.255}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.674944, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.885, "width_percent": 4.115}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2113418317 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2113418317\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-549722853 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-549722853\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1508470016 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1508470016\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1859024665 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=jfj0s4%7C1750143911623%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNtWVVZV0RsdXlTazBpZUdNNWlhbnc9PSIsInZhbHVlIjoiNmRJdm5BaVpZRStIOUFta3NuU2JVYUtxS2RZVDRqdzZoeGg5NmkySHozemFBTnFwOVdNY2IvV0JwNzQ4R2s4d0RDQjl3UE1nYTExeTF4dk5RMHFOekNSbUFGYlNpUTN5bkl2dVlUSXZZd0tLN1puemowMGNwdzJxYXhzdElxN3Q3MGZPbjFkRnJGUjlQdk54MG5MYUhWYS9iRCs5T0E0ekZKbGNoR0cxUGVQVFZ1WVB4bzlHVENpdjFDaGJ1YkFSdjh2cU9WR3RrS000MHFScEtESnhVS1dMU0ZFZnVORk13TXNFMlhoQmFQZ3hlcnoyWWlVU25rcnhEY3BxS3EyOCt3QTAwdktiTTB5enFQbkEwRUFaVUtwaFFjNFJ3OEt3NFJnV2V0M1VIS1ViUnZ5SXdhNTZpK2gwS2RJT2t2WE9lQ2xwSHo0d3JhUmJXRjQ2bXk4dFpzdytqUnB5RC9YOVhhTUxHMkhiT0Fhamdlb2QvV0VaeUZIRXc2M093c0puRnZ5K2FuSWE5MERpRWo1TWdpd2VBY2svLzdYSENTcG1GUUMrbFhNSW11ZHNBRnpmUWRNY2VpUitjcTRKTnpwa2RKeG83d2dCQ0xKN0Y0b3RiL1NjbUVWSWVjMXdDem1qT0NKbTFDSkFCT1NtWktiT0hnZzhrOFpuWlEyR1A1bWUiLCJtYWMiOiI0YzM4YzAwMWJiNTlhMzc2MjZhZTQzM2JiNGM1NzIwZGViYjJjZWI1ZDg2MmYyZDljOGUwZTBmNTAwYjYzNWI5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImNZZDhqR2xiUXJUaC9kYWpRWG13OFE9PSIsInZhbHVlIjoiT2xDVlZuekVFMS9GMCtGeDRLZmhtMnlFRGlSY1poRGFmRUdpQTFsR2loeGpJY2M0ZUFGM3FreC9WSFJBekczd0FPN3dJM2VlZEdWMnFCakNVeTZpdVdJRmI5UzZlOElHUldRZzdxb3FBUGtjS0REcEE3TnlZaVFnem40YVJyb0l4cS9MV25tRlNxSmV2WFRFNzgzY25HajJDWThDcnYvWVlkN3VwZHNmeDJCMWdRVTg4REtyallqYjhzckt4ZktBVUI5cVc1WFdXcjhtdnFTNkxJVHU4TEpia1dyT29sS1F6RWJCUXN6M3B3aTkvMnNRY3laU0psbzR4OU9aNDUvYy9qbXo2bEE0ZjVsS05kQjdGU0lWVUY4Ui90K1ArZ1kyZFdNSmtheStLU0RMU0tmV2JOeUMxNXM4WlZ3U1BYUDRDTHBMSTY1WEVEVFhreDA5Y2wvZldDd3BJejBSWnMwa0xhYTQrOU5GOUh5YzFQOThUam44ZkZmMGw0OUVsSG5CSExVVEtPTXhTcjJaa2NjT1BDTUhyWnJlR2N6T3VxbG5VakYwL0R4S2wxNm1WcVdsaFVibXVOOWloUTRnVWllVksxS2ZVNnRSeFRTbktoQnUxQ2NEQUlpYW5mcWtLSk81cDUxVmxudDN3MXp4cE01bFA2R1FSNGFGMlk2WllVVnIiLCJtYWMiOiI2NDNjZWFmZjg5YTViNTY3MTkwMGZmZTFjOTIwY2M4OTIxYjI3NTI0YTJlM2IyY2ZkOTAwYzVhMGIzZGJiOWFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1859024665\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1416594770 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1416594770\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1323442237 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:05:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1PYWR6WVZ1QmZaWmxoUUphM254eXc9PSIsInZhbHVlIjoiSXQzMWdKcG0wRVB5T1VESllxcEJBS3A0WnVLT2NkQWFiV05wOTZZTVB5amxGYVNOeC9oZW9uQWh3SHpTdFM0SVIwU3lXR0F6SGdRZ3NndENCbktSdGZiWGtRYWwrbXpJb0RYdmphNjZPRXhkWVRrSnMyQ2pOdTRwckx6aTFTSXNKT3NQWWNXdU1HdzNLQjd6Tmtrb2kxd1V1Y1JKZS9YdkIwNEl1N2h4a2lyQy81OUFVWHE3N3ZFSDJ1elhPdEVuRS9KSGFIMlMyZ3YwYlU3YkZSKzZlWUNPeUlCVy9TT0tqYzdZdHczN2wzNWhrdWo2TW82aXVzbHNNaE1RQk1ueGZUbWkwU1QzMFd0N0FydlQ3L2hHOG9aY3JIQVAzZkg4N3VTRmp2UmxKZ3JDMytsNGI3NEIva2FQaGVwT1pHQUJOMzM4OGJJbTU1L2FxT1Z5eFdCWHlvUWhSemRWV0JUL25FUWFxbzVxWkFvUTdNQlZVMmorWWw1bXdNY1pMY2lKaTZlZUhncVdBa0thRWE0Rm5jOS9VNWQzbk80MWFYVFA5TWhPZ3FGUEVrRWdFYUpxc0FDWkl4K3VkZlgrV2JsVnlTWWxRRlVreVRtNS9CVjNVWVpFcVU4WGFFRjZreDZKY3dnNnVsSE0vckQrL3F4aEhweDB5S3JaempPWmdwb3UiLCJtYWMiOiI4NzNlYzczYzYzY2U3NmMyMWRhNzA1Y2UxOWM4ZDRmYmIxOTY0OGJkNzk3MTI1ZjA0NDA3YjcwMzg3MDE2MGJjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:05:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imp2di9KdnlSalNWQUkvRmZ3eVpFeVE9PSIsInZhbHVlIjoiaG93bWFDL3ZUQlR0VFo3REVyVzViQXZjUFB1VTB6TG0wRndwL3Y2dzdBcXFEZlhyK002UTcyQzNGeEw5ak55UFFNOC96ZnprTHg3M240bHJkWXFOMmFzR1pvSjlKWWpUeDJrSFdmTGJmek9LZkRNSnNnL2o4STEyTjNWMnZPNFJOWXJONS9FWEtETnZhY0s5T05paVZRbnZVT2x0eU5TemcySXlMZUFVUDg5SmNDcURQak8rT2hFTjBwenIvVEZ6bFBldGFDVkZxRjVNTUV5QkhKd0J0ZnA2RUs0YnZGaTBtMFc3dzRNZEtER1BTRXdaSGFDck0xc2VTNVBMNzFRSzZqVU9JbnZab2czaCtGdU4xeHRCbFlnZVJzdlB4Ym9JcHJiNExzQmtLZzlqNmZQSitRNERZRTBmblViRC9qbzN0ZTJMck8vcWtaYXg3TkJsbEw1eWZ5M1JIUFVJdStCMHZkcXZSaFk3TExRK0c4KzJmL2svNG8rMm8yS2dhd2F4NldUY2tkOGU5c1F6WUx2OUJvM2VmWGZXV2RZOEpMVzBqY2xYZFBRV0RIaUNXT3pBNVQvWENZdXlzZVIxSytvU2FUUjlWMUd3ZWw3alZ6WG5iMHlMaGRKWlZ2dVpxNHRsMzFYNkFKMTVORWo5QjZMZ3FRbmhMZCtncjFVdFdNUnYiLCJtYWMiOiIwZTYxZDZmMWM2NDRkOTM3ZTcwOTgxOTJhNTI1OGQwNmRjNGVjYjljYmY4MzcxZmI0M2JjMjI4NTI5ZDM5ZmRmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:05:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1PYWR6WVZ1QmZaWmxoUUphM254eXc9PSIsInZhbHVlIjoiSXQzMWdKcG0wRVB5T1VESllxcEJBS3A0WnVLT2NkQWFiV05wOTZZTVB5amxGYVNOeC9oZW9uQWh3SHpTdFM0SVIwU3lXR0F6SGdRZ3NndENCbktSdGZiWGtRYWwrbXpJb0RYdmphNjZPRXhkWVRrSnMyQ2pOdTRwckx6aTFTSXNKT3NQWWNXdU1HdzNLQjd6Tmtrb2kxd1V1Y1JKZS9YdkIwNEl1N2h4a2lyQy81OUFVWHE3N3ZFSDJ1elhPdEVuRS9KSGFIMlMyZ3YwYlU3YkZSKzZlWUNPeUlCVy9TT0tqYzdZdHczN2wzNWhrdWo2TW82aXVzbHNNaE1RQk1ueGZUbWkwU1QzMFd0N0FydlQ3L2hHOG9aY3JIQVAzZkg4N3VTRmp2UmxKZ3JDMytsNGI3NEIva2FQaGVwT1pHQUJOMzM4OGJJbTU1L2FxT1Z5eFdCWHlvUWhSemRWV0JUL25FUWFxbzVxWkFvUTdNQlZVMmorWWw1bXdNY1pMY2lKaTZlZUhncVdBa0thRWE0Rm5jOS9VNWQzbk80MWFYVFA5TWhPZ3FGUEVrRWdFYUpxc0FDWkl4K3VkZlgrV2JsVnlTWWxRRlVreVRtNS9CVjNVWVpFcVU4WGFFRjZreDZKY3dnNnVsSE0vckQrL3F4aEhweDB5S3JaempPWmdwb3UiLCJtYWMiOiI4NzNlYzczYzYzY2U3NmMyMWRhNzA1Y2UxOWM4ZDRmYmIxOTY0OGJkNzk3MTI1ZjA0NDA3YjcwMzg3MDE2MGJjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:05:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imp2di9KdnlSalNWQUkvRmZ3eVpFeVE9PSIsInZhbHVlIjoiaG93bWFDL3ZUQlR0VFo3REVyVzViQXZjUFB1VTB6TG0wRndwL3Y2dzdBcXFEZlhyK002UTcyQzNGeEw5ak55UFFNOC96ZnprTHg3M240bHJkWXFOMmFzR1pvSjlKWWpUeDJrSFdmTGJmek9LZkRNSnNnL2o4STEyTjNWMnZPNFJOWXJONS9FWEtETnZhY0s5T05paVZRbnZVT2x0eU5TemcySXlMZUFVUDg5SmNDcURQak8rT2hFTjBwenIvVEZ6bFBldGFDVkZxRjVNTUV5QkhKd0J0ZnA2RUs0YnZGaTBtMFc3dzRNZEtER1BTRXdaSGFDck0xc2VTNVBMNzFRSzZqVU9JbnZab2czaCtGdU4xeHRCbFlnZVJzdlB4Ym9JcHJiNExzQmtLZzlqNmZQSitRNERZRTBmblViRC9qbzN0ZTJMck8vcWtaYXg3TkJsbEw1eWZ5M1JIUFVJdStCMHZkcXZSaFk3TExRK0c4KzJmL2svNG8rMm8yS2dhd2F4NldUY2tkOGU5c1F6WUx2OUJvM2VmWGZXV2RZOEpMVzBqY2xYZFBRV0RIaUNXT3pBNVQvWENZdXlzZVIxSytvU2FUUjlWMUd3ZWw3alZ6WG5iMHlMaGRKWlZ2dVpxNHRsMzFYNkFKMTVORWo5QjZMZ3FRbmhMZCtncjFVdFdNUnYiLCJtYWMiOiIwZTYxZDZmMWM2NDRkOTM3ZTcwOTgxOTJhNTI1OGQwNmRjNGVjYjljYmY4MzcxZmI0M2JjMjI4NTI5ZDM5ZmRmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:05:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1323442237\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-265172169 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-265172169\", {\"maxDepth\":0})</script>\n"}}