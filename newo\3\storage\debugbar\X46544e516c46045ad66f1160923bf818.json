{"__meta": {"id": "X46544e516c46045ad66f1160923bf818", "datetime": "2025-06-17 07:13:52", "utime": **********.886644, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.208037, "end": **********.886668, "duration": 0.678631067276001, "duration_str": "679ms", "measures": [{"label": "Booting", "start": **********.208037, "relative_start": 0, "end": **********.789369, "relative_end": **********.789369, "duration": 0.5813322067260742, "duration_str": "581ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.789382, "relative_start": 0.5813450813293457, "end": **********.886671, "relative_end": 3.0994415283203125e-06, "duration": 0.0972890853881836, "duration_str": "97.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45169128, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01972, "accumulated_duration_str": "19.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8312418, "duration": 0.01761, "duration_str": "17.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.3}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8639538, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.3, "width_percent": 3.854}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8731692, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.154, "width_percent": 6.846}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1160916388 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1160916388\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-736442148 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-736442148\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1612231663 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1612231663\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2092410051 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=jfj0s4%7C1750144421803%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJlT09IRU95aTJuUkVhcTZjTHBybVE9PSIsInZhbHVlIjoiTUZLaktOQWpqRmdzRXRxZ2N5UnFCeGJjUVVCWEltUFQ2UVBxb1F0elpkYjdFL2VuYzhnQ3Njck5Za2YzSXlqMUdhWGErNlZHOWR5TDJvbU1meHU4aW14c2xOdmFXWWJyR1RVQ3RPSEZlOUxFeFBUMTZ3OGpBZTdweENiS01JdnVHYSsvRmZxSGlJUk9OKzB4OW5RMzVudUMyUEkxdE9lSXg2SndmUjB5V0lJSmo3emx5enN4eE1MbVQ0OGNOaXl1elpPclc5UWVrR3JBdHhsRy9TdTI2Z1lmZkwxWTFaWFlnbHI1WDZpc3pIbmRkMC84eW5DdFVMSy8rTDNQS080UEJMTEFJYjRMcVAzaXhPZHNVamtxb0hGMWhMVU5wNWJSd1oxQy8vZDBtN1llbXlrbDVpZ2tFTmRQSU5ZZDJwQ1JDMk5ncXE1SFVQNVFpTGJkaGVXVVppU2UwRkRkNzhPQjU2cmJ6UE9zVFF0OUZ3S1daVW9Pd2pJTHVKUG5ybDJ6aElNeWRmNTM3Y29KNnI4T1VKV3YrZ0xJemx3dEZPblNZUzNoV09QTW9CdE54YTVaa2JudFVhbzhXb2JsNGRNV0lTcWl1UHVSZU9wcTV5bG5CQStSYnZnVFNLNGxCYUVUU2ZwNEp4c0t5alhaRTNLenIwU0w1aklyMjNIeWNkeTgiLCJtYWMiOiJjYmJkOWM3OWNiODJkNmRiYWJlNzgzODFlOTMxYTBjMTE2N2JmMWI1MTQ3YzVkNzc0ZGIxZWYwYjMwNDM0MWI0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImxhU3NnS1NYbjEwb3BQdGhDNVpHUnc9PSIsInZhbHVlIjoiYTE4b2pBSHdtelZaK04rL2VtRmU0S2J4dHpWTmgwdVJRUVpJSi94REhHUnZqWkhWL0ZaOEdwYjVrNXREN0pjRkJlUXQzaTlKeUwvc0xlTmdsL1NzNkQ4MGlrNURGOHBLbVRmUkVSUEFOVlozWHZLd3JsYVA4RWQ0aWZTS2N0OGdBUEE4Z0d3OHl3Ni9ERmVINnZLT3RCLzQxSVM1ZUd3MWZJUndzbjdXd1d5bGVyeWxyUTc4Y0xUZzRzZ2hYUENlcDNUNUxSZW9qOEtLVHVEditFZDY5V0hwcXMvSzhKNnlCcUtTNnBTMkl6U3YvaWFOdm5PUHlBemp1QnZiczlWYVZQV285SWhCSnpuTE11UFZmOFViOW5Mdm9TUnc0NU96QlQya3JCQWtySk9yRnpacXFXaG8wRXg3TE82ejdVbzFhMXRMcjNsTUxoclQ2L0QwSTNkaGE4NlpJeWRpZWJNSnhKRE1kbTVaeXdEdjJLK0dOSHdOb2haOVlLN2ZPeEVoTzVUMzNpS3FsaEFmZ25hNVF0VlErdUhEVDBwVEUvS3JwdmR3VnltRjVMbjdERlpvQkJZVWlzdXRxR2s3ZVJZc2dKcFFobDAxR2FnWUVTMjhyK29tTHBNdDA4VzJQNkMvSlJ0OEtKNGFvL1F1cjhGdnhqRDhpcDYyUGtSOG4wSU8iLCJtYWMiOiIzMzQxODQyZTM0MTI3NmRjZTY3NTQ1MTRhNjBjMzUxYTBhZWY1NjBjNzNiNjQyZDhmMGQ3ODQyZmFmNGExZTNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2092410051\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1626671356 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1626671356\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-102479613 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:13:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNpNHdFdzU3WnFCalBqTmtMUW1DNWc9PSIsInZhbHVlIjoiOGJSV0thME5HSEd5aWhXdW4xNDdZOUdtVFhqa25xeHJORFMrSk1ya3ZhQm90NjFTQU9XR1gzL2ZjcmRGTkpwclY0YWI4cXIxcUUzbWQwMEJZeHpjOHB1c1IxV0VTZlg0ekh5cW9DcVNLZlFwVkxDelVBTE1KdlR6WmxMaCttNE9xaVZjK0Y1bDdwUnc0WHphKzRldnFSeS8wc3h3eTVvRXdiZ3ZHQldzcHZ0Tm5Pbng2NnNMRUVBMjJIOERPcEt1REVHL2NDdGtmeVl4clVORG4xKzFsbFA3WHpCMW5NWW8vQ0FHejR3b1gzRjNCU0kzYWM5ODlEUmNRYUdyeC8zeWhyeER1ZkhsMDUvdmJmZDNEbXNLU2U5TWliZC9raHdXRVpSa1o4SmtyQ0UvRUg4QTBRL0txVFBEVW9DZTZ5R0cvbDd6M09sTjR5dU1lU1QwZmpMcTZLWTcwTkdLZGVFQlhseHdDMGtGTHdsQTdyNVhCaWtLK2t4NHlqK0tSaURlU0h3clFkR01TaFVuTzFrQkFTQVVYeDNCU2dqUHNuYlU0bTRxa3AwZ1k5dEhubUhEVmRnWmxsdFFYa2ZqL3NBUmpRSSszZnB3Wmh5THlEZ1pISlVkWjU3YWtXallpYUtxOFh3ZWZOK3UzRllHakE0R2IrMW44c3o5NnNOOHJxNGEiLCJtYWMiOiI0OWEzN2NkNWE0NzA4Njc3OTFhMmVlMDFmODBiNDZjZTMyOWM2YzU1NzBiMTc0NzY0MDFkZGZmYmQ5ZGEyODI2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:13:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZGZXpVK1JsNWZRQzc3RE5BOGxWWHc9PSIsInZhbHVlIjoiamczQUxjUjJMcUJQUEtXb0NhUVQ0b2FrUjFEdXBRK2hidkwwa0thb0U5blRhTjZmNnNOcVZ6UjhXU0lHcmV2RkYyVk5DLzlRMUYxSW11SWhrZDJGNEhvNCthM3JGKzZmOC9rd1pxVEZPMk5EaUJRUlcyTlc1MXZQSFVldmQvdzBXL1pDUE43NTBITUl6V2JNQjMrUHRVZEdkejFtM0dSeWorVy92ajIvT3U1dFZtMjFDT3pidmxUcGtCTDdUc2RkeHFBVURaSGhMS2pTMDFoY2VpdzhNc2tyTEFTSDIyZmNXLzlseVJ4RXZmWFhGc2VVaWM1dUJhbDZuRUdwcy82OVVFNjNhamcwR3VKWWNRQWYwVzFiZWpmK0piMXhvWXI1RUViYlJ6bDYzTEZIZWNRcktkTFJHMTdLbHdVb1FvUXh2OXpoL3NMVTBpR2tBZklJMFdQTmE5USs4RXZyblF1RGFmbUUxb2lUU0JXVTkwbnREc01vemVTOFVPM2tJbW5MSHZlbkxBUUo5TDZvSEpxTERkYUgydmh6cW5OQmRwVTZkZXErYUhQbWxzcTJ4cmdxRFlVS090YStWdmp4ZVpYTjhJS21ycW9MN2JGRmw2eE5RYVZyaUUvcUNPbHRldWs3SFhJcmp0bExsMkVlbll4SjNMcHNKdW1WVlkxTDZ1UFciLCJtYWMiOiI0ZjU4Y2IyZWNmNTRlNGMxMTBhMTgwNWVlZDFmNGExMDA5NjlhYWFlZDA3ODM4YjkwODg2NDk5YTNiY2FlYWI5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:13:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNpNHdFdzU3WnFCalBqTmtMUW1DNWc9PSIsInZhbHVlIjoiOGJSV0thME5HSEd5aWhXdW4xNDdZOUdtVFhqa25xeHJORFMrSk1ya3ZhQm90NjFTQU9XR1gzL2ZjcmRGTkpwclY0YWI4cXIxcUUzbWQwMEJZeHpjOHB1c1IxV0VTZlg0ekh5cW9DcVNLZlFwVkxDelVBTE1KdlR6WmxMaCttNE9xaVZjK0Y1bDdwUnc0WHphKzRldnFSeS8wc3h3eTVvRXdiZ3ZHQldzcHZ0Tm5Pbng2NnNMRUVBMjJIOERPcEt1REVHL2NDdGtmeVl4clVORG4xKzFsbFA3WHpCMW5NWW8vQ0FHejR3b1gzRjNCU0kzYWM5ODlEUmNRYUdyeC8zeWhyeER1ZkhsMDUvdmJmZDNEbXNLU2U5TWliZC9raHdXRVpSa1o4SmtyQ0UvRUg4QTBRL0txVFBEVW9DZTZ5R0cvbDd6M09sTjR5dU1lU1QwZmpMcTZLWTcwTkdLZGVFQlhseHdDMGtGTHdsQTdyNVhCaWtLK2t4NHlqK0tSaURlU0h3clFkR01TaFVuTzFrQkFTQVVYeDNCU2dqUHNuYlU0bTRxa3AwZ1k5dEhubUhEVmRnWmxsdFFYa2ZqL3NBUmpRSSszZnB3Wmh5THlEZ1pISlVkWjU3YWtXallpYUtxOFh3ZWZOK3UzRllHakE0R2IrMW44c3o5NnNOOHJxNGEiLCJtYWMiOiI0OWEzN2NkNWE0NzA4Njc3OTFhMmVlMDFmODBiNDZjZTMyOWM2YzU1NzBiMTc0NzY0MDFkZGZmYmQ5ZGEyODI2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:13:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZGZXpVK1JsNWZRQzc3RE5BOGxWWHc9PSIsInZhbHVlIjoiamczQUxjUjJMcUJQUEtXb0NhUVQ0b2FrUjFEdXBRK2hidkwwa0thb0U5blRhTjZmNnNOcVZ6UjhXU0lHcmV2RkYyVk5DLzlRMUYxSW11SWhrZDJGNEhvNCthM3JGKzZmOC9rd1pxVEZPMk5EaUJRUlcyTlc1MXZQSFVldmQvdzBXL1pDUE43NTBITUl6V2JNQjMrUHRVZEdkejFtM0dSeWorVy92ajIvT3U1dFZtMjFDT3pidmxUcGtCTDdUc2RkeHFBVURaSGhMS2pTMDFoY2VpdzhNc2tyTEFTSDIyZmNXLzlseVJ4RXZmWFhGc2VVaWM1dUJhbDZuRUdwcy82OVVFNjNhamcwR3VKWWNRQWYwVzFiZWpmK0piMXhvWXI1RUViYlJ6bDYzTEZIZWNRcktkTFJHMTdLbHdVb1FvUXh2OXpoL3NMVTBpR2tBZklJMFdQTmE5USs4RXZyblF1RGFmbUUxb2lUU0JXVTkwbnREc01vemVTOFVPM2tJbW5MSHZlbkxBUUo5TDZvSEpxTERkYUgydmh6cW5OQmRwVTZkZXErYUhQbWxzcTJ4cmdxRFlVS090YStWdmp4ZVpYTjhJS21ycW9MN2JGRmw2eE5RYVZyaUUvcUNPbHRldWs3SFhJcmp0bExsMkVlbll4SjNMcHNKdW1WVlkxTDZ1UFciLCJtYWMiOiI0ZjU4Y2IyZWNmNTRlNGMxMTBhMTgwNWVlZDFmNGExMDA5NjlhYWFlZDA3ODM4YjkwODg2NDk5YTNiY2FlYWI5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:13:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-102479613\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1588798410 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1588798410\", {\"maxDepth\":0})</script>\n"}}