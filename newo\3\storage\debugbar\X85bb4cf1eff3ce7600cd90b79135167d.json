{"__meta": {"id": "X85bb4cf1eff3ce7600cd90b79135167d", "datetime": "2025-06-17 06:55:05", "utime": **********.91807, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143304.297794, "end": **********.918108, "duration": 1.6203138828277588, "duration_str": "1.62s", "measures": [{"label": "Booting", "start": 1750143304.297794, "relative_start": 0, "end": **********.705737, "relative_end": **********.705737, "duration": 1.4079430103302002, "duration_str": "1.41s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.705756, "relative_start": 1.4079618453979492, "end": **********.918113, "relative_end": 5.0067901611328125e-06, "duration": 0.2123570442199707, "duration_str": "212ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45153992, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02493, "accumulated_duration_str": "24.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.805565, "duration": 0.02137, "duration_str": "21.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.72}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.867372, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.72, "width_percent": 6.498}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.888807, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.218, "width_percent": 7.782}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2146712369 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2146712369\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2075604461 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2075604461\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-216219440 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-216219440\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-586583188 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=1rkbmmi%7C1750143299616%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlducVBzb2VDMGpnd0pCVU1TZ1kxeUE9PSIsInZhbHVlIjoickhlOVNHbGdDdjlOV1hpZDkwUHZyNW9yN2Q3NUd0SkVid1JtYTE2Mjc5d3NJSVF4TE5OYkR0RTd5YXZ3SEVrUjQxSS9TbXF2ak5zamhRQ1Fjc0RyNVQwdVBxNUpjdDROeE1YUlY5V0M4bmhLWWtPT0VOeXVIaXBjU0ovUlVWUHZrQzl0MVErTitWY0wxdHZVU2FiODBMMkMzeklKVnppMkZRY25JVk8wa1llaGhXWEwvZ1RWZk1vVXpVYkN1dlBhUVRLNlBPZUt5UE5JZlQ2dGJtdlVQS1hlTU1uSUJWZDY1eTliTm9OOGdUM243blRMdmp6Qm5wc3g4SUxDZG8vbXBzWDF3NHlLaDB4b1FySll2UUxtRlIzVGVxQ1dJc3UxRDNyRGZtR29MRHdwWmVGRmhERTg1NW04Q3lCSDZZdU1sQXlwRWwvRWM1dm9TNHc4OE5zVWlNM1dWbEgzTWxRdS9PdFhvMmd3MVlkMjF5dnZMUi9BM0JKRDdDSXZVTTBRbmQ4YW9NUVVvd2kycEJZdys1NlJaU0k4a3JQSXVTbTBkT3l4bGVBTWxIM01ET2J4RmN4OU9OdmNjdkJ0cVRML05GRkI3TGJHQmk1bmZDVWkvTnREVkR4NG5qOFVRR1NLdzZMcE1SSkl2a2RZMjlLeWtZVUFTQ2pZcVlMUjlIOU4iLCJtYWMiOiI1OTI5MDczMmZkNTJlMmVkNjMwZDI0ZDAxNjY0YTc4OTVmYWE0MTYxYWEzYTFkNTA1YWRhZWI0MDVlMWNkOTY4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkhpbjRTaS9oTXJSQjJxWDBSNHhodGc9PSIsInZhbHVlIjoiQW5oQU9pOUZIVFdCSXF1WW5GSktHZXpEWXIzVnJZc3NEMnFTRzIxYk5CY29heGc2MnBTYURiZnRlVGRrUld2NWd1VmhFakloYXhDOFRzOStiS1VobDI5ZHlpWFU5TXpEOUNwZDJUSzRKajFVV25NOEcxdnJXR0xmK2dKaHFENmppTTJLaFdEdkNnS2x0V0RYYjQzVUl2NUV0c3lUMzRhL2FRRG5WVGtIMkcxancyWnYvVm5WUnAxUzlOdU1LbEpaOWZHTS9HcVFGN3ZaYTJ1cjBueTM3ZXY2WjNxcEx2bEROczRjWVhzemdaZUxmbFZkalBpUm85MnFzUFJlb3dGSzNiVVYxcVZ3c1FzZ1hVOTVFS0MwdXoyTEdQV0hGbnFkQThkYlYwYlN6MTR5MmxQYzhqRGJXMkFUQjAxKzR5d0xpL05TSTNVa1F2aTVKckExbHc4TzV1RnR3eEJwQjZacDIzSitvamlWRU9YVE02cGJXTGdkbDdDOEZ4YlBSMkkyb05UNEtvTDNTZmYyYXVzWFlLaHpFd2R1NURqSDM2UDRkWDlvNnRqT0Rhai9IV3M5cFZnNnZuTmFpclAxVUhOa1AyY2UrTlN1SjhlYW5qS0p4YTlyWEZVaU5KYUg5dDI5eXBwaHIyRGZTcDh0Wm5RbUo0OFRMeDlHSHE5NUtxVlEiLCJtYWMiOiJmZjA3MDI1MDBjYzRjMDk0YmQ3NDAzNjU5NjgyNzQxMTVmNDdlNzM4NTgyNDU0ZDdlZDUzYzkwYzYwMGZjMDkxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-586583188\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-165985673 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDWN9h6R9gAIdzZaXBzXfq0WGpauVfrUj70kKWA2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-165985673\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:55:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InkrMG1ocEdwVG11dzlvOXl6NndCR0E9PSIsInZhbHVlIjoibUhpaXBQSC83Nk1lTWIwQXFrNzdwNjFCVWlJbWVMK2w4YVVpdHV0WmVJRE81RTdUOUtySGh1SFh3MWNJY0tzTFpBeEEvMk9rVnVyd3Ria252QVBMTUlsZGxnTW9INVhrMm9ha1QwT1lNSEM1MjhCeEtsdktVN1hNVDVlaEFJeDErT0MxOENGRGF6N2pITFZXYzljZWhPZlhueHl4aUxTeVpJVitiaEtTQTUzU3FSS3kxVFhtMkJPSWhLeHlXSWFWMmFlUEovZkFqcVJRZ091ZGE4U2wvNkh2ellyTzhycEZOdDcrb0pULzlzeDJPRjBrZUVnVUZTTkpVaTc0c1FqaXFGM05Pc3VlQXlJSU1KVHFhYkZaV0lCa0p6UzZZbWJOSC9oMWFxQmVxUHN5S2tEaTV3cm5hVXdRd3FmbkJyQmRLV0hDMTE5eU9hTjNzQVpUcm84d3JEWTFtWWpQZVd6UnFuMzB3KzdabE1pZEpTcE9aTjF4RXZpVGxFblhTRmoyN3hyVjlLenVXY2NXcThKbXBCczlnbndCbnJzU2tZZC92NXRzTVc5bm11WjdRazNJcGZTVVZRVmFFeEtweXIveDVYSnNjbXNJRTVFWHpUQVNKM1N1SGZPdTVmdEJRazIzY09TRXlRbGIvaWxrRk9hSXovQnFZTkZoZk1uRW84MTAiLCJtYWMiOiJkODdiNmFmMjlkMGE4ZTliYjc2MzVkM2ZhYTNhMzBkM2FiMzdhOWY5ZDlhNjQ4NTM5MGExYzMxYmIyMDc3MWI3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:55:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imo4OGMrNWhhVDF2N0xvRjVHWENQMkE9PSIsInZhbHVlIjoibWVvd1FnRXpSMHhMNGUyeG5jbUxWaWIxL0VOT1M3SXFKemE2YS9SSzd1UWQ0SEdIRWVzd3FGR1Z1c3lBMVNNbGNHTktMT0JkTWllODBvcVpjS00xc2lKeG9TK0xHRU9kNS94S0NyYWtFRUFNWWtTU2RZYkJCNzJHckFTUnZqTmdRbFhUbkc2eXBaREpNOWVGYnQvQmNpQ3BJeHB5WTB2RTlFZG1scEExL0F5S3gyV3I0dFFmNWdwcTViVnpiT1QvSGhuSkhtV3VjbnJDc2drTHVqZTVWRk1JUnV6dXpZMzJmRFdsMlVWdDhza1dmQ05IZHlLNE5abmswUVZUeHNUSEtFeTRBMjRHSWUzNzFEKzRBNFc5UTZUSlhvNUdGR2djOTA3cTY5akhpdThZTDF3VU1hSWRMNXk2dUwreHEzNXloS1Q5c1l4cExoRloxV0x5cEJTUkNUN093QU1sVklRMGlmQ3pMek96V0ZPZStPT1Z6OXNvaU5BU3plZnpENTBtb0MzYVBaOWZZME40RTV4ZUdINDk1UFh1cG1DZlUzRC9UT0c3ZE1pbWxiWTlIK1lXTTQ2cGl3RHZ5ZUJzMEo1eU50dXhXR0RYdHJMUCs4SHdOY21BOVY0dXh0VzV2SVVOR3ZOTkpxcHcxNnFkOXJhckZJVytaUEV3cCsxZkFMaHoiLCJtYWMiOiI5MzhkZmM4Y2YxNTRjNDhmNWY5OGZkYjIwMzA5YmYwMTNkZGI1YTRkMzk0NTc4NzhmOTFlOWJmOWVkNDE3ZmQyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:55:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InkrMG1ocEdwVG11dzlvOXl6NndCR0E9PSIsInZhbHVlIjoibUhpaXBQSC83Nk1lTWIwQXFrNzdwNjFCVWlJbWVMK2w4YVVpdHV0WmVJRE81RTdUOUtySGh1SFh3MWNJY0tzTFpBeEEvMk9rVnVyd3Ria252QVBMTUlsZGxnTW9INVhrMm9ha1QwT1lNSEM1MjhCeEtsdktVN1hNVDVlaEFJeDErT0MxOENGRGF6N2pITFZXYzljZWhPZlhueHl4aUxTeVpJVitiaEtTQTUzU3FSS3kxVFhtMkJPSWhLeHlXSWFWMmFlUEovZkFqcVJRZ091ZGE4U2wvNkh2ellyTzhycEZOdDcrb0pULzlzeDJPRjBrZUVnVUZTTkpVaTc0c1FqaXFGM05Pc3VlQXlJSU1KVHFhYkZaV0lCa0p6UzZZbWJOSC9oMWFxQmVxUHN5S2tEaTV3cm5hVXdRd3FmbkJyQmRLV0hDMTE5eU9hTjNzQVpUcm84d3JEWTFtWWpQZVd6UnFuMzB3KzdabE1pZEpTcE9aTjF4RXZpVGxFblhTRmoyN3hyVjlLenVXY2NXcThKbXBCczlnbndCbnJzU2tZZC92NXRzTVc5bm11WjdRazNJcGZTVVZRVmFFeEtweXIveDVYSnNjbXNJRTVFWHpUQVNKM1N1SGZPdTVmdEJRazIzY09TRXlRbGIvaWxrRk9hSXovQnFZTkZoZk1uRW84MTAiLCJtYWMiOiJkODdiNmFmMjlkMGE4ZTliYjc2MzVkM2ZhYTNhMzBkM2FiMzdhOWY5ZDlhNjQ4NTM5MGExYzMxYmIyMDc3MWI3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:55:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imo4OGMrNWhhVDF2N0xvRjVHWENQMkE9PSIsInZhbHVlIjoibWVvd1FnRXpSMHhMNGUyeG5jbUxWaWIxL0VOT1M3SXFKemE2YS9SSzd1UWQ0SEdIRWVzd3FGR1Z1c3lBMVNNbGNHTktMT0JkTWllODBvcVpjS00xc2lKeG9TK0xHRU9kNS94S0NyYWtFRUFNWWtTU2RZYkJCNzJHckFTUnZqTmdRbFhUbkc2eXBaREpNOWVGYnQvQmNpQ3BJeHB5WTB2RTlFZG1scEExL0F5S3gyV3I0dFFmNWdwcTViVnpiT1QvSGhuSkhtV3VjbnJDc2drTHVqZTVWRk1JUnV6dXpZMzJmRFdsMlVWdDhza1dmQ05IZHlLNE5abmswUVZUeHNUSEtFeTRBMjRHSWUzNzFEKzRBNFc5UTZUSlhvNUdGR2djOTA3cTY5akhpdThZTDF3VU1hSWRMNXk2dUwreHEzNXloS1Q5c1l4cExoRloxV0x5cEJTUkNUN093QU1sVklRMGlmQ3pMek96V0ZPZStPT1Z6OXNvaU5BU3plZnpENTBtb0MzYVBaOWZZME40RTV4ZUdINDk1UFh1cG1DZlUzRC9UT0c3ZE1pbWxiWTlIK1lXTTQ2cGl3RHZ5ZUJzMEo1eU50dXhXR0RYdHJMUCs4SHdOY21BOVY0dXh0VzV2SVVOR3ZOTkpxcHcxNnFkOXJhckZJVytaUEV3cCsxZkFMaHoiLCJtYWMiOiI5MzhkZmM4Y2YxNTRjNDhmNWY5OGZkYjIwMzA5YmYwMTNkZGI1YTRkMzk0NTc4NzhmOTFlOWJmOWVkNDE3ZmQyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:55:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}