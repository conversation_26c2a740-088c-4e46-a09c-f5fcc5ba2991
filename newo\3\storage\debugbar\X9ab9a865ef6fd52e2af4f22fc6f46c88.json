{"__meta": {"id": "X9ab9a865ef6fd52e2af4f22fc6f46c88", "datetime": "2025-06-16 15:21:40", "utime": **********.464836, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.791238, "end": **********.464874, "duration": 1.****************, "duration_str": "1.67s", "measures": [{"label": "Booting", "start": **********.791238, "relative_start": 0, "end": **********.220521, "relative_end": **********.220521, "duration": 1.****************, "duration_str": "1.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.220542, "relative_start": 1.****************, "end": **********.464877, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "244ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.023639999999999998, "accumulated_duration_str": "23.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.32502, "duration": 0.01966, "duration_str": "19.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.164}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.37492, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.164, "width_percent": 5.541}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.436418, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 88.706, "width_percent": 11.294}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087289395%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZzdnRuUjUwbEhod01GcXJmb2RKMFE9PSIsInZhbHVlIjoiSHRCRlV2cGMyOXU5RlF0OWtLdWszZ3JFUjFRSTdJVlZCUUlGYTdCN2VSN3RvdThXT0l3T084NEtnZC9nSXNGS080eVkrYjdaRGUxQ2lEdFBFNnZVcFJvL1VXT0JqZlgyc01iNWo1RzRHZ3BZdGptMG1KTVlhUlRtWlByemtRKzdBa0RzVmttYUYxYkRUZHlMQXMveVo4clZyUEZtbGZDak1KT0V6SlM1Y3FxUTFGSVM0UVZoTnUrT2tiVi9lZ2dUbmtiUnFHS0Y2Ynd3NVAzMDl0R2tmUUV0NjZkK3l4RFB4bytRVmFWK3JUSDlnam1OZVlhOXBRQjUwTjF6aFhqYlZWMFRDZ0tnR0xRMWtlT1laRUhNd1ZrcTIrSnpDM1k1VWlNbkhZQUhVc25kQ0ZQWDZ6YWUxemkxcTJMR1dRVWVmMXJqaFBoQWlMTGVObm9UNmJ2NWlrdkp6cUtSNkNNU0xiVDRvdUZVd09vSkFqKzg4RUNtU2IxQWtrNHNMTEtSbkRwV3RSR1dFSzJWaTg3aFJ3WmQ4OFRib1VGVkphdndLcVppYXl0SEtYUXpRa3hweGhQWmhWZ05nbTFDR1BUNERGZzladFd3ZDY3WjB3UHdxQ0hwR2hJZ0lpZ3lHNFNwTG8vSXIra1pGNWlRVWRmMHc3dW9lTEpJSGM2aTFHMU0iLCJtYWMiOiI5MjE4ZTJiMGMzYTI0NDIxMzEwOGIyZjkxNjViYjVkNTg3NzNkMzE5ZjcwMmZkZGYzZjU2MTFiZGQxNTRlOWFlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IllhZm1lNUhyd01CSjd1TnM1S0hmV0E9PSIsInZhbHVlIjoiNHIzT3VQZGRqeWFGN1pPeHhDanpsSWJsVXlMKytYUEduYitFQ0V1bDRNSVRRVHY5TllYVXVxMlRTSkpqcDRIRnFjMVJZa1BKYVVQWGRQY0hwY1NKemh1Ym1icC9FM3ZRcnNNTjRncnpxOTVRS2ZTcGlYWnFhVkN3ejQ2c0FKRjdSTm50ZXh5N2l5MEZncVppdXo5TkY4REdlU2VYanBURU1lWTQxWlo4OVRScGc2Wk5XMXU0dkV3ZTVyNmxadWNIcEtWNVNSMmFjQmZxOWZQTnBOSkZ6RGYzTzdBQ1lic2Y0b0VucEFuYU55eVA4Sk95K01uSzlBVEdYSTUvMWpoTi95SGhsZGhGMlk1MXYwdEM4ZmlZeno0ditQVVd1SkJGM3cxd0Zwc1BCbTYxZVFnTzhrbkZCamNXSVMzZ2VYajB3R2U2anNNZU9wUE5TcFJWZE0yVnNTbkVLRmVmWUxlOUVZRlZSQ3lpSTNIM1JCSkxCNmFiS3RSa1R3Q3MybkpaV0tPWnpTSkQxVUJrMmFzSGtsTmpCS3V1anNaTGNqUXh4dnFheHRkOHZYbWcxWHdObG54aW5RcThCdHkxZGNrWldqTExoZkZWM1NuQVVpeFFNcWptTW9Eei9qUStsdExyMG9zTXoweksrYUtLRTFuWmRNY0RWQzF2SkdiVzRtbUoiLCJtYWMiOiIxYzA1ZDNhMjNlOWQxZmNjOWJiOGQwMWRlMmNhMTdjN2M1NGQ1ZDk1ZDJjMTRmYzVkNjU2Yzg5NGE0OTQwNDI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-630903603 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WjiNcn9cZJfMB3HvNRV2ADeuSdnp1JlxvgqAKjT1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-630903603\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1049168520 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:21:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9xeS9XT1F0MSs4K29CbHVJRCtSS0E9PSIsInZhbHVlIjoidFBlaXVySkVPVzVSQk1kUkpDTWtiZzJkMEg2alFabUdlY3YzUnhLVURWK1VST3NDTXR3ZnAzRE43bk44SkpWYjIxajdhTWI3djVGRVdIK1NpS3U4Z3NHbE1xblVVUlNYbWRQTThTV3h1bm13aEp1Q2RTYmlxOHNieUEwcDRMTDh5dDM0NEJCWlBTTDF4a1Zmd3NsVTUzV3RhaUw1RGlZUHVyMjJSK1JxWFNDQTJRTXFVaERWazNBRWE3RVBnZGFPNUd6L0lWc3dLa0FIMWlwdTByRmRpTjNGUFlDS3ZkZ2IyR3pmaENGUGp1Vm5rNS96RXVmSmtmbC96Vk80T1c2ZEVvZVZLUktrVm53TmFFc0tVTHNNVmhJYkFoV3Y4OGtuQ0QyU2kzTkVKUFVwQWhUU21Ebmx0YmgrSk4vVDNRRjhVRW5qa2ZRT0F3ZVpIekhPSi9MYndzNGZqY3k0SXo4V2h4bjVXTzkwSXk5b0d2dmUyUzduRG8yRUo4U3hUNTlBaTN3WEpXRDFuTGtNbEZDOC96ZjhUY1FxaGR5RDJEQS9naUZCOXRSd3JielZMbllPOGtDTUw3VE9uaDZwV2NxcDF4RER6WWVNNXVsSCtJWCtXQ21Qc1REWXQ0b1BLMHRwK1gya1dEM09tUE1hTmcxRHBQV1lJbjFhT3JVbGw0NEgiLCJtYWMiOiJkNjYyYWVhZTUwNTNmMTVhZTA2MDVjZDhjYTJiNGM5ZmE0OTY3YzY2Nzg1NzViNDNhZTkwMGJkZWQwOTdjYjM1IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:21:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IktLb1pISC95elRvam91SUlyeXBqZ1E9PSIsInZhbHVlIjoicFlmc2xmc01OY1dVMHUwMS9zTUoxWlpZN01IMHZxL3lZRmJjSU1iVnFFd3l3dWNTa1lyUW8wckkra2phU25waHJBK3B0Q1p5eE9kb0dNYkhaczZZeEwwaEVOcFh3RnMzNUxRQWdLY0t5UzJHd3NtUmh5eGdoWnlKZEVnbDc1aUxQVDF1ekpqV08wRzhyZHh1dHdIN250dDU3bXFaY3JFazdQNm0xK0VMaG5mR2JHRHdOQzRjOUN2NjBhd2U4djdwMnVFUzNtZmEzWlFONFRaZEQ0VU5xS1k5dU51cVlzT294a1FSMXVDaXdYS3c5NUlIek9PZ3VpVUtwZEhZSGNWc0R2dVBaMnpwcFA0ME91UW5rNHcwNVYrbldWb1pObnVGQXpFcFpqQTRJTUM3ZFRneEZQNkNvMVRzdHUyaUhTWDhHZSs2b2dGUm9MY1N6ZzM5WFZxbnVIVG90UUkxQytFNUhuSzdtbmE5dURVT3JjMExhdUNTUnJmZDNSQ2ZrUVB5dmNPTDNXTEdRMXFKVFF2NWdOTmlpUW5aOXJ0R09rZkVUT3dhaUV0TTNzUDhSUi8xWnl0bXFSMkJ5L3VqTVpEd1hvRWFlbFo0RDlEZ1NUZlp1Rjd5akZqZzBleENoUFNlNEs4UVhPRm4zMTJyUnVUQUVBdy8rd3lSNkV3TXlUMjciLCJtYWMiOiJkZTNmZGIxYTFkMzkwNWEyZjk0MzZhYTZiMWRhYTllMmY0NjE4NmQ1YWIwNGEyNDk2NDExM2E2NTcxMjMyYWUxIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:21:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9xeS9XT1F0MSs4K29CbHVJRCtSS0E9PSIsInZhbHVlIjoidFBlaXVySkVPVzVSQk1kUkpDTWtiZzJkMEg2alFabUdlY3YzUnhLVURWK1VST3NDTXR3ZnAzRE43bk44SkpWYjIxajdhTWI3djVGRVdIK1NpS3U4Z3NHbE1xblVVUlNYbWRQTThTV3h1bm13aEp1Q2RTYmlxOHNieUEwcDRMTDh5dDM0NEJCWlBTTDF4a1Zmd3NsVTUzV3RhaUw1RGlZUHVyMjJSK1JxWFNDQTJRTXFVaERWazNBRWE3RVBnZGFPNUd6L0lWc3dLa0FIMWlwdTByRmRpTjNGUFlDS3ZkZ2IyR3pmaENGUGp1Vm5rNS96RXVmSmtmbC96Vk80T1c2ZEVvZVZLUktrVm53TmFFc0tVTHNNVmhJYkFoV3Y4OGtuQ0QyU2kzTkVKUFVwQWhUU21Ebmx0YmgrSk4vVDNRRjhVRW5qa2ZRT0F3ZVpIekhPSi9MYndzNGZqY3k0SXo4V2h4bjVXTzkwSXk5b0d2dmUyUzduRG8yRUo4U3hUNTlBaTN3WEpXRDFuTGtNbEZDOC96ZjhUY1FxaGR5RDJEQS9naUZCOXRSd3JielZMbllPOGtDTUw3VE9uaDZwV2NxcDF4RER6WWVNNXVsSCtJWCtXQ21Qc1REWXQ0b1BLMHRwK1gya1dEM09tUE1hTmcxRHBQV1lJbjFhT3JVbGw0NEgiLCJtYWMiOiJkNjYyYWVhZTUwNTNmMTVhZTA2MDVjZDhjYTJiNGM5ZmE0OTY3YzY2Nzg1NzViNDNhZTkwMGJkZWQwOTdjYjM1IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:21:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IktLb1pISC95elRvam91SUlyeXBqZ1E9PSIsInZhbHVlIjoicFlmc2xmc01OY1dVMHUwMS9zTUoxWlpZN01IMHZxL3lZRmJjSU1iVnFFd3l3dWNTa1lyUW8wckkra2phU25waHJBK3B0Q1p5eE9kb0dNYkhaczZZeEwwaEVOcFh3RnMzNUxRQWdLY0t5UzJHd3NtUmh5eGdoWnlKZEVnbDc1aUxQVDF1ekpqV08wRzhyZHh1dHdIN250dDU3bXFaY3JFazdQNm0xK0VMaG5mR2JHRHdOQzRjOUN2NjBhd2U4djdwMnVFUzNtZmEzWlFONFRaZEQ0VU5xS1k5dU51cVlzT294a1FSMXVDaXdYS3c5NUlIek9PZ3VpVUtwZEhZSGNWc0R2dVBaMnpwcFA0ME91UW5rNHcwNVYrbldWb1pObnVGQXpFcFpqQTRJTUM3ZFRneEZQNkNvMVRzdHUyaUhTWDhHZSs2b2dGUm9MY1N6ZzM5WFZxbnVIVG90UUkxQytFNUhuSzdtbmE5dURVT3JjMExhdUNTUnJmZDNSQ2ZrUVB5dmNPTDNXTEdRMXFKVFF2NWdOTmlpUW5aOXJ0R09rZkVUT3dhaUV0TTNzUDhSUi8xWnl0bXFSMkJ5L3VqTVpEd1hvRWFlbFo0RDlEZ1NUZlp1Rjd5akZqZzBleENoUFNlNEs4UVhPRm4zMTJyUnVUQUVBdy8rd3lSNkV3TXlUMjciLCJtYWMiOiJkZTNmZGIxYTFkMzkwNWEyZjk0MzZhYTZiMWRhYTllMmY0NjE4NmQ1YWIwNGEyNDk2NDExM2E2NTcxMjMyYWUxIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:21:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1049168520\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}