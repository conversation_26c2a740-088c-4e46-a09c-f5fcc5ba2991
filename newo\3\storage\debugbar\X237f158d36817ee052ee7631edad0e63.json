{"__meta": {"id": "X237f158d36817ee052ee7631edad0e63", "datetime": "2025-06-17 05:42:03", "utime": **********.439133, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750138922.057643, "end": **********.439184, "duration": 1.3815410137176514, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1750138922.057643, "relative_start": 0, "end": **********.229269, "relative_end": **********.229269, "duration": 1.171626091003418, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.22929, "relative_start": 1.171647071838379, "end": **********.43919, "relative_end": 5.9604644775390625e-06, "duration": 0.20989990234375, "duration_str": "210ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45168888, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.027979999999999998, "accumulated_duration_str": "27.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3271239, "duration": 0.02566, "duration_str": "25.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.708}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.382262, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.708, "width_percent": 3.574}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.406917, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.282, "width_percent": 4.718}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2056028962 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2056028962\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1505675380 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1505675380\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-557581236 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-557581236\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1421400140 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750138904236%7C11%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Iit0eUdtM202Nk1abjR5QWRaYXN0TXc9PSIsInZhbHVlIjoic3B0V2F3cG5QTm85bUZ2U1RENGpUZ0ZkU0grTEtSWFIxTGo0Y3ZiRXJBb2dJVnI5VXFOcFdTRFNaT2hhNXRERlhKR2FDbUJJWXN5ZG5hWVFBa29qTWZwT2VhM1I4U3QyaGpNOVMyWlhQTWFodWtRdjZGMTdvY2QrNWxTbUtMS2pVZVNZT3pCemRocEE0YklwcS9xbWR0WTBwSG5RaUt1RFMra3hJTUtQOHpOcjlYVG1TaWdUcDM0bFZBc3IyanJYcUFFZGVzeU01S2E3NzFlczR2eEZ4d25wRG5Pa0kwS3NPMUxtTC9xd0RlWFRCVDFhK1Vmb2tEZll3VFl1dXVZTXhWTytCaHp5Qy9PYjJrZUVRdGZqdGVCU3ZTYzk1YzJFVVRqd3oyaU9LUUcvRlBzbW5JNm8yeG5oTWNSU1BlSVo5L3VNa281Vkx3SFkwY2h2ajAvZ0N6ekZRNEYyUUVMc1V3SVhQY1k1cElGNHBZTEx3ZUI0SEE4QnNDaFIvMjluR0RoRTloUUdDWCtpV0NQbVgzUDJmZ0gybXY5UzdidEJNbi9YcFNGYzYwZllDbnAxU0RqMnE4RkdlVG92SGVYMG5aU1ROa3JScTc3bFk0aXowd1VrY2oxaVVHdkNTNHpaRmxBa3FRN0g4dGhrbkM4RkdpbkxPOXhJVVlUVEI1L0kiLCJtYWMiOiJiMGI1MTVkZTg0ZjM3MTExZjdiNjZmNjM3MDVmYzYyMDBhMmJlNzA0NWYzMTg4OGI1NjRhMWQzMDQ0MmM0Y2FlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBnSndDeDQ0TTFlcGNVOUFmMHMwaXc9PSIsInZhbHVlIjoiUkd3WmU5UEV1WFJmNjF3bTFFSzB2M1hnSmJjZzMwZHlhWW9OMVdRU0dGT3JnZnlyZ2NLeGdacVF5V3dBZDM2d3YxQ043QmhUMk93TXVzVXV6U09ya3NFUThrWXlKcnJxQWVwSnhWSFlrTy9wZkl2MVdQMmNid0hqSkpjTmRYZGFRb2xObUpFYjUrUHlUbTE0QXUxSlgrd1JxaWVyaXdud3FtblBoOFZFSERrUmhtTFplVHoxb1lLcFp5R0d0VDFoSnRTWVM1YXBXRkwyejJNQmlvejB3TTRrOEswcStMVlRoL0EyRmt2SXluak0vVjBMYU10cUxSUk1pNlRtcXdGbGVrTThMVkVqcUxFNHlTK2YxUDRhUm8xdkM1WUJMT213aGE3ejNnTWlkTWJ5V21nTC9JcmRlQ0dJVlA3eGtUSWV5K3N2YlZlNUZPTFJjdU9aZEYxNDFueHQxeHRQcEpxdDBaSnQ1UUl5WDd0OURMRmtFWDJOd0lZM29ZWnJGcWdUbHpVcFJpLzZWL0Y4NEpxL1RuQVVqcWtVM1hQQWptZjlLM1hlY1UxcDlDQ3RBdmZSNU0xU0IySjNPL1pieW1mNERXWm12MzBLUFpNbFZnT3ZNMUN0U0V2N1c4cGh6QzlDSHdpWXBUVy9Hd1ZuWHJrTVAvL1oxa2pNSkNWNnhITHkiLCJtYWMiOiI1NTI5MTk0MmE5ZDc0YzNhOTNkMjhhNTA2ZGM1YzRiZGIwZmMwZWExOWY2ZDBjOWVlODY1NTY3ZjE1MWY1MmRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1421400140\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-617895498 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-617895498\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:42:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpxSWFYdWx6ZVhXVkJ3ekQyWWNNRWc9PSIsInZhbHVlIjoiTVB2V3pWL1BqbGNvL0ZMZ2M4bzhvTThvTXhyeUNWU3VzMU5LYkFKbXQ2MjBlVVFqQ2dkV2tvcXdydktjWHNNUWg3ZjJ3VDVGdnY2alc0UUhYOFJJWWxPQ1Q5UjNGREgwQlhodldCU0h6UkVxVHlyclBGbFU5TURrc0dVVFpWOVBGTjhZM2tkaVhLajF2Z2VIdEFDemNZN29oZkQzWGlYV1QrNmZYTnpxV0VidkhhbERpSmZiejRRcGJySjR1eVJwbGFIbVVWdTJmZkxIMU8wNlduMXZNdWl4dnlqNlc1MWFxMXN4aDNlWFh4Z01VRlhMT3lYRFJLeTlaazNyUlczSjU4UjhiV2lJREtobjZhVmJPVmFjUXRYZkkycExVWXJ2NFg3WXhLZG4xbjl2ZFA2WXRvQTdXK3B3UUU5MHF1bjNhM0R6OXZyQnJnODRKam9GUXNOS3k3VHV5eGh5L2trQ1Juc2NPWHljMXNPMm0vd0dGWnNJVmNjYkdyUmlYVGs2aW5sLzFmQVo5VkFEbENrSlEvVVQzL1B5b0crQ1ZIKzFOSXNpU3dNdGh1aXBqbjBqcVlBUHR0WDU2dGJvd3FadUxDVUFZN2lDNkp2WUhEV2psemNjSUY1RFpHUXkwVDduQWlXczFuQ3dIMUYxbmo1bzZSZW9Td2lRVDRmd0hLSXAiLCJtYWMiOiIwNDk4NzRjYzVkNGM1YmY3NDY2ZjQ2Y2JmNDdmY2VmM2NhN2Y3YzA4MzUxYjlhMzg4N2Y4YzcyMGJiZDdiNGNkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:42:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZXOHdSYVBtS2ZhZW9iVkFOVWlJT0E9PSIsInZhbHVlIjoiMWdFNTFxQVZMSTB0SmhkQXlyNzJqbkJuZTBia041OWVFdXF2WDVVcDRnd21QbWFJSDV6S0JCaEMrQnRGdzVDTkNscnNMRm9QWTgvUW9UMUJwV2JoVGkxZGM2YmJFclVtQlkyci9KSTh0TmowRVJCT0hrZTludWNQMjQxOE5DUnRYeDcrWW5DUnFxRWJ1bEhCRXk4S1hKdVF5U3k1bXZLM1kyUWRpWmdMYUtJY0tlNXpDcElWVW81OWsrSWdFZEpSZmR4RmY2Yi8xK0dkU0pydDNvYy9tSVFXelZiUXVVeHYzZmkyTys3MXdZaUJ5SUladVlvb3hDWlMyeFFJMXRWZjEzMWkwOFB5a2tLZFd6UlB4ZjNWQkFSOXVrdXVVYTV4Mnc0cHdsc081b1hBWFhLaFAvUnY2YVN2YWhmVDN2NWNOYzN6d0dERGgxcmFIR1ZraWFtY3p6QnhsQXpRWGFNRFZ5d2xqZWZRVnBWSldySkVFeWlWSTBXcXI2YnpLM01hWmNyNVlSOHR4aDJla0k2TG8ydWxCTEgvOC9kLys2V2UrcGJ1SFd3QVRycW5lWHVkck15YS9VempnQjhLb1k2ZTc4eC9iTDZxRHZMaEtyV2ZPRnI0blliOGt0cmI0YXJUcllTUEMrVzVtaS9CVkhxTzJRYk5meWhhSkdWOHZ1czQiLCJtYWMiOiJkMzZkMmE4MWQ3ZjBjZjE4NTNhN2I4MDYzNDM2ZTg4MzMxOWZjZTQ1ZjFlNGVmYzdlYzI5ZWNiNzhjNmRkZmRjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:42:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpxSWFYdWx6ZVhXVkJ3ekQyWWNNRWc9PSIsInZhbHVlIjoiTVB2V3pWL1BqbGNvL0ZMZ2M4bzhvTThvTXhyeUNWU3VzMU5LYkFKbXQ2MjBlVVFqQ2dkV2tvcXdydktjWHNNUWg3ZjJ3VDVGdnY2alc0UUhYOFJJWWxPQ1Q5UjNGREgwQlhodldCU0h6UkVxVHlyclBGbFU5TURrc0dVVFpWOVBGTjhZM2tkaVhLajF2Z2VIdEFDemNZN29oZkQzWGlYV1QrNmZYTnpxV0VidkhhbERpSmZiejRRcGJySjR1eVJwbGFIbVVWdTJmZkxIMU8wNlduMXZNdWl4dnlqNlc1MWFxMXN4aDNlWFh4Z01VRlhMT3lYRFJLeTlaazNyUlczSjU4UjhiV2lJREtobjZhVmJPVmFjUXRYZkkycExVWXJ2NFg3WXhLZG4xbjl2ZFA2WXRvQTdXK3B3UUU5MHF1bjNhM0R6OXZyQnJnODRKam9GUXNOS3k3VHV5eGh5L2trQ1Juc2NPWHljMXNPMm0vd0dGWnNJVmNjYkdyUmlYVGs2aW5sLzFmQVo5VkFEbENrSlEvVVQzL1B5b0crQ1ZIKzFOSXNpU3dNdGh1aXBqbjBqcVlBUHR0WDU2dGJvd3FadUxDVUFZN2lDNkp2WUhEV2psemNjSUY1RFpHUXkwVDduQWlXczFuQ3dIMUYxbmo1bzZSZW9Td2lRVDRmd0hLSXAiLCJtYWMiOiIwNDk4NzRjYzVkNGM1YmY3NDY2ZjQ2Y2JmNDdmY2VmM2NhN2Y3YzA4MzUxYjlhMzg4N2Y4YzcyMGJiZDdiNGNkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:42:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZXOHdSYVBtS2ZhZW9iVkFOVWlJT0E9PSIsInZhbHVlIjoiMWdFNTFxQVZMSTB0SmhkQXlyNzJqbkJuZTBia041OWVFdXF2WDVVcDRnd21QbWFJSDV6S0JCaEMrQnRGdzVDTkNscnNMRm9QWTgvUW9UMUJwV2JoVGkxZGM2YmJFclVtQlkyci9KSTh0TmowRVJCT0hrZTludWNQMjQxOE5DUnRYeDcrWW5DUnFxRWJ1bEhCRXk4S1hKdVF5U3k1bXZLM1kyUWRpWmdMYUtJY0tlNXpDcElWVW81OWsrSWdFZEpSZmR4RmY2Yi8xK0dkU0pydDNvYy9tSVFXelZiUXVVeHYzZmkyTys3MXdZaUJ5SUladVlvb3hDWlMyeFFJMXRWZjEzMWkwOFB5a2tLZFd6UlB4ZjNWQkFSOXVrdXVVYTV4Mnc0cHdsc081b1hBWFhLaFAvUnY2YVN2YWhmVDN2NWNOYzN6d0dERGgxcmFIR1ZraWFtY3p6QnhsQXpRWGFNRFZ5d2xqZWZRVnBWSldySkVFeWlWSTBXcXI2YnpLM01hWmNyNVlSOHR4aDJla0k2TG8ydWxCTEgvOC9kLys2V2UrcGJ1SFd3QVRycW5lWHVkck15YS9VempnQjhLb1k2ZTc4eC9iTDZxRHZMaEtyV2ZPRnI0blliOGt0cmI0YXJUcllTUEMrVzVtaS9CVkhxTzJRYk5meWhhSkdWOHZ1czQiLCJtYWMiOiJkMzZkMmE4MWQ3ZjBjZjE4NTNhN2I4MDYzNDM2ZTg4MzMxOWZjZTQ1ZjFlNGVmYzdlYzI5ZWNiNzhjNmRkZmRjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:42:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}