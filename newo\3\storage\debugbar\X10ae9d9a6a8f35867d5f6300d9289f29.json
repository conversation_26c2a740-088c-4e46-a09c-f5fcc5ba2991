{"__meta": {"id": "X10ae9d9a6a8f35867d5f6300d9289f29", "datetime": "2025-06-17 06:55:00", "utime": **********.797475, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143299.150806, "end": **********.797506, "duration": 1.646700143814087, "duration_str": "1.65s", "measures": [{"label": "Booting", "start": 1750143299.150806, "relative_start": 0, "end": **********.603622, "relative_end": **********.603622, "duration": 1.4528160095214844, "duration_str": "1.45s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.603645, "relative_start": 1.4528391361236572, "end": **********.797509, "relative_end": 2.86102294921875e-06, "duration": 0.1938638687133789, "duration_str": "194ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45506656, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00857, "accumulated_duration_str": "8.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.719754, "duration": 0.00552, "duration_str": "5.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.411}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.760983, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.411, "width_percent": 18.786}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.775438, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 83.197, "width_percent": 16.803}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-313185979 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-313185979\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1006289037 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1006289037\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2022421086 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2022421086\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-578923434 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=1rkbmmi%7C1750143267722%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IngrV2VnWEFaMlozeE93dVNJY0ZRMnc9PSIsInZhbHVlIjoiUXJDSmttOXVXK0FYYzgxTks3R3hFYi9wUmVpQXh5RUFUNVlZckJodVl5L0FtWWl3MUZMM0k1Z1hvSHpXNmswUFYrQWRIc1RuZzBLMjZQTUFvT014dVlBYnJ0MDExbjNQQU9idmxJbjNacCsrZzJlYTIzVVI3VjIweFI4VWlTZzIxbWVHc2l4UWY3KzlKajZKdEd0WFNJRjBFblozejVtTFVjTDZVTFhKOUgzR0lSOFRZS045VEYxbktES0RLWEtFM2FXbEplMzlJKzhWM0F0L0hwVTQ0OGUvOEFEMDZnQ3hPNFhMcmNIZFp6clhMYWRzL0p5YWxvUWZvb0xTMWM0ZE5DQWNDQUlNVDlvRnQwMjFIUWZCendRMjRqb3VpdTJmTzUyaXZzWDYyVGhRb2VOcXNUYWtCd2hMb1FmUzRIV1kzR3MvbEdHRkROcnRxQm12MkpSbmVrYlVtU0k3ZnRkQzBmRW4rdjU0QkZEZ0xVWVBFTldaY0JJbVY4U28wWjd3RU1FSENSaS9tWmp1aDVuOHNrcmNadXFPMEdRRWRYRlc0MTUyNHNXWHV1bWZ2d3ZENDEzMlg1WmZzSS9KOUNYSXlINHhOUllkL3hXcG9TOStqWVBrQVl6Nk1sMDd1QUk2YjFwNWhvQkxqZFdERGR4T3VSSDhJR2VuR2EvSTZPOXoiLCJtYWMiOiI0YWJlMGIxMzMxOTlmMjg3NTliNjY0YjEzMDEyYjFkMzBhZGNhYmJiODQ2ODI1ODU0Y2RkOWZlYWY2MGU3NjkxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjdOUkl5RDdpUmZjYVU0TnVjaGlQbUE9PSIsInZhbHVlIjoiZmpEaExZa0pBdFBOdzRKNit5dW5wZkVVY1dwOWlnVktZV1gxTUxvT1crN3AwelBLSCsxOTJiY1Q0a1lURkw3bkRjTVI0UFdqaHNFazB5L1JoUStNdHk0eThkek5WbDdWNFNmWkE3UEw5amtFTThmaVVRWW1ZME9Ob2hZRXJMalBuNGJxU1VsYi9DNlFvSWZvZ1g3NUtxbG83WmtyODlRbFAyTDdWV1VRbGV5blpsaWpPZlNEQWxhTU91UVIvaXB2TWJuc3NWU3F2citHQzNDVTZRWUhoVjl5U3o1TTZLd1BQSXFaQ25SRHlTai9QTmJ0bkNqa3AvQzFESVM4V2NsaFlmd1dnUkc5dE15Tk1YUGtla01ZaitpK0Vlem5lRDhpamtGdkpkbU5HR0JRTVVSYXhkTWwyUDJmR2o1TVJMbjVyL2FVbVhjTlpQY3lGeGhQNEFkMkJwRkFUeG03TXBFancrYmNHOW45R1BPZzFPbzRHcGplMVBRaFNqM3NEZnhUN0hHTTBKU3I3MXQ5N1RrenJrZkJoTVpGaWNTMTJyUE5jYXVoaDBsTmVaTithWDJqUUR4VjdVSklFYksyeURTb1UyMHZHbWwvUmFNTkZ6VldRb3EvckhOSEhjejE0Ykt0REZxYVhFS054b21lM0ZleWJOaW5iNUZ4MXVvMW9JcTQiLCJtYWMiOiJiMWNiNmJiYmQxODcwNTM4YzRmYjNjYTI1NmNiYzgxYmI0MzNiMTIzYzgzNjQ5Zjk1YTc0OGRkOWQ4YWE5ZmZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-578923434\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1611390806 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDWN9h6R9gAIdzZaXBzXfq0WGpauVfrUj70kKWA2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1611390806\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1616061338 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:55:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdDMWdncG4wQmlKUmlDNzNZckhNVFE9PSIsInZhbHVlIjoiZjQ0NEdtZWtxcGY0MTlRaTYxc2VscWNWNy8rS0I3Q1N3ZS9raWtIRGZRL2sxa2FFSzdYWnAweWM5REtGR3BuQVA1dkw5L3NJYjdpU20yMTNBUVBoNWhORlBVZ2FjK3ZSUU1qdW5yOTdTdW1sODVXUzJFV2V6ZVl2UGNOUDhacU5NY3dNOWV4L2QrOFp6STZ6bVlQN1NrR29zdmpKdndsLy9hd3N4bXA5TXorU0p4andNMGRvbmtXTEswMm0vNm5UclBtS1FTTG9TV0lmaXFHNXZVWE04ZVUxZ0dzR1M3M2tnSGhSNDloOFJuQ2tWUVhFbCtYT2J2bWhpeFhEL29laFVHY2pROTRWUjJKSzVqVy9nNkpBRUkwbjM0YXNVdnhENUZIWlFPNUw4Rzd5aktxbHBMbmpFOFRSc0JPb3NjVFp5ZytzZXhlMW1uREx6T3RWUVgvU1BiK05BN1ZLZk1lclF4RnVjWDFWVmpHMnQySExkZmMrWEg3d0E4Q3F6L3ppY0VIQ1NmTVlIYWlFR0srZnZGaU9la2s4ZS90dkxTenltS3lVd2Vod2w5eFhrSTZPcStnZjdFcHQwQkRUcHM2UWhvTXI4YkVRNEVOZlI0T290OTQ3WVJrMlFDTm1uUU4wbTRDSFM3VjU4bGRnWGNlckczWlZObFV3SjY5MVp1ZlUiLCJtYWMiOiIxZmY3N2UzOGYzYWM5ZGE5NmU5N2IwNDk4OWFlNjgxNzEwNzM2YjJiN2UyNTYwOTgyYjk4MTkxMTdiMWQ5MmUxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:55:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IloxMWtsbkJibDFQSzFCbnRpYXEyUlE9PSIsInZhbHVlIjoiYUZ0YzJOSEVPaWRUaUQyUGloQlV6Um9EbGw3QWNXcVZpazQyZFN1aWJlc0Q2cXpxa2x3RWhzU2J4NjNGSVR6MXpkVmRKb2Y4cjJjT2s5dkh3ZElMaWN2S2I2cm5VakRlazR2aUs1dW83YWE1WFRxaWVDY1dTUm1ReDdweGFUQUNVRWwxcGx4WUpMYi9FUjRWek8vMmFyQVAzL2NNOWZNUkZDQjMzUnZ4QVJHOHJib1lZVHNNTi9sY0hSY2pxSjVRa2pHK0FTVEdGekNCUE5JUDZhRVgwa1VXM1RxUHB5K0dYM0RpVzdOWkJDR3ZPN0ZBekxlcGlUYWNoZE9kQ0NmYzEzYkJndG96NHhWazlJZk1la2F5OEc2aFEzUVRFRlk1Q1lvNWxLRlRQY3c1cU0rOCtLUW43ZC82bk54K2RNRnpXYzNFWU5tc1hRZDdCbGRNbXB2ZXI4SXZiZkhNbVh3V1RVV3F0Y0tIRmF4RkxZN3hVaDkvbkFPakl4SXZ0U3VLaHc0bFh4ckFNWktqM2VWVUNYaU5mbnB5VHdGUWx3UkVjUVFKM3ZyM0NKMCtSVExaa2p2OFptWThLRmVPVG9mTUl6VldRTzRCM0IzOHR4QUJBS2JDNUtLK3Z6aXVYY2x4Tnp0TkI0OWVxNkpOSjZnMVVoR1pWa3RxNmEvMnVnWHIiLCJtYWMiOiJiOTlkODQ0ZDRiZTMzODBkOWQyZGJjMzExNjhjY2IyODk2NjhkNjA0NmU5YjE1YjBkMGFkZWVlNjAwZTIxNjAxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:55:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdDMWdncG4wQmlKUmlDNzNZckhNVFE9PSIsInZhbHVlIjoiZjQ0NEdtZWtxcGY0MTlRaTYxc2VscWNWNy8rS0I3Q1N3ZS9raWtIRGZRL2sxa2FFSzdYWnAweWM5REtGR3BuQVA1dkw5L3NJYjdpU20yMTNBUVBoNWhORlBVZ2FjK3ZSUU1qdW5yOTdTdW1sODVXUzJFV2V6ZVl2UGNOUDhacU5NY3dNOWV4L2QrOFp6STZ6bVlQN1NrR29zdmpKdndsLy9hd3N4bXA5TXorU0p4andNMGRvbmtXTEswMm0vNm5UclBtS1FTTG9TV0lmaXFHNXZVWE04ZVUxZ0dzR1M3M2tnSGhSNDloOFJuQ2tWUVhFbCtYT2J2bWhpeFhEL29laFVHY2pROTRWUjJKSzVqVy9nNkpBRUkwbjM0YXNVdnhENUZIWlFPNUw4Rzd5aktxbHBMbmpFOFRSc0JPb3NjVFp5ZytzZXhlMW1uREx6T3RWUVgvU1BiK05BN1ZLZk1lclF4RnVjWDFWVmpHMnQySExkZmMrWEg3d0E4Q3F6L3ppY0VIQ1NmTVlIYWlFR0srZnZGaU9la2s4ZS90dkxTenltS3lVd2Vod2w5eFhrSTZPcStnZjdFcHQwQkRUcHM2UWhvTXI4YkVRNEVOZlI0T290OTQ3WVJrMlFDTm1uUU4wbTRDSFM3VjU4bGRnWGNlckczWlZObFV3SjY5MVp1ZlUiLCJtYWMiOiIxZmY3N2UzOGYzYWM5ZGE5NmU5N2IwNDk4OWFlNjgxNzEwNzM2YjJiN2UyNTYwOTgyYjk4MTkxMTdiMWQ5MmUxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:55:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IloxMWtsbkJibDFQSzFCbnRpYXEyUlE9PSIsInZhbHVlIjoiYUZ0YzJOSEVPaWRUaUQyUGloQlV6Um9EbGw3QWNXcVZpazQyZFN1aWJlc0Q2cXpxa2x3RWhzU2J4NjNGSVR6MXpkVmRKb2Y4cjJjT2s5dkh3ZElMaWN2S2I2cm5VakRlazR2aUs1dW83YWE1WFRxaWVDY1dTUm1ReDdweGFUQUNVRWwxcGx4WUpMYi9FUjRWek8vMmFyQVAzL2NNOWZNUkZDQjMzUnZ4QVJHOHJib1lZVHNNTi9sY0hSY2pxSjVRa2pHK0FTVEdGekNCUE5JUDZhRVgwa1VXM1RxUHB5K0dYM0RpVzdOWkJDR3ZPN0ZBekxlcGlUYWNoZE9kQ0NmYzEzYkJndG96NHhWazlJZk1la2F5OEc2aFEzUVRFRlk1Q1lvNWxLRlRQY3c1cU0rOCtLUW43ZC82bk54K2RNRnpXYzNFWU5tc1hRZDdCbGRNbXB2ZXI4SXZiZkhNbVh3V1RVV3F0Y0tIRmF4RkxZN3hVaDkvbkFPakl4SXZ0U3VLaHc0bFh4ckFNWktqM2VWVUNYaU5mbnB5VHdGUWx3UkVjUVFKM3ZyM0NKMCtSVExaa2p2OFptWThLRmVPVG9mTUl6VldRTzRCM0IzOHR4QUJBS2JDNUtLK3Z6aXVYY2x4Tnp0TkI0OWVxNkpOSjZnMVVoR1pWa3RxNmEvMnVnWHIiLCJtYWMiOiJiOTlkODQ0ZDRiZTMzODBkOWQyZGJjMzExNjhjY2IyODk2NjhkNjA0NmU5YjE1YjBkMGFkZWVlNjAwZTIxNjAxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:55:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1616061338\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1067113585 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1067113585\", {\"maxDepth\":0})</script>\n"}}