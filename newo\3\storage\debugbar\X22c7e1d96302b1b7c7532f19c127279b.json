{"__meta": {"id": "X22c7e1d96302b1b7c7532f19c127279b", "datetime": "2025-06-17 05:41:04", "utime": **********.770238, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750138863.350812, "end": **********.770284, "duration": 1.4194719791412354, "duration_str": "1.42s", "measures": [{"label": "Booting", "start": 1750138863.350812, "relative_start": 0, "end": **********.589045, "relative_end": **********.589045, "duration": 1.2382330894470215, "duration_str": "1.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.589075, "relative_start": 1.2382631301879883, "end": **********.770289, "relative_end": 5.0067901611328125e-06, "duration": 0.1812138557434082, "duration_str": "181ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45156424, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021289999999999996, "accumulated_duration_str": "21.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6750672, "duration": 0.018699999999999998, "duration_str": "18.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.835}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7219, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.835, "width_percent": 6.764}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7433622, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.598, "width_percent": 5.402}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-538150814 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-538150814\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-579885426 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-579885426\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-755575675 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755575675\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2021568885 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750138856930%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFwTlBzZDRJdFZleXFmNVY5SkhvckE9PSIsInZhbHVlIjoiY2piL2t6V3NkeEZhSFNDTmNGVXR3ajlKRjJRd21NWnV5N1oyVnc0MUtjZnZKcDJES3NhWWNZVVc1SHZpeUJWOGRKTGR1M01Dc08rZVZXQnZWQ05ZaEU1Vld3dnhOeDRCMWcrcGpVWGhyRGtEVDZ6WDhHN0dPdFZHNVRyQjFlWnIvVzQyUG84dW8xZ3RqOTBnaWtNb0gzQjR0K3FXTHFxL1ViUXdxQzFoSkVSN1YyZ0d6WXZSQ2lYMGp1b3Bucy9zSUgvekxOWDgyS0ZlcXExcjlaQ05OdjFFL1hOTjhReDdNbXhBRHh6U3VvTEY5K1N0U1JlbDJCNGtqYTJsNkhaMWxSN2N6VVFXVVg0Mjg4VkdTSjB3Zko0a2dvR25mZTJQK1M1YVArQzhzK2tCZkR4V0NPTVdFQXhsU1dOVUhaRWhkMHdNbFhFbjNTMFRnR3gyQnBMUEhKOHFueDBxbkdGckphNWtMNU9rc2RNL0ZXMGQ0OFRPcmp6N2M0eE5pVXluVEQ4NVJRcGYyTDI0aHhob3M0V29qemRhYmNhM2FjYkJHWXhOc09NajZaOUg0eWhadzQ3YUMrM2krTWI5Sm5UM3Y1VVZxUGtjYW16TGF0aExEbW5tbnFJNkpqbXNoU1VFbFRXZVJsOHQrRjYxRDk1Z1BsWms3QzFFckNrQkJCeGUiLCJtYWMiOiI5ZjliMmIwMWE4YmNkMzUwOWRjZmJjOGY1NjllZjMyZTIzMjI4ZWU0YmFkMWI3Y2ZhNWVkNDM0MWMxYjQ2YWZlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImpGMjBlK21tanJwOVB1YTNuKzJVMGc9PSIsInZhbHVlIjoiSHgyQXhPcUVpWGRva29EMkwzbTEzaXM2K2NoeFZsbmc4VVJweExTOTZtODJpZVlxZk9SeHRPS2szZ2Nia2JsTEFOUkJEWDRNdXo5Uk5TU3hFaXdlaWcyYnRKdGZLbDhYdDZSR0trQlh6SmxvV2ZhVFlNLzZWajRHMFJXVnVESzlaRlN2R3ZNMFo4eTJvYW9mc3JaTEVLN1J1UDV6UGhITjF2UEgxK0k4aHZlVTVOY2VxUU9zV1d5NStaWEQzWHhQdDNFMzVhSVRyc1dQWDZKS0JCWTFKS3BkU1A2cTNtQTAxeDZXS3RsZm5EVkV5SVluK0R2S2tjWlhkYXNSd3QxR2hmTGZ2UE9uczFUMm9hUHMyMjRKVy9LYi9taWNIWUFqcXVtY0c5a0FaRDE1aUg2eEt6WFdYR3RoMkg3VDFDNjlRZFRvYlNXMWszYU9pTzk1bXRNNVg5YXhVRWI2QzBXOUNKcUFiZWRJMjhDbE0xVExGWUJKdWZwbjQ2T1ZBSmpmWTZBa0JEampKV1NLQjJLMEhuejdZOENSN3ZqekY5dzcxK3psdSthdXR2OUYvV0IzbUliM3hiMHIwc1JQSUJwV0s3N0lpTEw2Y2pwTUl4QXhmOGdjM2pENUxXUWl5UTFxaDh6K2YxdWhUR3VVanFWMkNiWVk1LzFEREc0L1dCaksiLCJtYWMiOiIxNjExNDBjYzdkYmI0MDM2YjAyN2QxODUzNzMxZjAwMzRjMzUwMWM3NTJiOGJlNWE0M2ZlN2I3MzQ0ZGY4ZTlkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2021568885\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1948828579 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1948828579\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2123293701 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:41:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldaajF6QlJhOHEyWmJXb0M3cDEwcEE9PSIsInZhbHVlIjoiem5RcVdnTXZETkdETDUzS2MvNlZ3Z0NJbS96VDZEdlZZYjhsVGFDU3BneHdEbWhENXV0YnVzcXdDUzJZVEw1a2ZqVjM1VHF5ZWJNN2JiWVVCSCtqK3YrVnk5SHFkSFhxUGhML2NuNmFPRmFiMkdodEF2YlpDVVhFQVBSTHZWYlZaUGVid0d3SHJUa1hIUm9QVGFNVXc4eUl0Yzh6d05SUkVWcmRTTG5pVHpCOXZzOGFsR3JBK21JRlZTY1IzWVZHbUlLWVlSaHZVYStzK1J6ditLamRNd3dPUTNnT1VjZmNrVDI3UXg1Wmp4UlJkcWFIOGJxTWNtOGVkc3dHZnVGNkY0ekNESmo2VC85NXZTenFuT1NPcEZZMHg4cGg2eWpBZURMWXRJNGsySmtlMFJYeEV2cW9hclJEbVV4TlVwWHpmeVIycUkwSWhDQ0lzTFFucFFRZTFSUnFBcHp2ZUIrZ2tJQ1FCZVUwakUrSnNORWJyaFlUNE5lQnR2SWRuM2tTVlg5Zk50WTZHcmFMaGdrNVBCaU9idzFDekkwQTlmSDNVaXkxaVFiVEZhVGJNM3hDVjFZb3ZTNkxKdFdVWDE5SE5ka3N4WXRkbktSKzZBZ1k0Um4vWkRXR21lbnBPNDB6UTQ2RkJxMFBkU3hRVk44QjV3NzMzeE8zK1dDRUt5b3ciLCJtYWMiOiIwNDc4MTZiMTUyMWRjZWE4MDhjZjkwMjQ1YTMyYmQ4YzIyMmI4MjViNWQ2OWQyNGE2NDVkOTg4NzljYTU1NGU1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:41:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZMcktKeFVlQXE0d1lDeFlWalZOUGc9PSIsInZhbHVlIjoiRVBhblJrYjZkNVZUcEpwZjMvdVlFSU1HaFZvYUVYaXdUU3VYWnBWSEdLSXVHVWdoYlpqOGVxbFByNDEwOGN5R1lMZjBKTnh2bWNVWXpvbmFrRHRKZTlEZTdkdTRLNzBzMEhlc25iQjFxeDdiOFFYdnJVSEpWRGdYd00zS05RTThkT09TcWNLbkMxamRjR2NyRlhnQWZKY2V3Zk1uYUdmTGJjS1l6WDdDSHNyaWl0cktkQUcyYWl2aFN2NWRlNjF3WmhtRmVINHZxMkFjbGd5WGlKYThReUc0NnRnT3gwZVhkU2tjbDB3Q3k2WHRkLzEyTnE0NTViSVR0TVNXN1hMejAvQXYrQSsrUnBoUVZ3WUFpaUR1cm0vWGE1WXAyYm05TkhRY25qWDdWVDJLekZUeGw0ZjQwV2FqSGZoNEdLRkxUeUhvNEcyTmZyVndML08xOUl1NkpYK3YyR0xJTXNHK0p1Vmk0WEorZlI0SzVzdmhINEZrYzF6dTRUekNqYjVaamRUVXh0OHN0aTZ0UkFsMi9qaHdFOE5seC9VZTNpdk9nTVA2L0F1Yk1lYkR0aU9PYXVDSmVNRmxadmNFb1ZYRmVTcEZtMDdoUzR5Z1FJUXdwdFZFN0xndUl5NTJDWlQ1UGlUZEN2VlBPWk9EVXNJL3hTa3k0VlZoRWg2VVFWMlkiLCJtYWMiOiIzMTYzMDJkZThiNWM1M2Q2OGE0NWM3ZmUzMWZkYmRkNGIyMzZiYjllNTlhYzY0NDYzZmYzZDY0NjBlMmVhZDMzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:41:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldaajF6QlJhOHEyWmJXb0M3cDEwcEE9PSIsInZhbHVlIjoiem5RcVdnTXZETkdETDUzS2MvNlZ3Z0NJbS96VDZEdlZZYjhsVGFDU3BneHdEbWhENXV0YnVzcXdDUzJZVEw1a2ZqVjM1VHF5ZWJNN2JiWVVCSCtqK3YrVnk5SHFkSFhxUGhML2NuNmFPRmFiMkdodEF2YlpDVVhFQVBSTHZWYlZaUGVid0d3SHJUa1hIUm9QVGFNVXc4eUl0Yzh6d05SUkVWcmRTTG5pVHpCOXZzOGFsR3JBK21JRlZTY1IzWVZHbUlLWVlSaHZVYStzK1J6ditLamRNd3dPUTNnT1VjZmNrVDI3UXg1Wmp4UlJkcWFIOGJxTWNtOGVkc3dHZnVGNkY0ekNESmo2VC85NXZTenFuT1NPcEZZMHg4cGg2eWpBZURMWXRJNGsySmtlMFJYeEV2cW9hclJEbVV4TlVwWHpmeVIycUkwSWhDQ0lzTFFucFFRZTFSUnFBcHp2ZUIrZ2tJQ1FCZVUwakUrSnNORWJyaFlUNE5lQnR2SWRuM2tTVlg5Zk50WTZHcmFMaGdrNVBCaU9idzFDekkwQTlmSDNVaXkxaVFiVEZhVGJNM3hDVjFZb3ZTNkxKdFdVWDE5SE5ka3N4WXRkbktSKzZBZ1k0Um4vWkRXR21lbnBPNDB6UTQ2RkJxMFBkU3hRVk44QjV3NzMzeE8zK1dDRUt5b3ciLCJtYWMiOiIwNDc4MTZiMTUyMWRjZWE4MDhjZjkwMjQ1YTMyYmQ4YzIyMmI4MjViNWQ2OWQyNGE2NDVkOTg4NzljYTU1NGU1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:41:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZMcktKeFVlQXE0d1lDeFlWalZOUGc9PSIsInZhbHVlIjoiRVBhblJrYjZkNVZUcEpwZjMvdVlFSU1HaFZvYUVYaXdUU3VYWnBWSEdLSXVHVWdoYlpqOGVxbFByNDEwOGN5R1lMZjBKTnh2bWNVWXpvbmFrRHRKZTlEZTdkdTRLNzBzMEhlc25iQjFxeDdiOFFYdnJVSEpWRGdYd00zS05RTThkT09TcWNLbkMxamRjR2NyRlhnQWZKY2V3Zk1uYUdmTGJjS1l6WDdDSHNyaWl0cktkQUcyYWl2aFN2NWRlNjF3WmhtRmVINHZxMkFjbGd5WGlKYThReUc0NnRnT3gwZVhkU2tjbDB3Q3k2WHRkLzEyTnE0NTViSVR0TVNXN1hMejAvQXYrQSsrUnBoUVZ3WUFpaUR1cm0vWGE1WXAyYm05TkhRY25qWDdWVDJLekZUeGw0ZjQwV2FqSGZoNEdLRkxUeUhvNEcyTmZyVndML08xOUl1NkpYK3YyR0xJTXNHK0p1Vmk0WEorZlI0SzVzdmhINEZrYzF6dTRUekNqYjVaamRUVXh0OHN0aTZ0UkFsMi9qaHdFOE5seC9VZTNpdk9nTVA2L0F1Yk1lYkR0aU9PYXVDSmVNRmxadmNFb1ZYRmVTcEZtMDdoUzR5Z1FJUXdwdFZFN0xndUl5NTJDWlQ1UGlUZEN2VlBPWk9EVXNJL3hTa3k0VlZoRWg2VVFWMlkiLCJtYWMiOiIzMTYzMDJkZThiNWM1M2Q2OGE0NWM3ZmUzMWZkYmRkNGIyMzZiYjllNTlhYzY0NDYzZmYzZDY0NjBlMmVhZDMzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:41:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2123293701\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-214579187 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-214579187\", {\"maxDepth\":0})</script>\n"}}