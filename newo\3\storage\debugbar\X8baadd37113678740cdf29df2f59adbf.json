{"__meta": {"id": "X8baadd37113678740cdf29df2f59adbf", "datetime": "2025-06-17 07:14:19", "utime": **********.207209, "method": "POST", "uri": "/inventory-management/update-min-quantity", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750144458.452413, "end": **********.207233, "duration": 0.7548198699951172, "duration_str": "755ms", "measures": [{"label": "Booting", "start": 1750144458.452413, "relative_start": 0, "end": **********.058737, "relative_end": **********.058737, "duration": 0.6063239574432373, "duration_str": "606ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.058756, "relative_start": 0.6063430309295654, "end": **********.207236, "relative_end": 3.0994415283203125e-06, "duration": 0.14847993850708008, "duration_str": "148ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50992616, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST inventory-management/update-min-quantity", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\InventoryManagementController@updateMinQuantity", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "inventory.management.update.min.quantity", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=203\" onclick=\"\">app/Http/Controllers/InventoryManagementController.php:203-240</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02064, "accumulated_duration_str": "20.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1094751, "duration": 0.015560000000000001, "duration_str": "15.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 75.388}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.142286, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 75.388, "width_percent": 5.766}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '7'", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.161768, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 81.153, "width_percent": 5.475}, {"sql": "select count(*) as aggregate from `warehouses` where `id` = '7'", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.166508, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 86.628, "width_percent": 3.779}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 212}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.177213, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:212", "source": "app/Http/Controllers/InventoryManagementController.php:212", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=212", "ajax": false, "filename": "InventoryManagementController.php", "line": "212"}, "connection": "ty", "start_percent": 90.407, "width_percent": 0}, {"sql": "select * from `warehouse_product_limits` where (`product_id` = '7' and `warehouse_id` = '7') limit 1", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.17806, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:215", "source": "app/Http/Controllers/InventoryManagementController.php:215", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=215", "ajax": false, "filename": "InventoryManagementController.php", "line": "215"}, "connection": "ty", "start_percent": 90.407, "width_percent": 5.717}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 215}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.185707, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:215", "source": "app/Http/Controllers/InventoryManagementController.php:215", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=215", "ajax": false, "filename": "InventoryManagementController.php", "line": "215"}, "connection": "ty", "start_percent": 96.124, "width_percent": 0}, {"sql": "insert into `warehouse_product_limits` (`product_id`, `warehouse_id`, `min_quantity`, `created_by`, `updated_at`, `created_at`) values ('7', '7', '0', 15, '2025-06-17 07:14:19', '2025-06-17 07:14:19')", "type": "query", "params": [], "bindings": ["7", "7", "0", "15", "2025-06-17 07:14:19", "2025-06-17 07:14:19"], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 215}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1862922, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:215", "source": "app/Http/Controllers/InventoryManagementController.php:215", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=215", "ajax": false, "filename": "InventoryManagementController.php", "line": "215"}, "connection": "ty", "start_percent": 96.124, "width_percent": 3.876}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 215}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.191758, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:215", "source": "app/Http/Controllers/InventoryManagementController.php:215", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=215", "ajax": false, "filename": "InventoryManagementController.php", "line": "215"}, "connection": "ty", "start_percent": 100, "width_percent": 0}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 226}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.197443, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:226", "source": "app/Http/Controllers/InventoryManagementController.php:226", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=226", "ajax": false, "filename": "InventoryManagementController.php", "line": "226"}, "connection": "ty", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/inventory-management/update-min-quantity", "status_code": "<pre class=sf-dump id=sf-dump-755042587 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-755042587\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1743017022 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1743017022\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-319777995 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>min_quantity</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-319777995\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-229474127 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">90</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=jfj0s4%7C1750144452817%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImdyU1hzdlc1QTJTdkp1RFMwOW9ONlE9PSIsInZhbHVlIjoibm9hcG16MFlZQmlQMHpFZzNCdGFjSkFNbWhSeVJWQmJVS3dZYjhtVHduOWNwK3ZlK2M5eGhMV2VSaGxRVmZPNUw4Zng5MWlzSDNmYnZtSENTWkxGNlFFRXZJTXRESzJ6NUM4QVh5MUR5TytJNmlnSlM5OG5lWkhlZnhJQmJHQWFQMTZJRi9PWWJEOG52NEROaFg5Qm9QT3prbzNuOHlXbnV2L0dVMzIwdUFYVWNDcDJ5aWFLYzg0MC9pNU9VaFI1bVVwcFdEcm9hdW5YR0FiZEVIMFg2em1CeGZFZGZWRHNHNnZySnY4NEZvbHNZNWdEVnJyWGtqNkxiVlRoLzdiK2JuTVRQZ2RkV1pmNk5kd3dzaWRpc0VLclZsTGxQNHRTZGhMTHA1cUh1UlkwNHBIaDAzd2xqSnpMRCtiNWZvZGVsamJhTjdpVTZEUHBMVTRUQU5vQnZmN1kvbVY1dmJaVnowbHREbU44UEJ1SFNCL1kvSHVwVHZpVVhBVkRmb3NHdFFDU0hTZ3J5QXo2ZDZ3SkZDOVFNcEJmOElTOGtQQ3VzK2ZtTWhiU1dUL29iNjkrV1RrdlBTVWhVQ2x2eUk0Rkc5NXdONXFoU2l4R3Rvd2FMUmx0bzNnc1RYbWJic0s3N2M3RmEyRzdycjlUeWZ6VExHcVdjU0lsTDFvWDB5SE4iLCJtYWMiOiJiMjIzNjgzYjAwYjE1OGNmMTMyZWMxNTMyM2VhMmQwYTFmNDM5MmY2MTA5YTc0OWU1MzY2MDk3MDg5OTRjZGMxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImhhRE55VUhqUTh1ME1IczNRSGJla3c9PSIsInZhbHVlIjoiaGpPRTEreE13ZHNUTWFWQUlxd2Y2MVJ3TVJXMURTbTVIRU93Ykl2T3BVSXk1SCthMkpoNXdpM2l2QmRBSS9BblNBcVZuOCtlY3hhcjRjZ0NqNzk2eUN1QlRwUHNkNFdXT25pRDNXZ3oyVkkxclVnZytxcnIwbEIyRmpIaTNoOUhXVWtta09FcEpkR2VMWWxKUGY3bG96SVhHVkZ1TnZoaHdsRlVZUklLMzlIeGVKcnZKemMvdnd2VE1wVEJETSt2Q2MzemxGYU83QjZSbW9CNm85T3RoTk5GWUtzWEIzTHVRQllCZ2NyT3dqNVdpb2RQa1RxYVRBMVlzRGZuTm5JV1VuMjVzelFKRElHc0RCRVNETVBmaThxNWZEYWVTT0twamRLOE5zOCtmUVpKb1ZhaEVTdGNoaldlR3RCclVsOFRxTXpNZ3lUd2hCanFBaXRiWm0xb1ZNNG1HSTZYR2FTa01KNElGV2FjMVBUcER2RGtYdFpnaW9YZEJibXE2bnRVRUZIYXc2LzBZV2ZUenhqYUtCZmh1UXVTeFhMYnM2MEpUV0ZpV1hCMVVBZHRzWXlRT29RU3ZQcUlpREpkVzNwVkhZOUhKUERjMDhobjdwNXp4MHA5djhiSFRDVDJQTStKQ282MTZuYmZBRy83YjlWZVNXczRhZnBTSE1xZmpNaXciLCJtYWMiOiJjMWY2MWVkMGM5NjU5YzMzNTA2MmE2ZmQ2NGFiYmY4ZGI3YzZiMmRlM2I0NGM1ODQ5NmM1YzNjOWQ3NmNjYTg1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-229474127\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-125240859 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-125240859\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1589424841 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:14:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxDY1V1VjIrU0tRVEFsSUg4RkFDN0E9PSIsInZhbHVlIjoicFpHMkZaaVVNU2YxWnQzU0NnUHZmN0FQaGR0bGF6Q2tvdDhPS1EyV2U0VnY2MzdabU9LVjRDa3U4RjM4MThVTzlRd2h5V254akxuZVBJNzJvNHlKZ2JXc0EwZDQ4NDEvTEIzVmpuQjZFaXcyRlQ0NWtkL3VOSmVneDV3ckU4RVkxRXFWOU1id1EzeTAvM2NzMkprMDM3K1NiamIyVC9odnhma0xhWU92NXBsUGs0cTZDTHRSR0pjczhDTWlPZUtRU1FFODA1YmJTZGJTcFpQd2ZLSXRISkhEWW5ieU9sUmtGL3dlWDNoS0REVnhzM1QxYjFtcnA4aVd1OHJkdkJoNFkxV3B5TGxmalBodG9JdlMyaFEvSWZQdnNuR0xxaGRoUERLN0xUdlk2YlBiUGk4TFRSUXlRYWo0SVg0dUxuekVCUUxyby9BVzBRNE03MFlJTkY0WDJRaGd4OUF0M3psL0pnUDQzMWJTeVQvcHE4U1hEQjBhRG9pdUpvWXRnUWozdkdxdEIyenNwdERXTmtUTWlFNm5PNnhHdzlLanYvUjFSVWlraDlyT0NjWk03ditZUk5iNFhhREZqcFJ0VVBDMXNBbm13d2RsSEl5NHkyNngxOVpvVFRGcHZuWjNJME9iSG1EdlVZZTA3SGZrb2Z2d2t5T3VsbnhXdUFwSEh3VXkiLCJtYWMiOiJjY2YwNWM1MTg2NTZhZTgzZTFjOThkZjZmOTgyZjAyNGFkNzQzNzU1YjQ0NWI0M2E5MjdkNjA2ZmNlZWM1M2I3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:14:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImNaa3N0SXZFV0VKcjd6aDNhTnFNZEE9PSIsInZhbHVlIjoicUowcktYVDluaDlzcTlJVGwxUjNqMzRSdWhEMTFlb3gxL2s0b29wUGp4M1VXeCtOMWQ3d1p5YXQwb2xteWpGRCs4UUpISmtCSFNQeEdZcHBoY2tLamtWSklybTR4anVWazdVb3ZkS1U2c3pnODdjWUgvdGovelVSbDZ0eGMyU0pKb2RjalNjejhUeEViVmN2QjRpUmZaQWJOS2xOQmVsb2lrbXZOa01tUEJTaTFLN2JSUHZMa2VUUTg2a1dJcVN0U29kLy9WcWRtT2cvd0crNGRZbXF5ZVd1aHFqM2NFY2hvY2UzMWtWd2tPV2pkeFhzLy9TYnlFaVNKL0g4b3krQ2tLUERSVEovMis5OXIxS0RYdlZLKzM5bmZtRFNwbnlrMWFDdnlkOHM3bGZMMzhBdXpYRlhZSlFpaUdBd20rY2JGUnBpbEo0NnJ5Tkp6aCtVT21qZ21wKzNvUGQra3lSSExMbUVRbWQ3ZWlBM0piVUs0cE9LZ2NiTzVlM2tRR1JyQTNsQVRvd04wSmlQOXMzMmVyU0hMa3NXNWlqRGlGLy9DR3Q4Si91dk1CM3UrQ0ZPOUFheU42anI3eUJlNUxrdE5icS8vMzVTSjdNcmx0NnZtaXRTbjNjbUFFMmtRbS9MT1Z2b0VqdE9Pc2JDNTdTUFo2Q1dBRVFrOE4rMHpIRC8iLCJtYWMiOiJkYTRjMTM5NWI5ZmU3YmMzOWU5NjA3ZmIyMDFjZjg1OTg2MzE0NzU0MTE5MTBiZWVmNjY3ZGQ3ZDg0Y2EzNmU3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:14:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxDY1V1VjIrU0tRVEFsSUg4RkFDN0E9PSIsInZhbHVlIjoicFpHMkZaaVVNU2YxWnQzU0NnUHZmN0FQaGR0bGF6Q2tvdDhPS1EyV2U0VnY2MzdabU9LVjRDa3U4RjM4MThVTzlRd2h5V254akxuZVBJNzJvNHlKZ2JXc0EwZDQ4NDEvTEIzVmpuQjZFaXcyRlQ0NWtkL3VOSmVneDV3ckU4RVkxRXFWOU1id1EzeTAvM2NzMkprMDM3K1NiamIyVC9odnhma0xhWU92NXBsUGs0cTZDTHRSR0pjczhDTWlPZUtRU1FFODA1YmJTZGJTcFpQd2ZLSXRISkhEWW5ieU9sUmtGL3dlWDNoS0REVnhzM1QxYjFtcnA4aVd1OHJkdkJoNFkxV3B5TGxmalBodG9JdlMyaFEvSWZQdnNuR0xxaGRoUERLN0xUdlk2YlBiUGk4TFRSUXlRYWo0SVg0dUxuekVCUUxyby9BVzBRNE03MFlJTkY0WDJRaGd4OUF0M3psL0pnUDQzMWJTeVQvcHE4U1hEQjBhRG9pdUpvWXRnUWozdkdxdEIyenNwdERXTmtUTWlFNm5PNnhHdzlLanYvUjFSVWlraDlyT0NjWk03ditZUk5iNFhhREZqcFJ0VVBDMXNBbm13d2RsSEl5NHkyNngxOVpvVFRGcHZuWjNJME9iSG1EdlVZZTA3SGZrb2Z2d2t5T3VsbnhXdUFwSEh3VXkiLCJtYWMiOiJjY2YwNWM1MTg2NTZhZTgzZTFjOThkZjZmOTgyZjAyNGFkNzQzNzU1YjQ0NWI0M2E5MjdkNjA2ZmNlZWM1M2I3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:14:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImNaa3N0SXZFV0VKcjd6aDNhTnFNZEE9PSIsInZhbHVlIjoicUowcktYVDluaDlzcTlJVGwxUjNqMzRSdWhEMTFlb3gxL2s0b29wUGp4M1VXeCtOMWQ3d1p5YXQwb2xteWpGRCs4UUpISmtCSFNQeEdZcHBoY2tLamtWSklybTR4anVWazdVb3ZkS1U2c3pnODdjWUgvdGovelVSbDZ0eGMyU0pKb2RjalNjejhUeEViVmN2QjRpUmZaQWJOS2xOQmVsb2lrbXZOa01tUEJTaTFLN2JSUHZMa2VUUTg2a1dJcVN0U29kLy9WcWRtT2cvd0crNGRZbXF5ZVd1aHFqM2NFY2hvY2UzMWtWd2tPV2pkeFhzLy9TYnlFaVNKL0g4b3krQ2tLUERSVEovMis5OXIxS0RYdlZLKzM5bmZtRFNwbnlrMWFDdnlkOHM3bGZMMzhBdXpYRlhZSlFpaUdBd20rY2JGUnBpbEo0NnJ5Tkp6aCtVT21qZ21wKzNvUGQra3lSSExMbUVRbWQ3ZWlBM0piVUs0cE9LZ2NiTzVlM2tRR1JyQTNsQVRvd04wSmlQOXMzMmVyU0hMa3NXNWlqRGlGLy9DR3Q4Si91dk1CM3UrQ0ZPOUFheU42anI3eUJlNUxrdE5icS8vMzVTSjdNcmx0NnZtaXRTbjNjbUFFMmtRbS9MT1Z2b0VqdE9Pc2JDNTdTUFo2Q1dBRVFrOE4rMHpIRC8iLCJtYWMiOiJkYTRjMTM5NWI5ZmU3YmMzOWU5NjA3ZmIyMDFjZjg1OTg2MzE0NzU0MTE5MTBiZWVmNjY3ZGQ3ZDg0Y2EzNmU3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:14:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1589424841\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1090244191 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1090244191\", {\"maxDepth\":0})</script>\n"}}