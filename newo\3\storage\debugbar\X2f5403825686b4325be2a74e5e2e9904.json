{"__meta": {"id": "X2f5403825686b4325be2a74e5e2e9904", "datetime": "2025-06-17 05:41:24", "utime": **********.600963, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750138883.221988, "end": **********.601003, "duration": 1.3790149688720703, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1750138883.221988, "relative_start": 0, "end": **********.414313, "relative_end": **********.414313, "duration": 1.1923251152038574, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.414332, "relative_start": 1.1923439502716064, "end": **********.601008, "relative_end": 5.0067901611328125e-06, "duration": 0.186676025390625, "duration_str": "187ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45156232, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02133, "accumulated_duration_str": "21.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5115528, "duration": 0.01849, "duration_str": "18.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.685}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5566058, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.685, "width_percent": 6.985}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.573615, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.671, "width_percent": 6.329}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1459094864 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1459094864\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-180728135 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-180728135\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1798014457 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1798014457\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-733713489 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750138863874%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IklFazNlS3FWTVE2TXlIMGpBb2JOenc9PSIsInZhbHVlIjoiUEZNaWNjR1lzVEV0bzM3THI2eXNNVENGRnJVckJYK2ExVVN0a2phRkhpeENLWmxkelprZ3BGMGhnbzVZdU9UbGQySDhFSnJoaWVxcHZxclZRam11WjlxZk81cFJQRFhiNHJlL1VORmJBcGFzUENxTGo3eG5kZUNNRWRaVXEvMktJbW9NRnFOeGRBWnNORFpjam9VVTZjWVZDTjhTYitENzFzcE9SRVN6RUh0OHNzMEtyeVFZVzJuQVkvVEpjYVdZbmh5djlqSVYrRnlIRXJzOGFSa0d1MEJlRnZjWDV2Z3haeSsxdGJvMitNSkI4R1cwczZOQXlkZjNWZFFwNmR0TXRIUVR5TmN0VWpiRzNYOWRiUk9ZdmI0RzBlcFFkMGIxb05VbE90RnZPTHhrZkgwdCtWL212K3Y2NFVPenRTdU1KUWsxdFJ4SC9YTWk2YlArS3NzNUNTOFloeEhnbjBveTV0S3BOelpPUUVDbG5MSUZ2SWRXMU03aDgyc0gxVnR2UUpQOGZYRzRhczBLaTBWQjZCNVdrM0plL1hUbWFSTlBsRFVIaUxnZmlha0VUMFFqRHZGaW90MW1GMEVRa3hDa24yVExsU3drMUFqSDkzOHY5dUFZNmNyRlJpS09TeUFxU2ZLTjFGZCtJc1pzWTV6akJVQzZHaWFobHpzcmdaWVgiLCJtYWMiOiI2MTcwMDVhYzg3Mzk1YmRhOTI0NGQzYjIxMGU0OTQ0N2VjNzllYTY3YzkyZDY3YWQ1MWRiNTFhMDBiYWI0M2UxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImJ3QTJFU0RGTCtNc0wzMWJNNmROWmc9PSIsInZhbHVlIjoiTGZiZjlkbFU3VUhNZmdhM3BBOE10RThEclhSTW84RDVSOXZUZFdscFRMdmNLYWF5bkNKeGY4dFBoVjhyWkJYZnVrN0JKcnUzdGdnampOdHBDb1Zqalh0MzFqL2p2NUpYOTl3MUU1SktsekxBR2d3WCtPRVk0eXJMQU9ZdWV1WFYvSWZvMGd6YkJSTGg2RjcyZ2k2TFpNWldpOTJPaXJxYmdhY2tUTGlqT2xCNmNldkQ0ek9sQTh1NVlHRDI2THJtVExraXpqRzJnc2xlclh4WE12TjY2S3M4NUthMUZSSnZtNkZ0eDJDbmhTQTVTTTBPZTZtS2FHTW1CZDNjUGI3TUJsWGVVZlRhd0IzWUY4WG4xQ1BMSDRHbkptNlBMUmdDK2VaK0Nwd3VUcG0wYjZ3L0VvSUVFUEpCTyt5V29PUU1JaFQ4OTJyN2xqMkdCNldwSTA4RC92bjhBcDVSclhsZ1ZxR0c0bXU2Ui9UUlh0L2xDMUdFNVFpN3gyMFk0NDFmczNKODVYWjRYblZ6Wk9kazVlZEU5NTdiTkRkekNJYlcrSjlkbHdSNDFPMGFxNTVNTlVtKy9VMmlKQ3NFalhnVUQ2QUR3MDZsZTBhdXpqSmNOMkpXSFY3U3B6OTZjQ2JjeWtWSFZtM3lPYUM2Wm92blNXRzU1NXZLQkFyZU1lOE8iLCJtYWMiOiI2NmIyNGVlZDFmODQ4MTBjMTYzOGQ0YmY0NDNmOTk3YzI4NjZlNTViMjVjMmJhNTIyODQ1ZWQ0MGEyZTU4NTY5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-733713489\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-946793002 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-946793002\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:41:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImU1WUUyKzhjbkFwMVZLNUFGYjN5Z0E9PSIsInZhbHVlIjoiZmZsYlczQTI1M2tlbXBIRXBUQWtEVm55TVcxaENucTg4ZHo4OWdWang1SjQrVklvM1MrZy9CaXp5TlgzZWlHajRHQVMwNWczZ0U2Wk8vakwvc2F2RE81K2UraFpjUC9ZY1hKbTI3S2VBTDZ5OXpDYTdKOUZmRUpJZ0t5bk9GQndYQTNtTDZ3cTJZRDRabEtGYlpHU2xuQkIxbnBmWGxSY05KWThLVHYxdHMrN1hYQ3ptdkdzTzgySWZ5dEVCK21sZ2RRd240NjB2b2FuM3laS0tFTktsWmtYWEZCQmVmTkgvQWJDWlIrcGdtOXRmakdmTEJLMU9TOGZXcVhQb2pqVlhJSnJnd2dLdms3dFArMDhCY0pmbjRTWkd3ZDZWRlcwb3l3cmFSRW9yamU3Yzk5OXNDS1FjQ0srbVJQUXp0WHZUV3ZGT3I1SllPUFgzZjBpTE5vLzE4ZGhhTlpOTUxwTGo2VFRZQVZjQll1QTVMZW9rcldJM2FMS1N3OUd4Q2ZhZEpDQUVnYmREMnV3dzBoVEhNZ0pialEzbHBzcVNTTnJ1VFo0OHFDUkFwQUNSR2pEdnozM3RzdndtM29ScW45QlhkbWpVQVdHTjdWcXYvMlVHK0JmMlZ2ay9Bb0NaWnpGS3U4N0ZHVm5QeFV3bmdPdVlXVzdueWwwMkJva3dJTUUiLCJtYWMiOiIyMjE1Y2YwNWNlODE0ZGY3OTkxMjc0MzM2MTM4NWExN2FiYWQ1N2I5ZTBlNjNkZTY5YTgwYTIyYzUwY2JhM2JmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:41:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imp1WlhIM1B1bmNoZ2pjTnpoU0U2bXc9PSIsInZhbHVlIjoiYnRNSW4wd0tQZTdPbmZxdDlpTFNJajhDSHlNbU0xQjFjMTZaZ0NNTmw1cmxmOThBdldnTnBFQWJvSEU4bzZBRXVKd1VtSmkvSmtjcjBHWjZRNXpXanJZSTVWd0JBa0pqMnVUTmxXOUlHRXJpN2pjSDQ2UTF0ZHZEbitETTRPaHFSN3pKTmMweTBseWtTSm9lYXA3ZGoxODZrWHVQZUw4M1JpOThtamxmb3dTUC9kY2ZYL0t2QUhRK2ZTTXJBWVlOM1ZDZERBVmZQajRMSk1WL0Npcmc0MlFLaVgyRHFRNW91eFA2VE9LUEVDMTgxaWRIYlE4Vzdjb1Y0a3h3V0ptZDlMM2lyNXlVTVc1Sk0zZHFoZ0pLZ3BoTGRxQWdidk5zazdENjdBMm1rZW5zSzVva2pLb1N5QlhqMFRDMEhMTExtNlUyUkJRdEZQcmU0VndNV1BMdHZlZTExVE9mK09iVEdhTlZlUzBsQkxlcVdrcjFRY0FhTC9ERmJiL0RQUnBRUmJpVkIxWHNjWUVoTnhiemFnU25FVGhTVCtEdkNYdWZKVTVKMEJnSytEVVBWRnF6ZjllTllOZ2Zadis3UE5TYk1UU1lYUGZFaFgzYmNSbGY0ZTR6aHFsRTArMU9ST2xJeHhpQllTc0tURElWblRWbklSNWRxdFlhL04rVDlhS0UiLCJtYWMiOiJhM2I4YmIzNDFlYmIwMDIwNmM0ODE1M2M5MWMxYjU4NDJmZDQ5ZGViODE1ODZkNjU3NzMzYzQ0MDk1ZmVmYmZjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:41:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImU1WUUyKzhjbkFwMVZLNUFGYjN5Z0E9PSIsInZhbHVlIjoiZmZsYlczQTI1M2tlbXBIRXBUQWtEVm55TVcxaENucTg4ZHo4OWdWang1SjQrVklvM1MrZy9CaXp5TlgzZWlHajRHQVMwNWczZ0U2Wk8vakwvc2F2RE81K2UraFpjUC9ZY1hKbTI3S2VBTDZ5OXpDYTdKOUZmRUpJZ0t5bk9GQndYQTNtTDZ3cTJZRDRabEtGYlpHU2xuQkIxbnBmWGxSY05KWThLVHYxdHMrN1hYQ3ptdkdzTzgySWZ5dEVCK21sZ2RRd240NjB2b2FuM3laS0tFTktsWmtYWEZCQmVmTkgvQWJDWlIrcGdtOXRmakdmTEJLMU9TOGZXcVhQb2pqVlhJSnJnd2dLdms3dFArMDhCY0pmbjRTWkd3ZDZWRlcwb3l3cmFSRW9yamU3Yzk5OXNDS1FjQ0srbVJQUXp0WHZUV3ZGT3I1SllPUFgzZjBpTE5vLzE4ZGhhTlpOTUxwTGo2VFRZQVZjQll1QTVMZW9rcldJM2FMS1N3OUd4Q2ZhZEpDQUVnYmREMnV3dzBoVEhNZ0pialEzbHBzcVNTTnJ1VFo0OHFDUkFwQUNSR2pEdnozM3RzdndtM29ScW45QlhkbWpVQVdHTjdWcXYvMlVHK0JmMlZ2ay9Bb0NaWnpGS3U4N0ZHVm5QeFV3bmdPdVlXVzdueWwwMkJva3dJTUUiLCJtYWMiOiIyMjE1Y2YwNWNlODE0ZGY3OTkxMjc0MzM2MTM4NWExN2FiYWQ1N2I5ZTBlNjNkZTY5YTgwYTIyYzUwY2JhM2JmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:41:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imp1WlhIM1B1bmNoZ2pjTnpoU0U2bXc9PSIsInZhbHVlIjoiYnRNSW4wd0tQZTdPbmZxdDlpTFNJajhDSHlNbU0xQjFjMTZaZ0NNTmw1cmxmOThBdldnTnBFQWJvSEU4bzZBRXVKd1VtSmkvSmtjcjBHWjZRNXpXanJZSTVWd0JBa0pqMnVUTmxXOUlHRXJpN2pjSDQ2UTF0ZHZEbitETTRPaHFSN3pKTmMweTBseWtTSm9lYXA3ZGoxODZrWHVQZUw4M1JpOThtamxmb3dTUC9kY2ZYL0t2QUhRK2ZTTXJBWVlOM1ZDZERBVmZQajRMSk1WL0Npcmc0MlFLaVgyRHFRNW91eFA2VE9LUEVDMTgxaWRIYlE4Vzdjb1Y0a3h3V0ptZDlMM2lyNXlVTVc1Sk0zZHFoZ0pLZ3BoTGRxQWdidk5zazdENjdBMm1rZW5zSzVva2pLb1N5QlhqMFRDMEhMTExtNlUyUkJRdEZQcmU0VndNV1BMdHZlZTExVE9mK09iVEdhTlZlUzBsQkxlcVdrcjFRY0FhTC9ERmJiL0RQUnBRUmJpVkIxWHNjWUVoTnhiemFnU25FVGhTVCtEdkNYdWZKVTVKMEJnSytEVVBWRnF6ZjllTllOZ2Zadis3UE5TYk1UU1lYUGZFaFgzYmNSbGY0ZTR6aHFsRTArMU9ST2xJeHhpQllTc0tURElWblRWbklSNWRxdFlhL04rVDlhS0UiLCJtYWMiOiJhM2I4YmIzNDFlYmIwMDIwNmM0ODE1M2M5MWMxYjU4NDJmZDQ5ZGViODE1ODZkNjU3NzMzYzQ0MDk1ZmVmYmZjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:41:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1371527502 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1371527502\", {\"maxDepth\":0})</script>\n"}}