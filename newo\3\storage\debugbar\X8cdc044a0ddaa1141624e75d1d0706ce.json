{"__meta": {"id": "X8cdc044a0ddaa1141624e75d1d0706ce", "datetime": "2025-06-16 15:23:44", "utime": **********.284346, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087422.620212, "end": **********.284386, "duration": 1.6641738414764404, "duration_str": "1.66s", "measures": [{"label": "Booting", "start": 1750087422.620212, "relative_start": 0, "end": **********.086265, "relative_end": **********.086265, "duration": 1.4660530090332031, "duration_str": "1.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.086287, "relative_start": 1.4660749435424805, "end": **********.284389, "relative_end": 3.0994415283203125e-06, "duration": 0.19810199737548828, "duration_str": "198ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45167408, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.009689999999999999, "accumulated_duration_str": "9.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.194165, "duration": 0.007019999999999999, "duration_str": "7.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 72.446}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2309191, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 72.446, "width_percent": 15.686}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2571182, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.132, "width_percent": 11.868}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order/1/edit\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1345524394 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1345524394\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1020731902 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1020731902\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-279260168 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-279260168\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-713569841 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087411438%7C10%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkkvQnVTUFE2TitqRDBBeG84ZGwzbFE9PSIsInZhbHVlIjoialE4MjV3d2FoTnU5bVhlTTd0U1UzOUFkTUJaODNLazdjNE1YbklhWkpNNCtPQmsrS2pyV0NqZkpRSFpJUTE1SEpHZitkam9HeHcxR1RVU1FpUG8wRVFJZlNOcGp4azd5cUhmSXpQRmhSZWJqMCtIa3gyeUhEMC9sT0MyNUpHZlRCZncyMnI0S1IwMW9jY3pVWUY4bU5oM2NyTHNOWGh0S2lMczROTGw1T29LdXlLUnNoa0t3L2xRaHMwQWRPOS9CS1ZZcHh5bzJaVEpBMlVPRlB4bThGZVdHS29nOTVSNkRWanJMUlMzKzAzR1RaOFNJVkJGQ0o5YmgvWDdFNy9WOXRFNzFzNGRNdWozT2tLMnkrUGwvVUZ2Qm80eFpTRVBXamJOOUlodW9YNU52bnBmdHpaMFlMK0xORmQ0U29jemxldENBci9HeExtWXFiWHNPdTcxZVlrYjdCODNSRFZRT09sLzUvRUZ2V0ZQa3VKWmR1dUg0Uld2aUsxT1RjSlhubGdUWWFOVXpyVkJ0amJveSszMjhLaGlrdG5mRzg1c3BrbFV3KytyU0tCM2FaVUdrTjlPSHF6OFIrbXRqMlJZRUcwd3E5cWNud3FvcklSRVgxK2I2RE82YW9BWUE1SGQ1blFURis4ZWhBSkFaOUlSMUh2L1QzcCsrYURvVU5uY2UiLCJtYWMiOiI2MmQxNzM0NTYxZGFmNjJjYjA3NDFlMmZmNTg3ZTNmMzYzYWFjOGVjZDdiOWM3ZWQ0NWQ1YzcyN2JjYTRkMGU1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IldtWlIxeGxOMGZnTTJDbkVXcEE2cnc9PSIsInZhbHVlIjoicnZoMUZCQmNYOE5WTG1CWlRYUVRnSlpySDhwdWdqb1NBRVF5QlByeTlKeTIrT1NRYlpNU1l3Q2ZCelloZFdMZ2dWbytnemFNNXpUdEtMclpIQlJobnkwV3hYZUpuZWlwbUtJT2owQWdvU1RsUHdXYUZJdHJiVGRDRkpBbUxCSXI2aXhSanF5Nmw4SkNnSU9BNm16aXdWRVZuL01mL2JiS05ubmIycTcxZVdyaWJDY0EwZEY5b3MzTVcvZXl6L0QvejUvUHFVOGZ3bTBvdGhIdTdiRTFaL2pEMmNKQi9Uc0pNMThoSTFZOWRtbmFabXlrNjR1TlFXRW9YM0tSQVJmVTMrRlh2Sk0zellIRUdZWWt3VkkrNHdUbU5PMkxGanhrazZvTDY3eFNuMnFQc3ZRTmFFdUl2VFUrZW1ON2U3MDBmbXRlb2hCdXpMRkE4NUJKN0d2OFI0c2VENlBDenJCdFVKd1c5cS80RzVxSEFONzhsTjlZNE5PNE9BZWJDVmxJQ01WbWhMK09RbDFvQ0ZDNnNwR3c4RDRjRldOaXA4bDNualhSNXZEeXNuZmc3RTR5dGVvWlN3SURLbXpZUWFnWGFaV0l1b1Zncy9qalpXZVE4U0xiYTRTTlJRZmJ1bGF0UjBidXBLVy9LT1JCdmFIL3E0bFZIaE03S1QyVVpaWDciLCJtYWMiOiIxOTI0NzIwYmNiNjJkODZiOTEwYzdkNWVlMDIyOGFmNWQyMTU5NTQxY2VmYjRlMWI4MmM4YTFlMzY2NWQyMDllIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-713569841\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2046441788 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzcvmOKIgVMCCvEHD9HLB5trqF9yJ9GcIs8Pndo9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2046441788\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1522331413 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:23:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklGV2x5RFo3SG5rR1J5VUNhNWtqZmc9PSIsInZhbHVlIjoiZ2cwdjlGRFRBQUovcktFcld4WWJtbnhSelRISU1yZjloTzF6YW9lRGQyR1llVVpaLzNGcWlIQllFY1ZVVE1MTk1Ha1ZyT2dLWFZXbUxDTHZVRktoMjBzNDhyb1BRazBVQjhNYlB3czJjWWsxUGp5aGtYVnJyUVlzVURxU0JDdW9Uc1NtRGY5V0tCSXpGOCthQlZYbm9ydEdKK2toQ1paN3lJZFJaTTdnbWpMeWlCVDVjdTRGRVZXWk0vcW9RamVOSVhkeVd4c2k5bEdsMGZmM1UrZmNjRVg4bFhPajJFNDRYZ25OVW1ySEdhandjYzZESTVoOXlRNWdOWGhvdnpxUXFLSFluYnJhdzBMMzNUMDdTK2M0REEvWDc4elpJRnFnTi9uR1V1VXg0YjI1Vk1xY1JYOHRWc2tvM2VVa3R0Tm0ycjdxR2F1QkpodTJhRVRSYkZFZEtDeG1IdG94SlowTkwyenRobFJXdTkwOHRaTWI1R0pUS3VCVURyc0pKdmN0TitmZGFMa1U2clNveDROUzhJMUo0dmxBT1pwT08xd1h3bVB0NStMekhKNnJPcmtEbG9hb2dUWGVRbmpIK0djbGx4SGFSSWEzcEdkM2dhNXRWNWp6YVZpZnVjWEFyVVU5Q0pzUm5lVVlKdDh4NGc4OUdtNmFISEV3clVHWVZZcHIiLCJtYWMiOiJhMjRjZTZhMGJjOTZlYmU1NjkxMjUyZWZmNGU3MTc0N2YyNTY0NDA2NDI1MDcyZjIxNmRiNjA0M2RkZDZlZDYxIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjUydEYxeUFhc05FbnVUeVVkcDlNcUE9PSIsInZhbHVlIjoiajNjcXBiUmxmYktKVVhjblZBbU9yWlg2c0owVXk5bWh0MkV3M3IvY3hud0NhREY5Zk1VWC9rWVY3L3g1VXRvdnUwRHpkU3MzWVE2UDFXY2VlK2M4bUovQmlpTnRRS1JwQ0xRKzU0anl5RlhYMUFpc3BqM2xrYTE4QTJhbXVNQklDc3owSjEyTlRZMW1RaHJpcEYzR3ovMlRrVFg3Q1RBam05ZGl4VDZBS2VYSU9CYzIyOGN4aFFxM0I2djdpR2dDaW1IZ3J5ZFdtTW5ldDV2eC93ek5KQ2FUUDBWQ1ZjenJlSDM2SW5jWnB5TGFxNTlCdUQ2Tk5jeHhLZ0FOaU9FMGFNK1NyV0p0Zk5yLzJmaGZVZWNNSFpJcWpGWTV6QlBuUDNKN1lCSVJRamJTTC83emlRMlUzNjArdFRBcndzcHZoL1lNdlZ6blMzTDdhQTRkV0QxNlY1TEljRVE2YTBicUNpMmx5NVBGd1VJZGs2K3cyU1RBTVB3cVBhNXlzV3pvN3A0OG9RMTZKQmNFM3hkbk45eDRZbFlVajRJeVo1OThETDl1TEJjZFFqbWxZaElHbWtRQjdQVEJPbncweXczZEFNajFNK1hmbVI1RS9KUHpaQWpFeDlmdW5UQlg4SWhocmVrUGtXRXJUNENaWElFV3ZJR1B4UGpiL2NwOEcrczkiLCJtYWMiOiI0ZDRiYmE1NTY0Y2RhYTAxNTFmYmZlYjk2Y2RjNWNlYTFlMTM1MzI5YmNjMzZlMWMwMTUwOGRkMzNiZjNlYjgyIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklGV2x5RFo3SG5rR1J5VUNhNWtqZmc9PSIsInZhbHVlIjoiZ2cwdjlGRFRBQUovcktFcld4WWJtbnhSelRISU1yZjloTzF6YW9lRGQyR1llVVpaLzNGcWlIQllFY1ZVVE1MTk1Ha1ZyT2dLWFZXbUxDTHZVRktoMjBzNDhyb1BRazBVQjhNYlB3czJjWWsxUGp5aGtYVnJyUVlzVURxU0JDdW9Uc1NtRGY5V0tCSXpGOCthQlZYbm9ydEdKK2toQ1paN3lJZFJaTTdnbWpMeWlCVDVjdTRGRVZXWk0vcW9RamVOSVhkeVd4c2k5bEdsMGZmM1UrZmNjRVg4bFhPajJFNDRYZ25OVW1ySEdhandjYzZESTVoOXlRNWdOWGhvdnpxUXFLSFluYnJhdzBMMzNUMDdTK2M0REEvWDc4elpJRnFnTi9uR1V1VXg0YjI1Vk1xY1JYOHRWc2tvM2VVa3R0Tm0ycjdxR2F1QkpodTJhRVRSYkZFZEtDeG1IdG94SlowTkwyenRobFJXdTkwOHRaTWI1R0pUS3VCVURyc0pKdmN0TitmZGFMa1U2clNveDROUzhJMUo0dmxBT1pwT08xd1h3bVB0NStMekhKNnJPcmtEbG9hb2dUWGVRbmpIK0djbGx4SGFSSWEzcEdkM2dhNXRWNWp6YVZpZnVjWEFyVVU5Q0pzUm5lVVlKdDh4NGc4OUdtNmFISEV3clVHWVZZcHIiLCJtYWMiOiJhMjRjZTZhMGJjOTZlYmU1NjkxMjUyZWZmNGU3MTc0N2YyNTY0NDA2NDI1MDcyZjIxNmRiNjA0M2RkZDZlZDYxIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjUydEYxeUFhc05FbnVUeVVkcDlNcUE9PSIsInZhbHVlIjoiajNjcXBiUmxmYktKVVhjblZBbU9yWlg2c0owVXk5bWh0MkV3M3IvY3hud0NhREY5Zk1VWC9rWVY3L3g1VXRvdnUwRHpkU3MzWVE2UDFXY2VlK2M4bUovQmlpTnRRS1JwQ0xRKzU0anl5RlhYMUFpc3BqM2xrYTE4QTJhbXVNQklDc3owSjEyTlRZMW1RaHJpcEYzR3ovMlRrVFg3Q1RBam05ZGl4VDZBS2VYSU9CYzIyOGN4aFFxM0I2djdpR2dDaW1IZ3J5ZFdtTW5ldDV2eC93ek5KQ2FUUDBWQ1ZjenJlSDM2SW5jWnB5TGFxNTlCdUQ2Tk5jeHhLZ0FOaU9FMGFNK1NyV0p0Zk5yLzJmaGZVZWNNSFpJcWpGWTV6QlBuUDNKN1lCSVJRamJTTC83emlRMlUzNjArdFRBcndzcHZoL1lNdlZ6blMzTDdhQTRkV0QxNlY1TEljRVE2YTBicUNpMmx5NVBGd1VJZGs2K3cyU1RBTVB3cVBhNXlzV3pvN3A0OG9RMTZKQmNFM3hkbk45eDRZbFlVajRJeVo1OThETDl1TEJjZFFqbWxZaElHbWtRQjdQVEJPbncweXczZEFNajFNK1hmbVI1RS9KUHpaQWpFeDlmdW5UQlg4SWhocmVrUGtXRXJUNENaWElFV3ZJR1B4UGpiL2NwOEcrczkiLCJtYWMiOiI0ZDRiYmE1NTY0Y2RhYTAxNTFmYmZlYjk2Y2RjNWNlYTFlMTM1MzI5YmNjMzZlMWMwMTUwOGRkMzNiZjNlYjgyIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1522331413\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1000673584 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1000673584\", {\"maxDepth\":0})</script>\n"}}