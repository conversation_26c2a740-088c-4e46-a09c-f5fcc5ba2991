{"__meta": {"id": "X89315b8428fcb52f5c1161a6d8d15c93", "datetime": "2025-06-17 07:14:08", "utime": **********.863623, "method": "POST", "uri": "/inventory-management/add-quantity-all", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.229598, "end": **********.863654, "duration": 0.6340558528900146, "duration_str": "634ms", "measures": [{"label": "Booting", "start": **********.229598, "relative_start": 0, "end": **********.697054, "relative_end": **********.697054, "duration": 0.4674558639526367, "duration_str": "467ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.697065, "relative_start": 0.4674670696258545, "end": **********.863657, "relative_end": 3.0994415283203125e-06, "duration": 0.16659188270568848, "duration_str": "167ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47957568, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST inventory-management/add-quantity-all", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\InventoryManagementController@addQuantityToAll", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "inventory.add.quantity.all", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=745\" onclick=\"\">app/Http/Controllers/InventoryManagementController.php:745-851</a>"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.032909999999999995, "accumulated_duration_str": "32.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.745292, "duration": 0.026, "duration_str": "26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 79.003}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7839131, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 79.003, "width_percent": 2.978}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 749}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.792802, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 81.981, "width_percent": 2.066}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.810357, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 84.047, "width_percent": 2.066}, {"sql": "select * from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 768}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.81672, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:768", "source": "app/Http/Controllers/InventoryManagementController.php:768", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=768", "ajax": false, "filename": "InventoryManagementController.php", "line": "768"}, "connection": "ty", "start_percent": 86.114, "width_percent": 2.249}, {"sql": "select * from `product_services` where `created_by` = 15 and `type` = 'product'", "type": "query", "params": [], "bindings": ["15", "product"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 783}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.820806, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:783", "source": "app/Http/Controllers/InventoryManagementController.php:783", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=783", "ajax": false, "filename": "InventoryManagementController.php", "line": "783"}, "connection": "ty", "start_percent": 88.362, "width_percent": 1.914}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 785}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.829545, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:785", "source": "app/Http/Controllers/InventoryManagementController.php:785", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=785", "ajax": false, "filename": "InventoryManagementController.php", "line": "785"}, "connection": "ty", "start_percent": 90.277, "width_percent": 0}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 3 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 792}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8301299, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:792", "source": "app/Http/Controllers/InventoryManagementController.php:792", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=792", "ajax": false, "filename": "InventoryManagementController.php", "line": "792"}, "connection": "ty", "start_percent": 90.277, "width_percent": 2.279}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 5 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["8", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 792}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.833601, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:792", "source": "app/Http/Controllers/InventoryManagementController.php:792", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=792", "ajax": false, "filename": "InventoryManagementController.php", "line": "792"}, "connection": "ty", "start_percent": 92.555, "width_percent": 1.732}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 6 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["8", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 792}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.836749, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:792", "source": "app/Http/Controllers/InventoryManagementController.php:792", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=792", "ajax": false, "filename": "InventoryManagementController.php", "line": "792"}, "connection": "ty", "start_percent": 94.287, "width_percent": 1.61}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 7 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["8", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 792}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.839954, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:792", "source": "app/Http/Controllers/InventoryManagementController.php:792", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=792", "ajax": false, "filename": "InventoryManagementController.php", "line": "792"}, "connection": "ty", "start_percent": 95.898, "width_percent": 2.188}, {"sql": "insert into `warehouse_products` (`warehouse_id`, `product_id`, `quantity`, `created_by`, `updated_at`, `created_at`) values ('8', 7, 1, 15, '2025-06-17 07:14:08', '2025-06-17 07:14:08')", "type": "query", "params": [], "bindings": ["8", "7", "1", "15", "2025-06-17 07:14:08", "2025-06-17 07:14:08"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 806}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8435912, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:806", "source": "app/Http/Controllers/InventoryManagementController.php:806", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=806", "ajax": false, "filename": "InventoryManagementController.php", "line": "806"}, "connection": "ty", "start_percent": 98.086, "width_percent": 1.914}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 818}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.853386, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:818", "source": "app/Http/Controllers/InventoryManagementController.php:818", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=818", "ajax": false, "filename": "InventoryManagementController.php", "line": "818"}, "connection": "ty", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 10, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage inventory, result => null, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1314866287 data-indent-pad=\"  \"><span class=sf-dump-note>manage inventory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage inventory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1314866287\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.815122, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-59989131 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-59989131\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.816054, "xdebug_link": null}]}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/inventory-management/add-quantity-all", "status_code": "<pre class=sf-dump id=sf-dump-1669888450 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1669888450\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1459136929 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1459136929\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2025374239 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2025374239\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1611468807 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">62</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=jfj0s4%7C1750144433135%7C6%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im84Rk1CU3VmV0RMam5sMlFwRjVFOEE9PSIsInZhbHVlIjoieCtJc0RNOHUzR25LUWhjMWxmS2N3REwreFNETG9DcllVckFndHlZMTJqK0pyN2pONkJ0UkNpWUNCZUtCNXlGTk43Q3hGVmswaitEM2lRSytsRGZIT3JLREJqd1l1T1VLeFIybzA0WVlaOWFyTVcxQmt5amx5ZW4zZ3RQR1lGRm1jQWN1ZlFDL3VLOWpPWjZIajBaUHY1bFYrSVZ4U0xCQis5MU12TzlRaHNCc09KdHFLbUh5cUVFMG1PcGlubWppaUZlcnkrTFhySTJGSXNRSXlxeWd5dkJFTUw2YkhRNmpwR0t6MmtWSExXcUxsRFRtbkFPRCtrMC9WdXBEQWN3Mzl6QS9KL2NNcmp1OHRqTXZGZHdnNnRVZDRrVkloQUMyOTI1RUx0cHhYL3YwczRVcVU1Sk5tbWlNTHFFYVRiN2hxMU5DRkJ1YldmRUMzNktOT0VRYnMrRFE3TnJKQ2cwZVNjVkVxMkRFYVFUOWN6UmkxZnpVbjhsVC8zWTlDRXZsWkxOZDlFcVV1eHBzWE0wVnlHMWtEYVV3dUUzN05xYldaS0t3Q3RRYThybUNXbjZESTg0SUxMOVIrUmdGeklWS1hjNHJTUEJBUjcrV1BMalhOL3FIRVRjclRjb2ljemlHblhYQjl2TFlaU2NZSkZpek40WlpYL2Z6TG1mUnVTbkMiLCJtYWMiOiIzODk0MmQ3ZjBmZWE1MDcyODM3ODVmYzM2NjcwMGI4ZDdmMDUzZjc3YzY0ZjRjNmIzMzZhNTUxYmViMWI1OGFmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ink4UzlDcDZMK0FKLzM2a3FEaTNvL2c9PSIsInZhbHVlIjoiZDd4VTNiTkIrdzFIZDc4VlNJZEJSZXh2N0VkZTErekVlaGQ5VXNLUDZPYU1XWG9UdEZRZlBxTTU0TGtqWm83TDVoN1FnOUtTaVJPNmRuRWg0UnkxWSt1SE9ONXFoYTI0NEt1NjBETTN0cXJkWXVmV3BUVGMyMUZZWUVkSms5aituaHRKemFTbnJaaFRVa09sd0I1VUkvRHBXNGFhVGtqWXdMSkQwazNqM1ZmcXdvR2R3bmFrUVZVb1VZOEoyNk43dk1zckYycW90NmpCL3lyRWlCdll6U2xjQ0tJVWVPbXl1d0dSWld5aFZpV1Q3NnZMUzJVRkFFa29lcUJpdW5QN2cwaGk1Q2k2OVJSclJQcXJXSFdzN1JMSFJSMEJlSDdOWGo1c2FIMnYxbVU5ZkNzTm80RnRaVDVQZHl6VEx5NDZjSzZQQUVCQWVYUWtHYlk5RnhOVmppT29lTU1NbEVFYnA5Vmx5VVp3YTArSktsSDF4dGtiakkvdkxIcm81eGUwTUsrQW54UTc2RkpKcTM3Q25HU0ZWSENJVjNRZE9VOVN5UTQ4VUF0dXg3bVdXOVRvR3dLMlBkTVJRM25RQmVzczhLcmpjNHlQZjBVaG9DL2RCZTNsMkJ3OEF3T2p0T3lmNWVRdnN5czNmRVBRcHdMUkdhQS82eGtMVUZIclpXYkgiLCJtYWMiOiIwNjdkY2U5NTc3ZGNiZWRkNWYxYzllZGY2YjRjNmJlN2MwZmZlMzRjYjQxNzU1MzQyNzYzMGQyYzhjNWJhNmJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1611468807\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1035386334 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1035386334\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1382264532 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:14:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ill3Z1V6UXhhc0tkNVIrcnM3S1l4OVE9PSIsInZhbHVlIjoiNjJhVjhheElNejl3d3FBK1dJQUtBejlCdXUrTFdQU3JJNW9RL1NBRlFpR0ZiOXVjcXlMbTlqT1RORnE2Q0grekVSNVRTWUZOZTU5Mjk5NEo1eEI5QzhuSTltMVRUc3QweTNTbXdLUWxFNXVveWxkeGd3MnZrZkRKZDZUYjYzMk1VbG13Z211clc2TVlBcTJjMy9QZFBPdHdmaEJKZ3FKWVZFc1NzMTJBTWZHam1CcGFKUjVRZCtBUUhGYVF1RVgwYjJUVVdIdWtXUjBoM2ZOd3RMMGRwMFI0SWF0RzBWL0hmOVdHWU1kaGlHNlhrSnFHZklzQXdkTGdkS3c1SC9EeGQ0eWF0Rmd1MkFoMmlyenU5bUYvelgzdmxnR3ZveTAzREFYbWxYMzZNK0lKRjdKZlhFS3J2UWU4ZWd4M2hqdU1YT0t4K0RXNm5hVStHME1UMVdPSU5DMVVVaGxscDRvK01MN0FpZThpbE5GcE5FUzk4emxYSGgrb202ZVQ1UXNCeW1sK0xBemdsMDlhQlpVSU9XYjhldXFrTTlRL1hOUmlxbE1ybnVnclNSRU0zcTA4SmZvS2FhWjVsb2tvVmdiRjBoaWUydEl6OWZYbWxZY3J0VEQrRDk0OG42UEpYZVNGOCtOU3UzTWNTMGhpQlhZYlYxd2dlTFdITFN3eTkyNCsiLCJtYWMiOiI3YTQxNDBmMmMwYmFhMDQ4YWM3Y2ZlMjkwNTgxNGRjOTc1MGZiNTQ1N2NhY2VhMTVhZDVmNjUyMWQ2MWJhMmY1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:14:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImptWGFVSDNvYkpHOGRaN2c1MXdZbmc9PSIsInZhbHVlIjoiZzlSYzc4TWZiTXJpcU56VldaZG5IRk96cXc1ektlQjhFQmUrbUdiVk1ZWlE2SmsyNm9kU2JpWFQxV1l3eTFhcnVEMDdMdmNDNnJMNTY1ejF6V1BRUUlEVnNFNlVMZkdwWG1kSmx2WHc0YmZTVjJTMVd3ZzFBaHA1Ris3N09GOUk3UGR0WlpiRlA0cTlGVnEwSGRyeEUvd0lYTTE3eTNHY0xoNkZuNytrQXJZeDNaTDFrZlB5Y2RKanBETVFESmpoKzVWRHlzWS9EUEIwb2trc1NGVmYwR0tOSUh4RllPa3lvaGhxaXBDUUNLVk4xT0JLMGFnMko3TUtUL2k3cFNncXRqai9YVnY0cys0SVFYRzFBRm1XZ3JHSzhGMklyT0QzTVZDTTFHZXlpUmFWc3pTSXl0V2ZESm5zWEVoMjg3UXZocmNKMmlMOUtrUzl3VE1zTHJrYXM0Q2lubGJVdFlGRXBzb1MrWjh1cjY4d3JiS2VVOE11Q2tHR0FXZC94aGhUS3g1T3FVZlVBdld5ZDlBUWxoNUIwdUFkM3B3YVYwdzdPL29GWVB0YUtlaFNnL0VDUVVBaXI1c0ZENXMwMEFvdXBBQ0x4bWhhK3RDZ2haVm85YU5NVTcrQ2gwU3UzM2c2amkwY2FoYzRaOXdzOXkzNk1PTEtRTXAzSmZaYmt6WE0iLCJtYWMiOiJlMGRjMzNlMTEyYTM4MDIyOWM5MDZmOTAyMzgxMTY3YmEwZmVkMWI5Y2Y3ZWM5NTdhYWQxODUzYmVkNzIxOWE4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:14:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ill3Z1V6UXhhc0tkNVIrcnM3S1l4OVE9PSIsInZhbHVlIjoiNjJhVjhheElNejl3d3FBK1dJQUtBejlCdXUrTFdQU3JJNW9RL1NBRlFpR0ZiOXVjcXlMbTlqT1RORnE2Q0grekVSNVRTWUZOZTU5Mjk5NEo1eEI5QzhuSTltMVRUc3QweTNTbXdLUWxFNXVveWxkeGd3MnZrZkRKZDZUYjYzMk1VbG13Z211clc2TVlBcTJjMy9QZFBPdHdmaEJKZ3FKWVZFc1NzMTJBTWZHam1CcGFKUjVRZCtBUUhGYVF1RVgwYjJUVVdIdWtXUjBoM2ZOd3RMMGRwMFI0SWF0RzBWL0hmOVdHWU1kaGlHNlhrSnFHZklzQXdkTGdkS3c1SC9EeGQ0eWF0Rmd1MkFoMmlyenU5bUYvelgzdmxnR3ZveTAzREFYbWxYMzZNK0lKRjdKZlhFS3J2UWU4ZWd4M2hqdU1YT0t4K0RXNm5hVStHME1UMVdPSU5DMVVVaGxscDRvK01MN0FpZThpbE5GcE5FUzk4emxYSGgrb202ZVQ1UXNCeW1sK0xBemdsMDlhQlpVSU9XYjhldXFrTTlRL1hOUmlxbE1ybnVnclNSRU0zcTA4SmZvS2FhWjVsb2tvVmdiRjBoaWUydEl6OWZYbWxZY3J0VEQrRDk0OG42UEpYZVNGOCtOU3UzTWNTMGhpQlhZYlYxd2dlTFdITFN3eTkyNCsiLCJtYWMiOiI3YTQxNDBmMmMwYmFhMDQ4YWM3Y2ZlMjkwNTgxNGRjOTc1MGZiNTQ1N2NhY2VhMTVhZDVmNjUyMWQ2MWJhMmY1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:14:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImptWGFVSDNvYkpHOGRaN2c1MXdZbmc9PSIsInZhbHVlIjoiZzlSYzc4TWZiTXJpcU56VldaZG5IRk96cXc1ektlQjhFQmUrbUdiVk1ZWlE2SmsyNm9kU2JpWFQxV1l3eTFhcnVEMDdMdmNDNnJMNTY1ejF6V1BRUUlEVnNFNlVMZkdwWG1kSmx2WHc0YmZTVjJTMVd3ZzFBaHA1Ris3N09GOUk3UGR0WlpiRlA0cTlGVnEwSGRyeEUvd0lYTTE3eTNHY0xoNkZuNytrQXJZeDNaTDFrZlB5Y2RKanBETVFESmpoKzVWRHlzWS9EUEIwb2trc1NGVmYwR0tOSUh4RllPa3lvaGhxaXBDUUNLVk4xT0JLMGFnMko3TUtUL2k3cFNncXRqai9YVnY0cys0SVFYRzFBRm1XZ3JHSzhGMklyT0QzTVZDTTFHZXlpUmFWc3pTSXl0V2ZESm5zWEVoMjg3UXZocmNKMmlMOUtrUzl3VE1zTHJrYXM0Q2lubGJVdFlGRXBzb1MrWjh1cjY4d3JiS2VVOE11Q2tHR0FXZC94aGhUS3g1T3FVZlVBdld5ZDlBUWxoNUIwdUFkM3B3YVYwdzdPL29GWVB0YUtlaFNnL0VDUVVBaXI1c0ZENXMwMEFvdXBBQ0x4bWhhK3RDZ2haVm85YU5NVTcrQ2gwU3UzM2c2amkwY2FoYzRaOXdzOXkzNk1PTEtRTXAzSmZaYmt6WE0iLCJtYWMiOiJlMGRjMzNlMTEyYTM4MDIyOWM5MDZmOTAyMzgxMTY3YmEwZmVkMWI5Y2Y3ZWM5NTdhYWQxODUzYmVkNzIxOWE4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:14:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1382264532\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-68136620 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-68136620\", {\"maxDepth\":0})</script>\n"}}