{"__meta": {"id": "X70e7b7b7cd7ea185675236a844170b2c", "datetime": "2025-06-17 06:28:27", "utime": **********.291344, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750141705.640243, "end": **********.291379, "duration": 1.6511359214782715, "duration_str": "1.65s", "measures": [{"label": "Booting", "start": 1750141705.640243, "relative_start": 0, "end": **********.086615, "relative_end": **********.086615, "duration": 1.4463720321655273, "duration_str": "1.45s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.086641, "relative_start": 1.4463980197906494, "end": **********.291383, "relative_end": 4.0531158447265625e-06, "duration": 0.2047419548034668, "duration_str": "205ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45169128, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0138, "accumulated_duration_str": "13.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1876318, "duration": 0.01132, "duration_str": "11.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 82.029}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.233897, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 82.029, "width_percent": 8.696}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.261126, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 90.725, "width_percent": 9.275}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-502557364 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-502557364\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1433440820 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1433440820\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-415750538 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-415750538\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2145444797 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1c3qljs%7C1750141691270%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjRXVXB0VFRxbmpvUWprZnkvK3J1OEE9PSIsInZhbHVlIjoiaE8ranFwcmI1U3M5c1c0d3Q2VVN0OVVPRXpjMDl3OG44VDRCdE11YTVDQmx5ZnNzTUM4aXJQOUcxaXJPcWtFRHpsY2wzMDMxczlpcHQveW5NUk1NV0xrY0N3UThERDRoendVdW1laWQ3SSt6ZUFaalFNQVJpTU1WWDFmUWlhYmFUUTZXQ0R2dXJMcVN3c0FJS1cvNDBMQXFqWEtBd0FxeENkdlNwRnFucFB3M200STJEUVhucUZaSGN5enBQNkx5WDRmdUtYcWV0ekd6NVNFdlRKM1hudnN2SGhJbmw1NEQ2WHNOOXI3dnFmVFpaMHNvOGZZRkVDSW9ua3JyVWVUWVJ5WHlldU9NZmRxcTlnTjBYaHVkeS9WYUF4NWtUeEl2aDgwMUZPMEJCdEJpZ1h5V3FmMkxnT0d1dURHMG1SNUhYdTVWNllNRFZuaXR3QmRadzFHbXV5cWhwU1lteTZFMjNMSFA3U2QyWkRDYUV4SU1DaEN5N2RPVUpycXhXT053d2cxY2ZDcVN1cTdHRUxqc1NqZHZjRmFQSmNvcDhpTVhUK0ViWExKeDQ4UjlYTGpmSmhPTllDSGVXeEhoWlRPWmFuWmt3clcrNktzTFlITUsvdVNtS1FxSkpRSzZnS2Rzb0FjR09VQXFRZ0s0cnV3aWRPRXYyeEcrQTU4TSsrc1oiLCJtYWMiOiIyYmE5MWI4MzRhNWE1YWMxNGRlZjMxNTE4ZWQ5OWIyNzAxYmZmY2JkODhlNjE0OTM4ZjEwMGVjODRiMzAzOWQxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik03eTRGUHFaK05keXZoR0JMOEdNUVE9PSIsInZhbHVlIjoiVC9RWDNEbVJFaDBEYWw3NGt1RTdWZWFUUnJLSmpkdGx1M0V5aXBvSE4xaEhtS2JmZWRsVnBOUFpuUVY1WFo2RmpncSt6T2FYbUxOVmdtZkR2eGxCTzBIMS8yWVZYOXFRS2p4b2xML3UyWDNFZit3V1J1cUowaTJaUmlFaTlhZzNvZnk5N2Uyc1UvWlVJVzFuaThnY2VxQTIyWm15dy9LTkxSRGN2UDl3Mk8wUHRFZGRzVUJiTDQ2VSsvY0EzU2lIZjM4cENzMnBSeXRXSEkzOUxyZ2o5cTEwbHA4dUR1b1ZLSE95aWJ4bzIrbTFaRHZBeWVuWXc1KzNwNkNwbmVxTTRyR0V5bHFtbkd2STBKd1Q3RVpuVGdyazB3SUpFVDgxcHJFWFhlTUsrNEVueVVPOGtxOW9ZcExiU3VPNVpZazBjdUU2S25QazcvNWxNcXdWZVJSMm16ZHJUS3hBUmR5d2d3WndyaDRHM09Jb0h6MXJKMU96dGJnNlNWdG9NWENyTE9wcFQ1YnJEaWVDUGU0R2ZVWU84dmUwQnkyVHUzeEx5M0hqTDlpR01JcFNXN0xFazFqNzdjbUNiV0RYNVN0QUQvaUVVeXdkcDd2RWk0bitWK3VFdEkvN2pkTEpjNm4rVnB3UGtNcC9QMEdYTkdMd1pmVXcyWjQ4TTI3VURJWVEiLCJtYWMiOiI4Njc4N2UwNDg5ZjlkNTVjODc4ZjkzOTBmMmNkMjc4NWVhZmU4NDUwMzFjNTdhMWRiZDVhYmUxNjU4Y2JkNWRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2145444797\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1632859528 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1632859528\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1162882129 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:28:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVTWXBmUlJlY1JYd2xRNEhiNUM2Rmc9PSIsInZhbHVlIjoiZ1YzV1pjMTNQVkFEQU1JSHVBTGtSanptclFGQlI0bzk2bk0xOUtzUFRTUkI3Q09RMzQ5MVhleTdFT3l1VEFsS1pQUWhjQzE1UHNZK2ZSRGF0SmpPblhhY1ZGdDJKQzhGbzcxREYzdThaLzMxUWhrL1hGclNXdmJlNW5PaWpuSlBxRGhueHJHNHpTR3RDN2VxNzRiS1BGRE9VM2JKVS9WN3hvU1J3VkF3SitRZy9oeHpUVUlMTm5nc3p1b3lWYWZWUGdMOE1pWHMzdjBWK05PYW9hK3l1Si9WWDBMc1lucXZOZHFTM1IxdkdxYkR5YUxDbUNKK0lvQTB0UDZSR09Zc3Z0SmlUWWl4RWZOck4yQ2svczZiaXlyZzl3OXFBRTVscmVWR0JGc0pLRzd3Vmh4Zk5LWEpGVm9IYlpHU3QzeGZjQ0hIMVRRZXp1bkhTU016Mk44R1dKMFZiRzFGSTd5NVhQdmpBaTY0eklob2NYMUFYc1gwelF0RnhXVjR1a2plVmtNaEx6ZFhKR203TFY2NG5rNzAwSmRGUjJXQXZlU3FweFFNZG4zWkNOc3U5RnFWdDQ2Ulh6VHhiYm0zNUdXODdkRG5Vcko1S0thVGlBNWdzb3VJVEdrZFZqS0RMMjJGVyszUEdXWU5qeEY2OVpkT0VZVndBeFBod014MC9zeVkiLCJtYWMiOiI4NTQ3ZmIwNTZmZTU2MjlmOWRjOWNlNzBhNGQ1ZDU4NWY4MmQ2YTdkY2NhZTdjMGQxMTQwZDE2M2NmNGY3ODlhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:28:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik52d0FJV0Z6dmgrTFdnT3RmOEFYOHc9PSIsInZhbHVlIjoiY1lCTDY4VEJZRmNKVkJPSGZCNEp3YlRzYnlmTFNoUWg4NHB5U3JrdG9zNDZCbXFVcnBZazJoeWp4RUZXTmREVHlNVnBtMXZGSlJPV2V0VUo0Z3d1Y3dXMlFObGoxYlFLcHI0VEJpM21nQ09kSHdXckR4WnQxK0VIOS82TzFsVWxKeVhldEthTjdGWGFpZU8yRm1iY1k1NVp0bGg4Q2ZVcTM2V3VhYzVqU21zcUs4dHlZTWcwSHFwZ0RWeG5hK1ZPQ0JJRnhmc2RubS9nclFjODFDTm1hdVh2OTJ6QnVBcVZHeGNBSGE3ZjZuNnlGL2JNS3dXSWZBSkswaEZiaVdaY3FSVnRYd0k0aGVEN2ZmMzdTTzZaS2g4ZHlBMnF1ZEZSazdYL1ZxcnBuOERjS2tKRHJwQXg2M0JrNkxrV0JWUEVaWHJrSS9nQUQwM1hieWdiZHQyQVlWeGhMK3BJN2RjMHBHeHhENEt2eTcxN09XaTh1TTV4Q0prNnJ5SnpkSDJwZU1lcG9kSGZyWTRaUWpGUnJPSDdmd3l5N2lySWxQTVFHcTNlNEU3NExHbVZtemlJNVNQYTlEcmRscDFPcUw0N1h2WDdVdVlKRVhxNFBoUTZFWU1IcHVEeVRMbmJYbHFXVVF3eTRadkFvNkNrcS9henJLOWx3OTZURW80VnZYdEkiLCJtYWMiOiIxN2RjMzU2ZDNjZDJlMzY4OTg2OWIyZDkxNTg4ZTMyYTVjMDkzODM2MjI4NWQyZDExODM3MTNjOGUwZDBlNzhhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:28:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVTWXBmUlJlY1JYd2xRNEhiNUM2Rmc9PSIsInZhbHVlIjoiZ1YzV1pjMTNQVkFEQU1JSHVBTGtSanptclFGQlI0bzk2bk0xOUtzUFRTUkI3Q09RMzQ5MVhleTdFT3l1VEFsS1pQUWhjQzE1UHNZK2ZSRGF0SmpPblhhY1ZGdDJKQzhGbzcxREYzdThaLzMxUWhrL1hGclNXdmJlNW5PaWpuSlBxRGhueHJHNHpTR3RDN2VxNzRiS1BGRE9VM2JKVS9WN3hvU1J3VkF3SitRZy9oeHpUVUlMTm5nc3p1b3lWYWZWUGdMOE1pWHMzdjBWK05PYW9hK3l1Si9WWDBMc1lucXZOZHFTM1IxdkdxYkR5YUxDbUNKK0lvQTB0UDZSR09Zc3Z0SmlUWWl4RWZOck4yQ2svczZiaXlyZzl3OXFBRTVscmVWR0JGc0pLRzd3Vmh4Zk5LWEpGVm9IYlpHU3QzeGZjQ0hIMVRRZXp1bkhTU016Mk44R1dKMFZiRzFGSTd5NVhQdmpBaTY0eklob2NYMUFYc1gwelF0RnhXVjR1a2plVmtNaEx6ZFhKR203TFY2NG5rNzAwSmRGUjJXQXZlU3FweFFNZG4zWkNOc3U5RnFWdDQ2Ulh6VHhiYm0zNUdXODdkRG5Vcko1S0thVGlBNWdzb3VJVEdrZFZqS0RMMjJGVyszUEdXWU5qeEY2OVpkT0VZVndBeFBod014MC9zeVkiLCJtYWMiOiI4NTQ3ZmIwNTZmZTU2MjlmOWRjOWNlNzBhNGQ1ZDU4NWY4MmQ2YTdkY2NhZTdjMGQxMTQwZDE2M2NmNGY3ODlhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:28:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik52d0FJV0Z6dmgrTFdnT3RmOEFYOHc9PSIsInZhbHVlIjoiY1lCTDY4VEJZRmNKVkJPSGZCNEp3YlRzYnlmTFNoUWg4NHB5U3JrdG9zNDZCbXFVcnBZazJoeWp4RUZXTmREVHlNVnBtMXZGSlJPV2V0VUo0Z3d1Y3dXMlFObGoxYlFLcHI0VEJpM21nQ09kSHdXckR4WnQxK0VIOS82TzFsVWxKeVhldEthTjdGWGFpZU8yRm1iY1k1NVp0bGg4Q2ZVcTM2V3VhYzVqU21zcUs4dHlZTWcwSHFwZ0RWeG5hK1ZPQ0JJRnhmc2RubS9nclFjODFDTm1hdVh2OTJ6QnVBcVZHeGNBSGE3ZjZuNnlGL2JNS3dXSWZBSkswaEZiaVdaY3FSVnRYd0k0aGVEN2ZmMzdTTzZaS2g4ZHlBMnF1ZEZSazdYL1ZxcnBuOERjS2tKRHJwQXg2M0JrNkxrV0JWUEVaWHJrSS9nQUQwM1hieWdiZHQyQVlWeGhMK3BJN2RjMHBHeHhENEt2eTcxN09XaTh1TTV4Q0prNnJ5SnpkSDJwZU1lcG9kSGZyWTRaUWpGUnJPSDdmd3l5N2lySWxQTVFHcTNlNEU3NExHbVZtemlJNVNQYTlEcmRscDFPcUw0N1h2WDdVdVlKRVhxNFBoUTZFWU1IcHVEeVRMbmJYbHFXVVF3eTRadkFvNkNrcS9henJLOWx3OTZURW80VnZYdEkiLCJtYWMiOiIxN2RjMzU2ZDNjZDJlMzY4OTg2OWIyZDkxNTg4ZTMyYTVjMDkzODM2MjI4NWQyZDExODM3MTNjOGUwZDBlNzhhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:28:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1162882129\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}