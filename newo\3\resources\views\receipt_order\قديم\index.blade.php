@extends('layouts.admin')
@section('page-title')
    {{ __('أوامر الاستلام') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('الرئيسية') }}</a></li>
    <li class="breadcrumb-item">{{ __('إدارة العمليات المالية') }}</li>
    <li class="breadcrumb-item">{{ __('أوامر الاستلام') }}</li>
@endsection

@push('script-page')
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('.datatable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
        },
        "order": [[ 6, "desc" ]], // Sort by created date
        "pageLength": 25,
        "responsive": true
    });

    // View details modal
    $('.view-details').on('click', function() {
        var orderId = $(this).data('order-id');
        var orderType = $(this).data('order-type');
        
        // You can implement AJAX call here to get order details
        $('#orderDetailsModal').modal('show');
    });
});
</script>
@endpush

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('أوامر الاستلام') }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        @if(Auth::user()->can('manage warehouse') || Auth::user()->hasRole('Cashier'))
            <a href="{{ route('receipt-order.create') }}" class="btn btn-sm btn-primary"
               data-bs-toggle="tooltip" title="{{ __('إنشاء أمر استلام جديد') }}">
                <i class="ti ti-plus"></i> {{ __('إنشاء أمر استلام') }}
            </a>
        @endif
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('قائمة أوامر الاستلام') }}</h5>
                </div>
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table datatable">
                            <thead>
                                <tr>
                                    <th>{{ __('رقم الأمر') }}</th>
                                    <th>{{ __('نوع الأمر') }}</th>
                                    <th>{{ __('المورد/المصدر') }}</th>
                                    <th>{{ __('المستودع') }}</th>
                                    <th>{{ __('المستخدم المنشئ') }}</th>
                                    <th>{{ __('المبلغ الإجمالي') }}</th>
                                    <th>{{ __('التاريخ') }}</th>
                                    <th>{{ __('تاريخ الإنشاء') }}</th>
                                    <th>{{ __('الإجراءات') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($receiptOrders as $order)
                                    <tr>
                                        <td>
                                            <strong>{{ $order['reference_number'] }}</strong>
                                        </td>
                                        <td>
                                            @if($order['type'] === 'استلام بضاعة')
                                                <span class="badge bg-success">{{ $order['type'] }}</span>
                                            @else
                                                <span class="badge bg-info">{{ $order['type'] }}</span>
                                            @endif
                                        </td>
                                        <td>{{ $order['vendor_name'] }}</td>
                                        <td>{{ $order['warehouse_name'] }}</td>
                                        <td>
                                            <span class="badge bg-primary">{{ $order['creator_name'] }}</span>
                                        </td>
                                        <td>
                                            @if($order['total_amount'] > 0)
                                                {{ number_format($order['total_amount'], 2) }}
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>{{ \App\Models\Utility::getDateFormated($order['date']) }}</td>
                                        <td>{{ $order['created_at']->format('Y-m-d H:i') }}</td>
                                        <td>
                                            <div class="action-btn bg-info ms-2">
                                                <a href="{{ route('receipt-order.show', $order['id']) }}"
                                                   class="mx-3 btn btn-sm align-items-center"
                                                   data-bs-toggle="tooltip"
                                                   title="{{ __('عرض التفاصيل') }}">
                                                    <i class="ti ti-eye text-white"></i>
                                                </a>
                                            </div>

                                            <div class="action-btn bg-secondary ms-2">
                                                <a href="{{ route('receipt-order.show', $order['id']) }}?print=1"
                                                   class="mx-3 btn btn-sm align-items-center"
                                                   data-bs-toggle="tooltip"
                                                   title="{{ __('طباعة الفاتورة') }}"
                                                   target="_blank">
                                                    <i class="ti ti-printer text-white"></i>
                                                </a>
                                            </div>

                                            <div class="action-btn bg-danger ms-2">
                                                <a href="{{ route('receipt-order.pdf', $order['id']) }}"
                                                   class="mx-3 btn btn-sm align-items-center"
                                                   data-bs-toggle="tooltip"
                                                   title="{{ __('تحميل PDF') }}">
                                                    <i class="ti ti-file-type-pdf text-white"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Details Modal -->
    <div class="modal fade" id="orderDetailsModal" tabindex="-1" aria-labelledby="orderDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="orderDetailsModalLabel">{{ __('تفاصيل أمر الاستلام') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="orderDetailsContent">
                        <!-- Order details will be loaded here -->
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">{{ __('جاري التحميل...') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('إغلاق') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="theme-avtar bg-primary">
                            <i class="ti ti-package"></i>
                        </div>
                        <div class="ms-3">
                            <small class="text-muted">{{ __('إجمالي أوامر الاستلام') }}</small>
                            <h6 class="m-0">{{ $receiptOrders->count() }}</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="theme-avtar bg-success">
                            <i class="ti ti-truck-delivery"></i>
                        </div>
                        <div class="ms-3">
                            <small class="text-muted">{{ __('أوامر استلام البضاعة') }}</small>
                            <h6 class="m-0">{{ $receiptOrders->where('type', 'استلام بضاعة')->count() }}</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="theme-avtar bg-info">
                            <i class="ti ti-arrows-exchange"></i>
                        </div>
                        <div class="ms-3">
                            <small class="text-muted">{{ __('أوامر نقل البضاعة') }}</small>
                            <h6 class="m-0">{{ $receiptOrders->where('type', 'نقل بضاعة')->count() }}</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="theme-avtar bg-warning">
                            <i class="ti ti-currency-dollar"></i>
                        </div>
                        <div class="ms-3">
                            <small class="text-muted">{{ __('إجمالي القيمة') }}</small>
                            <h6 class="m-0">{{ number_format($receiptOrders->sum('total_amount'), 2) }}</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('النشاط الأخير') }}</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        @foreach($receiptOrders->take(5) as $order)
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    @if($order['type'] === 'استلام بضاعة')
                                        <i class="ti ti-truck-delivery text-success"></i>
                                    @else
                                        <i class="ti ti-arrows-exchange text-info"></i>
                                    @endif
                                </div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">{{ $order['type'] }} - {{ $order['reference_number'] }}</h6>
                                    <p class="mb-1 text-muted">
                                        {{ $order['vendor_name'] }} → {{ $order['warehouse_name'] }}
                                        @if($order['total_amount'] > 0)
                                            <span class="badge bg-primary">{{ number_format($order['total_amount'], 2) }}</span>
                                        @endif
                                    </p>
                                    <small class="text-muted">{{ $order['created_at']->diffForHumans() }}</small>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style-page')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -30px;
    top: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #fff;
    border: 2px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -21px;
    top: 10px;
    bottom: 0;
    width: 2px;
    background: #ddd;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}
</style>
@endpush
