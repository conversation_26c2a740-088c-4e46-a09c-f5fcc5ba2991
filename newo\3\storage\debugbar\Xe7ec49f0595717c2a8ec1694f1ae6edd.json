{"__meta": {"id": "Xe7ec49f0595717c2a8ec1694f1ae6edd", "datetime": "2025-06-17 06:27:13", "utime": **********.505812, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750141631.922689, "end": **********.505844, "duration": 1.5831551551818848, "duration_str": "1.58s", "measures": [{"label": "Booting", "start": 1750141631.922689, "relative_start": 0, "end": **********.29952, "relative_end": **********.29952, "duration": 1.3768310546875, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.299547, "relative_start": 1.3768579959869385, "end": **********.505848, "relative_end": 3.814697265625e-06, "duration": 0.20630097389221191, "duration_str": "206ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45168936, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01035, "accumulated_duration_str": "10.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.414329, "duration": 0.0073, "duration_str": "7.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.531}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.454458, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.531, "width_percent": 16.618}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.47945, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.15, "width_percent": 12.85}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-67161076 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-67161076\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-718351584 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-718351584\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-685450172 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-685450172\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1036815408 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750141457263%7C21%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndkM1p3c0F1UmlTVk91cXEyWW1BQ0E9PSIsInZhbHVlIjoiZHJPMnNiN1VWaldFTWdwNnFZTXN4VUtMMTZlRHhSZ1laK09hZXZNcFE4dXNmMXdLOGVtbVdLT0VWRGxjNXdWSTNxTmpWMHcyYmRnOFVxdUF0UjdXRHpPNmdRUkNxaHFwdGN0d0g3RDdHRzRFMTd2dERRNGhWcXF1T2VnQ25MaXMwcldPOWtXVEV1S1I2cE1TTTZSNzhQbkZabWF3SzFheWh5aHpLc1F4V3Qvd0luTWp2U1JuWjZGMjkwMGk1WFZueFAvd09LSHBiTDZhbFJ4S0Y2MkdmcCt4RmVoak5QUUt0SVJjaUxpdjEyZGtIajY1OXYzSnFpRi9kVUVVREpoK0RmS1dGQkUyWExKMGhEb2c3OWNhSXZjZ083aFAwR1RIWGsrRUdONmF0SjVxazhtdkVYTUpUenltcDRXZndDQTMyQzEvUnBjT3BtZEd3dEIrczNQOUZaamtoSjJ5N2RlUzMrcEp3Mm93cGdraHZHN2E2emE3M1B2YkFUdWc2aFVSR0JCYXlxR0lNd1dmQ3F6ZFhBdE9mQTFxRWRZeGEzb1cyZUlyMzZrMmNLWENOR2p4VVZieHQxVG1peEsyS1Zsanlka0lrNTFSUE9jWUdJYnRuVys0czc0VjVrV0pMeWZYTFFob0MwaEpRSDV3N0hpUy90Sjc0TTZLaDBydU4xTjUiLCJtYWMiOiI3ODQ1MDY4NTMzZjNlOGQxYTk3YmE2M2Y3MWE5MTRjMTAwNWNlODY3ZTQ5MmQwMTcyMGFjYjc1Zjg1MWJkODA2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IndNQTVqVERqcHBIUXZ5c0hhV28zc0E9PSIsInZhbHVlIjoiRTliRDQzbm83QXdyOHN0cmlhNVltUDJndzlhaWkwNnY4RVlPUmczbmFmZ3p3SlZiZ1oyejF2VURuYUZ6MDJ0eEZtbng3WXplcmozUU9LR1grTlVYR2pCTU1rMXcraEtDSVFOVFE0Z0ErMlJEY0NiTVQ5Qk9RbHJ1eTlLUVhmOWJFZzZ5a3ZjelQxcXU2Yk0wSUxXS1hOTXpjampwRDVtQ3dPNGR6ZnR0NjMxNUtnM1ZVN3BVVWJFNGVYdGZOeTNFUUhESGxjVFVJKy9zK3o3UHhBWHJnRTFRNDFJUm1YRUtTV1Rqbm9ORThELzFzcHFhZ1ovU1dlVXlRaVFVNWFlYTQ2Mzd5OG83VXJFaENvMVlSZjBWeUdtZzNPM0hBckNJZmlUZndzR1BtL2FReUNueFlGNFBoSmhuVUdBVnFKSVhicUNLbFJhVFpkZllsTm40NlVwSFpZOWR5Tkg1UVJBRHg4c3RTTVZ5emYvZk5BeUxseGIzQTRtQVZJemIybVBPZEVrVWtBSHhsQ2s1VTdnRzJNWjZXN3BnZ3c3cTd5TnFKT2Z4S01ZRzV1U29FQ1dNS0Y0WTI1VHJUM3grNmszYnA0YmdoaHNDOGd6cy9uWi8xQzViSkR2akJDMkc3SzZ1UmNRRHcxK0VHN0V1Uy9lZ2VxMjZPWlBoS3lLcG54YUciLCJtYWMiOiJiZDMyZDQyMTMwN2IzMTRhNDI3NjQ1YTA2ZTE0YWFiYTYzNzM2M2YyMWVlZGZhMDMxNDgxYjE3OWYzMTQ3MzdmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1036815408\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2144765675 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2144765675\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1714738366 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:27:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRPQUJmNkFFa0dXd2ZSc0JteTdXZHc9PSIsInZhbHVlIjoiU09WQVRveWR0ZFNtMHorTlB1S1FXeFBmT2dQWU0zb1hLQVE0RmNmb3U0a2M5L29ZRmNaMm9jNHE5OStIS0lmbDJZTEtiR1A3dGVSZTBCVVFDMzNiU3N6VU9qdzdJQncvc1VKdENla0tGMzlvRm9DTmNFMVBNL0t2dExkb2JtK0RUL0dNbHV0eWlBWXZFdXJIRU1qVCtFbTFzdnNBT0t6dVhMZTRTdHVoT1lnbnNMOUJlR2MrTEJWM0hTdlcrY1dESmJxSUJKTituRVVqK1dKN3c4OFArVFBJUDIzUlEyYnRxM0trRFJRTjNjWE5aNjJMM28rT2ZVMU1DRVJ0VlNmR1R2bEVULytRNkZLUjlUR0Y2YVVUVTlKVDFseFAwOENXWnRyd0E0S08vQ0txN1U5M1JSMDk4N01SNUlCeUhPVGJBK1FwR2p3VklJOUpRaG1BQmNyUEozaU1TWnJYanExZ2dvdytFKzdQT0VMeWhERGUwdU93SWZpcGRlb3YvMFFLemduUWFUdDFNOUx6ZzZTcnh6NEtSRGR0aDVLaFlreGVjWXNKWGxpZDEyMXhMamZIMDBWREgyV01pT1Brb0xiU0o1d05WNFpHcEFtQ2Y5V2s3NzYwNXZyZlRKSGY4UUplbmhBY2d3MGlhTXJnR2IvVWJvSGhDNlVQOGY0a2VBSVgiLCJtYWMiOiIwMDVjZjdmOTAzMmE1YzRhZDljZDg5YmE2ZDQwZjhmZjk0OTg4ZTg4YTUzZThjZjlhYjkzYjkyNWRkMWViMWNjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:27:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikt1Sk1tSG9WclNsRDJLbjNiRWFCK3c9PSIsInZhbHVlIjoia2RwSi9ZVEtUUVZkeFR3d2VVTXZKQXpad1NXQ2lGaFFtVXZGdGk4T1NmbEwyVUUxMFdtNkJ4S1ZlT1kySkNoMTJmUFVUU3k0aWtZeHYwWXplR1VyYVZlTE9lNUkvZnpLR0U3ZjVuSHorQUhGUlUybUJ0SGF3MHBidzlSMCs2QVRxTHU1cHVvejRwMnNocC9BaGhsaldFakpKSDg1K0VJdXBDamJwclplVE1TTXhhUytIRnNLZmREOFBQTTEwTEN3Y3lBS0lFWGpqYnNnTVRjckdCOHcyQktJZWJNT1pNWnJZUUhUR1RvcElSSEt5ZXVHOWNaWlJBL0g5N01kbS80RUozMENwYnJvVVFjTnM5RmJWS1BiQnFFQ0QyOVZxdTZFMXhqNGVFN29ycTlsMlcrdHE5NmZjYUNqZ3ZDQUtYYmlJK0lXY0t1azNQWmcrV2Jyc0QyZm50R1hyQ0hsczNLZVdHck8xbWN1VGJKWG1YdlZSWFd4ZHZuaWVSRGRQQWFoblBCVSt0RS8zVnM1S3lLdTJyN1BxRHVGV2g4U1YyczNaNk1QaGJqRk5GNk42d2o3N3JBYnZPN095VjYwZUo0QlpWeVlZL2c3My9NQm0vQjZXcUZUc1U3WmtLamY3S2pwdWdLdzJHdW1pZEh4NEltOWhvQXBOclVoaW9zUCtGZzgiLCJtYWMiOiI4NTBjOGIwMjZhYTZlYjY0OTU1NTMwMzA1YmNiZTY5YWU5ZGZkNTZkYmEzMzE5OWE3YjY3MDNiOWU2NGZlNzA1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:27:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRPQUJmNkFFa0dXd2ZSc0JteTdXZHc9PSIsInZhbHVlIjoiU09WQVRveWR0ZFNtMHorTlB1S1FXeFBmT2dQWU0zb1hLQVE0RmNmb3U0a2M5L29ZRmNaMm9jNHE5OStIS0lmbDJZTEtiR1A3dGVSZTBCVVFDMzNiU3N6VU9qdzdJQncvc1VKdENla0tGMzlvRm9DTmNFMVBNL0t2dExkb2JtK0RUL0dNbHV0eWlBWXZFdXJIRU1qVCtFbTFzdnNBT0t6dVhMZTRTdHVoT1lnbnNMOUJlR2MrTEJWM0hTdlcrY1dESmJxSUJKTituRVVqK1dKN3c4OFArVFBJUDIzUlEyYnRxM0trRFJRTjNjWE5aNjJMM28rT2ZVMU1DRVJ0VlNmR1R2bEVULytRNkZLUjlUR0Y2YVVUVTlKVDFseFAwOENXWnRyd0E0S08vQ0txN1U5M1JSMDk4N01SNUlCeUhPVGJBK1FwR2p3VklJOUpRaG1BQmNyUEozaU1TWnJYanExZ2dvdytFKzdQT0VMeWhERGUwdU93SWZpcGRlb3YvMFFLemduUWFUdDFNOUx6ZzZTcnh6NEtSRGR0aDVLaFlreGVjWXNKWGxpZDEyMXhMamZIMDBWREgyV01pT1Brb0xiU0o1d05WNFpHcEFtQ2Y5V2s3NzYwNXZyZlRKSGY4UUplbmhBY2d3MGlhTXJnR2IvVWJvSGhDNlVQOGY0a2VBSVgiLCJtYWMiOiIwMDVjZjdmOTAzMmE1YzRhZDljZDg5YmE2ZDQwZjhmZjk0OTg4ZTg4YTUzZThjZjlhYjkzYjkyNWRkMWViMWNjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:27:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikt1Sk1tSG9WclNsRDJLbjNiRWFCK3c9PSIsInZhbHVlIjoia2RwSi9ZVEtUUVZkeFR3d2VVTXZKQXpad1NXQ2lGaFFtVXZGdGk4T1NmbEwyVUUxMFdtNkJ4S1ZlT1kySkNoMTJmUFVUU3k0aWtZeHYwWXplR1VyYVZlTE9lNUkvZnpLR0U3ZjVuSHorQUhGUlUybUJ0SGF3MHBidzlSMCs2QVRxTHU1cHVvejRwMnNocC9BaGhsaldFakpKSDg1K0VJdXBDamJwclplVE1TTXhhUytIRnNLZmREOFBQTTEwTEN3Y3lBS0lFWGpqYnNnTVRjckdCOHcyQktJZWJNT1pNWnJZUUhUR1RvcElSSEt5ZXVHOWNaWlJBL0g5N01kbS80RUozMENwYnJvVVFjTnM5RmJWS1BiQnFFQ0QyOVZxdTZFMXhqNGVFN29ycTlsMlcrdHE5NmZjYUNqZ3ZDQUtYYmlJK0lXY0t1azNQWmcrV2Jyc0QyZm50R1hyQ0hsczNLZVdHck8xbWN1VGJKWG1YdlZSWFd4ZHZuaWVSRGRQQWFoblBCVSt0RS8zVnM1S3lLdTJyN1BxRHVGV2g4U1YyczNaNk1QaGJqRk5GNk42d2o3N3JBYnZPN095VjYwZUo0QlpWeVlZL2c3My9NQm0vQjZXcUZUc1U3WmtLamY3S2pwdWdLdzJHdW1pZEh4NEltOWhvQXBOclVoaW9zUCtGZzgiLCJtYWMiOiI4NTBjOGIwMjZhYTZlYjY0OTU1NTMwMzA1YmNiZTY5YWU5ZGZkNTZkYmEzMzE5OWE3YjY3MDNiOWU2NGZlNzA1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:27:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1714738366\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-911044620 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-911044620\", {\"maxDepth\":0})</script>\n"}}