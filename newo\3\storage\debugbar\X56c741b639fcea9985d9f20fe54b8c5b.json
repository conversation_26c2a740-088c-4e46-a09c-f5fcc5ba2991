{"__meta": {"id": "X56c741b639fcea9985d9f20fe54b8c5b", "datetime": "2025-06-17 05:40:52", "utime": **********.066249, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750138850.583225, "end": **********.066282, "duration": 1.4830570220947266, "duration_str": "1.48s", "measures": [{"label": "Booting", "start": 1750138850.583225, "relative_start": 0, "end": **********.875536, "relative_end": **********.875536, "duration": 1.2923109531402588, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.875563, "relative_start": 1.2923378944396973, "end": **********.066286, "relative_end": 4.0531158447265625e-06, "duration": 0.19072318077087402, "duration_str": "191ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43901864, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.023850000000000003, "accumulated_duration_str": "23.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.987844, "duration": 0.022510000000000002, "duration_str": "22.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.382}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0255451, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "ty", "start_percent": 94.382, "width_percent": 5.618}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1176411317 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1176411317\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1478345284 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1478345284\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1644940219 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750138848236%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFpWDdaQnBkUE9OMVpYRkN1Rit5TFE9PSIsInZhbHVlIjoiQ1RjT3F0NUlyaDJsZ0ZONklLVmJzR0NyNC9wemwvNzJKVUc1QVVIWW0remN4ek1ieUZoV2lPOE54d3lmL2I4b0JMQVZhc3I0dEVIUldPWUg3L2pWU2hoZHAzcFFSdDZWMnJ2OHZFbXVibXFhaW9sM01Ga1ljc1ZUTEhkVzRKSnM5N21YdE9yMFlxYWZHbm4xbHpYc1AyMGNhWGZPcTJDU0I4ZWY0L3VVUWV1R3doSHFnWGhQYVc5ZVU4VTBGQXdHcmpYK24xRFkvTk43cVBnYVF6SkMwem5MMEdtdzc4WWFWWkdWK2ovV2ZQQjJ4Ui9yUFgwcHltaGF5S3EwNWhYNUpKY3dYRW9YTzBNNHJVN0VUMTFKMVZHL1RtazNVUEM3cFRydkUyaEVLNldNWFJKTlo4djh1K2pvbnYrRm94TTBNditXVTloV0w5TXVuWTFIU2s4ODlaNzlEa2twQWpDbWE4N08yRlJQUmxTRGJQVjlCWWFIamQ3MmdBSERpUFR0alRSWVg5MkxMZStwa3JZQzRPdVpuSWFmZno0clcvZFgrbXg1TWVvS3lWaTBUWjVYUUpXWTdHWUhsODZSR1JqY2RnN2gvWnUybW1nVzBWSHN0NlN3SysyZFpiOHhzdC9XZjZ1Y0RHZlc4UkwwYlllSWpjTFkzVlZ1RFJTREZvMFciLCJtYWMiOiI1M2NhYzMwZThmZWM4ZTZhNmZkODAyMWJkOGUyZGU5MjBmZjllOTU3MWIxZTczYjFhMTcyNDFmYmQzODAyMTdlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IndYQUtrMlZQSDBUajlyVGIyOXNZNkE9PSIsInZhbHVlIjoiWGJ2cFRtaU0zU2sveFFMaysyRlA5dWdiSVNNWFM5MlAxY1h5K0sxdFpJS0tJSitrODNRK3dISUlTL09ac1o4OGp2ZnhMdWdIOHJjYlVadHBzWGtLVUg0M2dWU2ZUNnNhK0FSaGUxaFdqOWdscXVLeHB3c3lobmRMVjlCblNNczlPU2FDdHpETERpb3RMMW5ZS3dYYXd4OVdOdnhmSkVKSzZCMittSWxiaE1KK2pRbXIyeEtNSm0zMkVCZW4vVFdyeFdHYmQxYUFPbFRtOXROY0pxUE5lbnNwUlB1bTZJSVk5aXJKbjc1S2JVODVLZS9UWmJGSC9XTVhySHdNQUpoV2w2SWZNb3p3ZHJrcWdPTHdJcm9GWVN0MHZXd2FQdFRRL1ZoNlNvZ3lXWkI5QzBvYWtrc0JheTU3c09GaFBMMnU4bWdJVHRteFRIN3NjcWdQRUY5blpSY1Yvb1NZV3lxWktPUi9qdTBNWER4VTd1WVZ5YnYyQWtlN2dnaHRqOVd4bVMzQkFkdXZheWtGVzJ4Q01MUkUvSk9yKzVpUTMxTGpvZkVxeFdyV2N4L1NsT1pNeFlvbXdiamEvaWVIV1VYUm9ROGU0Z1VBbll6UWJIOFhXblVLYUc4bjFHY2hpaHFBeDFReXdBdXBONS93cDl2blZJNDV6V1BGRzRYRkNEYlgiLCJtYWMiOiI2OTE0MzQ0Y2JjOGY5MDY2OWM4MTA3NzBmY2YyMzk3MzI2OTZkYWUzMTU4OGI5M2QwNGMwYzhlZTQ4YTUxNjhkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1644940219\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-914142907 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MsEpVpyAabX9l6kTbr1IUggTy4SSXp1me81GakZC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-914142907\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-141474260 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:40:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBIRGM1MG1qcDBlTU1laS91M21sdmc9PSIsInZhbHVlIjoiSmNwRzVpK2xlRFQ1c1k5eC9ITGYxVjhRV0lmck1SNDJBRmM0SVFrZ3puK1ovWTNZSWJlVUczdHNWYlM5N0x1YkVJNkRkRlExdGQyNGtTNUJCbHRGbG5Ta3MwTUUwZWFHUTIxMUJ4Tmp4SkRqOXJGeDlhMEFPZUF2MHlTTVhEL1hJK3VRUVBBbnBXcDNJcTlHclZZZXJnTzFsd3JqQ3MxVDNjZkZ6ZUg2WnRRM3hzd3ZVV3E3VWx1YmV4cmRTSDQ0b0dQNXR0MjNFOENXQkxyR2orNzZWNWxaRVFvOGhVeUVsNEdreWtlUHB5V1pGVGFIc1FhVXNzOXZtUVJlSkVKOVdGUXM3c0VYODB5OWl1dGk0QncwNU5kRTdZd0p6bjUyZ21VclRkNitTQ0NlZmFHWjlReG1tS0xpVVRITGpSZG01NHVBa0c5K3FQMHA2WUtTbmpGVmx3UTAxdmw4NnNNMTdYVHpGeEZTbDd5dmdyaEJZL0svQmFneGd2eHVUVStsVTdmUVJtNFBHYUlKRXpCZkVQcExmbDVaVllrUWZJSzd2QzVUTVllMU1IQmljQjFGMEpoczUvNnZzaDNwd0trdVdkUUw2bll6RFVIbUJ3RlFmajBKc0ROZVF2V1pKVTUwQzVydVJQTGsycXpaMnJWME5YSVZ2NlUzTTdJU0RqTHMiLCJtYWMiOiI3MTEwZDM3YTEyYmNlZDM1ZWJlZjVkMzJhNWZiZTViNGExYTM5MGJlYTQzNzhjOTZlN2IyMWUyYTIyZmRlZjYzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjIrRmV1T3FTRG4vb2tTZ2R2SUNOTnc9PSIsInZhbHVlIjoiZkNZZGVTVWMxcXEzQk9zSUJTVUFaN2lEdU5TNWFlUEIwNk5NVUIzb2RENkRMNVdXQ2o2amYrTXRENkg1dzlLc1N3YTRxTmFDS3pjeDYzazB2Y2xENUY1KzhTTWtZSUFhc012ZHdKZWttdnduVWN6cE9ROHYvZlZwYWtjNnNPSUtJejhJUWhmaldFbUY2bk9IczBxSEpRVlRqNFR0bmhlbUxmb3NVM0VKZGpBdlJ4MmJqTmFWdkhhMi9ZM2JRaWN6aUlESWNWNjEwYlh1Wkxjcms0emRBOWI1eHFrYzlySnk3dXppblFEOE5ldDVKRkswSW93YXB0SUJxRXlpOC8rbUVYSVVZR2JNZzlTNWtTOElNMzZoU3RJeGRHb3ZaRlcxT0lYUkMzaEpjSnhNMC9JQmJ6RWJZemtmTkhsbGVsRDZUaHc3ZE9Yb0VIeW5Vakp4NHRtSlNKTnU3azZubUlyVnlma3VGSi82bkFvT05EZitraE5zcG9NTWtMYmxYR0x6YlhBc3QvVU1EVG1CZWZ4NnRMa2wxMEJpRWNRbEdaWk5PN0RvZEtHRjl1MEZnQkR4dnhZcm1aa0NPTGpIK3VKUTN4eCtrRVMwWk1CcmJRZG5pbUxPVjNEQlFLWWx0Y0ZnV1kyeWl6cmcrU3V6UDJGS3A3N2lvQStUb0FoVGJMdGwiLCJtYWMiOiI2NWRiZGEwMGVhYmRhZmU2NDIxZTZmMmNkOTBhOTczNTUxYmRiMWNlNmZlMDIxMzlkNjk3MzUxYTI0YzUzODBmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBIRGM1MG1qcDBlTU1laS91M21sdmc9PSIsInZhbHVlIjoiSmNwRzVpK2xlRFQ1c1k5eC9ITGYxVjhRV0lmck1SNDJBRmM0SVFrZ3puK1ovWTNZSWJlVUczdHNWYlM5N0x1YkVJNkRkRlExdGQyNGtTNUJCbHRGbG5Ta3MwTUUwZWFHUTIxMUJ4Tmp4SkRqOXJGeDlhMEFPZUF2MHlTTVhEL1hJK3VRUVBBbnBXcDNJcTlHclZZZXJnTzFsd3JqQ3MxVDNjZkZ6ZUg2WnRRM3hzd3ZVV3E3VWx1YmV4cmRTSDQ0b0dQNXR0MjNFOENXQkxyR2orNzZWNWxaRVFvOGhVeUVsNEdreWtlUHB5V1pGVGFIc1FhVXNzOXZtUVJlSkVKOVdGUXM3c0VYODB5OWl1dGk0QncwNU5kRTdZd0p6bjUyZ21VclRkNitTQ0NlZmFHWjlReG1tS0xpVVRITGpSZG01NHVBa0c5K3FQMHA2WUtTbmpGVmx3UTAxdmw4NnNNMTdYVHpGeEZTbDd5dmdyaEJZL0svQmFneGd2eHVUVStsVTdmUVJtNFBHYUlKRXpCZkVQcExmbDVaVllrUWZJSzd2QzVUTVllMU1IQmljQjFGMEpoczUvNnZzaDNwd0trdVdkUUw2bll6RFVIbUJ3RlFmajBKc0ROZVF2V1pKVTUwQzVydVJQTGsycXpaMnJWME5YSVZ2NlUzTTdJU0RqTHMiLCJtYWMiOiI3MTEwZDM3YTEyYmNlZDM1ZWJlZjVkMzJhNWZiZTViNGExYTM5MGJlYTQzNzhjOTZlN2IyMWUyYTIyZmRlZjYzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjIrRmV1T3FTRG4vb2tTZ2R2SUNOTnc9PSIsInZhbHVlIjoiZkNZZGVTVWMxcXEzQk9zSUJTVUFaN2lEdU5TNWFlUEIwNk5NVUIzb2RENkRMNVdXQ2o2amYrTXRENkg1dzlLc1N3YTRxTmFDS3pjeDYzazB2Y2xENUY1KzhTTWtZSUFhc012ZHdKZWttdnduVWN6cE9ROHYvZlZwYWtjNnNPSUtJejhJUWhmaldFbUY2bk9IczBxSEpRVlRqNFR0bmhlbUxmb3NVM0VKZGpBdlJ4MmJqTmFWdkhhMi9ZM2JRaWN6aUlESWNWNjEwYlh1Wkxjcms0emRBOWI1eHFrYzlySnk3dXppblFEOE5ldDVKRkswSW93YXB0SUJxRXlpOC8rbUVYSVVZR2JNZzlTNWtTOElNMzZoU3RJeGRHb3ZaRlcxT0lYUkMzaEpjSnhNMC9JQmJ6RWJZemtmTkhsbGVsRDZUaHc3ZE9Yb0VIeW5Vakp4NHRtSlNKTnU3azZubUlyVnlma3VGSi82bkFvT05EZitraE5zcG9NTWtMYmxYR0x6YlhBc3QvVU1EVG1CZWZ4NnRMa2wxMEJpRWNRbEdaWk5PN0RvZEtHRjl1MEZnQkR4dnhZcm1aa0NPTGpIK3VKUTN4eCtrRVMwWk1CcmJRZG5pbUxPVjNEQlFLWWx0Y0ZnV1kyeWl6cmcrU3V6UDJGS3A3N2lvQStUb0FoVGJMdGwiLCJtYWMiOiI2NWRiZGEwMGVhYmRhZmU2NDIxZTZmMmNkOTBhOTczNTUxYmRiMWNlNmZlMDIxMzlkNjk3MzUxYTI0YzUzODBmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-141474260\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}