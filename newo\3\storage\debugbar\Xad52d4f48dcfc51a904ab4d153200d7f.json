{"__meta": {"id": "Xad52d4f48dcfc51a904ab4d153200d7f", "datetime": "2025-06-16 15:23:48", "utime": **********.457487, "method": "PUT", "uri": "/receipt-order/1", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087426.38125, "end": **********.457519, "duration": 2.0762691497802734, "duration_str": "2.08s", "measures": [{"label": "Booting", "start": 1750087426.38125, "relative_start": 0, "end": 1750087427.893079, "relative_end": 1750087427.893079, "duration": 1.511829137802124, "duration_str": "1.51s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750087427.893099, "relative_start": 1.5118491649627686, "end": **********.457522, "relative_end": 2.86102294921875e-06, "duration": 0.5644228458404541, "duration_str": "564ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52004136, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT receipt-order/{receipt_order}", "middleware": "web, verified, auth, XSS, revalidate", "as": "receipt-order.update", "controller": "App\\Http\\Controllers\\ReceiptOrderController@update", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=602\" onclick=\"\">app/Http/Controllers/ReceiptOrderController.php:602-748</a>"}, "queries": {"nb_statements": 24, "nb_failed_statements": 0, "accumulated_duration": 0.04128999999999999, "accumulated_duration_str": "41.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.033267, "duration": 0.00777, "duration_str": "7.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 18.818}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.083628, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 18.818, "width_percent": 3.052}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.155683, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 21.87, "width_percent": 5.473}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.166151, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 27.343, "width_percent": 3.681}, {"sql": "select count(*) as aggregate from `venders` where `id` = '4'", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.198837, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 31.024, "width_percent": 3.923}, {"sql": "select count(*) as aggregate from `warehouses` where `id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.206732, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 34.948, "width_percent": 2.858}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '3'", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.226222, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 37.806, "width_percent": 3.488}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '5'", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.234143, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 41.293, "width_percent": 2.809}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 626}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.25313, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:626", "source": "app/Http/Controllers/ReceiptOrderController.php:626", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=626", "ajax": false, "filename": "ReceiptOrderController.php", "line": "626"}, "connection": "ty", "start_percent": 44.103, "width_percent": 0}, {"sql": "select * from `receipt_orders` where `id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 639}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.255181, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:639", "source": "app/Http/Controllers/ReceiptOrderController.php:639", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=639", "ajax": false, "filename": "ReceiptOrderController.php", "line": "639"}, "connection": "ty", "start_percent": 44.103, "width_percent": 3.899}, {"sql": "select * from `receipt_order_products` where `receipt_order_products`.`receipt_order_id` = 1 and `receipt_order_products`.`receipt_order_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 642}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.268264, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:642", "source": "app/Http/Controllers/ReceiptOrderController.php:642", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=642", "ajax": false, "filename": "ReceiptOrderController.php", "line": "642"}, "connection": "ty", "start_percent": 48.002, "width_percent": 5.062}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 3 limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 478}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 657}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.280777, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:478", "source": "app/Http/Controllers/ReceiptOrderController.php:478", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=478", "ajax": false, "filename": "ReceiptOrderController.php", "line": "478"}, "connection": "ty", "start_percent": 53.064, "width_percent": 3.318}, {"sql": "update `warehouse_products` set `quantity` = 0, `warehouse_products`.`updated_at` = '2025-06-16 15:23:48' where `id` = 2", "type": "query", "params": [], "bindings": ["0", "2025-06-16 15:23:48", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 494}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 657}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.28913, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:494", "source": "app/Http/Controllers/ReceiptOrderController.php:494", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=494", "ajax": false, "filename": "ReceiptOrderController.php", "line": "494"}, "connection": "ty", "start_percent": 56.382, "width_percent": 3.342}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 5 limit 1", "type": "query", "params": [], "bindings": ["8", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 478}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 657}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.29932, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:478", "source": "app/Http/Controllers/ReceiptOrderController.php:478", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=478", "ajax": false, "filename": "ReceiptOrderController.php", "line": "478"}, "connection": "ty", "start_percent": 59.724, "width_percent": 2.882}, {"sql": "update `warehouse_products` set `quantity` = 10, `warehouse_products`.`updated_at` = '2025-06-16 15:23:48' where `id` = 4", "type": "query", "params": [], "bindings": ["10", "2025-06-16 15:23:48", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 494}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 657}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.309597, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:494", "source": "app/Http/Controllers/ReceiptOrderController.php:494", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=494", "ajax": false, "filename": "ReceiptOrderController.php", "line": "494"}, "connection": "ty", "start_percent": 62.606, "width_percent": 2.567}, {"sql": "delete from `receipt_order_products` where `receipt_order_products`.`receipt_order_id` = 1 and `receipt_order_products`.`receipt_order_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 675}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.32602, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:675", "source": "app/Http/Controllers/ReceiptOrderController.php:675", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=675", "ajax": false, "filename": "ReceiptOrderController.php", "line": "675"}, "connection": "ty", "start_percent": 65.173, "width_percent": 5.183}, {"sql": "insert into `receipt_order_products` (`receipt_order_id`, `product_id`, `quantity`, `unit_cost`, `expiry_date`, `is_return`, `notes`, `total_cost`, `updated_at`, `created_at`) values (1, '3', '10.00', '1.00', '2025-06-18 00:00:00', 0, '', 10, '2025-06-16 15:23:48', '2025-06-16 15:23:48')", "type": "query", "params": [], "bindings": ["1", "3", "10.00", "1.00", "2025-06-18 00:00:00", "0", "", "10", "2025-06-16 15:23:48", "2025-06-16 15:23:48"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 690}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.336437, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:690", "source": "app/Http/Controllers/ReceiptOrderController.php:690", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=690", "ajax": false, "filename": "ReceiptOrderController.php", "line": "690"}, "connection": "ty", "start_percent": 70.356, "width_percent": 3.996}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = '3' limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 478}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 698}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.347248, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:478", "source": "app/Http/Controllers/ReceiptOrderController.php:478", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=478", "ajax": false, "filename": "ReceiptOrderController.php", "line": "478"}, "connection": "ty", "start_percent": 74.352, "width_percent": 2.373}, {"sql": "update `warehouse_products` set `quantity` = 10, `warehouse_products`.`updated_at` = '2025-06-16 15:23:48' where `id` = 2", "type": "query", "params": [], "bindings": ["10", "2025-06-16 15:23:48", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 494}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 698}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.357223, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:494", "source": "app/Http/Controllers/ReceiptOrderController.php:494", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=494", "ajax": false, "filename": "ReceiptOrderController.php", "line": "494"}, "connection": "ty", "start_percent": 76.726, "width_percent": 3.052}, {"sql": "select * from `product_expiry_dates` where (`product_id` = '3' and `warehouse_id` = '8') limit 1", "type": "query", "params": [], "bindings": ["3", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 716}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.368454, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:716", "source": "app/Http/Controllers/ReceiptOrderController.php:716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=716", "ajax": false, "filename": "ReceiptOrderController.php", "line": "716"}, "connection": "ty", "start_percent": 79.777, "width_percent": 4.723}, {"sql": "insert into `receipt_order_products` (`receipt_order_id`, `product_id`, `quantity`, `unit_cost`, `expiry_date`, `is_return`, `notes`, `total_cost`, `updated_at`, `created_at`) values (1, '5', '20', '8.00', '2025-06-24 00:00:00', 0, '', 160, '2025-06-16 15:23:48', '2025-06-16 15:23:48')", "type": "query", "params": [], "bindings": ["1", "5", "20", "8.00", "2025-06-24 00:00:00", "0", "", "160", "2025-06-16 15:23:48", "2025-06-16 15:23:48"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 690}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.377623, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:690", "source": "app/Http/Controllers/ReceiptOrderController.php:690", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=690", "ajax": false, "filename": "ReceiptOrderController.php", "line": "690"}, "connection": "ty", "start_percent": 84.5, "width_percent": 3.221}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = '5' limit 1", "type": "query", "params": [], "bindings": ["8", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 478}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 698}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.386755, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:478", "source": "app/Http/Controllers/ReceiptOrderController.php:478", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=478", "ajax": false, "filename": "ReceiptOrderController.php", "line": "478"}, "connection": "ty", "start_percent": 87.721, "width_percent": 2.64}, {"sql": "update `warehouse_products` set `quantity` = 30, `warehouse_products`.`updated_at` = '2025-06-16 15:23:48' where `id` = 4", "type": "query", "params": [], "bindings": ["30", "2025-06-16 15:23:48", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 494}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 698}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.393714, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:494", "source": "app/Http/Controllers/ReceiptOrderController.php:494", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=494", "ajax": false, "filename": "ReceiptOrderController.php", "line": "494"}, "connection": "ty", "start_percent": 90.361, "width_percent": 2.543}, {"sql": "select * from `product_expiry_dates` where (`product_id` = '5' and `warehouse_id` = '8') limit 1", "type": "query", "params": [], "bindings": ["5", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 716}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4020479, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:716", "source": "app/Http/Controllers/ReceiptOrderController.php:716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=716", "ajax": false, "filename": "ReceiptOrderController.php", "line": "716"}, "connection": "ty", "start_percent": 92.904, "width_percent": 3.851}, {"sql": "update `receipt_orders` set `total_amount` = 170, `receipt_orders`.`updated_at` = '2025-06-16 15:23:48' where `id` = 1", "type": "query", "params": [], "bindings": ["170", "2025-06-16 15:23:48", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 732}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.410799, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:732", "source": "app/Http/Controllers/ReceiptOrderController.php:732", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=732", "ajax": false, "filename": "ReceiptOrderController.php", "line": "732"}, "connection": "ty", "start_percent": 96.755, "width_percent": 3.245}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 734}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.434015, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:734", "source": "app/Http/Controllers/ReceiptOrderController.php:734", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=734", "ajax": false, "filename": "ReceiptOrderController.php", "line": "734"}, "connection": "ty", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\WarehouseProduct": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\ReceiptOrderProduct": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FReceiptOrderProduct.php&line=1", "ajax": false, "filename": "ReceiptOrderProduct.php", "line": "?"}}, "App\\Models\\ProductExpiryDate": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductExpiryDate.php&line=1", "ajax": false, "filename": "ProductExpiryDate.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ReceiptOrder": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FReceiptOrder.php&line=1", "ajax": false, "filename": "ReceiptOrder.php", "line": "?"}}}, "count": 11, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-250038808 data-indent-pad=\"  \"><span class=sf-dump-note>manage warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-250038808\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.180342, "xdebug_link": null}]}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order/1/edit\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "تم تحديث أمر الاستلام بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/receipt-order/1", "status_code": "<pre class=sf-dump id=sf-dump-356066446 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-356066446\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-161301037 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-161301037\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-799859339 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>vendor_id</span>\" => \"<span class=sf-dump-str>4</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>invoice_number</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8475</span>\"\n  \"<span class=sf-dump-key>invoice_total</span>\" => \"<span class=sf-dump-str title=\"6 characters\">900.00</span>\"\n  \"<span class=sf-dump-key>invoice_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-08</span>\"\n  \"<span class=sf-dump-key>notes</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>products</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>unit_cost</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1.00</span>\"\n      \"<span class=sf-dump-key>expiry_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-18</span>\"\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n      \"<span class=sf-dump-key>unit_cost</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8.00</span>\"\n      \"<span class=sf-dump-key>expiry_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-24</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-799859339\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1716974905 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">455</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087422981%7C11%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ino1NDNFL2Q4Qit6bmRxbTZnc0NuNGc9PSIsInZhbHVlIjoibXFtd3Z1dk5TSnVDR0lKZ1JkRTM2S2xmR2NLZmNhdlVXejh3S1ErVTN1OGJDRlVPUGNDZURhTlZhNWkxUG55YWEyVFNobEx4c1Ntd2dMVzhIM0c2YjBQcmd5TERYRWlBU2NSLzRNU3ZNT2VEMlA4Z1ZnUFNQRXhNdi8yNDNlZ3VtMytPT0FLUVFlamNRU3BDNDZJR3QyMEpESzFWL0txZG9YaHgrZGhmc2FUendSRXBIM3FPNHdOalkxZmFGUi9kUHMxT0NPa2xwZ3FtelE1bnl5ZmpwTmc1ZkZFTjFVVUgyRCtxRmlXZGJCeExyaUZxd3lFQ0tWYUw2TnhwcWtUd1BiMFRnMlI0czNRdTJGa0RpOUZWZnNyVEdKR2ZLSzlLUFJsSHM2YkdhY0grYVZJeGRTYkdwS2svZlMvOFFVZUdUb0IrUFJLWkF4M0xqSnpsUDlHYkJwODgrZWFoQUU2ZlhBT0lMai9SZy9RVHl5YUxVTkc3VWdqTlE5SE82Sk9VOW5kK1BPdk1vL1JBRHFRRTF6a20rYlM2dmNhVkRXYTV2NTVFNmxTS0Q5Z3NCWXA3ZUU3d1BnYy9JNE9uYXo0L1VydjVacmE0WDBLVzlYbXlHUVZuUmtSNkRoQ1g4bHI2V3dCeUtJRmc1M1diOU1pTytjU2lWOVVkakZpTXNlb0UiLCJtYWMiOiIyNGNkMTE2OGNkNWIyZDlkNDJkMWFhM2UwMzUzYzM5YjFiZmU0OTg0MWQ5Yzc5MDI4MDhjNThjYjk2Y2MwNTZlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkdOd2JxS2gyMldjRC9RMkt1bGFlc2c9PSIsInZhbHVlIjoiNVZ6R2lUZDBERFA2eVRrOUFIeUZYMG45bDV5Q1FxMEZTV1BnKy9UNGJsZFNFMk1JWm1hVTM0N3FsU0NpaytQL0xMRlpyMzRRT0VKbFVMamFQOVZsVHUrSHk4cFJMV2I5RUp2dHN4c0dZSkVEci8rN3dNQ3ZlUGNwRjZPa0EwUTdkUnZqdHdPTXNZTmhDR2g4S3QzbzM2UWw1NUtobHpQZFFRSytPSkJnanpVVFR4bVlYRGkraExhTStOeHc4SmF3akwwS2RqZzNqR0VOdFJhdTdwK3ZLcFVOUUNFWjBtbDdER3N4YnQ3MXE3Mk5qbWM0ejdnTjMrMHJ4aTN3cFYvVVNMYnRTbzFQdCt0UzFreU9UTDc0TGdIWVlGSGZWanpSUG1lS1RFbHF4TTRWUG15Qm9Ma3pvQmhnY0hmUVhLT1NpTEF3OURFblo4NE4xZVE5YlRJYUZYV3lpK3Zoc1JvQ3BqSnB6OVgzYStHQXJpaHB5S1NkdU9nVWJNbXhvUFBiRDV3L0lXbldsRms5R21hRnd1WERZZ3NtUkR2clhwTCtTUEVVcURLRzcrakZWSkNWR1ZtRlVCdUcxdEhaTkdrdVBLbVVjTWpqVHRBYmtTMGFpSHV4MVphMGZyN0pwZ0oyaFZrODFKT0RXbDZxamloMGF2dU9NS3FNU1lpQ2ROZGwiLCJtYWMiOiIzMzMzMzQ4MDhjMWRhNGVlZGUwNDIzOWYyYzc3Y2Y1ZmZlOTdkZjUyZjZkNjRlZGIxZjczMmZiNzY2MDAyYTM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1716974905\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-530681986 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzcvmOKIgVMCCvEHD9HLB5trqF9yJ9GcIs8Pndo9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-530681986\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-291017543 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:23:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost/receipt-order/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNnWjJqK2FhMWNRSDVJRDdlNU5SZEE9PSIsInZhbHVlIjoia2lHSUgyZzVnWjZGZWI2ZWFUbFRpek01bXkyQ2J0VXc3RUk2QVg2N3lYR3M2VktzSHRxNThLbnNsbXc5aGZ1WTNESGJxSHJqUHA2ckZ3MGEyeTJvVHBGK3Rpc2FaNGNGemFIdUc0T0FXT3Z0L2lBNmlQNFkzVlBvcXhMRzk2SEt4bFJrOUFkUDRib1FXQ3R4WGpDNGtlYWhRRWgranBraGUzMDgvZnFQdEFjaW9Kc3NwWDVtaCtEZm93OG1jSHMwN3ZIWStlK25TVkhhUUczUjcwS2hyMS9kN1RXSmNoYks4UWR4QTVvdE9iTlEra1NUYXdENlJ4WGtSakRvMXJLdWZYKzI2WnlNVW1PWlVFcUNMTWl1UkNJMmJaVWRzQ0JyZWoxQ2g2S2dGdlNjU2hZS2E4M3Q1RFV4MDhOUWtXamZSTWlNVkhTSVBDOUhMaGpxV1gwNFJ1TGJYaDJWQk1SWExxb2xiWW5RTjBnRVdGb2ZxSFBSK3VaL1BlZWd3NllqbjNqK2UzRVQ4RVlDZWVsbmtGTFBkQmFibTNsSnhHSnRXWDZhQjVqR3VZS2xiVnJZNEFNR2F0VWtZVlhpT0dQU2k1RXF4Y1AzTFZ5d2lwSWx3bmQrc3JISkdrck9Kc2RiZ0VVQXlPRkNwNUlXZndqM0lOSTNjQk1iZFg1eWtaSUEiLCJtYWMiOiIzN2RmODNmYWY3MDY0ODk3ZDg0OWJlYzQ4ZGYyYmI1YTY2MTY1NzVkZWFiM2YxNGUxMTQzOTBkNjVhMjk5MmU0IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjBBVVplQW5WSFM0OUJkQWw5R0VxVmc9PSIsInZhbHVlIjoid0pOWmVZTHorVUtXdlhkcllqaGdBUWw0OTBEeVBLcU1QeG85dW44Nk1wWlJLdVo5N1FiTUEzVW8zTGROT0pqVml4YUt6SEJIUGZHS3lKYnhUUTVFOWlyeUtGbVZ6cjJnU0xwcS9uSjBxUElSYzhhWWpNNzNxbm9rZkdWMVNTN3AzZG05VUtLc3lVVlExMmtMMnMyajRkQVNzc3JNZGJ5NU50dWpiN3dYNU5JTFZGVmU2dG9PRlFKdFVyMnFWQnZiZ3JkdUNvWlg1R0NMRTRqckNjQVFDc09XQUJoNGJZVWliMHNOUHFqQ3dyaVJrSHV6MmRhRExwNk1Mai9IZ0VhZGRLZURITmpIS09iMmVXYTVSSjVGQmZsbmx2cDJwUDAxUW0zbk12TDk1RStYRHpVc3hrMWp4cGhRdWx6OE41RlFROS9QTlFERGk4Y08vOTdPL3dsem96bC9qWHM0YkJLcjFCTUNFTjNJOURYdE9CSVp0S01wakdNR0pxeUhrK2NsVVAxNHlWUXVkVVo0QUJuczlzbTJFMm5sUGxoVXhLRDB2Q3VYMWh1WENXOGJianJ5bWpsTDlEeUFvSC9QZGEzM3Jrc3ZXQVdoNTVidGkzUHNtYXpjM0J0MWJHNitQbDlVTm01dENBVmR4QnhGa1FWSTIvQ2hhaldzZkpURWJpNmkiLCJtYWMiOiJiZTk1OWRmOGQyY2MyYzE0ZmU0ZTM0NzVmNjQ2NjFhMWRhNmI5NDg3MTg3ZDNlZmYxYzFlOTI2OTY4Y2E3NjUyIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNnWjJqK2FhMWNRSDVJRDdlNU5SZEE9PSIsInZhbHVlIjoia2lHSUgyZzVnWjZGZWI2ZWFUbFRpek01bXkyQ2J0VXc3RUk2QVg2N3lYR3M2VktzSHRxNThLbnNsbXc5aGZ1WTNESGJxSHJqUHA2ckZ3MGEyeTJvVHBGK3Rpc2FaNGNGemFIdUc0T0FXT3Z0L2lBNmlQNFkzVlBvcXhMRzk2SEt4bFJrOUFkUDRib1FXQ3R4WGpDNGtlYWhRRWgranBraGUzMDgvZnFQdEFjaW9Kc3NwWDVtaCtEZm93OG1jSHMwN3ZIWStlK25TVkhhUUczUjcwS2hyMS9kN1RXSmNoYks4UWR4QTVvdE9iTlEra1NUYXdENlJ4WGtSakRvMXJLdWZYKzI2WnlNVW1PWlVFcUNMTWl1UkNJMmJaVWRzQ0JyZWoxQ2g2S2dGdlNjU2hZS2E4M3Q1RFV4MDhOUWtXamZSTWlNVkhTSVBDOUhMaGpxV1gwNFJ1TGJYaDJWQk1SWExxb2xiWW5RTjBnRVdGb2ZxSFBSK3VaL1BlZWd3NllqbjNqK2UzRVQ4RVlDZWVsbmtGTFBkQmFibTNsSnhHSnRXWDZhQjVqR3VZS2xiVnJZNEFNR2F0VWtZVlhpT0dQU2k1RXF4Y1AzTFZ5d2lwSWx3bmQrc3JISkdrck9Kc2RiZ0VVQXlPRkNwNUlXZndqM0lOSTNjQk1iZFg1eWtaSUEiLCJtYWMiOiIzN2RmODNmYWY3MDY0ODk3ZDg0OWJlYzQ4ZGYyYmI1YTY2MTY1NzVkZWFiM2YxNGUxMTQzOTBkNjVhMjk5MmU0IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjBBVVplQW5WSFM0OUJkQWw5R0VxVmc9PSIsInZhbHVlIjoid0pOWmVZTHorVUtXdlhkcllqaGdBUWw0OTBEeVBLcU1QeG85dW44Nk1wWlJLdVo5N1FiTUEzVW8zTGROT0pqVml4YUt6SEJIUGZHS3lKYnhUUTVFOWlyeUtGbVZ6cjJnU0xwcS9uSjBxUElSYzhhWWpNNzNxbm9rZkdWMVNTN3AzZG05VUtLc3lVVlExMmtMMnMyajRkQVNzc3JNZGJ5NU50dWpiN3dYNU5JTFZGVmU2dG9PRlFKdFVyMnFWQnZiZ3JkdUNvWlg1R0NMRTRqckNjQVFDc09XQUJoNGJZVWliMHNOUHFqQ3dyaVJrSHV6MmRhRExwNk1Mai9IZ0VhZGRLZURITmpIS09iMmVXYTVSSjVGQmZsbmx2cDJwUDAxUW0zbk12TDk1RStYRHpVc3hrMWp4cGhRdWx6OE41RlFROS9QTlFERGk4Y08vOTdPL3dsem96bC9qWHM0YkJLcjFCTUNFTjNJOURYdE9CSVp0S01wakdNR0pxeUhrK2NsVVAxNHlWUXVkVVo0QUJuczlzbTJFMm5sUGxoVXhLRDB2Q3VYMWh1WENXOGJianJ5bWpsTDlEeUFvSC9QZGEzM3Jrc3ZXQVdoNTVidGkzUHNtYXpjM0J0MWJHNitQbDlVTm01dENBVmR4QnhGa1FWSTIvQ2hhaldzZkpURWJpNmkiLCJtYWMiOiJiZTk1OWRmOGQyY2MyYzE0ZmU0ZTM0NzVmNjQ2NjFhMWRhNmI5NDg3MTg3ZDNlZmYxYzFlOTI2OTY4Y2E3NjUyIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-291017543\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-989022672 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"27 characters\">&#1578;&#1605; &#1578;&#1581;&#1583;&#1610;&#1579; &#1571;&#1605;&#1585; &#1575;&#1604;&#1575;&#1587;&#1578;&#1604;&#1575;&#1605; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-989022672\", {\"maxDepth\":0})</script>\n"}}