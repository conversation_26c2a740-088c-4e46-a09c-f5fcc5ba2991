{"__meta": {"id": "Xa58d9b8e00816dc275f545ddc2caf7c2", "datetime": "2025-06-17 05:40:34", "utime": **********.24633, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.931725, "end": **********.246367, "duration": 1.****************, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": **********.931725, "relative_start": 0, "end": **********.036971, "relative_end": **********.036971, "duration": 1.****************, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.036997, "relative_start": 1.****************, "end": **********.24637, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "209ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02167, "accumulated_duration_str": "21.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.132427, "duration": 0.01823, "duration_str": "18.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.126}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.176566, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.126, "width_percent": 5.999}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.223305, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 90.125, "width_percent": 9.875}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750138821284%7C4%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkZweVVqbDhYSk4renNqeTBLbnJUYmc9PSIsInZhbHVlIjoiWVNxT1RxQUxwZ0lYR3V1RmpBRGZYWmdKanVzYjFUODVlSFVuY2NBV3FHVXJsQVFpQVpqUCs4RjVjRE1OV1NjTmVSMENxVStEZVZPK0dNVkFET3dkekN6UU9CRzdKbzlxdStmVDFvZGxDT08yd2ZGd3pxUFpIUUZsdjREWHNlUDJMTmtZQVRWMUtBRkttTmJpemZBbWY3aWxwN3J1UVp3enBjNGNxY1dsMTRxbDNFVHBOUFAxalFPVnZYOVhIcnVOQ3J2SjRYL2JnSUpUV2p1c0t4eTJZK3o4Z0hVVnVPM2N0ZTlpSGZzMmZxN0JzeDlvWG5OV0M1d0k1T1FLNy9BZ0ZTMlJPaWc0ZUFYNjlrY3d0cnM5d3lCK1V4bDhMZFhEcVRwQkQ5RUY2NEI5OFEzT3ZQaE5jTGRlVm1zT1FGWG0vWlJMTFlkd1RQMjVkajRBWURPbFN1TStPSGIyMVhSZmg1Y3dQR293c2o3bzQrQkJvWVBoeTY5OGtVWm9GTGRDVTdpNDdEUDkySnRQYWdiYmU2ZGhUTWdVd3VvQUJUeW5NK3VFbXEvbkVsWmNPSmVNM1l5MGgzeGlLOWJBRW5XcE1Ma2VsVUtjSzltUjJ3dlptZUYydWc3VW1EUTRjVXArSXhuR0VsL2tnS05UUStaQU5jalJwY3NvekJRWkZFcnMiLCJtYWMiOiI4MWRlNjI1YmI3ODEzZGJjNzUxYmFmODdkZjkwZmJhMTIxNWQ0ZGIzNTUzNTJiYmNjMTc5ZTExNWQxYTgzYzQ2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjI0ZzRZdnFKU1Z3YU1zSzU5K3E2b2c9PSIsInZhbHVlIjoiTjBLbmJzbUh0NkxVcUtxWWJvbisxWERSSytjMmhBb3phV21ncU5uZGdXUHN0dlJMMlFtU0hJUjZOWDZWeXBKMTVlWmV3MHRBR1kxT0wwT0sxQlpJdXk2dXFJQ29rdWV0blNBQWN6YUsrYyt6ZU9CekV2aHdaT3VHdmZwL3FNelF5UzdISDJEUzMrdTVTS3lmK0JuT0l1MXdock54c1crWk13UzZ1R204eXBHWWtha2RRUC84dlB3RkNiSlVhOGQyVUlLYnNoL05WQjZ2YlNidjNPMFhFR1RxMTdUNTV0QnUxclJhMzVpbi9nZG53TjhZa3BMYVE4S3RjbEo0OU1VelgwclliZWhGaEtxYXBmYXVXaS9xRGdwSXFJRTZiOXhYcndVTEt0VU9UQ09tQ1BCQytwWE1YVHcveXV0cnVGcUJ1cjk2YnFqUFY4MXV6TWpRbXlhZ0hKdmcxTDQ1UVB5dm00eUd2ZEJXdmJTM1RyT2tIZTVmTzBjZ3BldnRyK295OGVQTXNzNkJHcmJ2aG1COHd1YkE2bGN6Q1Ywcm84VjJoUnE4L1hyRlhGbGNUa09ya3NBTkhLdVJhZHpvdUt3UUpoVXdNTHJpdUxCRXdDYVUrQ1A0RFVPY1E3RTV1QzdOOEc2V2ZybVBlelV5YWZWYU1obkdmNDJ0TnVkREJEVHIiLCJtYWMiOiI4MjU5NTJjNmVkMzc3NTFjMDI5MjI4ZDc2NzIyYzJkYTU5ZTc2MDQwNjMxMGFjMzFmZDI3MGNmZTY0MjZiMThlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-544973465 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MsEpVpyAabX9l6kTbr1IUggTy4SSXp1me81GakZC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-544973465\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1156210120 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:40:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9CUVpFMGR4djRadEx0VU9Qc04xU3c9PSIsInZhbHVlIjoiSjc1WHlKSFRqN2x0UlZzSmdCaEtUU3VkeG9KOUh0K2ZkRWpxTnlISEtwMTJNTjZyRUc4Vml6empQamkyUC9uVkZQc1RZNXU5TnFJQVE1OE5GMnR1UytXQTVjVzl6S1FuUEU2VzlkY1JWTTlOK3B5M2lvMytuM1E0WHI0dFlYblNrdlROY1lLUUdMcCtPS2diS0dHUkJIOUVjSWJ4Zmp3elgwelAyNk1iMkZQQlZDYnAvYTlLRGtXS1Z0Z2duaHdEWWpGRExRWTFHNS91eHhSMlZRWDA2elVJcU1wMVR4UFA0cVRpbDgwSndKRHNMMGdYNU5vOFh5Z1RBZ0ZYOTNlZjc2QUlFQmw5aVJ2RUQyQUFKa1AvR0xSRURkVTZpQTB2T2hKSVJYUzZQejRKR2g4dThmK2NlSTVLMENwU3V0aDlIR1g4YkM5WDFMM0NzQVdrU1p0d3B0b2QraUUvVXdmWjAzdm1MYndMd1BZVWh3VVVvQ3FZaDhxMkRNSjhxcS9maTRnZFBPNE9iQ0phWnBXdkRHT2NFb081d2kxY2M5ZzEzZXZpN212ckxnMk04RFFFWENkN0ZJa25nMlliQzhKTVRNcFhUOGY1NGZXL2JlS1VydHJ3TnRDZGNDMUhTVjVZR3FBYlBQbllqeE5nRVJYNTZmeWVlZklSaitmdWVjUVkiLCJtYWMiOiJhMWM3ZWY1YjEwNmEwM2NhZWQyM2VhMjQ4YzllYzRkZTg2MGUyYWVhYTcwM2MzNTI2Njk0YWUxODQyMGQ5MmNiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjJBeldzeENMOWZMSXpwT3NUSkRSaGc9PSIsInZhbHVlIjoiUFhsZEVvaTVUNXZQc3hWb3ZLZ2FYcGtUL3JzZjhVQ2pnTis4dXN0aXhTMFJqeEZzeTFBVGFQYVB6VHpTcTV0SnBTcXYxaEdDYlZ5WTlrRTF4Qjc0WWt3UkJvUUNQSXpkalZjWnJCbWtia29jUVRiNFRZd3N3ZmM0Z1gwNUc2LyszWEx6Ync4NENiSCtaZDZETFI1VkZyS20yTVVPdXVBQVovdG1DNlY5dVFwNHd6cU1aKzAxYlJyQTBMSE1YYm0wL0MxQXB6VER0RWtLejZVVFF0QUpieHN3MW52QnEvalBFOFdiOU5lYU00TUw5dWxPcXBGc0NVTnVDZE5qekRFcFYrZjVIMjMyOThCUGhoNnBkcGszL0FsdHlIS1lxQUtxSnp3ZE4rQWF6RnR3QWE2SU1zbmVVUTMyVDBlODhBbmlYZ3RhNTR3K2hEdjJ5enQyc0NzRzdVem1oTDNnNCs4ek90L2IwTDMzM00xNWVocFpmZU9rcFJva0w4b0VmM1RYWnB2SGhJa3pWaWFnWFE4enBaRkd6U05MOHFpQkx2RkRmOU9SS2ZWeVg4dU1KczhNemZid3JBSGZCdW5mQ3d5ZU8vdzZJMCtnYURDYU1OTi9oaDhXdE9wTFNBS000eTlORmg4RlMvUTRPVklQbmVEV21XNFZ1Wk9qUHRNNmR3SUEiLCJtYWMiOiJjMGFkMTk3MmE0NzQwYzQwYjQ3ZWI5MzJhN2EyNTkxMzA3MTJiM2VlYzY2YzkwODYwN2ZkNDJmYzE3ZjhlZGEzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9CUVpFMGR4djRadEx0VU9Qc04xU3c9PSIsInZhbHVlIjoiSjc1WHlKSFRqN2x0UlZzSmdCaEtUU3VkeG9KOUh0K2ZkRWpxTnlISEtwMTJNTjZyRUc4Vml6empQamkyUC9uVkZQc1RZNXU5TnFJQVE1OE5GMnR1UytXQTVjVzl6S1FuUEU2VzlkY1JWTTlOK3B5M2lvMytuM1E0WHI0dFlYblNrdlROY1lLUUdMcCtPS2diS0dHUkJIOUVjSWJ4Zmp3elgwelAyNk1iMkZQQlZDYnAvYTlLRGtXS1Z0Z2duaHdEWWpGRExRWTFHNS91eHhSMlZRWDA2elVJcU1wMVR4UFA0cVRpbDgwSndKRHNMMGdYNU5vOFh5Z1RBZ0ZYOTNlZjc2QUlFQmw5aVJ2RUQyQUFKa1AvR0xSRURkVTZpQTB2T2hKSVJYUzZQejRKR2g4dThmK2NlSTVLMENwU3V0aDlIR1g4YkM5WDFMM0NzQVdrU1p0d3B0b2QraUUvVXdmWjAzdm1MYndMd1BZVWh3VVVvQ3FZaDhxMkRNSjhxcS9maTRnZFBPNE9iQ0phWnBXdkRHT2NFb081d2kxY2M5ZzEzZXZpN212ckxnMk04RFFFWENkN0ZJa25nMlliQzhKTVRNcFhUOGY1NGZXL2JlS1VydHJ3TnRDZGNDMUhTVjVZR3FBYlBQbllqeE5nRVJYNTZmeWVlZklSaitmdWVjUVkiLCJtYWMiOiJhMWM3ZWY1YjEwNmEwM2NhZWQyM2VhMjQ4YzllYzRkZTg2MGUyYWVhYTcwM2MzNTI2Njk0YWUxODQyMGQ5MmNiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjJBeldzeENMOWZMSXpwT3NUSkRSaGc9PSIsInZhbHVlIjoiUFhsZEVvaTVUNXZQc3hWb3ZLZ2FYcGtUL3JzZjhVQ2pnTis4dXN0aXhTMFJqeEZzeTFBVGFQYVB6VHpTcTV0SnBTcXYxaEdDYlZ5WTlrRTF4Qjc0WWt3UkJvUUNQSXpkalZjWnJCbWtia29jUVRiNFRZd3N3ZmM0Z1gwNUc2LyszWEx6Ync4NENiSCtaZDZETFI1VkZyS20yTVVPdXVBQVovdG1DNlY5dVFwNHd6cU1aKzAxYlJyQTBMSE1YYm0wL0MxQXB6VER0RWtLejZVVFF0QUpieHN3MW52QnEvalBFOFdiOU5lYU00TUw5dWxPcXBGc0NVTnVDZE5qekRFcFYrZjVIMjMyOThCUGhoNnBkcGszL0FsdHlIS1lxQUtxSnp3ZE4rQWF6RnR3QWE2SU1zbmVVUTMyVDBlODhBbmlYZ3RhNTR3K2hEdjJ5enQyc0NzRzdVem1oTDNnNCs4ek90L2IwTDMzM00xNWVocFpmZU9rcFJva0w4b0VmM1RYWnB2SGhJa3pWaWFnWFE4enBaRkd6U05MOHFpQkx2RkRmOU9SS2ZWeVg4dU1KczhNemZid3JBSGZCdW5mQ3d5ZU8vdzZJMCtnYURDYU1OTi9oaDhXdE9wTFNBS000eTlORmg4RlMvUTRPVklQbmVEV21XNFZ1Wk9qUHRNNmR3SUEiLCJtYWMiOiJjMGFkMTk3MmE0NzQwYzQwYjQ3ZWI5MzJhN2EyNTkxMzA3MTJiM2VlYzY2YzkwODYwN2ZkNDJmYzE3ZjhlZGEzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1156210120\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}