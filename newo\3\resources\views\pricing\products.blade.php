@extends('layouts.admin')

@section('page-title')
    {{ __('التسعير') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('التسعير') }}</li>
@endsection

@section('content')
    <!-- جدول التسعير -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('التسعير - أسعار البيع والشراء') }}</h5>
                    <small class="text-muted">{{ __('يمكنك النقر على أسعار البيع والشراء لتعديلها مباشرة') }}</small>
                    <div class="mt-2">
                        <button type="button" class="btn btn-sm btn-info" onclick="testEditableFunction()">
                            اختبار التعديل المباشر
                        </button>
                    </div>
                </div>
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table" id="pricing-products-table">
                            <thead>
                                <tr>
                                    <th>{{ __('اسم المنتج') }}</th>
                                    <th>{{ __('الباركود') }}</th>
                                    <th>{{ __('سعر البيع') }}</th>
                                    <th>{{ __('سعر الشراء') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($products as $product)
                                    <tr data-product-id="{{ $product->id }}">
                                        <!-- اسم المنتج -->
                                        <td>{{ $product->name }}</td>
                                        
                                        <!-- الباركود -->
                                        <td>{{ $product->sku }}</td>
                                        
                                        <!-- سعر البيع - قابل للتعديل -->
                                        <td class="editable" data-field="sale_price" data-type="number" title="اضغط للتعديل">
                                            {{ Auth::user()->priceFormat($product->sale_price) }}
                                        </td>

                                        <!-- سعر الشراء - قابل للتعديل -->
                                        <td class="editable" data-field="purchase_price" data-type="number" title="اضغط للتعديل">
                                            {{ Auth::user()->priceFormat($product->purchase_price) }}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<style>
.editable {
    cursor: pointer !important;
    background-color: #f8f9fa !important;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    padding: 8px !important;
    min-height: 40px;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.editable:hover {
    background-color: #e3f2fd !important;
    border: 1px solid #2196f3;
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.2);
}

.table td.editable {
    position: relative;
}

.table td.editable::after {
    content: "✏️ اضغط للتعديل";
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
    color: #666;
    opacity: 0;
    transition: opacity 0.3s;
    background: rgba(255,255,255,0.9);
    padding: 2px 4px;
    border-radius: 3px;
    white-space: nowrap;
}

.table td.editable:hover::after {
    opacity: 1;
}

.table-success {
    background-color: #d4edda !important;
    border-color: #c3e6cb !important;
}

.table-danger {
    background-color: #f8d7da !important;
    border-color: #f5c6cb !important;
}
</style>

<script>
console.log('Loading pricing products page...');

$(document).ready(function() {
    console.log('Document ready - pricing products');

    // التحقق من وجود jQuery
    if (typeof $ === 'undefined') {
        console.error('jQuery is not loaded!');
        return;
    }

    console.log('jQuery loaded, setting up table...');

    // بدلاً من DataTable، سنستخدم جدول عادي أولاً للاختبار
    console.log('Table setup complete');
    console.log('Editable cells count:', $('.editable').length);

    // إضافة event listener إضافي للتأكد
    setTimeout(function() {
        console.log('Adding additional event listeners...');
        $('.editable').each(function() {
            $(this).css('cursor', 'pointer');
            console.log('Cell:', $(this).text(), 'Field:', $(this).data('field'));
        });
        console.log('Event listeners added to', $('.editable').length, 'cells');
    }, 500);

    // التعديل المباشر للخلايا - استخدام event delegation
    $(document).on('click', '.editable', function(e) {
        console.log('Editable cell clicked!');
        e.preventDefault();
        e.stopPropagation();

        var $this = $(this);

        // تجنب التعديل المتعدد
        if ($this.find('input').length > 0) {
            console.log('Input already exists, skipping...');
            return;
        }

        var field = $this.data('field');
        var type = $this.data('type');
        var productId = $this.closest('tr').data('product-id');
        var currentValue = $this.text().trim();

        console.log('Cell data:', {
            field: field,
            type: type,
            productId: productId,
            currentValue: currentValue
        });

        // التحقق من وجود البيانات المطلوبة
        if (!field || !productId) {
            console.error('Missing required data:', {field: field, productId: productId});
            alert('خطأ: بيانات المنتج غير مكتملة');
            return;
        }

        // حفظ القيمة الأصلية
        $this.data('original-value', currentValue);

        // إزالة تنسيق العملة للحصول على القيمة الرقمية
        var numericValue = currentValue.replace(/[^\d.-]/g, '');

        console.log('Editing field:', field, 'Product ID:', productId, 'Current value:', currentValue, 'Numeric:', numericValue);

        // إنشاء input للتعديل
        var input;
        if (type === 'number') {
            input = $('<input type="number" class="form-control form-control-sm" step="0.01" min="0" style="width: 100%; border: 2px solid #007bff; padding: 5px;">');
            input.val(numericValue);
        } else {
            input = $('<input type="text" class="form-control form-control-sm" style="width: 100%; border: 2px solid #007bff; padding: 5px;">');
            input.val(currentValue);
        }

        // استبدال النص بـ input
        $this.html(input);

        // التركيز على الـ input مع تأخير بسيط
        setTimeout(function() {
            input.focus().select();
        }, 100);
        
        // حفظ التغييرات عند الضغط على Enter أو فقدان التركيز
        input.on('blur keydown', function(e) {
            if (e.type === 'blur' || e.which === 13) {
                e.preventDefault();
                var newValue = $(this).val().trim();

                console.log('Saving value:', newValue, 'Original numeric:', numericValue);

                // التحقق من صحة القيمة
                if (type === 'number' && newValue !== '' && (isNaN(newValue) || parseFloat(newValue) < 0)) {
                    if (typeof show_toastr === 'function') {
                        show_toastr('Error', 'يرجى إدخال قيمة رقمية صحيحة', 'error');
                    } else {
                        alert('يرجى إدخال قيمة رقمية صحيحة');
                    }
                    $this.text($this.data('original-value'));
                    return;
                }

                // التحقق من وجود تغيير
                if (newValue !== numericValue && newValue !== currentValue) {
                    // إرسال التحديث
                    updateField(productId, field, newValue, $this);
                } else {
                    // إرجاع القيمة الأصلية
                    $this.text($this.data('original-value'));
                }
            } else if (e.which === 27) { // Escape key
                e.preventDefault();
                $this.text($this.data('original-value'));
            }
        });
    });
});

// دالة اختبار التعديل المباشر
function testEditableFunction() {
    console.log('Testing editable function...');
    var editableCells = $('.editable');
    console.log('Found', editableCells.length, 'editable cells');

    if (editableCells.length > 0) {
        var firstCell = editableCells.first();
        console.log('First cell data:', {
            text: firstCell.text(),
            field: firstCell.data('field'),
            type: firstCell.data('type'),
            productId: firstCell.closest('tr').data('product-id')
        });

        // محاكاة النقر
        firstCell.trigger('click');
    } else {
        alert('لم يتم العثور على خلايا قابلة للتعديل');
    }
}

// دالة تحديث الحقل - خارج نطاق document.ready
function updateField(productId, field, value, $element) {
        console.log('Updating:', productId, field, value);

        // إظهار مؤشر التحميل
        $element.html('<i class="fas fa-spinner fa-spin text-primary"></i>');

        $.ajax({
            url: '{{ route("pricing.update.inline") }}',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                id: productId,
                field: field,
                value: value
            },
            success: function(response) {
                console.log('Update response:', response);
                if (response.success) {
                    // عرض القيمة المحدثة مع تأثير بصري
                    $element.text(response.display_value);
                    $element.addClass('table-success');

                    setTimeout(function() {
                        $element.removeClass('table-success');
                    }, 2000);

                    // إظهار رسالة نجاح
                    if (typeof show_toastr === 'function') {
                        show_toastr('Success', response.message || 'تم التحديث بنجاح', 'success');
                    }
                } else {
                    // إرجاع القيمة الأصلية وإظهار رسالة خطأ
                    $element.text($element.data('original-value') || value);
                    $element.addClass('table-danger');

                    setTimeout(function() {
                        $element.removeClass('table-danger');
                    }, 2000);

                    if (typeof show_toastr === 'function') {
                        show_toastr('Error', response.message || 'فشل في التحديث', 'error');
                    }
                }
            },
            error: function(xhr) {
                console.error('Update error:', xhr);
                var errorMessage = 'حدث خطأ أثناء التحديث';

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                // إرجاع القيمة الأصلية وإظهار رسالة خطأ
                $element.text($element.data('original-value') || value);
                $element.addClass('table-danger');

                setTimeout(function() {
                    $element.removeClass('table-danger');
                }, 2000);

                if (typeof show_toastr === 'function') {
                    show_toastr('Error', errorMessage, 'error');
                } else {
                    alert(errorMessage);
                }
            }
        });
    }
});
</script>
@endpush
