@extends('layouts.admin')

@section('page-title')
    {{ __('التسعير') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('التسعير') }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <div class="d-inline-block">
            <span class="badge bg-info">CSRF: {{ substr(csrf_token(), 0, 10) }}...</span>
            <span class="badge bg-success">Route: {{ route('pricing.update.inline') }}</span>
        </div>
    </div>
@endsection

@section('content')
    <!-- جدول التسعير -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('التسعير - أسعار البيع والشراء') }}</h5>
                    <small class="text-muted">{{ __('يمكنك النقر على أسعار البيع والشراء لتعديلها مباشرة') }}</small>
                    <div class="mt-2">
                        <button type="button" class="btn btn-sm btn-primary" onclick="testClick()">
                            اختبار النقر على الخلية الأولى
                        </button>
                        <span class="badge bg-info ms-2">عدد الخلايا القابلة للتعديل: <span id="editable-count">0</span></span>
                    </div>
                </div>
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table" id="pricing-products-table">
                            <thead>
                                <tr>
                                    <th>{{ __('اسم المنتج') }}</th>
                                    <th>{{ __('الباركود') }}</th>
                                    <th>{{ __('سعر البيع') }}</th>
                                    <th>{{ __('سعر الشراء') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($products as $product)
                                    <tr data-product-id="{{ $product->id }}">
                                        <!-- اسم المنتج -->
                                        <td>{{ $product->name }}</td>
                                        
                                        <!-- الباركود -->
                                        <td>{{ $product->sku }}</td>
                                        
                                        <!-- سعر البيع - قابل للتعديل -->
                                        <td class="editable" data-field="sale_price" data-type="number" title="اضغط للتعديل">
                                            {{ Auth::user()->priceFormat($product->sale_price) }}
                                        </td>

                                        <!-- سعر الشراء - قابل للتعديل -->
                                        <td class="editable" data-field="purchase_price" data-type="number" title="اضغط للتعديل">
                                            {{ Auth::user()->priceFormat($product->purchase_price) }}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<style>
.editable {
    cursor: pointer !important;
    background-color: #f8f9fa !important;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    padding: 8px !important;
    min-height: 40px;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.editable:hover {
    background-color: #e3f2fd !important;
    border: 1px solid #2196f3;
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.2);
}

.table td.editable {
    position: relative;
}

.table td.editable::after {
    content: "✏️ اضغط للتعديل";
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
    color: #666;
    opacity: 0;
    transition: opacity 0.3s;
    background: rgba(255,255,255,0.9);
    padding: 2px 4px;
    border-radius: 3px;
    white-space: nowrap;
}

.table td.editable:hover::after {
    opacity: 1;
}

.table-success {
    background-color: #d4edda !important;
    border-color: #c3e6cb !important;
}

.table-danger {
    background-color: #f8d7da !important;
    border-color: #f5c6cb !important;
}
</style>

<script>
console.log('Loading pricing products page...');

// التأكد من تحميل jQuery
if (typeof jQuery === 'undefined') {
    console.error('jQuery is not loaded!');
    alert('خطأ: jQuery غير محمل!');
} else {
    console.log('jQuery loaded successfully, version:', jQuery.fn.jquery);
}

// إعداد CSRF token
jQuery.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': jQuery('meta[name="csrf-token"]').attr('content')
    }
});

jQuery(document).ready(function($) {
    console.log('Document ready - pricing products');
    console.log('jQuery version:', $.fn.jquery);
    console.log('Editable cells found:', $('.editable').length);

    // إضافة مؤشر بصري للخلايا القابلة للتعديل
    $('.editable').each(function() {
        $(this).css({
            'cursor': 'pointer',
            'position': 'relative'
        });
        console.log('Cell setup:', $(this).text().trim(), 'Field:', $(this).data('field'));
    });

    // تحديث عداد الخلايا
    $('#editable-count').text($('.editable').length);

    // التعديل المباشر للخلايا
    $(document).on('click', '.editable', function(e) {
        console.log('=== CELL CLICKED ===');
        e.preventDefault();
        e.stopPropagation();

        var $cell = $(this);

        // تجنب التعديل المتعدد
        if ($cell.find('input').length > 0) {
            console.log('Input already exists, skipping...');
            return;
        }

        // جمع البيانات
        var field = $cell.data('field');
        var type = $cell.data('type');
        var productId = $cell.closest('tr').data('product-id');
        var currentValue = $cell.text().trim();
        var numericValue = currentValue.replace(/[^\d.-]/g, '');

        console.log('Cell clicked data:', {
            field: field,
            type: type,
            productId: productId,
            currentValue: currentValue,
            numericValue: numericValue
        });

        // التحقق من البيانات المطلوبة
        if (!field || !productId) {
            console.error('Missing data!');
            alert('خطأ: بيانات غير مكتملة');
            return;
        }

        // حفظ القيمة الأصلية
        $cell.data('original-value', currentValue);

        // إنشاء input للتعديل
        var input = $('<input type="number" class="form-control" step="0.01" min="0" style="width: 100%; border: 2px solid #007bff; font-size: 14px; padding: 5px;">');
        input.val(numericValue);

        console.log('Creating input with value:', numericValue);

        // استبدال النص بـ input
        $cell.html(input);

        // التركيز على الـ input
        input.focus().select();
        
        // معالجة الأحداث
        input.on('blur keydown', function(e) {
            if (e.type === 'blur' || e.which === 13) { // Enter key
                var newValue = $(this).val().trim();
                console.log('Saving new value:', newValue);

                // التحقق من صحة القيمة
                if (newValue === '' || isNaN(newValue) || parseFloat(newValue) < 0) {
                    alert('يرجى إدخال قيمة رقمية صحيحة');
                    $cell.text($cell.data('original-value'));
                    return;
                }

                // التحقق من وجود تغيير
                if (newValue !== numericValue) {
                    console.log('Value changed, updating...');
                    updateField(productId, field, newValue, $cell);
                } else {
                    console.log('No change, restoring original value');
                    $cell.text($cell.data('original-value'));
                }
            } else if (e.which === 27) { // Escape key
                console.log('Escape pressed, canceling edit');
                $cell.text($cell.data('original-value'));
            }
        });
    });
});

// دالة اختبار التعديل المباشر
function testEditableFunction() {
    console.log('Testing editable function...');
    var editableCells = $('.editable');
    console.log('Found', editableCells.length, 'editable cells');

    if (editableCells.length > 0) {
        var firstCell = editableCells.first();
        console.log('First cell data:', {
            text: firstCell.text(),
            field: firstCell.data('field'),
            type: firstCell.data('type'),
            productId: firstCell.closest('tr').data('product-id')
        });

        // محاكاة النقر
        firstCell.trigger('click');
    } else {
        alert('لم يتم العثور على خلايا قابلة للتعديل');
    }
}

// دالة اختبار الاتصال
function testAjaxConnection() {
    console.log('Testing AJAX connection...');

    $.ajax({
        url: '{{ route("pricing.update.inline") }}',
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}',
            id: 999999, // ID غير موجود للاختبار
            field: 'sale_price',
            value: '100'
        },
        success: function(response) {
            console.log('Connection test - Success response:', response);
            alert('الاتصال يعمل! (متوقع: خطأ منتج غير موجود)');
        },
        error: function(xhr) {
            console.log('Connection test - Error response:', xhr);
            if (xhr.status === 404) {
                alert('الاتصال يعمل! (404 - منتج غير موجود كما متوقع)');
            } else {
                alert('خطأ في الاتصال: ' + xhr.status);
            }
        }
    });
}

// دالة اختبار التحديث المباشر
function testDirectUpdate() {
    console.log('Testing direct update...');
    var editableCells = $('.editable');

    if (editableCells.length > 0) {
        var firstCell = editableCells.first();
        var productId = firstCell.closest('tr').data('product-id');
        var field = firstCell.data('field');
        var currentValue = firstCell.text().trim();
        var numericValue = currentValue.replace(/[^\d.-]/g, '');
        var newValue = parseFloat(numericValue) + 0.01; // زيادة بسيطة للاختبار

        console.log('Testing update with:', {
            productId: productId,
            field: field,
            currentValue: currentValue,
            newValue: newValue
        });

        updateField(productId, field, newValue, firstCell);
    } else {
        alert('لم يتم العثور على خلايا قابلة للتعديل');
    }
}

// دالة تحديث الحقل
function updateField(productId, field, value, $element) {
    console.log('=== UPDATING FIELD ===');
    console.log('Product ID:', productId, 'Field:', field, 'Value:', value);

    // إظهار مؤشر التحميل
    $element.html('<span style="color: blue;">جاري الحفظ...</span>');

    jQuery.ajax({
        url: '{{ route("pricing.update.inline") }}',
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}',
            id: productId,
            field: field,
            value: value
        },
        success: function(response) {
            console.log('=== SUCCESS ===', response);
            if (response.success) {
                // عرض القيمة المحدثة
                $element.text(response.display_value);
                $element.css('background-color', '#d4edda');

                setTimeout(function() {
                    $element.css('background-color', '');
                }, 2000);

                alert('تم التحديث بنجاح!');
            } else {
                // إرجاع القيمة الأصلية
                $element.text($element.data('original-value'));
                alert('خطأ: ' + (response.message || 'فشل في التحديث'));
            }
        },
        error: function(xhr) {
            console.log('=== ERROR ===', xhr.status, xhr.responseText);

            var errorMessage = 'حدث خطأ أثناء التحديث';
            if (xhr.status === 403) {
                errorMessage = 'ليس لديك صلاحية للقيام بهذا الإجراء';
            } else if (xhr.status === 404) {
                errorMessage = 'الصفحة غير موجودة';
            } else if (xhr.status === 500) {
                errorMessage = 'خطأ في الخادم';
            }

            // إرجاع القيمة الأصلية
            $element.text($element.data('original-value'));
            $element.css('background-color', '#f8d7da');

            setTimeout(function() {
                $element.css('background-color', '');
            }, 2000);

            alert(errorMessage);
        }
        });
    });
}

// دالة اختبار بسيطة
function testClick() {
    console.log('Testing click...');
    var $firstCell = jQuery('.editable').first();
    if ($firstCell.length > 0) {
        console.log('Clicking first cell:', $firstCell.text());
        $firstCell.trigger('click');
    } else {
        alert('لا توجد خلايا قابلة للتعديل');
    }
}
</script>
@endpush
