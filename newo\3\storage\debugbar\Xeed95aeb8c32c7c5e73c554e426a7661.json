{"__meta": {"id": "Xeed95aeb8c32c7c5e73c554e426a7661", "datetime": "2025-06-17 06:22:10", "utime": **********.859023, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750141329.238302, "end": **********.859067, "duration": 1.620764970779419, "duration_str": "1.62s", "measures": [{"label": "Booting", "start": 1750141329.238302, "relative_start": 0, "end": **********.65912, "relative_end": **********.65912, "duration": 1.4208180904388428, "duration_str": "1.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.659147, "relative_start": 1.4208450317382812, "end": **********.859072, "relative_end": 5.0067901611328125e-06, "duration": 0.19992494583129883, "duration_str": "200ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45169128, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.028840000000000005, "accumulated_duration_str": "28.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7600021, "duration": 0.0261, "duration_str": "26.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.499}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.812759, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.499, "width_percent": 4.889}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8361552, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.388, "width_percent": 4.612}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-943871208 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-943871208\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-567873931 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-567873931\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2008931448 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2008931448\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1097275587 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750141322801%7C19%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhrbytTcjFhODVPU3R5REI3R3NUS1E9PSIsInZhbHVlIjoiUVlrZkhvamQ5dFV6ZFRBb2taOUhJSW1Ga1c2aEg5TEUvbUhsL0hnVEY1UHJtVzQycnVwMjhGbmZBZlhCRG5ZaGJucG5BY3Uzb1dLZ3BoSDJFOGxpVnFRU0Z3bmN0MXZwd1pTTEQveXFrWktxSUJ1VE5GWjFibzB5NXJtOFcydVF6Rzd6Z2x5ellLa3Z6Y29LU0Qzck44NnVRNm1oWjRNZmdrbGIyVkZxMDFCS3dLeTY3eXFHa3hQcjdtNDBVdk9VZVNQMUUvWlFLUGkxRmtqR3JNZHlGYUZWZWwybUdJblFCdHRDQ3V3WExPY2FKM1ZoRDRVeXRMMklCL2RmWlNaOVlTZEI3NDdQQUtaK2s5TEZmUGFIdjljclQ1c1NHWjhsUERLQ2U3SERyKzNNNVgwK2Y1cHNrSW1pcnBkWHU4Uk5TMG1vZmVRS0FIMFVVQkRXZlAwL09IU3dxeUxoMktid1pzT2xwb1VFaDhpclNuZ3AyV3kyTGF1YUhFeEx2dy9YcnA0V3haOWVLam0xVTlRZEdXNkxWZ09NVVRPeURaUVU4ZmZOVWNRUk95andhMHF5OVgxR1J1TW4rc1dwSitNRHlqcVVuK2lwOWNEWUZiNWVKUUsxZmdjYXA4ZzhZTmpVOHRGeDZ6YmdkMXFaR0luRlN2ZjU2QkVpbmtrNEhmR0siLCJtYWMiOiJmNzYwMDk1ZjU5NGU1MThjMTdlYjUzMDkxZDU5MGI2NDE5NTk4YTM0ZGIwYTIwNWRjNTQ5NzBkYzQ0YTUzYmMyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IitVc1pyUVNJR0NLZERmUGEyS2VFd3c9PSIsInZhbHVlIjoieEJId3dyRXhPM2o2ajRGdUNMWjNCYi9ZVUVpMXZ3M016RFhHMDU0TS9KV2M1M093UklRaEtpL2F1aDNybTF1NEV1OWlzM1JobTlpcnd3aUd2RzUzMVhGRFQ4UG9MV2dKdlljWmlTSXlUdjR3emUxWVRWYWJkMXAxNkwvaHpDU1dmazgzWXhQQURtMkdiMWhKQXJjL2ZnMGMwdzB1ZzE2eTNZUk5PanA5ZDZpTVRQL3JnMmJObWtueDFYZ0FUMEwxaDY1Njlzd2FPRUllbHMwWHNJOVRJZnFNYWh4d1BZcnVKMzNQNUJpSmRlU3dNekpheDE0ZzBIL21mREhNMmprdTF6OXdaVldLekNPRUNPQmx4RlV6SHdoRm95cklJM2lzOGxSRVlhRmE5anpLOWxBcWE5SHVOMG5BRE8xRWlMT1dOQmFLZkYrcXRTSzVGNDJralFUeFd2SDYxNTJGaHhaMThaWis5YjRnNkkvWTB5cDVNdlJIUnRydXRwQStaRlpqUFFvRVRSaWtrUUZvTHFTMUNXWW9TblhiSVJqeUE4ZVF4cWgvRXhxT2ZsQ2kzak1EZ01yVWxiczNRWk40K2ViTDZtNG04MnBKY1ZpMnB6cG9uYzI0SVNyMmlxVnErdnMwYXdOSFUzeUhyZzVlOHZyS0ZkRE01elBOVDhXa0xpMzciLCJtYWMiOiIwYmQ3ZDg4MDllZmMzOGRlN2I5MjI1YTNiNzlmNGViZjFiOTEwYmUwNzY1OGZlZGQ0YzQxOWQ2YWQ0NWJmYjA1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1097275587\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1937322147 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1937322147\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1715041281 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:22:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ind0ODRBSzdmcG1uK2grbTlDcURuMEE9PSIsInZhbHVlIjoiMWNzSE85SURnT2JVbnU4V3grMmFYbS9oMkk3b1JFUHlsaGR3eHoxL2Fma0hSVlJjNGtnTk9LVUZNT2JoNjUrWnBzU2NUcnk0V0NRbGpQYVdPWFg1b0tNNkJGR2I2MjJMNDVIdzhIU1hBTTcyQ1JVLzl1cWNydC9jWGNrUVFlZldDekovbGsvRzhvaTBZLy9sb1hlNGs1MHNlMWNLMlVMVzB5aU5Pb040R0h0d3FUSmtCRGRpL0R5UjNGUE9CVEN3Q21Dcy81SVA5R3NGSElHR1VsS2FaRGJFNGRXZzZSa2tzTEt6SUZFaW9TVEUvRDQyd0JYY25odFpzV0FURFlYdlpDaHgzM3JkUk9sTDZ6OTRCOENaekliODgvSEJYUzZrT3Boc0NYRnlabVdRNE1tcHhkOUZzVTYvUitLSVZRbDdISVR0TlBBU2dWVk1uSDk4M3JGWnZibGNJM2ora1AxWTVxUXVVTHArd3JpR3Z6ZFErVjhoWWkyRWpzSXlwdUlkSm9CREpVSExRSDNjcnpHR2o1bXFxY0VVcEh3Q21ROFJzNXNuTTAwcmRSbkxyTFpSSXB6SXU5L0NhN1lNR0JIcGhtd0srcE9nMnNCM2M3ZXpCaGRiU3BndGNwTzFVNExNSERmL3M0eDlOaEs1K2ovMUcrQ3VXK0gvMG1IR3VIVmEiLCJtYWMiOiJmN2YxNDgxZDQ4YTQxNTRmNzNiMzBlMzIxN2NiZDU0NmM4MmYwNDlmMGRmYjc4YzhlOTNkZGZlMzhjNGFiYjhjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:22:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkM2OFB0T2xldjVCclBoOVZKTDQxUVE9PSIsInZhbHVlIjoiM2Jyb3ZUcG9TRGxQL0RXZ2lYRzlaYzFVZlVOS3FvOVFOOUd6MHVmTlVZd1ZuWVNrc3dvR3NqU21OUnBKS3lsS1poNVBRUC9QblNIMDNGTjJ4MzJBdndiNk9VamFXRGxHY2hsdnhKYVk2V1Vpdmt5RW5paDEvZnh6dzZWL2U4YVgvWGc5WnpEeHlSUjJMMmNHQnB3dklobklkNHIxQTQ0Rm96TExPSUdhUTlmUVFrMWE0MkJCL3ZKWkZ0UzcvLzIwTUhGY01OVVhPZEowTEVNejVYN254bHNUUVNVeUw4NW1YcWFhWmFtMzlKaWY4TFQzb2VkWFJ4ZGVpRDNCRFA3aHZXRnBUR2MrWUhseDBmRENsU25td1lSOUZHdFpOV2hsNzhyM2RPVkFSeExLNWpvUGdGTVlUTjBwaDhjQTM3QW5mSUdNdU9DV3Z0S3UyRG84L01mdno1c0dMbEdlVWtzN1I5N0VUMUJZVUJHMEdPZHNBYVJwNU9sQlVvcHhiSzBJeTBrNmZNV2h5Vk5TNitCU3I1Qjd1R1d0MHQ1bW9tYzVrVFFNMzE5UnBYK3prSUVFN2UwN1M1M1dHU0VxWHlhQU5BR3FLOGRpU1IrdllPeXpGQnUrVWVwRDQzSlppTWtwVUpWQTAxSTNhYUltVjU5RzRPSkcxNE5nK0RvNGhnN1YiLCJtYWMiOiIxNjdhY2ExZTQ4OTI1OGYxMzNkNzg5MmFmNGUwYjhhYzAxM2VjN2MzNzM0NDhhOWQ4ZGJhNmYyZjM2Mzc4MDcxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:22:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ind0ODRBSzdmcG1uK2grbTlDcURuMEE9PSIsInZhbHVlIjoiMWNzSE85SURnT2JVbnU4V3grMmFYbS9oMkk3b1JFUHlsaGR3eHoxL2Fma0hSVlJjNGtnTk9LVUZNT2JoNjUrWnBzU2NUcnk0V0NRbGpQYVdPWFg1b0tNNkJGR2I2MjJMNDVIdzhIU1hBTTcyQ1JVLzl1cWNydC9jWGNrUVFlZldDekovbGsvRzhvaTBZLy9sb1hlNGs1MHNlMWNLMlVMVzB5aU5Pb040R0h0d3FUSmtCRGRpL0R5UjNGUE9CVEN3Q21Dcy81SVA5R3NGSElHR1VsS2FaRGJFNGRXZzZSa2tzTEt6SUZFaW9TVEUvRDQyd0JYY25odFpzV0FURFlYdlpDaHgzM3JkUk9sTDZ6OTRCOENaekliODgvSEJYUzZrT3Boc0NYRnlabVdRNE1tcHhkOUZzVTYvUitLSVZRbDdISVR0TlBBU2dWVk1uSDk4M3JGWnZibGNJM2ora1AxWTVxUXVVTHArd3JpR3Z6ZFErVjhoWWkyRWpzSXlwdUlkSm9CREpVSExRSDNjcnpHR2o1bXFxY0VVcEh3Q21ROFJzNXNuTTAwcmRSbkxyTFpSSXB6SXU5L0NhN1lNR0JIcGhtd0srcE9nMnNCM2M3ZXpCaGRiU3BndGNwTzFVNExNSERmL3M0eDlOaEs1K2ovMUcrQ3VXK0gvMG1IR3VIVmEiLCJtYWMiOiJmN2YxNDgxZDQ4YTQxNTRmNzNiMzBlMzIxN2NiZDU0NmM4MmYwNDlmMGRmYjc4YzhlOTNkZGZlMzhjNGFiYjhjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:22:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkM2OFB0T2xldjVCclBoOVZKTDQxUVE9PSIsInZhbHVlIjoiM2Jyb3ZUcG9TRGxQL0RXZ2lYRzlaYzFVZlVOS3FvOVFOOUd6MHVmTlVZd1ZuWVNrc3dvR3NqU21OUnBKS3lsS1poNVBRUC9QblNIMDNGTjJ4MzJBdndiNk9VamFXRGxHY2hsdnhKYVk2V1Vpdmt5RW5paDEvZnh6dzZWL2U4YVgvWGc5WnpEeHlSUjJMMmNHQnB3dklobklkNHIxQTQ0Rm96TExPSUdhUTlmUVFrMWE0MkJCL3ZKWkZ0UzcvLzIwTUhGY01OVVhPZEowTEVNejVYN254bHNUUVNVeUw4NW1YcWFhWmFtMzlKaWY4TFQzb2VkWFJ4ZGVpRDNCRFA3aHZXRnBUR2MrWUhseDBmRENsU25td1lSOUZHdFpOV2hsNzhyM2RPVkFSeExLNWpvUGdGTVlUTjBwaDhjQTM3QW5mSUdNdU9DV3Z0S3UyRG84L01mdno1c0dMbEdlVWtzN1I5N0VUMUJZVUJHMEdPZHNBYVJwNU9sQlVvcHhiSzBJeTBrNmZNV2h5Vk5TNitCU3I1Qjd1R1d0MHQ1bW9tYzVrVFFNMzE5UnBYK3prSUVFN2UwN1M1M1dHU0VxWHlhQU5BR3FLOGRpU1IrdllPeXpGQnUrVWVwRDQzSlppTWtwVUpWQTAxSTNhYUltVjU5RzRPSkcxNE5nK0RvNGhnN1YiLCJtYWMiOiIxNjdhY2ExZTQ4OTI1OGYxMzNkNzg5MmFmNGUwYjhhYzAxM2VjN2MzNzM0NDhhOWQ4ZGJhNmYyZjM2Mzc4MDcxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:22:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1715041281\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2038016856 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2038016856\", {\"maxDepth\":0})</script>\n"}}