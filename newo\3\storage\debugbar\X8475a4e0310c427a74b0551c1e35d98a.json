{"__meta": {"id": "X8475a4e0310c427a74b0551c1e35d98a", "datetime": "2025-06-17 05:40:51", "utime": **********.97742, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.601313, "end": **********.977462, "duration": 1.****************, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": **********.601313, "relative_start": 0, "end": **********.733854, "relative_end": **********.733854, "duration": 1.****************, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.733873, "relative_start": 1.****************, "end": **********.977466, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "244ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019849999999999996, "accumulated_duration_str": "19.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.840814, "duration": 0.016329999999999997, "duration_str": "16.33ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 82.267}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.884418, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 82.267, "width_percent": 7.355}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.949806, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 89.622, "width_percent": 10.378}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750138848236%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFpWDdaQnBkUE9OMVpYRkN1Rit5TFE9PSIsInZhbHVlIjoiQ1RjT3F0NUlyaDJsZ0ZONklLVmJzR0NyNC9wemwvNzJKVUc1QVVIWW0remN4ek1ieUZoV2lPOE54d3lmL2I4b0JMQVZhc3I0dEVIUldPWUg3L2pWU2hoZHAzcFFSdDZWMnJ2OHZFbXVibXFhaW9sM01Ga1ljc1ZUTEhkVzRKSnM5N21YdE9yMFlxYWZHbm4xbHpYc1AyMGNhWGZPcTJDU0I4ZWY0L3VVUWV1R3doSHFnWGhQYVc5ZVU4VTBGQXdHcmpYK24xRFkvTk43cVBnYVF6SkMwem5MMEdtdzc4WWFWWkdWK2ovV2ZQQjJ4Ui9yUFgwcHltaGF5S3EwNWhYNUpKY3dYRW9YTzBNNHJVN0VUMTFKMVZHL1RtazNVUEM3cFRydkUyaEVLNldNWFJKTlo4djh1K2pvbnYrRm94TTBNditXVTloV0w5TXVuWTFIU2s4ODlaNzlEa2twQWpDbWE4N08yRlJQUmxTRGJQVjlCWWFIamQ3MmdBSERpUFR0alRSWVg5MkxMZStwa3JZQzRPdVpuSWFmZno0clcvZFgrbXg1TWVvS3lWaTBUWjVYUUpXWTdHWUhsODZSR1JqY2RnN2gvWnUybW1nVzBWSHN0NlN3SysyZFpiOHhzdC9XZjZ1Y0RHZlc4UkwwYlllSWpjTFkzVlZ1RFJTREZvMFciLCJtYWMiOiI1M2NhYzMwZThmZWM4ZTZhNmZkODAyMWJkOGUyZGU5MjBmZjllOTU3MWIxZTczYjFhMTcyNDFmYmQzODAyMTdlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IndYQUtrMlZQSDBUajlyVGIyOXNZNkE9PSIsInZhbHVlIjoiWGJ2cFRtaU0zU2sveFFMaysyRlA5dWdiSVNNWFM5MlAxY1h5K0sxdFpJS0tJSitrODNRK3dISUlTL09ac1o4OGp2ZnhMdWdIOHJjYlVadHBzWGtLVUg0M2dWU2ZUNnNhK0FSaGUxaFdqOWdscXVLeHB3c3lobmRMVjlCblNNczlPU2FDdHpETERpb3RMMW5ZS3dYYXd4OVdOdnhmSkVKSzZCMittSWxiaE1KK2pRbXIyeEtNSm0zMkVCZW4vVFdyeFdHYmQxYUFPbFRtOXROY0pxUE5lbnNwUlB1bTZJSVk5aXJKbjc1S2JVODVLZS9UWmJGSC9XTVhySHdNQUpoV2w2SWZNb3p3ZHJrcWdPTHdJcm9GWVN0MHZXd2FQdFRRL1ZoNlNvZ3lXWkI5QzBvYWtrc0JheTU3c09GaFBMMnU4bWdJVHRteFRIN3NjcWdQRUY5blpSY1Yvb1NZV3lxWktPUi9qdTBNWER4VTd1WVZ5YnYyQWtlN2dnaHRqOVd4bVMzQkFkdXZheWtGVzJ4Q01MUkUvSk9yKzVpUTMxTGpvZkVxeFdyV2N4L1NsT1pNeFlvbXdiamEvaWVIV1VYUm9ROGU0Z1VBbll6UWJIOFhXblVLYUc4bjFHY2hpaHFBeDFReXdBdXBONS93cDl2blZJNDV6V1BGRzRYRkNEYlgiLCJtYWMiOiI2OTE0MzQ0Y2JjOGY5MDY2OWM4MTA3NzBmY2YyMzk3MzI2OTZkYWUzMTU4OGI5M2QwNGMwYzhlZTQ4YTUxNjhkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1353327256 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MsEpVpyAabX9l6kTbr1IUggTy4SSXp1me81GakZC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353327256\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-348269540 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:40:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlQM2J6cWQwWmRZRE04N3NVSlNpN0E9PSIsInZhbHVlIjoiTVJZWmUybjMvQ0x1a2hNd2VoOUM0Mk0wR3ZZZFZwSk1rUGZ0ei9ic2d6UDd3MnJDODdpTFo4cHZ6c0hNZ1JRc280c2JUOG5NckpnVEtNWTZrTitYeHhBT2ovVW1UUHhzQ2J6L0FORWlLSm5FazF2RnBFM1B2V0V4dlNIekUwMzQ1MjBjTjIyN3pkSGxKSDR5bE5MZllLRlZoak42ZGR3bHdrdVBTVFo2V3hpWUtPSWN3TnNkNFBicjgySFdKVGIvYk9SN0pBWkZyWDZtVXBxTWdlaDNvQWthU1VjT0NhS0ZZNFRNanY3MFRCMHFxRTJJQldaaS9YUFFEL1kyWFBLKzV0dEl1NEhpaHNvaU5HSlpGelZaS211dEJSVllkREx3NG4yMHJWTCt6c2RKc0MrL0dCaEdISElhVlBIOWZQYXo0ejIzZTFPVHNSTXlXMzVWa2x1eGxwTVI3ckpRR1NuVTlxTUZRRmV1eTJyQWt1OUlFVXlHNFphYlBFOWluck0vd2FLYjZuT3VYL3RRZDlPRkJJaWRFck0vdDFCVGhZM25ZN3JCNllOZXFJUXhSaWdOVFZLK2s5cWN4eHdOcnc4NzFkRmhwRGdET0Z2eWh6bHB0UmJnMHYzZHI3NUtKUmZIL0NucXk4bFJPd2Q0eUg1Y1hLaGpMZEplODA2WWttU04iLCJtYWMiOiJiNzgyMzMyZWY3ZWE3YjNmMzBjNDVlOWQzODUwZWRlNzc4YWFiODA5NTQyYzFiNzBiOGQ5YjFlNGE5ZmY4NGY3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImpVSGhhSFhiZFFkNThCaTg1NzhHRHc9PSIsInZhbHVlIjoiS2pvLzdkaGFTTHZDS1FUTDB0Q0tPWDdScThEa0VOUzk3WE1vT3AzS0UvTExXU21Wam5wVFV3bVhSSEF6MkRTQnZ5aDdJZHYzZmtjeCtoNGRsOGNNT0RkaElERnlpTWd0NE1HUTB5K05BVUJOeksxQ2R2WURTQVJUOGRCVW13WEJtVFk0ekJQTklTQTAvV3JVQ29tQjRxaE5USVNCREswVmdMUHZ4UGZZc3NRQmprT0VaekJUd0tkWXJ0Y1BPRExzakxOd3FCY0xZL3ZTanFzMWcxNWhka3JiOWdJYS9WMmJQdHB2R3BESHlzTFlxWStFSTNUNEdiVHlUTndYbU1RbEZtS1dqaWsyV1BWVnFFbjFvZXZTbTJyeWtyQzNVRTFhVnY0eTBTVWlYQjMzTHhRL2dlRXB1R3B0d21vRlRxMmJQZStoN1lpZzNnOVV5TjVONEo5S3BkcytCN3FTc0tiT0h3dXMxd3EyQTk5c0MvcERpTmVwYTRwMXhiaGVVZ0lCNEtlYzVpYkI0S0lzT2dVQ2VUeUpQVDU0QVAxOHB1cnl3NURMQzVsdldBbnpEMzdSVmJBTUMvN1lzRkJOamQyWm1WR0sxOTlGRDdRYW1IMnBnWlRYRWM1UjNaVmpaWk9PQ055eW9COVBHRWtIdHd5OE5TNnJESi95cXl3SWNpQ2QiLCJtYWMiOiJhYTYyOTkxYjI0MWEwMmU1MzNlYTI1Y2MwYWI0MTUwN2U0ZmRiZWZlMDA3Y2E4ZWFlMmM0NGFmMjJkNTBlOTZjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlQM2J6cWQwWmRZRE04N3NVSlNpN0E9PSIsInZhbHVlIjoiTVJZWmUybjMvQ0x1a2hNd2VoOUM0Mk0wR3ZZZFZwSk1rUGZ0ei9ic2d6UDd3MnJDODdpTFo4cHZ6c0hNZ1JRc280c2JUOG5NckpnVEtNWTZrTitYeHhBT2ovVW1UUHhzQ2J6L0FORWlLSm5FazF2RnBFM1B2V0V4dlNIekUwMzQ1MjBjTjIyN3pkSGxKSDR5bE5MZllLRlZoak42ZGR3bHdrdVBTVFo2V3hpWUtPSWN3TnNkNFBicjgySFdKVGIvYk9SN0pBWkZyWDZtVXBxTWdlaDNvQWthU1VjT0NhS0ZZNFRNanY3MFRCMHFxRTJJQldaaS9YUFFEL1kyWFBLKzV0dEl1NEhpaHNvaU5HSlpGelZaS211dEJSVllkREx3NG4yMHJWTCt6c2RKc0MrL0dCaEdISElhVlBIOWZQYXo0ejIzZTFPVHNSTXlXMzVWa2x1eGxwTVI3ckpRR1NuVTlxTUZRRmV1eTJyQWt1OUlFVXlHNFphYlBFOWluck0vd2FLYjZuT3VYL3RRZDlPRkJJaWRFck0vdDFCVGhZM25ZN3JCNllOZXFJUXhSaWdOVFZLK2s5cWN4eHdOcnc4NzFkRmhwRGdET0Z2eWh6bHB0UmJnMHYzZHI3NUtKUmZIL0NucXk4bFJPd2Q0eUg1Y1hLaGpMZEplODA2WWttU04iLCJtYWMiOiJiNzgyMzMyZWY3ZWE3YjNmMzBjNDVlOWQzODUwZWRlNzc4YWFiODA5NTQyYzFiNzBiOGQ5YjFlNGE5ZmY4NGY3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImpVSGhhSFhiZFFkNThCaTg1NzhHRHc9PSIsInZhbHVlIjoiS2pvLzdkaGFTTHZDS1FUTDB0Q0tPWDdScThEa0VOUzk3WE1vT3AzS0UvTExXU21Wam5wVFV3bVhSSEF6MkRTQnZ5aDdJZHYzZmtjeCtoNGRsOGNNT0RkaElERnlpTWd0NE1HUTB5K05BVUJOeksxQ2R2WURTQVJUOGRCVW13WEJtVFk0ekJQTklTQTAvV3JVQ29tQjRxaE5USVNCREswVmdMUHZ4UGZZc3NRQmprT0VaekJUd0tkWXJ0Y1BPRExzakxOd3FCY0xZL3ZTanFzMWcxNWhka3JiOWdJYS9WMmJQdHB2R3BESHlzTFlxWStFSTNUNEdiVHlUTndYbU1RbEZtS1dqaWsyV1BWVnFFbjFvZXZTbTJyeWtyQzNVRTFhVnY0eTBTVWlYQjMzTHhRL2dlRXB1R3B0d21vRlRxMmJQZStoN1lpZzNnOVV5TjVONEo5S3BkcytCN3FTc0tiT0h3dXMxd3EyQTk5c0MvcERpTmVwYTRwMXhiaGVVZ0lCNEtlYzVpYkI0S0lzT2dVQ2VUeUpQVDU0QVAxOHB1cnl3NURMQzVsdldBbnpEMzdSVmJBTUMvN1lzRkJOamQyWm1WR0sxOTlGRDdRYW1IMnBnWlRYRWM1UjNaVmpaWk9PQ055eW9COVBHRWtIdHd5OE5TNnJESi95cXl3SWNpQ2QiLCJtYWMiOiJhYTYyOTkxYjI0MWEwMmU1MzNlYTI1Y2MwYWI0MTUwN2U0ZmRiZWZlMDA3Y2E4ZWFlMmM0NGFmMjJkNTBlOTZjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-348269540\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1948010598 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1948010598\", {\"maxDepth\":0})</script>\n"}}