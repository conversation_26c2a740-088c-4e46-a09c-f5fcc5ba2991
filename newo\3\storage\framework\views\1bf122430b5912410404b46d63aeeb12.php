<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('التسعير')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('التسعير')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0"><?php echo e(__('التسعير - أسعار البيع والشراء')); ?></h5>
                            <small class="text-muted"><?php echo e(__('انقر على الأسعار لتعديلها مباشرة')); ?></small>
                        </div>
                        <div class="col-md-6">
                            <div class="float-end">
                                <input type="text" id="search-products" class="form-control" placeholder="البحث عن منتج..." style="width: 250px;">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="pricing-table">
                            <thead class="table-dark">
                                <tr>
                                    <th width="30%"><?php echo e(__('اسم المنتج')); ?></th>
                                    <th width="20%"><?php echo e(__('الباركود')); ?></th>
                                    <th width="25%"><?php echo e(__('سعر البيع')); ?></th>
                                    <th width="25%"><?php echo e(__('سعر الشراء')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr data-product-id="<?php echo e($product->id); ?>">
                                        <td><?php echo e($product->name); ?></td>
                                        <td><?php echo e($product->sku); ?></td>
                                        <td class="price-cell" data-field="sale_price" data-product-id="<?php echo e($product->id); ?>">
                                            <span class="price-display"><?php echo e(number_format($product->sale_price, 2)); ?></span>
                                        </td>
                                        <td class="price-cell" data-field="purchase_price" data-product-id="<?php echo e($product->id); ?>">
                                            <span class="price-display"><?php echo e(number_format($product->purchase_price, 2)); ?></span>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
<style>
.price-cell {
    cursor: pointer;
    padding: 12px;
    transition: background-color 0.3s ease;
    position: relative;
}

.price-cell:hover {
    background-color: #e3f2fd;
    border-radius: 4px;
}

.price-cell:hover::after {
    content: "✏️ انقر للتعديل";
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 11px;
    color: #666;
    background: rgba(255,255,255,0.9);
    padding: 2px 6px;
    border-radius: 3px;
    white-space: nowrap;
}

.price-input {
    width: 100%;
    border: 2px solid #007bff;
    padding: 8px;
    font-size: 14px;
    border-radius: 4px;
    text-align: center;
}

.updating {
    background-color: #fff3cd !important;
    color: #856404;
}

.success {
    background-color: #d4edda !important;
    color: #155724;
}

.error {
    background-color: #f8d7da !important;
    color: #721c24;
}
</style>

<script>
$(document).ready(function() {
    console.log('Pricing page loaded');

    // إعداد CSRF token
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // وظيفة البحث
    $('#search-products').on('keyup', function() {
        var searchText = $(this).val().toLowerCase();
        $('#pricing-table tbody tr').each(function() {
            var productName = $(this).find('td:first').text().toLowerCase();
            var barcode = $(this).find('td:nth-child(2)').text().toLowerCase();

            if (productName.includes(searchText) || barcode.includes(searchText)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // التعديل المباشر للأسعار
    $(document).on('click', '.price-cell', function() {
        var $cell = $(this);
        var $display = $cell.find('.price-display');

        // تجنب التعديل المتعدد
        if ($cell.find('input').length > 0) {
            return;
        }

        var currentValue = $display.text().trim();
        var field = $cell.data('field');
        var productId = $cell.data('product-id');

        console.log('Editing:', field, 'for product:', productId, 'current value:', currentValue);

        // إنشاء input للتعديل
        var $input = $('<input type="number" class="price-input" step="0.01" min="0">');
        $input.val(currentValue);

        // استبدال النص بـ input
        $cell.html($input);
        $input.focus().select();

        // معالجة الأحداث
        $input.on('blur keydown', function(e) {
            if (e.type === 'blur' || e.which === 13) { // Enter
                var newValue = $(this).val().trim();

                if (newValue === '' || isNaN(newValue) || parseFloat(newValue) < 0) {
                    alert('يرجى إدخال قيمة رقمية صحيحة');
                    restoreOriginalValue();
                    return;
                }

                if (newValue !== currentValue) {
                    updatePrice(productId, field, newValue, $cell);
                } else {
                    restoreOriginalValue();
                }
            } else if (e.which === 27) { // Escape
                restoreOriginalValue();
            }
        });

        function restoreOriginalValue() {
            $cell.html('<span class="price-display">' + currentValue + '</span>');
        }
    });

    // دالة تحديث السعر
    function updatePrice(productId, field, value, $cell) {
        console.log('Updating price:', productId, field, value);

        // إظهار حالة التحديث
        $cell.addClass('updating').html('جاري الحفظ...');

        $.ajax({
            url: '<?php echo e(route("pricing.update.inline")); ?>',
            method: 'POST',
            data: {
                _token: '<?php echo e(csrf_token()); ?>',
                id: productId,
                field: field,
                value: value
            },
            success: function(response) {
                console.log('Update response:', response);

                if (response.success) {
                    // عرض القيمة المحدثة
                    var displayValue = parseFloat(value).toFixed(2);
                    $cell.removeClass('updating').addClass('success')
                         .html('<span class="price-display">' + displayValue + '</span>');

                    // إزالة تأثير النجاح بعد ثانيتين
                    setTimeout(function() {
                        $cell.removeClass('success');
                    }, 2000);

                    // رسالة نجاح
                    if (typeof show_toastr === 'function') {
                        show_toastr('Success', 'تم تحديث السعر بنجاح', 'success');
                    }
                } else {
                    // عرض خطأ
                    $cell.removeClass('updating').addClass('error')
                         .html('<span class="price-display">خطأ</span>');

                    setTimeout(function() {
                        $cell.removeClass('error')
                             .html('<span class="price-display">' + value + '</span>');
                    }, 2000);

                    alert('خطأ: ' + (response.message || 'فشل في التحديث'));
                }
            },
            error: function(xhr) {
                console.error('Update error:', xhr);

                var errorMessage = 'حدث خطأ أثناء التحديث';
                if (xhr.status === 403) {
                    errorMessage = 'ليس لديك صلاحية للقيام بهذا الإجراء';
                } else if (xhr.status === 404) {
                    errorMessage = 'المنتج غير موجود';
                } else if (xhr.status === 500) {
                    errorMessage = 'خطأ في الخادم';
                }

                // عرض خطأ
                $cell.removeClass('updating').addClass('error')
                     .html('<span class="price-display">خطأ</span>');

                setTimeout(function() {
                    $cell.removeClass('error')
                         .html('<span class="price-display">' + value + '</span>');
                }, 2000);

                alert(errorMessage);
            }
        });
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\to\newo\3\resources\views/pricing/products.blade.php ENDPATH**/ ?>