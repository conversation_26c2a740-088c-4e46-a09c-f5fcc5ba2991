<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('التسعير')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('التسعير')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- جدول التسعير -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('التسعير - أسعار البيع والشراء')); ?></h5>
                    <small class="text-muted"><?php echo e(__('يمكنك النقر على أسعار البيع والشراء لتعديلها مباشرة')); ?></small>
                </div>
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table datatable" id="pricing-products-table">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('اسم المنتج')); ?></th>
                                    <th><?php echo e(__('الباركود')); ?></th>
                                    <th><?php echo e(__('سعر البيع')); ?></th>
                                    <th><?php echo e(__('سعر الشراء')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr data-product-id="<?php echo e($product->id); ?>">
                                        <!-- اسم المنتج -->
                                        <td><?php echo e($product->name); ?></td>
                                        
                                        <!-- الباركود -->
                                        <td><?php echo e($product->sku); ?></td>
                                        
                                        <!-- سعر البيع - قابل للتعديل -->
                                        <td class="editable" data-field="sale_price" data-type="number">
                                            <?php echo e(Auth::user()->priceFormat($product->sale_price)); ?>

                                        </td>
                                        
                                        <!-- سعر الشراء - قابل للتعديل -->
                                        <td class="editable" data-field="purchase_price" data-type="number">
                                            <?php echo e(Auth::user()->priceFormat($product->purchase_price)); ?>

                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
<style>
.editable {
    cursor: pointer;
    background-color: #f8f9fa;
    transition: background-color 0.2s;
}

.editable:hover {
    background-color: #e9ecef;
}

.table td.editable {
    position: relative;
}

.table td.editable::after {
    content: "✏️";
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.2s;
}

.table td.editable:hover::after {
    opacity: 1;
}
</style>

<script>
console.log('Loading pricing products page...');

$(document).ready(function() {
    console.log('Document ready - pricing products');

    // تهيئة DataTable
    $('#pricing-products-table').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
        },
        "pageLength": 25,
        "order": [[0, "asc"]],
        "columnDefs": [
            { "orderable": false, "targets": [2, 3] } // منع ترتيب أعمدة الأسعار
        ]
    });

    // التعديل المباشر للخلايا
    $('.editable').on('click', function() {
        var $this = $(this);

        // تجنب التعديل المتعدد
        if ($this.find('input').length > 0) {
            return;
        }

        var field = $this.data('field');
        var type = $this.data('type');
        var productId = $this.closest('tr').data('product-id');
        var currentValue = $this.text().trim();

        // حفظ القيمة الأصلية
        $this.data('original-value', currentValue);

        // إزالة تنسيق العملة للحصول على القيمة الرقمية
        var numericValue = currentValue.replace(/[^\d.-]/g, '');

        console.log('Editing field:', field, 'Current value:', currentValue, 'Numeric:', numericValue);

        // إنشاء input للتعديل
        var input;
        if (type === 'number') {
            input = $('<input type="number" class="form-control form-control-sm" step="0.01" min="0" style="width: 100%; border: 2px solid #007bff;">');
            input.val(numericValue);
        } else {
            input = $('<input type="text" class="form-control form-control-sm" style="width: 100%; border: 2px solid #007bff;">');
            input.val(currentValue);
        }

        // استبدال النص بـ input
        $this.html(input);
        input.focus().select();
        
        // حفظ التغييرات عند الضغط على Enter أو فقدان التركيز
        input.on('blur keypress', function(e) {
            if (e.type === 'blur' || e.which === 13) {
                var newValue = $(this).val().trim();

                // التحقق من صحة القيمة
                if (type === 'number' && (isNaN(newValue) || newValue < 0)) {
                    show_toastr('Error', 'يرجى إدخال قيمة رقمية صحيحة', 'error');
                    $this.text($this.data('original-value'));
                    return;
                }

                if (newValue !== numericValue && newValue !== currentValue && newValue !== '') {
                    // إرسال التحديث
                    updateField(productId, field, newValue, $this);
                } else {
                    // إرجاع القيمة الأصلية
                    $this.text($this.data('original-value'));
                }
            }
        });

        // إلغاء التعديل عند الضغط على Escape
        input.on('keyup', function(e) {
            if (e.which === 27) { // Escape key
                $this.text($this.data('original-value'));
            }
        });
    });
    
    // دالة تحديث الحقل
    function updateField(productId, field, value, $element) {
        console.log('Updating:', productId, field, value);

        // إظهار مؤشر التحميل
        $element.html('<i class="fas fa-spinner fa-spin text-primary"></i>');

        $.ajax({
            url: '<?php echo e(route("pricing.update.inline")); ?>',
            method: 'POST',
            data: {
                _token: '<?php echo e(csrf_token()); ?>',
                id: productId,
                field: field,
                value: value
            },
            success: function(response) {
                console.log('Update response:', response);
                if (response.success) {
                    // عرض القيمة المحدثة مع تأثير بصري
                    $element.text(response.display_value);
                    $element.addClass('table-success');

                    setTimeout(function() {
                        $element.removeClass('table-success');
                    }, 2000);

                    // إظهار رسالة نجاح
                    if (typeof show_toastr === 'function') {
                        show_toastr('Success', response.message || 'تم التحديث بنجاح', 'success');
                    }
                } else {
                    // إرجاع القيمة الأصلية وإظهار رسالة خطأ
                    $element.text($element.data('original-value') || value);
                    $element.addClass('table-danger');

                    setTimeout(function() {
                        $element.removeClass('table-danger');
                    }, 2000);

                    if (typeof show_toastr === 'function') {
                        show_toastr('Error', response.message || 'فشل في التحديث', 'error');
                    }
                }
            },
            error: function(xhr) {
                console.error('Update error:', xhr);
                var errorMessage = 'حدث خطأ أثناء التحديث';

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                // إرجاع القيمة الأصلية وإظهار رسالة خطأ
                $element.text($element.data('original-value') || value);
                $element.addClass('table-danger');

                setTimeout(function() {
                    $element.removeClass('table-danger');
                }, 2000);

                if (typeof show_toastr === 'function') {
                    show_toastr('Error', errorMessage, 'error');
                } else {
                    alert(errorMessage);
                }
            }
        });
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\to\newo\3\resources\views/pricing/products.blade.php ENDPATH**/ ?>