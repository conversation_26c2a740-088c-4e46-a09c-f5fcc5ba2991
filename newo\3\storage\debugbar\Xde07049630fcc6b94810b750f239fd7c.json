{"__meta": {"id": "Xde07049630fcc6b94810b750f239fd7c", "datetime": "2025-06-16 15:23:31", "utime": **********.684447, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087409.933831, "end": **********.68449, "duration": 1.7506589889526367, "duration_str": "1.75s", "measures": [{"label": "Booting", "start": 1750087409.933831, "relative_start": 0, "end": **********.471609, "relative_end": **********.471609, "duration": 1.5377781391143799, "duration_str": "1.54s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.471635, "relative_start": 1.537804126739502, "end": **********.684495, "relative_end": 5.0067901611328125e-06, "duration": 0.2128598690032959, "duration_str": "213ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45152544, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020149999999999998, "accumulated_duration_str": "20.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.585262, "duration": 0.01714, "duration_str": "17.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.062}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.632787, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.062, "width_percent": 6.749}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6547818, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.811, "width_percent": 8.189}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order/1\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1929858994 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1929858994\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-709713374 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-709713374\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1975659062 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1975659062\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1960156094 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost/receipt-order/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087400433%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpDRVJMQXBLcDdkeEprUGRhN0RWaFE9PSIsInZhbHVlIjoiWHRjUHMwYmp5cHlidnM1VGY5S2k3OWNWVU05bFYrN3dWZTlyYTUreXYwRkVCNXJ1Qld5aURDRUhJZjJsaUNpaXdsRllYZFlSQ09VL3BITU1GUWNUT012eGsybXdYRnpLaW5GaU1ESm9FR2dUZzJqZCtJNklHUkw1aDhyTUM0MjdadDFZc1BPOGFnZWg2VW1nNzJ6Vld3QStnV2dBN2FSSUx0NEE3N09ZY2s1dzREcUUvWXhzMmZTNUxtQmt6dmNualBpdzc2RDlWMXpRQ1hwNk1WRHIyRTRZNTVNemRFd0phQndsMkh2ZGZTN1ZNLzRZKzhvZG90cXNHK254L2Y1UmY0Q0VYVVI1ZGpDQS9Kc0RwclFHNE1rNkd2WkJUTG9zZC9QM0hQK0ZQMEphUktaNnYwOXVETFZSa3FyY1diR0lKcVdsTVVoc1ZsYTZsVG5wL3F2a1o0ai9OM1FXUWRkd1VXWmcya0xTY1NnWUVwKzN6Zk5ZYUo3NEFSVW93WnhUMkhxR2pxbU5Lc0szRFlnTVJuU0F4WWhtQlRjb3d0clV1MXNjcDk2Z2tVY2p1OW5WKzl2TkFNRWYvbVhTSml6N2JuUnVUQmpGSEprMG9kR1h5K2pIazAxbmx3VDNWN1lNdFF1REEyRlhaQW9rMnFEVkw4cmY4R3VheUpseWlkK2UiLCJtYWMiOiJhNDVmNzAwOTNkZGM1Nzk2ZWQzNzczMTU1YTQ5Y2IzOWNjNTc0ZTExNTk5NzI5ZTU5NGE3YTc1MGJjNDU2ZWI4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InBDT0RsYWdhYWRHaTdLakxxck1qMWc9PSIsInZhbHVlIjoicVJOZHE5cC9QRmJyc2d4KzduUEZteUp6L1Z2VysxUHdvSjhVSEJaOURLMWpBNG0vVlFsQnlTajlPZEVVU0EzUkNqYlJTQW54VDhuaXdOOGNmQWRjYjE0MzlDY2VKZU11ZCtudlJyNi9Bcm5ibDQ3NEk1enl3eDRMNFRsb0FpR0wrczd0VHh3VlFtMXoyNDJMMUw4V29sZ0lqeUZGM0Fwc1RCU25jOWYvZ2o1MzBJUk0yWTlCQnZLYzNMS0VtQ2FtTFB1UVc5NS9HSFhJQjBaeVRyWlppS0V6NEhNYU5USXFOQ2E0RlJlSGJLM29nY2pvSGwyUmVsaVl4OVJSVnB4akZoOHpRTWFlMDhWWFMrczV3SWliMGc5MTlSRzRiMjVFMEJxWXZyR0NlTm1Ma2taMldSTnhmUUNVZTFHeTl2eElZc3YrOWtDNDd6K3U3MlhXRmFocHp6enhlQ1BPdmM0WkJSWXFoK2t5a1ZjbU5Hb1U5R2VRNlFiL2pPdmRTNG9mYkMrSVpmQnB0R3R0RFRpcHE4RkJyWVdZWDlDSUZtNjFJaUMxczFheVlzMUVZM29aS2VpR3dJVXNMcFc4bXZmYWVBRDJ1cmNYZHM5Qkp5aG5Pb3lxY20ySzVmZ2ZrNHJ3YUU1NG9NTjlhLzVHZWxUREhuZ2NzU0U4czZ3NzJWZ2siLCJtYWMiOiJiMmUxNzg0NzBhNjdiZGE4OTg1NDdhZGZjZjUxYmE2YWRkZWYxYzM2YTdiNmE2NmQ0NTY3YTAwZTA1NDY5NjEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1960156094\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-446866676 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzcvmOKIgVMCCvEHD9HLB5trqF9yJ9GcIs8Pndo9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-446866676\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1711093079 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:23:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhlRWh3bkJXRERSdjduVnh6MFVHM0E9PSIsInZhbHVlIjoiWkFKMWFZYnJDRGtKeWpiWjBoRXZkR0FOeGw3eHhOSk5yemtwNWNEc2NUTzVwUTIxemRSNDkrRkhLU3BFRDFmT2UvZzdFUUYyQnBkZGtmWmtjbkJkWSt0MGlUVDgrSmJVTE5SUEI4Z1JSUmxqV2tneS8yOXo3OVBHc3RCN1ZPZHp6cTRva3RTWjRYR1FWdFg0WThCQ28yVGZkbHFDY2hBOXJmaXVRUTJacFpON0JzTFg1WnhSQXpjT1FEWk9oRSttUlNGRXhWcGI1cGxqZWU2N0djMmlVRVV6ai83eVdIUjhWVzM2SS9CSnp1TEV4K1pjbStwWnZSOUl3ODVqaDdzdHVKUEgvZFNGd3BsbGd2YTRBd0hoNUM1QnJYUGhVZUNCb09sWkdxQUFEVTc1cm9NcGJHbm9DMjRUSUR3YUJjQ01FbEM5UUdMZlE2SjBGNDg3ZEVVMHlvTWg5ZHcwUEZKdzFOVjdUR2JzZGNUa1ppay85dGhSVGRvejVnT0JPcUNrNmxWRkZ6eHkyQzdRcTZsVDZjVG5yK0NYTnpNMTIzeWQxWG10czQzMzgzQWswUWxmZGN0RG9XOXdsSVRhWEx5bGJNT1hPdm9MUWJtc2Z0N3NqaFpzcXNKbE54Mm9mUFJKSC80MGxnZjlZZnBEc1R4dThPYjYrMFYrU3JVWFdtNGgiLCJtYWMiOiIzNDk1OWRmMmI0MDgwZTM5Yjk2MjJiYWVmNWFkZDk1YTM0YTg5NGJkYzBjOTA0MWYzMGFhYWVmMmI2MWRiN2MyIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imc5OVlYQ0lFWlJxU0FuQmpkeW56Snc9PSIsInZhbHVlIjoiOXRuRDkxbUFwVGtlWXM2UkM5UkVEUmM1QWFSRkFQRE05UjRrSlN3dzdIcUpEZm1yZzlONVFSNVdhRnBCZ3BSL2VHL2xoVU92aENma0xGcVF0eWZsaG5qbU56ajFOTkpENmJVM0JJSld4cTFvc3Y5cmt6VWR5ZTJybjc0T1hOVVRnNkpzTXlYemxIMVdBemhyU01wOG1mUmFoVFJhd01kdzI5bGh2WFltQTJUTlV5Z3RJcmRDV2FXeHVDV2RYbk1SaTEvM2liamVSclZZRWRLU1JVWG0vUHR2Y21JYVVTRWlsNVhHWDViWEwyUWxScDRFRHdqa1I1YzhoWmxNU3NCbzhDMnE4WFJNdmtkME9lOG0xWEVsc2JLNUZrZEtsRkJ0ZDh1SGFlamNjT0JhR0xtOWd5YmF2ZEc0amNwQ2U5NE5OazgxcDdpM3BlYmFVYkovUy9jcS81clVVTi80ZjFvNmNrMitCWXBOZDl0dk1GZEczN2ZNMkVpa2hHSHAzWXk4d3l4em1wV2hvMVlXMXZ6N3ZqS1ZRdFRGc05wWlpqaHlGbElxOFhJemw5WGg1aWRESjdPd085djBEeExUM25OcjFpbXJQa2tQTzduUDRMbkNCcy81RWxsREQrSGVrZUtzK0VmY2lWOGE2TERMTTM4VUJOaU1VVktrMjVnYlZGbWkiLCJtYWMiOiJkMThhYjEyOTI3M2E2ZWI1NWVlN2YzYjdhOTU0NmJkNjRjMTYxMzQ5YmJiMjU1ZDM4MTU4OTkxODk5MzA2ZmI1IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhlRWh3bkJXRERSdjduVnh6MFVHM0E9PSIsInZhbHVlIjoiWkFKMWFZYnJDRGtKeWpiWjBoRXZkR0FOeGw3eHhOSk5yemtwNWNEc2NUTzVwUTIxemRSNDkrRkhLU3BFRDFmT2UvZzdFUUYyQnBkZGtmWmtjbkJkWSt0MGlUVDgrSmJVTE5SUEI4Z1JSUmxqV2tneS8yOXo3OVBHc3RCN1ZPZHp6cTRva3RTWjRYR1FWdFg0WThCQ28yVGZkbHFDY2hBOXJmaXVRUTJacFpON0JzTFg1WnhSQXpjT1FEWk9oRSttUlNGRXhWcGI1cGxqZWU2N0djMmlVRVV6ai83eVdIUjhWVzM2SS9CSnp1TEV4K1pjbStwWnZSOUl3ODVqaDdzdHVKUEgvZFNGd3BsbGd2YTRBd0hoNUM1QnJYUGhVZUNCb09sWkdxQUFEVTc1cm9NcGJHbm9DMjRUSUR3YUJjQ01FbEM5UUdMZlE2SjBGNDg3ZEVVMHlvTWg5ZHcwUEZKdzFOVjdUR2JzZGNUa1ppay85dGhSVGRvejVnT0JPcUNrNmxWRkZ6eHkyQzdRcTZsVDZjVG5yK0NYTnpNMTIzeWQxWG10czQzMzgzQWswUWxmZGN0RG9XOXdsSVRhWEx5bGJNT1hPdm9MUWJtc2Z0N3NqaFpzcXNKbE54Mm9mUFJKSC80MGxnZjlZZnBEc1R4dThPYjYrMFYrU3JVWFdtNGgiLCJtYWMiOiIzNDk1OWRmMmI0MDgwZTM5Yjk2MjJiYWVmNWFkZDk1YTM0YTg5NGJkYzBjOTA0MWYzMGFhYWVmMmI2MWRiN2MyIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imc5OVlYQ0lFWlJxU0FuQmpkeW56Snc9PSIsInZhbHVlIjoiOXRuRDkxbUFwVGtlWXM2UkM5UkVEUmM1QWFSRkFQRE05UjRrSlN3dzdIcUpEZm1yZzlONVFSNVdhRnBCZ3BSL2VHL2xoVU92aENma0xGcVF0eWZsaG5qbU56ajFOTkpENmJVM0JJSld4cTFvc3Y5cmt6VWR5ZTJybjc0T1hOVVRnNkpzTXlYemxIMVdBemhyU01wOG1mUmFoVFJhd01kdzI5bGh2WFltQTJUTlV5Z3RJcmRDV2FXeHVDV2RYbk1SaTEvM2liamVSclZZRWRLU1JVWG0vUHR2Y21JYVVTRWlsNVhHWDViWEwyUWxScDRFRHdqa1I1YzhoWmxNU3NCbzhDMnE4WFJNdmtkME9lOG0xWEVsc2JLNUZrZEtsRkJ0ZDh1SGFlamNjT0JhR0xtOWd5YmF2ZEc0amNwQ2U5NE5OazgxcDdpM3BlYmFVYkovUy9jcS81clVVTi80ZjFvNmNrMitCWXBOZDl0dk1GZEczN2ZNMkVpa2hHSHAzWXk4d3l4em1wV2hvMVlXMXZ6N3ZqS1ZRdFRGc05wWlpqaHlGbElxOFhJemw5WGg1aWRESjdPd085djBEeExUM25OcjFpbXJQa2tQTzduUDRMbkNCcy81RWxsREQrSGVrZUtzK0VmY2lWOGE2TERMTTM4VUJOaU1VVktrMjVnYlZGbWkiLCJtYWMiOiJkMThhYjEyOTI3M2E2ZWI1NWVlN2YzYjdhOTU0NmJkNjRjMTYxMzQ5YmJiMjU1ZDM4MTU4OTkxODk5MzA2ZmI1IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1711093079\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-384129888 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://localhost/receipt-order/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-384129888\", {\"maxDepth\":0})</script>\n"}}