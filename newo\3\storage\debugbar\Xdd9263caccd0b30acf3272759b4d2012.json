{"__meta": {"id": "Xdd9263caccd0b30acf3272759b4d2012", "datetime": "2025-06-17 06:54:28", "utime": **********.465224, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143266.737726, "end": **********.465259, "duration": 1.7275331020355225, "duration_str": "1.73s", "measures": [{"label": "Booting", "start": 1750143266.737726, "relative_start": 0, "end": **********.240181, "relative_end": **********.240181, "duration": 1.5024549961090088, "duration_str": "1.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.240209, "relative_start": 1.5024831295013428, "end": **********.465264, "relative_end": 5.0067901611328125e-06, "duration": 0.22505497932434082, "duration_str": "225ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45506648, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02123, "accumulated_duration_str": "21.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.363573, "duration": 0.01789, "duration_str": "17.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.268}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4203029, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.268, "width_percent": 8.573}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4340992, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 92.84, "width_percent": 7.16}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-2056460129 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2056460129\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2011419796 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2011419796\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1573780372 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1573780372\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1261063230 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=lxd95%7C1750140707036%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlgvTTltdloxL3hvVi9TT1JBR1d6UHc9PSIsInZhbHVlIjoiTlZzbWgxb0hVZU9MTTVZZHFNT1hpQmJFTGFuOTZXTm0zK1ZrMjBaYnZMZU9mU0MyaFVaSzN0clJwUTN2NjJMZ1V1dlBZeXVqcmxmLy9zTnFMZ0dXbENqYmRzSktwNWdKTVE3K1BSSGxsblIycUd1MXpvR1ZpRUR3MnRuYVJqMzhZNWJUZml0YnJYaSt2bmg3NVl1S0E4NVVmQzJkTlMwMWFWZTVGOVJwNDJtSUJ3ajZCYjRTTkpTckdvbzgrWEVZY252VWpBTmFMTnlCWlVTcHhHVm1zZ2s0YnphL1J2ZDlVYjBkclNGTjBVZVVYeXlXRlJwcTVLM29vZGJ3RGkxM2J6bE9KRHcwbWozYk5VQmlvcUFIaExOd1Y2K0MzOCs1M05JTVJXZWpya0pXRkFZcngyWGN5YmxmOE1hNUpiWVJFSHNzaGg0Q0FpQUh5M2Fzdnd6eVFhMXI2bXd2T2RCVmFIcnV6M1NNVTVzYndxRHVhWjJ1NkNSL1ZPMllrYU16VjBraGJMcXFsMjRMOUg3M1BwOEhXamRBbUhXOFNaRTh5R2RkRWFmbFQzVmVnYTkvN3RERE9mNEJsK2VicFFQTGJDVit1OEszemxiM09sL0c2UkN5bzBNZ0RzcmdyRFdyYVh2UzF4OGw5SFNkakRnMTZQVXBhekw0Ty9lQzUyZHgiLCJtYWMiOiI1YzYwZTZiMTMzZjEzYTY5NWQ0MGM0ZmVlZmY3OTUxZjg0ZTJhM2RjOWJlNDNmOGNhYWE5Y2ZkMGMzMGUzNzE2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjlnT2p5dEx6OHh0K0RBc1RGbkpjOWc9PSIsInZhbHVlIjoiTkxrMnBmamVPSDRVcmNIeTZ5RFFUbGNRNGVUMGVKcysyUjJ4TnpWNHdNZ0prMllvODN5L2dNVGhJdTlEc1dNbmZNRXdQZkNNMDNyZWV3THdmdllGY3lhSkxVMDBzbW90QlRPVFpGZDdBc1N0NUdvSFFSNWZWM0FDV2JXVUZLYXRxc2RzMHpMeHkxajVsbkthc2NiNlBManBiTktsWXBGTmxOaXFmNlRlNC96bkVOQnNsREVodHZtaks5T0RpTDhSRkV6ZHdrdXlJNWc5bXFqanNITmJGdHlmTVRyd3U3UnR2alo0d05mMjI3THFobCtkVjFEMWp6OS9udzljaWZNejVySWdhU0ZnL01jN2dQaTg3RlpXd0VrcVZCQUtwWUNQMDJvN2tmZDZPQ1ltV2ZLOGxNdXQxbkpRTDJLTUtIcDN5QjJMd1BsQXV4VTR5djFLOHd2WnRGZXJsem1XTTJ1SG9Bdi9WZ0NXb1BSUTZTdnJxUGFjeDFIL09ZbktMYklYZDNnM01WWmcvQ2pObzJGcGNab0Q4RGxYZGhNMGZ2V0QxRzF4MmI3OFp4NEVDT2N5QkdER0FYQTdyOE9HQ2xXc0pQQ2UxMm11eW1oUjZ6UE5ESkUzWTJLcWRpbnoyNWphNis4bHMycldpdkZ2VHlocWdxL3FjRVF3aStYQVVFdjIiLCJtYWMiOiJiOTUzYTBkMTEyOGVmNGZjOGUxNDU1OTNiYmJjZDE1YWU3MzJmNmVjOTcxNWUzOGNlMzhhOTFhYjZlM2Y2NGYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1261063230\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-87242419 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDWN9h6R9gAIdzZaXBzXfq0WGpauVfrUj70kKWA2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-87242419\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:54:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldvSUIrWm5aUm96U2c2OVI3ZXIyT3c9PSIsInZhbHVlIjoiWldrR3QwRVpUVHdaMmhjTVorZnI2c0tXS3QxYVVjcWk0Q1E1Y0puamR5dVpwRTBYTE5JVk1tT1FIMVFTWHRmL3NNVzBtU1ZTQVl3WDA5enBzQng3aW96UVpPeEM4SktSOHRzcFFhUVJJTTBrTzQydnNYdEFCMyt4V1hzNlkvcWtVNlJtazdQa1Z6VjlxQjF5VUM1S1YzYytnengvWGQ0aFE0UVlTYVpNRVBxcVcvQkd0OXFlZm5IMkdBc1NzMUhMbjBFZkgzN0orYzRoQU9OdFpJNXRVaUVOYkZYMW0rVHVwTkFmZEQ1QWVoQlAzV2pKU2tEZGNwcTZ6QmZUT01oSjdNYUFDeVMyYWxKRHhSR0c4ZENpbzB5a2VqU01PMURjT29RdjBVU0ljSEQrV1IwTmZ4V1FrMisvRitSMGp4RmxqNUE1MG10emdrMzg3YVdzc1A5UGxLTFFoTHN6WFVBakFya0VWNm5TRXdiY0dRQ1JITDFmR2RTZUxFcUZOemJvVG9kZUFueTA1eHVYbkgxdFRSY1FPYktlZDViTkhHSHJ6TytnR0loM0RETVIxMXRqTmYwOGMyakJBSkx2Y3JQQnhOS2hHME5ldnBqT0g5QmRCa2YrK3AySEVzaXJoUDhVT3QwbTBCY2NJbWs2K1pXb3ZEeGZMcXFJWm9jNXJKYmEiLCJtYWMiOiIxMGVhZjZiNzI2NTlkNTFkODAyZWRkNzgwOGJlNmMwODIxNmVjODllZDBiNjdhODAzYWY2MTczMzg3ZDVjN2I0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:54:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjA1STh0cG02MGJRaWFVbVdCNDlIcHc9PSIsInZhbHVlIjoiVFJ5VzQwZTVmODB4SU1vSXRRTWdNZ1Z4aHBPQ3l0clVENWlnZjdTK1RNZHVIRzg4VWpiZVpRK0ZjL2FzOWJtaHFLK3Q4TlBLaXpCQzdCems3cG51WldibGhIZUVCNndwekljK0FOS1BPVzlxY2wyMjEwSU90OElZRzdyUDZiV1p1dW9TcUhTL0NzNVZDaEZaMHFUaVZjYy8vbzRMMVNHMHQwVHUvM2pRYVNjK1dkRm5BR0lrOGdjMURYNkVLWGdHa01IMGtyL1hNYm8wdFV5SGM0ZGRvSjVpUmtXNVZJWWRwV1g5TzZyM2JkMXJVdFNmbzFSRlFCblZ1RXVPVnM0Z0YvVEhNWHFNakRUVzAxMXZacEx2YlNCMnF0Rmx2VVpxd2xxRDN3YlNuQ25jREFqd3p0QjdIcU9pVTdJZi94VEdvL0pQdW9pQXFmZUw3UGZEUE5LS0NIYWhjdGcxYmFOcGkySjZsWi9xT3piMWU2cFhKeUo5SCtHaGlBbTc5ZmlQSXoyQjVCRkZsZllOODZiaDZjREt4ejFrVWVWMktUN2NpNGlIYjFhYnc3ZEJJUVdyUURSbHdvaGs1eTV4MU1BRWZkZEo1b1BRWHJBYm1iQlk3UXIvTXNnZDVOSjJwaHZ4TkNxOTVUekpRdGpHMUJkSWREcG9FSHZhaDNsalVpaFIiLCJtYWMiOiI0ZGNiMTEzYzUyZDIyYjQ0NjIxM2RlNzg0NWIyOWRjMmJhMDYwMTgwODgxOTI2Y2QzZTcyYjBhNTcyM2RjOTZmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:54:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldvSUIrWm5aUm96U2c2OVI3ZXIyT3c9PSIsInZhbHVlIjoiWldrR3QwRVpUVHdaMmhjTVorZnI2c0tXS3QxYVVjcWk0Q1E1Y0puamR5dVpwRTBYTE5JVk1tT1FIMVFTWHRmL3NNVzBtU1ZTQVl3WDA5enBzQng3aW96UVpPeEM4SktSOHRzcFFhUVJJTTBrTzQydnNYdEFCMyt4V1hzNlkvcWtVNlJtazdQa1Z6VjlxQjF5VUM1S1YzYytnengvWGQ0aFE0UVlTYVpNRVBxcVcvQkd0OXFlZm5IMkdBc1NzMUhMbjBFZkgzN0orYzRoQU9OdFpJNXRVaUVOYkZYMW0rVHVwTkFmZEQ1QWVoQlAzV2pKU2tEZGNwcTZ6QmZUT01oSjdNYUFDeVMyYWxKRHhSR0c4ZENpbzB5a2VqU01PMURjT29RdjBVU0ljSEQrV1IwTmZ4V1FrMisvRitSMGp4RmxqNUE1MG10emdrMzg3YVdzc1A5UGxLTFFoTHN6WFVBakFya0VWNm5TRXdiY0dRQ1JITDFmR2RTZUxFcUZOemJvVG9kZUFueTA1eHVYbkgxdFRSY1FPYktlZDViTkhHSHJ6TytnR0loM0RETVIxMXRqTmYwOGMyakJBSkx2Y3JQQnhOS2hHME5ldnBqT0g5QmRCa2YrK3AySEVzaXJoUDhVT3QwbTBCY2NJbWs2K1pXb3ZEeGZMcXFJWm9jNXJKYmEiLCJtYWMiOiIxMGVhZjZiNzI2NTlkNTFkODAyZWRkNzgwOGJlNmMwODIxNmVjODllZDBiNjdhODAzYWY2MTczMzg3ZDVjN2I0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:54:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjA1STh0cG02MGJRaWFVbVdCNDlIcHc9PSIsInZhbHVlIjoiVFJ5VzQwZTVmODB4SU1vSXRRTWdNZ1Z4aHBPQ3l0clVENWlnZjdTK1RNZHVIRzg4VWpiZVpRK0ZjL2FzOWJtaHFLK3Q4TlBLaXpCQzdCems3cG51WldibGhIZUVCNndwekljK0FOS1BPVzlxY2wyMjEwSU90OElZRzdyUDZiV1p1dW9TcUhTL0NzNVZDaEZaMHFUaVZjYy8vbzRMMVNHMHQwVHUvM2pRYVNjK1dkRm5BR0lrOGdjMURYNkVLWGdHa01IMGtyL1hNYm8wdFV5SGM0ZGRvSjVpUmtXNVZJWWRwV1g5TzZyM2JkMXJVdFNmbzFSRlFCblZ1RXVPVnM0Z0YvVEhNWHFNakRUVzAxMXZacEx2YlNCMnF0Rmx2VVpxd2xxRDN3YlNuQ25jREFqd3p0QjdIcU9pVTdJZi94VEdvL0pQdW9pQXFmZUw3UGZEUE5LS0NIYWhjdGcxYmFOcGkySjZsWi9xT3piMWU2cFhKeUo5SCtHaGlBbTc5ZmlQSXoyQjVCRkZsZllOODZiaDZjREt4ejFrVWVWMktUN2NpNGlIYjFhYnc3ZEJJUVdyUURSbHdvaGs1eTV4MU1BRWZkZEo1b1BRWHJBYm1iQlk3UXIvTXNnZDVOSjJwaHZ4TkNxOTVUekpRdGpHMUJkSWREcG9FSHZhaDNsalVpaFIiLCJtYWMiOiI0ZGNiMTEzYzUyZDIyYjQ0NjIxM2RlNzg0NWIyOWRjMmJhMDYwMTgwODgxOTI2Y2QzZTcyYjBhNTcyM2RjOTZmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:54:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}