<?php

namespace App\Http\Controllers;

use App\Models\warehouse;
use App\Models\Vender;
use App\Models\ProductService;
use App\Models\WarehouseProduct;
use App\Models\ProductExpiryDate;
use App\Models\WarehouseTransfer;
use App\Models\ReceiptOrder;
use App\Models\ReceiptOrderProduct;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\User;

class ReceiptOrderController extends Controller
{
    /**
     * Display a listing of receipt orders.
     */
    public function index()
    {
        if (Auth::user()->can('manage warehouse') || Auth::user()->can('show warehouse') || Auth::user()->hasRole('Cashier') || Auth::user()->hasRole('company')) {
            $user = Auth::user();

            // Get all receipt orders from the new table
            $receiptOrders = collect(); // إنشاء مجموعة فارغة مؤقتاً

            // إذا كان الجدول موجود، جلب البيانات
            try {
                $receiptOrdersQuery = ReceiptOrder::with(['vendor', 'warehouse', 'fromWarehouse', 'creator']);

                // إذا كان المستخدم لديه دور company، أظهر جميع الأوامر
                if ($user->hasRole('company')) {
                    // عرض جميع أوامر الاستلام في النظام
                    $receiptOrdersQuery = $receiptOrdersQuery->whereNotNull('id');
                } else {
                    // للمستخدمين الآخرين، أظهر أوامرهم فقط
                    $receiptOrdersQuery = $receiptOrdersQuery->where(function($query) use ($user) {
                        $query->where('created_by', $user->id)
                              ->orWhere('created_by', $user->creatorId());
                    });
                }

                $receiptOrders = $receiptOrdersQuery->orderBy('created_at', 'desc')
                    ->get()
                    ->map(function ($order) {
                        return [
                            'id' => $order->id,
                            'type' => $order->order_type,
                            'reference_number' => $order->order_number,
                            'vendor_name' => $order->vendor ? $order->vendor->name : ($order->order_type === 'نقل بضاعة' ? 'نقل داخلي' : 'غير محدد'),
                            'warehouse_name' => $order->warehouse->name ?? 'غير محدد',
                            'from_warehouse_name' => $order->fromWarehouse->name ?? null,
                            'creator_name' => $order->creator ? $order->creator->name : 'غير محدد',
                            'total_amount' => $order->total_amount ?? 0,
                            'total_products' => $order->total_products ?? 0,
                            'date' => $order->invoice_date ?? $order->created_at->format('Y-m-d'),
                            'created_at' => $order->created_at,
                            'status' => $order->status ?? 'مكتمل',
                            'status_color' => $order->status_color ?? 'success',
                        ];
                    });
            } catch (\Exception $e) {
                // إذا لم يكن الجدول موجود بعد، إرجاع مجموعة فارغة
                $receiptOrders = collect();
            }

            return view('receipt_order.index', compact('receiptOrders'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Show the form for creating a new receipt order.
     */
    public function create()
    {
        if (Auth::user()->can('manage warehouse') || Auth::user()->hasRole('Cashier')) {
            $user = Auth::user();

            $vendors = Vender::where('created_by', $user->creatorId())->get();

            // إذا كان المستخدم لديه مستودع محدد، أظهر مستودعه فقط
            if ($user->warehouse_id) {
                $warehouses = warehouse::where('id', $user->warehouse_id)
                    ->where('created_by', $user->creatorId())
                    ->get();
            } else {
                // إذا لم يكن لديه مستودع محدد، أظهر جميع المستودعات
                $warehouses = warehouse::where('created_by', $user->creatorId())->get();
            }

            return view('receipt_order.create', compact('vendors', 'warehouses'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Get products by warehouse for AJAX
     */
    public function getWarehouseProducts(Request $request)
    {
        $warehouseId = $request->warehouse_id;
        $user = Auth::user();

        if (!$warehouseId) {
            return response()->json([
                'success' => false,
                'message' => 'معرف المستودع مطلوب'
            ]);
        }

        // Get all products in the warehouse
        $warehouseProducts = WarehouseProduct::where('warehouse_id', $warehouseId)
            ->with(['product'])
            ->get();

        // Get all products (even those not in warehouse)
        $allProducts = ProductService::where('created_by', $user->creatorId())
            ->get();

        $products = $allProducts->map(function ($product) use ($warehouseProducts) {
            $warehouseProduct = $warehouseProducts->firstWhere('product_id', $product->id);
            
            return [
                'id' => $product->id,
                'name' => $product->name,
                'sku' => $product->sku,
                'current_quantity' => $warehouseProduct ? $warehouseProduct->quantity : 0,
                'sale_price' => $product->sale_price,
                'purchase_price' => $product->purchase_price,
            ];
        });

        return response()->json([
            'success' => true,
            'products' => $products
        ]);
    }

    /**
     * Search products by SKU or name
     */
    public function searchProducts(Request $request)
    {
        $search = $request->search;
        $warehouseId = $request->warehouse_id;
        $user = Auth::user();

        if (!$search) {
            return response()->json([
                'success' => false,
                'message' => 'نص البحث مطلوب'
            ]);
        }

        $query = ProductService::where('created_by', $user->creatorId())
            ->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%");
            });

        $products = $query->get()->map(function ($product) use ($warehouseId) {
            $warehouseProduct = null;
            if ($warehouseId) {
                $warehouseProduct = WarehouseProduct::where('warehouse_id', $warehouseId)
                    ->where('product_id', $product->id)
                    ->first();
            }

            // Get expiry date
            $expiryDate = null;
            if ($warehouseId) {
                $expiry = ProductExpiryDate::where('product_id', $product->id)
                    ->where('warehouse_id', $warehouseId)
                    ->first();
                $expiryDate = $expiry ? $expiry->expiry_date : null;
            }

            return [
                'id' => $product->id,
                'name' => $product->name,
                'sku' => $product->sku,
                'current_quantity' => $warehouseProduct ? $warehouseProduct->quantity : 0,
                'sale_price' => $product->sale_price,
                'purchase_price' => $product->purchase_price,
                'expiry_date' => $expiryDate,
            ];
        });

        return response()->json([
            'success' => true,
            'products' => $products
        ]);
    }

    /**
     * Display the specified receipt order.
     */
    public function show($id, Request $request)
    {
        if (Auth::user()->can('manage warehouse') || Auth::user()->can('show warehouse') || Auth::user()->hasRole('Cashier') || Auth::user()->hasRole('company')) {
            $user = Auth::user();

            try {
                $receiptOrderQuery = ReceiptOrder::with(['vendor', 'warehouse', 'fromWarehouse', 'products.product'])
                    ->where('id', $id);

                // إذا كان المستخدم لديه دور company، يمكنه عرض جميع الأوامر
                if (!$user->hasRole('company')) {
                    // للمستخدمين الآخرين، تحقق من الصلاحية
                    $receiptOrderQuery = $receiptOrderQuery->where(function($query) use ($user) {
                        $query->where('created_by', $user->id)
                              ->orWhere('created_by', $user->creatorId());
                    });
                }

                $receiptOrder = $receiptOrderQuery->firstOrFail();

                // البحث عن المنشئ مع معالجة الأخطاء
                $creator = null;
                if ($receiptOrder->created_by) {
                    $creator = User::find($receiptOrder->created_by);
                }

                // إذا لم يوجد المنشئ، استخدم المستخدم الحالي
                if (!$creator) {
                    $creator = $user;
                }

                // إذا كان طلب طباعة، استخدم layout مختلف
                if ($request->get('print') == '1') {
                    return view('receipt_order.print', compact('receiptOrder', 'creator'));
                }

                return view('receipt_order.show', compact('receiptOrder', 'creator'));

            } catch (\Exception $e) {
                \Log::error('Receipt Order Show Error: ' . $e->getMessage());
                return redirect()->back()->with('error', __('أمر الاستلام غير موجود: ') . $e->getMessage());
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }



    /**
     * Store a newly created receipt order.
     */
    public function store(Request $request)
    {
        if (Auth::user()->can('manage warehouse') || Auth::user()->hasRole('Cashier')) {
            $user = Auth::user();

            $validator = \Validator::make($request->all(), [
                'order_type' => 'required|in:استلام بضاعة,نقل بضاعة,أمر إخراج',
                'warehouse_id' => 'required|exists:warehouses,id',
                'products' => 'required|array|min:1',
                'products.*.product_id' => 'required|exists:product_services,id',
                'products.*.quantity' => 'required|numeric|min:0.01',
            ]);

            // Additional validation for exit orders
            if ($request->order_type === 'أمر إخراج') {
                $exitValidator = \Validator::make($request->all(), [
                    'exit_reason' => 'required|in:فقدان,منتهي الصلاحية,تلف/خراب,بيع بالتجزئة',
                    'exit_date' => 'required|date',
                ]);

                if ($exitValidator->fails()) {
                    return redirect()->back()
                        ->withErrors($exitValidator)
                        ->withInput();
                }
            }



            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            DB::beginTransaction();
            try {
                if ($request->order_type === 'استلام بضاعة') {
                    $this->processReceiptOrder($request, $user);
                } elseif ($request->order_type === 'نقل بضاعة') {
                    $this->processTransferOrder($request, $user);
                } else {
                    $this->processExitOrder($request, $user);
                }

                DB::commit();
                return redirect()->route('receipt-order.index')
                    ->with('success', __('تم إنشاء الأمر بنجاح'));

            } catch (\Exception $e) {
                DB::rollback();
                return redirect()->back()
                    ->with('error', __('حدث خطأ: ') . $e->getMessage())
                    ->withInput();
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Process receipt order (استلام بضاعة)
     */
    private function processReceiptOrder($request, $user)
    {
        // Create receipt order record
        $receiptOrder = new ReceiptOrder();
        $receiptOrder->order_number = ReceiptOrder::generateOrderNumber();
        $receiptOrder->order_type = 'استلام بضاعة';
        $receiptOrder->vendor_id = $request->vendor_id;
        $receiptOrder->warehouse_id = $request->warehouse_id;
        $receiptOrder->invoice_number = $request->invoice_number;
        $receiptOrder->invoice_total = $request->invoice_total ?? 0;
        $receiptOrder->invoice_date = $request->invoice_date ?? date('Y-m-d');
        $receiptOrder->has_return = $request->has_return ?? false;
        $receiptOrder->created_by = $user->id;
        $receiptOrder->save();

        $totalAmount = 0;
        $totalProducts = 0;

        foreach ($request->products as $productData) {
            // Create receipt order product
            $receiptOrderProduct = new ReceiptOrderProduct();
            $receiptOrderProduct->receipt_order_id = $receiptOrder->id;
            $receiptOrderProduct->product_id = $productData['product_id'];
            $receiptOrderProduct->quantity = $productData['quantity'];
            $receiptOrderProduct->unit_cost = $productData['unit_cost'] ?? 0;
            $receiptOrderProduct->expiry_date = $productData['expiry_date'] ?? null;
            $receiptOrderProduct->is_return = isset($productData['is_return']) && $productData['is_return'];
            $receiptOrderProduct->save();

            $totalAmount += $receiptOrderProduct->total_cost;
            $totalProducts++;

            // Update warehouse stock (only for non-return items)
            if (!$receiptOrderProduct->is_return) {
                $this->updateWarehouseStock(
                    $request->warehouse_id,
                    $productData['product_id'],
                    $productData['quantity'],
                    'add'
                );
            } else {
                // For return items, subtract from warehouse
                $this->updateWarehouseStock(
                    $request->warehouse_id,
                    $productData['product_id'],
                    $productData['quantity'],
                    'subtract'
                );
            }

            // Update expiry date if provided
            if (isset($productData['expiry_date']) && $productData['expiry_date']) {
                ProductExpiryDate::updateOrCreate(
                    [
                        'product_id' => $productData['product_id'],
                        'warehouse_id' => $request->warehouse_id,
                    ],
                    [
                        'expiry_date' => $productData['expiry_date'],
                        'created_by' => $user->id,
                    ]
                );
            }
        }

        // Update receipt order totals
        $receiptOrder->total_products = $totalProducts;
        $receiptOrder->total_amount = $totalAmount;
        $receiptOrder->save();

        return $receiptOrder;
    }

    /**
     * Process transfer order (نقل بضاعة)
     */
    private function processTransferOrder($request, $user)
    {
        // Create receipt order record for transfer
        $receiptOrder = new ReceiptOrder();
        $receiptOrder->order_number = ReceiptOrder::generateOrderNumber();
        $receiptOrder->order_type = 'نقل بضاعة';
        $receiptOrder->from_warehouse_id = $request->from_warehouse_id;
        $receiptOrder->warehouse_id = $request->warehouse_id;
        $receiptOrder->invoice_date = date('Y-m-d');
        $receiptOrder->created_by = $user->id;
        $receiptOrder->save();

        $totalProducts = 0;

        foreach ($request->products as $productData) {
            // Create receipt order product for transfer
            $receiptOrderProduct = new ReceiptOrderProduct();
            $receiptOrderProduct->receipt_order_id = $receiptOrder->id;
            $receiptOrderProduct->product_id = $productData['product_id'];
            $receiptOrderProduct->quantity = $productData['quantity'];
            $receiptOrderProduct->unit_cost = 0; // No cost for transfers
            $receiptOrderProduct->expiry_date = $productData['expiry_date'] ?? null;
            $receiptOrderProduct->is_return = false;
            $receiptOrderProduct->save();

            $totalProducts++;

            // Create warehouse transfer record for tracking
            $transfer = new WarehouseTransfer();
            $transfer->from_warehouse = $request->from_warehouse_id;
            $transfer->to_warehouse = $request->warehouse_id;
            $transfer->product_id = $productData['product_id'];
            $transfer->quantity = $productData['quantity'];
            $transfer->date = date('Y-m-d');
            $transfer->created_by = $user->id;
            $transfer->save();

            // Update stock in both warehouses
            $this->updateWarehouseStock(
                $request->from_warehouse_id,
                $productData['product_id'],
                $productData['quantity'],
                'subtract'
            );

            $this->updateWarehouseStock(
                $request->warehouse_id,
                $productData['product_id'],
                $productData['quantity'],
                'add'
            );

            // Update expiry date for destination warehouse if provided
            if (isset($productData['expiry_date']) && $productData['expiry_date']) {
                ProductExpiryDate::updateOrCreate(
                    [
                        'product_id' => $productData['product_id'],
                        'warehouse_id' => $request->warehouse_id,
                    ],
                    [
                        'expiry_date' => $productData['expiry_date'],
                        'created_by' => $user->id,
                    ]
                );
            }
        }

        // Update receipt order totals
        $receiptOrder->total_products = $totalProducts;
        $receiptOrder->total_amount = 0; // No monetary value for transfers
        $receiptOrder->save();

        return $receiptOrder;
    }

    /**
     * Update warehouse stock
     */
    private function updateWarehouseStock($warehouseId, $productId, $quantity, $operation)
    {
        $warehouseProduct = WarehouseProduct::where('warehouse_id', $warehouseId)
            ->where('product_id', $productId)
            ->first();

        if (!$warehouseProduct) {
            $warehouseProduct = new WarehouseProduct();
            $warehouseProduct->warehouse_id = $warehouseId;
            $warehouseProduct->product_id = $productId;
            $warehouseProduct->quantity = 0;
            $warehouseProduct->created_by = Auth::user()->id;
        }

        if ($operation === 'add') {
            $warehouseProduct->quantity += $quantity;
        } else {
            $warehouseProduct->quantity = max(0, $warehouseProduct->quantity - $quantity);
        }

        $warehouseProduct->save();
    }

    /**
     * Process exit order (أمر إخراج)
     */
    private function processExitOrder($request, $user)
    {
        // Create receipt order record for exit
        $receiptOrder = new ReceiptOrder();
        $receiptOrder->order_number = ReceiptOrder::generateOrderNumber();
        $receiptOrder->order_type = 'أمر إخراج';
        $receiptOrder->warehouse_id = $request->warehouse_id;
        $receiptOrder->invoice_date = $request->exit_date ?? date('Y-m-d');
        $receiptOrder->notes = $request->exit_notes;
        $receiptOrder->created_by = $user->id;
        $receiptOrder->save();

        $totalProducts = 0;

        foreach ($request->products as $productData) {
            // Create receipt order product for exit
            $receiptOrderProduct = new ReceiptOrderProduct();
            $receiptOrderProduct->receipt_order_id = $receiptOrder->id;
            $receiptOrderProduct->product_id = $productData['product_id'];
            $receiptOrderProduct->quantity = $productData['quantity'];
            $receiptOrderProduct->unit_cost = 0; // No cost for exits
            $receiptOrderProduct->expiry_date = $productData['expiry_date'] ?? null;
            $receiptOrderProduct->is_return = false;
            $receiptOrderProduct->notes = 'إخراج - ' . $request->exit_reason .
                                        ($request->responsible_person ? ' - مسؤول: ' . $request->responsible_person : '');
            $receiptOrderProduct->save();

            $totalProducts++;

            // Subtract from warehouse stock (exit reduces inventory)
            $this->updateWarehouseStock(
                $request->warehouse_id,
                $productData['product_id'],
                $productData['quantity'],
                'subtract'
            );

            // Log exit reason in product expiry dates for tracking
            if ($request->exit_reason === 'منتهي الصلاحية' && isset($productData['expiry_date']) && $productData['expiry_date']) {
                ProductExpiryDate::updateOrCreate(
                    [
                        'product_id' => $productData['product_id'],
                        'warehouse_id' => $request->warehouse_id,
                    ],
                    [
                        'expiry_date' => $productData['expiry_date'],
                        'created_by' => $user->id,
                    ]
                );
            }
        }

        // Update receipt order totals
        $receiptOrder->total_products = $totalProducts;
        $receiptOrder->total_amount = 0; // No monetary value for exits
        $receiptOrder->save();

        return $receiptOrder;
    }
}
