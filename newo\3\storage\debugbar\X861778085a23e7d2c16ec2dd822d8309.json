{"__meta": {"id": "X861778085a23e7d2c16ec2dd822d8309", "datetime": "2025-06-17 05:40:19", "utime": **********.982336, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750138818.028625, "end": **********.982375, "duration": 1.9537498950958252, "duration_str": "1.95s", "measures": [{"label": "Booting", "start": 1750138818.028625, "relative_start": 0, "end": **********.232522, "relative_end": **********.232522, "duration": 1.2038969993591309, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.23255, "relative_start": 1.2039248943328857, "end": **********.982378, "relative_end": 3.0994415283203125e-06, "duration": 0.7498281002044678, "duration_str": "750ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46096720, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.83551, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.862069, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.949082, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.961412, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.45698999999999995, "accumulated_duration_str": "457ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3328319, "duration": 0.01183, "duration_str": "11.83ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 2.589}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.354656, "duration": 0.*****************, "duration_str": "435ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 2.589, "width_percent": 95.289}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8031702, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 97.877, "width_percent": 0.214}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8386178, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 98.092, "width_percent": 0.322}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.865157, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 98.414, "width_percent": 0.282}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.910223, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 98.696, "width_percent": 0.396}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.924809, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 99.092, "width_percent": 0.265}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.931994, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 99.357, "width_percent": 0.315}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.9541101, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 99.672, "width_percent": 0.328}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jKp0sVR1VQfynh75ods55VSOKiwwtvkIeoaTXLdm", "url": "array:1 [\n  \"intended\" => \"http://localhost/receipt-order/1\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-126625386 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-126625386\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1445173889 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1445173889\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2002705769 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2002705769\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-419847485 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750138813754%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxTV0FaRzY5WXl5RnQxQnkraFVyWHc9PSIsInZhbHVlIjoiTHJPUDMrbHg4UkJJYXVXWFJyM2t1U2JuTVZkSDN5WGJOaVRncUlmUmplNXZEVkdRbmhXYnNCajNwSllGcWFqQ2FPandmaDU1YnBLeXlORE5aVlFVNFN5M3Y0NHg4dGcrdy9kbkxhcXBpWDJGNVR2MVU1WWtFWXlDK2Qwd2tQVVY3eDJJT3I5b013TGpsRndBQnRwRWloYTJvbUx3TTdDMXBxcDNvMU1OS05xMlhRWE05TllKSUlET3BNT0grNFFLMHp0Y1ZKY3FKeWVjZ1h5VG84endwcFlEdVFwejFYY3h6Z0J5cSs3QXVCMmJCVU5IQzhpcDFnb0lURzlnN3QrNVJ2d2tKaVB4cmhua0VFV3k5RUNZb3hpTHZDT3pSdzNHZUhmZnB6SmhlK2kybk1Fak1wOXJhejBNaWUwTkdHdkxZelVlcjhuWDA5MllESk5xRWdvMkszaE5QZWFnaDBlZ25RdlFqSjd2YXZxMUJIL1gzQzRjTlFqZDM1eFdaNWZDcTBiZXVURmpLTUlJUVVaM1poTTJJNDFYT3NPYnUrN3hxNUVQeGNERDF6UXd5NitmWi9LQ0lKbGtDcWVSejVCZmVOWWVJMktzSjVxWlBQZHRQQjM4MnZDY0QvdVVRajVHRU90T1RaYkxZOUVMTStLeDF6VFEwamM4UDIvSTZmU0IiLCJtYWMiOiIyNjQ1N2RiODk2NTY0OWY1M2NiMTFmYmY4MjdmN2QxNzEwNTg4MDhlYTdkMjhlNDdiZWQ2YmVhMzUxNGE0ZWZjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImtBbGZVNmRyZG9ESWlXV1JHRXFiemc9PSIsInZhbHVlIjoib1BKekt1N0NKY09HZlRvbCtLdE5KRWRQc3dDR3RCTFRYVW95bmhrK3pRejVlZlRqOGJDamhBT0JBTFVXRkM0WW1zcFQzLzdvNUU0c0ZFLzN2WlpKOFBibFRUd2hmMnIyanUyWjNzTUs0NTJpdFp2czBFWDJLM2lZaW00emNaYXpHOHI0bXl1TmlMdnFJb1k4RjFvbDd0TEdybzZoNkZXR1ZxQUF2blVQUHFTZmdYUGp1emNnU1l1VlNWWHNUVEduV0h0dE9BOWY0VVE1OTRIVGFUZzZiS09TZFB6TGYrbEZMZ054Vi9yUzVFOFdYeEtvREtEWUQ4MmVpZTM1YjhIMWRBNzJhWHNrUXdsWlRrLzJZa3I3L3FBQmV0TEVlaWxNaVd2T3hoaitQQnRuVkpVY2NleEpuOFhQWFNrb3Q1a2ZpV3QzZUlaRDRwRlUwNmc1VDI2VlZNMUFCMkFMR3JrTFV3ak9YdmhpM09HeVl1Z2h6d0dSMFp0eGtEZysxNWMyTkhIekhUVlVpazBRdnR5eFYySUhHQ2xrVjMxSS8xaXZJRkxRc3V2QzE3T2lGVC9CR3lFSUJiM1BZRFBORzBWRER0Q21QNXgyZGN3SDVEV0RuMDJSZGtLRFFndzBmZEFqNzArczBEZnp6UDZsMkxMRitjY0w4LzVsMkNROHJCQ1oiLCJtYWMiOiI3NWIxOTI1ZWJjNGU0NjljNzRjYzdiMGMxYWUwYTJiZWM5NzliYTIzY2NjZmJlZDI2MWU5ODcxOGUwZjQzZWE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-419847485\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1953222366 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jKp0sVR1VQfynh75ods55VSOKiwwtvkIeoaTXLdm</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QIdQuXDvi707mSLKhJ4slCS6tWaUd1jBjJceCKcu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1953222366\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:40:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlMvMjBKUnNNTGtwd2UxMG9nUUJTeFE9PSIsInZhbHVlIjoiTmg5dTFyVDdWM2ZDY1ZhRndhblQyU2RYOU5kNkFYeWJXVnBiaHVVTGk2WFgza3lrclNiQjNGYUVLbTFuWlVnRmJkYTlQdnpSVGdpa0lTbkpBeXQ4VURJekdMa1pyTndudFF1NkNJajFQeVRjMnRqN1B1c3BJMVpGQTJRbW83aHhISUUwRTNnQ0FGQzljU3B1L09GekJndjlhMFdiMnNWMXdUYnBiSGhvRDRPYkFScE0vZXU5Y2J6QkluVUlFa2lwTDVJdnJnTnJOeWx2TkJjUTRVR1ZaaUtQaDY0dXlZZkpkbDUwR2N0NmRISEJnR3c3QUdLM2dwTm1sSkxaRzNCZXltWUFVZElvSXJzMTllVHV1UHFidFRMSEM0RmxPc0h0VDd5TjU0ZUphcDZlRjk5cVk1d3cxSmNTNEh1YVlYRlhwNkJMbmc5Y3hVUTJhWk14bUZlMEtvT3pqOHJtcExDUmhwdnducEZzckUydHpZbHYxZWF0ODhzNFQ4TGtwVzdBdE9OWk81Sk1WT2JFZ1YvY3lZMk5GeTR4aXJNTXBleGtFekhQenpHTlU1YXptWkE5V01XeWxLUjI5WjdQb3FFTlVlQXpoMnU0UHQ4WTBqcDlFTkl0UEl2dHI1UW1VbDVwUVlVbXNxZ0RhQ2hqK1BzQkhiSGRHbDUvY2kyWGJHYVAiLCJtYWMiOiJiYWY1OTIzNGQzOTczZmVkNzFlZWYxZTEyMzIzMDQwN2QxMTEwY2MzMzcxYTJmYjNlM2NhODQxNzQ5ZTg5ZTJkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkVQN2czdU5ybmVZd1RLNXFuWUxMREE9PSIsInZhbHVlIjoicVJVYjJGRk42dW5IaFdyUUV5VWFRMGhtbk5ScXNPblAxOUFrM1NheFQ3anZ6WTFNMlRSQ21EWERhVUdNcDFyaTV4SVpMNS9DdkFkekJMTVdrSXZQMzBUblJVeFNIZVNpY2w0NUZ3N2svNGhLTU10T3ZBbm9XSk11TEI1ejhHaDZ2Qk54VXAwMU1wdmFMNnVSMUJobWhOWk9FVG9VbnlneGFZdHR3REgyUmFhQlVGNlo3WWZXTkdPZWFrWjZxM01LL1lpMURZaXB5UnBqamh0SUVKQ2xKQ1ltYzVPZHhISU8wbThtYTBpUFhrQkVpNzBhVFZHQlJqcE1zT1BFTlN6d1ViWlhuY2xUWG5FaVEzVzRPOGdZSWU4NHhQQ2FPYkRXcEQ0RjFQYXBmeGJpWTdralh2VlFEUm4yMkFDRU1aUFVJUE1yMGU3dDBYL2lOOXE3VjhjZzc3aWxBb1V0RlovYTFxRkQ2VFE5d3c1TFBqMGhIeTB1OC9LcDlOY0xFb09UUjR2MG9KTXp2YzJpdW5TTUZVNGxPN0ljNjFhNm8xYU0ya1IvM0tNV1pnMmU3SHRZeTNTZndJNXNpZURhRklieisyazVqS1h3eGdoWXBjSXRTMFpJNmVUVS9Zdm5RSjJGUHp0U0lzdjZqT09TSWtiaWhJcHdBeUpGSHNtNkFtUk8iLCJtYWMiOiIwMTUwMTY3MmQ0YzUzZjAwNmIyZmU2ZDI4Mjk5YjRlOWMyODg0ZmRhYzdmM2Y5MGEyNTgwYTA5ZjRkYzIzMmY2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlMvMjBKUnNNTGtwd2UxMG9nUUJTeFE9PSIsInZhbHVlIjoiTmg5dTFyVDdWM2ZDY1ZhRndhblQyU2RYOU5kNkFYeWJXVnBiaHVVTGk2WFgza3lrclNiQjNGYUVLbTFuWlVnRmJkYTlQdnpSVGdpa0lTbkpBeXQ4VURJekdMa1pyTndudFF1NkNJajFQeVRjMnRqN1B1c3BJMVpGQTJRbW83aHhISUUwRTNnQ0FGQzljU3B1L09GekJndjlhMFdiMnNWMXdUYnBiSGhvRDRPYkFScE0vZXU5Y2J6QkluVUlFa2lwTDVJdnJnTnJOeWx2TkJjUTRVR1ZaaUtQaDY0dXlZZkpkbDUwR2N0NmRISEJnR3c3QUdLM2dwTm1sSkxaRzNCZXltWUFVZElvSXJzMTllVHV1UHFidFRMSEM0RmxPc0h0VDd5TjU0ZUphcDZlRjk5cVk1d3cxSmNTNEh1YVlYRlhwNkJMbmc5Y3hVUTJhWk14bUZlMEtvT3pqOHJtcExDUmhwdnducEZzckUydHpZbHYxZWF0ODhzNFQ4TGtwVzdBdE9OWk81Sk1WT2JFZ1YvY3lZMk5GeTR4aXJNTXBleGtFekhQenpHTlU1YXptWkE5V01XeWxLUjI5WjdQb3FFTlVlQXpoMnU0UHQ4WTBqcDlFTkl0UEl2dHI1UW1VbDVwUVlVbXNxZ0RhQ2hqK1BzQkhiSGRHbDUvY2kyWGJHYVAiLCJtYWMiOiJiYWY1OTIzNGQzOTczZmVkNzFlZWYxZTEyMzIzMDQwN2QxMTEwY2MzMzcxYTJmYjNlM2NhODQxNzQ5ZTg5ZTJkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkVQN2czdU5ybmVZd1RLNXFuWUxMREE9PSIsInZhbHVlIjoicVJVYjJGRk42dW5IaFdyUUV5VWFRMGhtbk5ScXNPblAxOUFrM1NheFQ3anZ6WTFNMlRSQ21EWERhVUdNcDFyaTV4SVpMNS9DdkFkekJMTVdrSXZQMzBUblJVeFNIZVNpY2w0NUZ3N2svNGhLTU10T3ZBbm9XSk11TEI1ejhHaDZ2Qk54VXAwMU1wdmFMNnVSMUJobWhOWk9FVG9VbnlneGFZdHR3REgyUmFhQlVGNlo3WWZXTkdPZWFrWjZxM01LL1lpMURZaXB5UnBqamh0SUVKQ2xKQ1ltYzVPZHhISU8wbThtYTBpUFhrQkVpNzBhVFZHQlJqcE1zT1BFTlN6d1ViWlhuY2xUWG5FaVEzVzRPOGdZSWU4NHhQQ2FPYkRXcEQ0RjFQYXBmeGJpWTdralh2VlFEUm4yMkFDRU1aUFVJUE1yMGU3dDBYL2lOOXE3VjhjZzc3aWxBb1V0RlovYTFxRkQ2VFE5d3c1TFBqMGhIeTB1OC9LcDlOY0xFb09UUjR2MG9KTXp2YzJpdW5TTUZVNGxPN0ljNjFhNm8xYU0ya1IvM0tNV1pnMmU3SHRZeTNTZndJNXNpZURhRklieisyazVqS1h3eGdoWXBjSXRTMFpJNmVUVS9Zdm5RSjJGUHp0U0lzdjZqT09TSWtiaWhJcHdBeUpGSHNtNkFtUk8iLCJtYWMiOiIwMTUwMTY3MmQ0YzUzZjAwNmIyZmU2ZDI4Mjk5YjRlOWMyODg0ZmRhYzdmM2Y5MGEyNTgwYTA5ZjRkYzIzMmY2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1100238453 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jKp0sVR1VQfynh75ods55VSOKiwwtvkIeoaTXLdm</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://localhost/receipt-order/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1100238453\", {\"maxDepth\":0})</script>\n"}}