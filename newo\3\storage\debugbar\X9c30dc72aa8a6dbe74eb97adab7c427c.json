{"__meta": {"id": "X9c30dc72aa8a6dbe74eb97adab7c427c", "datetime": "2025-06-17 05:40:32", "utime": **********.913889, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750138831.488404, "end": **********.91392, "duration": 1.42551589012146, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1750138831.488404, "relative_start": 0, "end": **********.777472, "relative_end": **********.777472, "duration": 1.2890679836273193, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.777494, "relative_start": 1.2890899181365967, "end": **********.913923, "relative_end": 3.0994415283203125e-06, "duration": 0.1364290714263916, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43731336, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.01727, "accumulated_duration_str": "17.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "guest", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\RedirectIfAuthenticated.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.873462, "duration": 0.01727, "duration_str": "17.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1621501638 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1621501638\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1472555698 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1472555698\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-7696531 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750138821284%7C4%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im9kUWZtZityOEhYZ0o5L0UzMnBOZmc9PSIsInZhbHVlIjoiNVhrYjBlZU1rbVprZkxlQ1NqOG1oNk04KzY5MSs4bERsNnZxZW9YbzBuZWlTNU9zSTZ2bmUwVWQ4RHRLTjFrNzFyZ3N6RURVMC9Benpud01NMCswUWc4ZS95K3BTSWFvNnNUQ2djN3BuYzIrRHcvVWxLMEc1TXRIZEwzRXpkS3JnUkswczdGK0lxK3pacW9JcVdra000WEFZVW56L1AyMWdsckhsMXFrYll4MGZ4UmFxY3dOVTdITmViVFlBMEcwNWhrWXFaYWlBZVBKaytpVW8vZ3N3MmdTcVJPc1Y3MDVoMU1RY2hrWnYxdXdBNk1qdnV0UlRCOXA0TGRhSys5K0RLb2JjL3VybU1lNW5XczZuR2x3aG9WU3FGQ2ZWcjdzLy9zMzZ5MDVhWUk4bHdhYm16Z0xwMm0vYWlUNC8yYTV6VWhYQlR5a3FWc0pIQW04R1Q0c0RJcWl4Tkx5R1dFeXlUSlNudXd1UGtvbElDZ1Z2ekFoTDMwWkRGY3N0STFtSFh6bm4vVzRJUGtscTNsOExTeGprUnFUR21xK0tXNTZ2MzZkS2tLN3VwUytsNHl6R3VhQUhHeTJjZEM5ZEVjSy9KZzFhMFpJL0RnSFdWdTVPaUV3T1kyM3NVd1ZSZ2pHL0dXSVRmVFNkQ08vNWdWT2x0MDBDQy80UkFxbTVFM3QiLCJtYWMiOiI3NDBiNjY4YmE2MTljMTNiMTVmN2JiNGI1YzkxMDAyYWRjMWNiNWRjYWEwODY2OTMzNTE2Yjk3OGFiZTYxNDEyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik1Fb3ZWNVFBVXYwd3RuRzJnUDMwQXc9PSIsInZhbHVlIjoieUZJdm94U2tqNnZ6SkordFNEbmltdVRiZlpiM3Faa0YzY1NpZDVVK3l1UWEwZFpYT01tNjliSG05UCtKZzZUZ2RIWjR0cVRiQ1B2dmhIY2lMSHEvR0cvc3NDbTNsZ0t3NnpQUUt0NU5aUHJFOWlRSXhqaGRveFlSQWpvT3NUdldyTVE2bTlZejJDV0ZqUjBRcXErMjAzdGV5dVZvUkFRUlNYWHBsQWVxd2p3ZWZUd1RXekhvY1BpWHZiYzVvcG9PUnlqRmpwQUVScTgyc1R6T0xXd0RmYUdKMTkydTQ5OUxPZXl0eXNyYVJGd2E3WDRmQmxpWThqKytiWUFvajIwbDFER0dZemFkMmp6UWVSRklyeUVRS0ticldtaDRFSndTY0pJSlBldm43YjVlZHpUK3dRSmU1NnF4R3k1VkFuQTVtaXlzUmdHRHZiVnoya3dlTDNRTzhBc2p3QmU0dXdMYWJQeXRVcW5GYThHR3JXYVkzaThHdStVU3E3ak4zRTJ1OFVJcmlLNGx0MW9DMUF2V3hyV3ZXTHN0ViszNGJkSlgzYkNXbGtjU3RROHVuSDZQYWVRNmlGOXhLaHducnZBcHJVa3djN05aVnQ5SVkrdUkreGN6V2V4cGNDRHRqSnh0Nmd5Q3k2UThqWUw5Zi96ZUFEbytOY0VZZXZlaHNiK3YiLCJtYWMiOiJmYjNmMjNkMWFiOWI2MjBkZTVlYTQ5ZGE2NDI0YTEyM2I3MDQ3NWRiNDE5YThhYzVhZjAwYWFlOWQ5OTkyMGIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-7696531\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1626149666 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MsEpVpyAabX9l6kTbr1IUggTy4SSXp1me81GakZC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1626149666\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1584723541 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:40:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZweVVqbDhYSk4renNqeTBLbnJUYmc9PSIsInZhbHVlIjoiWVNxT1RxQUxwZ0lYR3V1RmpBRGZYWmdKanVzYjFUODVlSFVuY2NBV3FHVXJsQVFpQVpqUCs4RjVjRE1OV1NjTmVSMENxVStEZVZPK0dNVkFET3dkekN6UU9CRzdKbzlxdStmVDFvZGxDT08yd2ZGd3pxUFpIUUZsdjREWHNlUDJMTmtZQVRWMUtBRkttTmJpemZBbWY3aWxwN3J1UVp3enBjNGNxY1dsMTRxbDNFVHBOUFAxalFPVnZYOVhIcnVOQ3J2SjRYL2JnSUpUV2p1c0t4eTJZK3o4Z0hVVnVPM2N0ZTlpSGZzMmZxN0JzeDlvWG5OV0M1d0k1T1FLNy9BZ0ZTMlJPaWc0ZUFYNjlrY3d0cnM5d3lCK1V4bDhMZFhEcVRwQkQ5RUY2NEI5OFEzT3ZQaE5jTGRlVm1zT1FGWG0vWlJMTFlkd1RQMjVkajRBWURPbFN1TStPSGIyMVhSZmg1Y3dQR293c2o3bzQrQkJvWVBoeTY5OGtVWm9GTGRDVTdpNDdEUDkySnRQYWdiYmU2ZGhUTWdVd3VvQUJUeW5NK3VFbXEvbkVsWmNPSmVNM1l5MGgzeGlLOWJBRW5XcE1Ma2VsVUtjSzltUjJ3dlptZUYydWc3VW1EUTRjVXArSXhuR0VsL2tnS05UUStaQU5jalJwY3NvekJRWkZFcnMiLCJtYWMiOiI4MWRlNjI1YmI3ODEzZGJjNzUxYmFmODdkZjkwZmJhMTIxNWQ0ZGIzNTUzNTJiYmNjMTc5ZTExNWQxYTgzYzQ2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjI0ZzRZdnFKU1Z3YU1zSzU5K3E2b2c9PSIsInZhbHVlIjoiTjBLbmJzbUh0NkxVcUtxWWJvbisxWERSSytjMmhBb3phV21ncU5uZGdXUHN0dlJMMlFtU0hJUjZOWDZWeXBKMTVlWmV3MHRBR1kxT0wwT0sxQlpJdXk2dXFJQ29rdWV0blNBQWN6YUsrYyt6ZU9CekV2aHdaT3VHdmZwL3FNelF5UzdISDJEUzMrdTVTS3lmK0JuT0l1MXdock54c1crWk13UzZ1R204eXBHWWtha2RRUC84dlB3RkNiSlVhOGQyVUlLYnNoL05WQjZ2YlNidjNPMFhFR1RxMTdUNTV0QnUxclJhMzVpbi9nZG53TjhZa3BMYVE4S3RjbEo0OU1VelgwclliZWhGaEtxYXBmYXVXaS9xRGdwSXFJRTZiOXhYcndVTEt0VU9UQ09tQ1BCQytwWE1YVHcveXV0cnVGcUJ1cjk2YnFqUFY4MXV6TWpRbXlhZ0hKdmcxTDQ1UVB5dm00eUd2ZEJXdmJTM1RyT2tIZTVmTzBjZ3BldnRyK295OGVQTXNzNkJHcmJ2aG1COHd1YkE2bGN6Q1Ywcm84VjJoUnE4L1hyRlhGbGNUa09ya3NBTkhLdVJhZHpvdUt3UUpoVXdNTHJpdUxCRXdDYVUrQ1A0RFVPY1E3RTV1QzdOOEc2V2ZybVBlelV5YWZWYU1obkdmNDJ0TnVkREJEVHIiLCJtYWMiOiI4MjU5NTJjNmVkMzc3NTFjMDI5MjI4ZDc2NzIyYzJkYTU5ZTc2MDQwNjMxMGFjMzFmZDI3MGNmZTY0MjZiMThlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZweVVqbDhYSk4renNqeTBLbnJUYmc9PSIsInZhbHVlIjoiWVNxT1RxQUxwZ0lYR3V1RmpBRGZYWmdKanVzYjFUODVlSFVuY2NBV3FHVXJsQVFpQVpqUCs4RjVjRE1OV1NjTmVSMENxVStEZVZPK0dNVkFET3dkekN6UU9CRzdKbzlxdStmVDFvZGxDT08yd2ZGd3pxUFpIUUZsdjREWHNlUDJMTmtZQVRWMUtBRkttTmJpemZBbWY3aWxwN3J1UVp3enBjNGNxY1dsMTRxbDNFVHBOUFAxalFPVnZYOVhIcnVOQ3J2SjRYL2JnSUpUV2p1c0t4eTJZK3o4Z0hVVnVPM2N0ZTlpSGZzMmZxN0JzeDlvWG5OV0M1d0k1T1FLNy9BZ0ZTMlJPaWc0ZUFYNjlrY3d0cnM5d3lCK1V4bDhMZFhEcVRwQkQ5RUY2NEI5OFEzT3ZQaE5jTGRlVm1zT1FGWG0vWlJMTFlkd1RQMjVkajRBWURPbFN1TStPSGIyMVhSZmg1Y3dQR293c2o3bzQrQkJvWVBoeTY5OGtVWm9GTGRDVTdpNDdEUDkySnRQYWdiYmU2ZGhUTWdVd3VvQUJUeW5NK3VFbXEvbkVsWmNPSmVNM1l5MGgzeGlLOWJBRW5XcE1Ma2VsVUtjSzltUjJ3dlptZUYydWc3VW1EUTRjVXArSXhuR0VsL2tnS05UUStaQU5jalJwY3NvekJRWkZFcnMiLCJtYWMiOiI4MWRlNjI1YmI3ODEzZGJjNzUxYmFmODdkZjkwZmJhMTIxNWQ0ZGIzNTUzNTJiYmNjMTc5ZTExNWQxYTgzYzQ2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjI0ZzRZdnFKU1Z3YU1zSzU5K3E2b2c9PSIsInZhbHVlIjoiTjBLbmJzbUh0NkxVcUtxWWJvbisxWERSSytjMmhBb3phV21ncU5uZGdXUHN0dlJMMlFtU0hJUjZOWDZWeXBKMTVlWmV3MHRBR1kxT0wwT0sxQlpJdXk2dXFJQ29rdWV0blNBQWN6YUsrYyt6ZU9CekV2aHdaT3VHdmZwL3FNelF5UzdISDJEUzMrdTVTS3lmK0JuT0l1MXdock54c1crWk13UzZ1R204eXBHWWtha2RRUC84dlB3RkNiSlVhOGQyVUlLYnNoL05WQjZ2YlNidjNPMFhFR1RxMTdUNTV0QnUxclJhMzVpbi9nZG53TjhZa3BMYVE4S3RjbEo0OU1VelgwclliZWhGaEtxYXBmYXVXaS9xRGdwSXFJRTZiOXhYcndVTEt0VU9UQ09tQ1BCQytwWE1YVHcveXV0cnVGcUJ1cjk2YnFqUFY4MXV6TWpRbXlhZ0hKdmcxTDQ1UVB5dm00eUd2ZEJXdmJTM1RyT2tIZTVmTzBjZ3BldnRyK295OGVQTXNzNkJHcmJ2aG1COHd1YkE2bGN6Q1Ywcm84VjJoUnE4L1hyRlhGbGNUa09ya3NBTkhLdVJhZHpvdUt3UUpoVXdNTHJpdUxCRXdDYVUrQ1A0RFVPY1E3RTV1QzdOOEc2V2ZybVBlelV5YWZWYU1obkdmNDJ0TnVkREJEVHIiLCJtYWMiOiI4MjU5NTJjNmVkMzc3NTFjMDI5MjI4ZDc2NzIyYzJkYTU5ZTc2MDQwNjMxMGFjMzFmZDI3MGNmZTY0MjZiMThlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1584723541\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1529274337 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1529274337\", {\"maxDepth\":0})</script>\n"}}