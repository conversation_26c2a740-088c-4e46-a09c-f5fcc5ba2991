{"__meta": {"id": "Xbf0ebfbe2c74d4a482122a52598bb6c3", "datetime": "2025-06-17 07:03:23", "utime": **********.247676, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143802.478862, "end": **********.247702, "duration": 0.7688398361206055, "duration_str": "769ms", "measures": [{"label": "Booting", "start": 1750143802.478862, "relative_start": 0, "end": **********.165946, "relative_end": **********.165946, "duration": 0.6870839595794678, "duration_str": "687ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.165958, "relative_start": 0.6870958805084229, "end": **********.247704, "relative_end": 2.1457672119140625e-06, "duration": 0.08174610137939453, "duration_str": "81.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45168856, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00867, "accumulated_duration_str": "8.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.204942, "duration": 0.00679, "duration_str": "6.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 78.316}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.223028, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 78.316, "width_percent": 8.766}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2329068, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.082, "width_percent": 12.918}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1377470673 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1377470673\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1635343843 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1635343843\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-295833286 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-295833286\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1178860597 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=1rkbmmi%7C1750143782541%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im1WMjR5YzlqdHNYeVFEbEozbUc5ekE9PSIsInZhbHVlIjoiL2NJalh4N2FmTlhBbmJMcnRwNzBXNE42WldQWEJvSmVVNitpV3R6ekxObHlsdTkza1JjRldQdG9adFZwaEsyZFE5OEJqdUc3b0lDSlo3dVVoVm5ML2ZwcG1abEFYbzhseVpUbHpsbHVMVExzNnNURzBpWm41RmFVVzN3cURoZVRmKzZGa290bFhhOHozbzJhWmdNZnRsdk55RDhyZER3a1k1c05zOHNpZWRQT0dkZUpMQVlzWEFKMEdrRmR4V2pobGcvdElmcVBpakVkdTJlc1E1T2xnR3NqMi9KeXpyWVNQSTBwRlltLzUwSlNlWXJaOVdOMDhkUUlUaU9aZ2hLanZYdGNzaUwwaHU5VDFCejkzTUlZMFYrM1FQOXVlVkJBbmxvMDdQajJBaENwcVVJMUdCcEpIY3ZGTkczaFZyK0dVcXc3K1RvdnBQNFdVKy9VNklXbkVmUDVOd2l3bnk4Uk1Fa3ZmSGcza0p0Y1FOTWtsV2RvMWFvRjdhekVERFJPZm1VSFpRSHBPeThXdUlmZUdMMjY0azJLYUV3TEpVaXRUNVV0RnFJcUJKc2pSbVlFeXI0a0x6bFhpNzVSNGtVV01xbDEyTkx1RklJTlJVcXRVUUIraVUvWGRtUnk3b05Ya0tXVzJoY2pRdEsvT2U3V3oyQW83KzlIaEFtLzlaWlEiLCJtYWMiOiJiZjY0OTAzMDkwNGUzNDY2ZWI4OTExOTVmZDYyMmZmNGYyNDNlZGEzMjZlZmJiN2UwOTdhYTI3MGFmMDdmZTgxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ilo4SU1vT0lYUVhOeWdiY0w2dGhQd1E9PSIsInZhbHVlIjoiVVVtQTF1T2hkY1lqbGVBNVIyenRVa0pzZ0tPdjZFQUo4em15OWQ3VjArZG1ITUF6MDdyWEtnSFZ2QXd6MlhPWlNxYnl4Q1ZqQ2VYM1pQc21NZUpPWTZ1aGdjZ0hlZHIwRHZtVklDYWQ3QmU2OUI3b1Nhc0plOU9jcnAzYWR2VzQ4NGtnY2NIdFBuQzBtWmVVSGE5bWt5NnhTbkhpeHJNM0VwUlhTNkEyOGJTejFOZ3JPZW94K2tlVGJpb0JtZGcyNDhvQnU1K0FZL3QzdmlNTldMWDlscmxWRXp2akNXZ3g2bXdlRWNSa2JIVkE4UVBNZ0dkYmtoK1Y1eEJ6QVAwRHE4bE12ekhWMVAyQktIVXBrV2JNcEk2bXZ0SCt0Rzg5d09uM1dsN3o0V3hGZitXQVRiR21Fc09mMG9GS2twQnR3M0RudU5ZMC9mZisrNWdVRmpIeVV0S2RGRmk0dmZEUDV3d3dwdGRqZTlPbGdpWU9pdTh3Ui9ZWjFVdWJYZVg2Ulk1b2hoWkxXa1Q2bWtEdTZlSGNWL1A1WjVZZnduRUVodTROYWdjcFlQb3AvRkVmZmt2MXZSdWgvOHIweUFvY2VqOWtic3RKY2hjYzNIR0U3ekJYVXduSkNjK1lIVzJ0anlxVjYrbjJPZmNiSDBRbW9FRUFidUVsMjRudXJveHciLCJtYWMiOiIxYmVmYmE2NmUwY2YyMmJhZDQzMTg2OGFjNjZmNDJlNjhkYzA2NzJiNmEwMGZmMDM5MDA0MzZmMWYzYmM1Mjg2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1178860597\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1867032815 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDWN9h6R9gAIdzZaXBzXfq0WGpauVfrUj70kKWA2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1867032815\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-943930122 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:03:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imh4bFU4M09GUDZtNnRMVGZNOWNMU2c9PSIsInZhbHVlIjoiMDZ0ZXduWndPdlYxUFQyaUFKQ3UwN0NxU3ppL0JrZ0lPb21xaHZrMTZkcW9FaWxlZHdOSjRmbHBRSjAxaDNCUlRCdUZsaG4xem1SWURsZEZBWDBlemx1NjZaVDdVVWhvVmI1c0lXWlltOXdtRWFkaFpwTTUwYTNQWXo5UzhwcVRkK3RIamZ0MlpraFB4Y0ZCV0dHQldHNnh4eGllMDYvcW9BKzlVdC9BeWhDaEFFR2xjaXhuZWx1SHBUZ1BIdWtkN3F0VXBKR1RWdzY5MmZ5V2lVQWZRa2xSR25PLzNOeC9xNS9vWXczdTRTMVRITElnbmxEUFFPRGpnRTBaK3dKd1BzOTJuZ2VhaFdlM3NGdWxidmYzZ3VUdnFzV2ZtOGNnd2RJSnczQkFGZXk4dXM1MjhkNWFzSHVLbWR5c1NIZWNrMTZieGJMMHBGWlhDbEtKS1QrRzVIQm4rbGtYRXZNelpWM2ROcC9lamRyVkVTUmVhNHo0YS8rNmtld1BQcHpkQWlxdk1DV3ZZcUNkVGs2WnpPaDVDR3lDSUNKWnhSc0VHVnA4SWFKUUgrRmFnOHM3cnlteVA1Q3ROTFZNalFaTm9pMHdOaWhpZjZHalc5dUFqNlJja3pKWTl3QjlvUEJIek5IcVI1Z3c1Wm1uQkFodjhnT1ZRTStzMldVODZ3ZEgiLCJtYWMiOiIyNDk0NjM1NmZmNjFmY2ExMmZkNmE1NmMwYTY5M2ZkOWYzNWRjMDE1NDc1ZGYzMWRjODI4MTJjNmVhZDAyNzg3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:03:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IksxL05XdWxFdS90NSs2MllvUmdsd0E9PSIsInZhbHVlIjoiSFcvaWdLUlZzb3p4YThLNEQ1Q210NHROTUNnbkpnam5OVjhCZVFJN0VhK1V2a3pXK3JtY1diZE9BWWlsV3VGNXlrUVpqNENNcWpySkRSVnhiblBqWXcwd0RkaXZlMWFKdTZUWkVNWFlOTkllZHhlLzNrSjJUQW1KTis4OGZrM1ZkUDY0VzBxSFdRcUlOMFp3UjNmRHZWWm1rbEV4TERPOVJGbXU3S1A3V3kwRXBIc012MEFkcGdFMFE1NW1UYXpnT0F2SGhTYjZiV09RSWwwNnlWK3lEaGx5eUVGZGpLNHBKTUdFWi9UT1FoVEdyTy9lMC9KTERFRm91NUtLQkFxeUtNQlNPc2duRkFESzNYRmNrc3lyZ05rQmJQSzkrTkFxc3kvVENqV2pjN1VpbjdYOE1Hc3ZxRXptS0wvYmFFQ0ZFeG9SSVlyR3JTT2M2OG1SWWYrWEhDL2ltc3Q0c2FrcFBYdmdIY0lkLytzaTNOWnZqRkFNejhIN0htSkNYMVg5SUN4ZGZMYk9uZitSb2V3dUxxZ1JaRldGdEs2dWdoWFU0aEM0SC9Wc21ydk43ZXVDSFlqWU1VNWRxUGZrZzZoY0svVk9GVlN4SFJabGNPTzNRZlZ3Y3hURmhIcmdFeXY0czN0aUJXZllsT1J6MWllN1RTMnNUWWZseVAxdlBta2wiLCJtYWMiOiJhODk0NmJhYTc5ZWM3NjA5ZGJhMmUwMjBlODFmMDcwMDQ3ZWNhODM4ZWFjMjFlMzg0NzFlZjIzMjRhZDJkMjY3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:03:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imh4bFU4M09GUDZtNnRMVGZNOWNMU2c9PSIsInZhbHVlIjoiMDZ0ZXduWndPdlYxUFQyaUFKQ3UwN0NxU3ppL0JrZ0lPb21xaHZrMTZkcW9FaWxlZHdOSjRmbHBRSjAxaDNCUlRCdUZsaG4xem1SWURsZEZBWDBlemx1NjZaVDdVVWhvVmI1c0lXWlltOXdtRWFkaFpwTTUwYTNQWXo5UzhwcVRkK3RIamZ0MlpraFB4Y0ZCV0dHQldHNnh4eGllMDYvcW9BKzlVdC9BeWhDaEFFR2xjaXhuZWx1SHBUZ1BIdWtkN3F0VXBKR1RWdzY5MmZ5V2lVQWZRa2xSR25PLzNOeC9xNS9vWXczdTRTMVRITElnbmxEUFFPRGpnRTBaK3dKd1BzOTJuZ2VhaFdlM3NGdWxidmYzZ3VUdnFzV2ZtOGNnd2RJSnczQkFGZXk4dXM1MjhkNWFzSHVLbWR5c1NIZWNrMTZieGJMMHBGWlhDbEtKS1QrRzVIQm4rbGtYRXZNelpWM2ROcC9lamRyVkVTUmVhNHo0YS8rNmtld1BQcHpkQWlxdk1DV3ZZcUNkVGs2WnpPaDVDR3lDSUNKWnhSc0VHVnA4SWFKUUgrRmFnOHM3cnlteVA1Q3ROTFZNalFaTm9pMHdOaWhpZjZHalc5dUFqNlJja3pKWTl3QjlvUEJIek5IcVI1Z3c1Wm1uQkFodjhnT1ZRTStzMldVODZ3ZEgiLCJtYWMiOiIyNDk0NjM1NmZmNjFmY2ExMmZkNmE1NmMwYTY5M2ZkOWYzNWRjMDE1NDc1ZGYzMWRjODI4MTJjNmVhZDAyNzg3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:03:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IksxL05XdWxFdS90NSs2MllvUmdsd0E9PSIsInZhbHVlIjoiSFcvaWdLUlZzb3p4YThLNEQ1Q210NHROTUNnbkpnam5OVjhCZVFJN0VhK1V2a3pXK3JtY1diZE9BWWlsV3VGNXlrUVpqNENNcWpySkRSVnhiblBqWXcwd0RkaXZlMWFKdTZUWkVNWFlOTkllZHhlLzNrSjJUQW1KTis4OGZrM1ZkUDY0VzBxSFdRcUlOMFp3UjNmRHZWWm1rbEV4TERPOVJGbXU3S1A3V3kwRXBIc012MEFkcGdFMFE1NW1UYXpnT0F2SGhTYjZiV09RSWwwNnlWK3lEaGx5eUVGZGpLNHBKTUdFWi9UT1FoVEdyTy9lMC9KTERFRm91NUtLQkFxeUtNQlNPc2duRkFESzNYRmNrc3lyZ05rQmJQSzkrTkFxc3kvVENqV2pjN1VpbjdYOE1Hc3ZxRXptS0wvYmFFQ0ZFeG9SSVlyR3JTT2M2OG1SWWYrWEhDL2ltc3Q0c2FrcFBYdmdIY0lkLytzaTNOWnZqRkFNejhIN0htSkNYMVg5SUN4ZGZMYk9uZitSb2V3dUxxZ1JaRldGdEs2dWdoWFU0aEM0SC9Wc21ydk43ZXVDSFlqWU1VNWRxUGZrZzZoY0svVk9GVlN4SFJabGNPTzNRZlZ3Y3hURmhIcmdFeXY0czN0aUJXZllsT1J6MWllN1RTMnNUWWZseVAxdlBta2wiLCJtYWMiOiJhODk0NmJhYTc5ZWM3NjA5ZGJhMmUwMjBlODFmMDcwMDQ3ZWNhODM4ZWFjMjFlMzg0NzFlZjIzMjRhZDJkMjY3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:03:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-943930122\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-295142870 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-295142870\", {\"maxDepth\":0})</script>\n"}}