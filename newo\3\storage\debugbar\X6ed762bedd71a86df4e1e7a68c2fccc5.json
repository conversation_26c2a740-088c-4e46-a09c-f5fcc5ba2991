{"__meta": {"id": "X6ed762bedd71a86df4e1e7a68c2fccc5", "datetime": "2025-06-16 15:23:26", "utime": **********.816669, "method": "PUT", "uri": "/receipt-order/1", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087403.220831, "end": **********.816712, "duration": 3.5958809852600098, "duration_str": "3.6s", "measures": [{"label": "Booting", "start": 1750087403.220831, "relative_start": 0, "end": **********.231338, "relative_end": **********.231338, "duration": 3.010507106781006, "duration_str": "3.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.231366, "relative_start": 3.0105350017547607, "end": **********.816717, "relative_end": 5.0067901611328125e-06, "duration": 0.5853509902954102, "duration_str": "585ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52019000, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT receipt-order/{receipt_order}", "middleware": "web, verified, auth, XSS, revalidate", "as": "receipt-order.update", "controller": "App\\Http\\Controllers\\ReceiptOrderController@update", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=602\" onclick=\"\">app/Http/Controllers/ReceiptOrderController.php:602-748</a>"}, "queries": {"nb_statements": 24, "nb_failed_statements": 0, "accumulated_duration": 0.03778, "accumulated_duration_str": "37.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.36111, "duration": 0.00726, "duration_str": "7.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 19.217}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.405779, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 19.217, "width_percent": 3.335}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.4727678, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 22.552, "width_percent": 4.791}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.481779, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 27.343, "width_percent": 3.653}, {"sql": "select count(*) as aggregate from `venders` where `id` = '4'", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.530056, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 30.995, "width_percent": 3.494}, {"sql": "select count(*) as aggregate from `warehouses` where `id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.541265, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 34.489, "width_percent": 2.567}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '3'", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.564556, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 37.057, "width_percent": 2.806}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '5'", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.574546, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 39.862, "width_percent": 3.07}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 626}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.595609, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:626", "source": "app/Http/Controllers/ReceiptOrderController.php:626", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=626", "ajax": false, "filename": "ReceiptOrderController.php", "line": "626"}, "connection": "ty", "start_percent": 42.933, "width_percent": 0}, {"sql": "select * from `receipt_orders` where `id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 639}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.597842, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:639", "source": "app/Http/Controllers/ReceiptOrderController.php:639", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=639", "ajax": false, "filename": "ReceiptOrderController.php", "line": "639"}, "connection": "ty", "start_percent": 42.933, "width_percent": 2.991}, {"sql": "select * from `receipt_order_products` where `receipt_order_products`.`receipt_order_id` = 1 and `receipt_order_products`.`receipt_order_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 642}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6134639, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:642", "source": "app/Http/Controllers/ReceiptOrderController.php:642", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=642", "ajax": false, "filename": "ReceiptOrderController.php", "line": "642"}, "connection": "ty", "start_percent": 45.924, "width_percent": 3.282}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 3 limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 478}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 657}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.625095, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:478", "source": "app/Http/Controllers/ReceiptOrderController.php:478", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=478", "ajax": false, "filename": "ReceiptOrderController.php", "line": "478"}, "connection": "ty", "start_percent": 49.206, "width_percent": 2.912}, {"sql": "update `warehouse_products` set `quantity` = 0, `warehouse_products`.`updated_at` = '2025-06-16 15:23:26' where `id` = 2", "type": "query", "params": [], "bindings": ["0", "2025-06-16 15:23:26", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 494}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 657}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6322649, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:494", "source": "app/Http/Controllers/ReceiptOrderController.php:494", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=494", "ajax": false, "filename": "ReceiptOrderController.php", "line": "494"}, "connection": "ty", "start_percent": 52.118, "width_percent": 3.388}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 5 limit 1", "type": "query", "params": [], "bindings": ["8", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 478}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 657}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.64378, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:478", "source": "app/Http/Controllers/ReceiptOrderController.php:478", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=478", "ajax": false, "filename": "ReceiptOrderController.php", "line": "478"}, "connection": "ty", "start_percent": 55.506, "width_percent": 2.409}, {"sql": "update `warehouse_products` set `quantity` = 0, `warehouse_products`.`updated_at` = '2025-06-16 15:23:26' where `id` = 4", "type": "query", "params": [], "bindings": ["0", "2025-06-16 15:23:26", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 494}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 657}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.653238, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:494", "source": "app/Http/Controllers/ReceiptOrderController.php:494", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=494", "ajax": false, "filename": "ReceiptOrderController.php", "line": "494"}, "connection": "ty", "start_percent": 57.914, "width_percent": 3.759}, {"sql": "delete from `receipt_order_products` where `receipt_order_products`.`receipt_order_id` = 1 and `receipt_order_products`.`receipt_order_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 675}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.662939, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:675", "source": "app/Http/Controllers/ReceiptOrderController.php:675", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=675", "ajax": false, "filename": "ReceiptOrderController.php", "line": "675"}, "connection": "ty", "start_percent": 61.673, "width_percent": 4.103}, {"sql": "insert into `receipt_order_products` (`receipt_order_id`, `product_id`, `quantity`, `unit_cost`, `expiry_date`, `is_return`, `notes`, `total_cost`, `updated_at`, `created_at`) values (1, '3', '10.00', '1.00', '2025-06-18 00:00:00', 0, '', 10, '2025-06-16 15:23:26', '2025-06-16 15:23:26')", "type": "query", "params": [], "bindings": ["1", "3", "10.00", "1.00", "2025-06-18 00:00:00", "0", "", "10", "2025-06-16 15:23:26", "2025-06-16 15:23:26"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 690}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.67396, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:690", "source": "app/Http/Controllers/ReceiptOrderController.php:690", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=690", "ajax": false, "filename": "ReceiptOrderController.php", "line": "690"}, "connection": "ty", "start_percent": 65.776, "width_percent": 4.764}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = '3' limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 478}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 698}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.687144, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:478", "source": "app/Http/Controllers/ReceiptOrderController.php:478", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=478", "ajax": false, "filename": "ReceiptOrderController.php", "line": "478"}, "connection": "ty", "start_percent": 70.54, "width_percent": 3.256}, {"sql": "update `warehouse_products` set `quantity` = 10, `warehouse_products`.`updated_at` = '2025-06-16 15:23:26' where `id` = 2", "type": "query", "params": [], "bindings": ["10", "2025-06-16 15:23:26", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 494}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 698}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.698071, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:494", "source": "app/Http/Controllers/ReceiptOrderController.php:494", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=494", "ajax": false, "filename": "ReceiptOrderController.php", "line": "494"}, "connection": "ty", "start_percent": 73.796, "width_percent": 2.885}, {"sql": "select * from `product_expiry_dates` where (`product_id` = '3' and `warehouse_id` = '8') limit 1", "type": "query", "params": [], "bindings": ["3", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 716}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7107172, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:716", "source": "app/Http/Controllers/ReceiptOrderController.php:716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=716", "ajax": false, "filename": "ReceiptOrderController.php", "line": "716"}, "connection": "ty", "start_percent": 76.681, "width_percent": 3.07}, {"sql": "insert into `receipt_order_products` (`receipt_order_id`, `product_id`, `quantity`, `unit_cost`, `expiry_date`, `is_return`, `notes`, `total_cost`, `updated_at`, `created_at`) values (1, '5', '10', '8.00', '2025-06-24 00:00:00', 0, '', 80, '2025-06-16 15:23:26', '2025-06-16 15:23:26')", "type": "query", "params": [], "bindings": ["1", "5", "10", "8.00", "2025-06-24 00:00:00", "0", "", "80", "2025-06-16 15:23:26", "2025-06-16 15:23:26"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 690}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.719095, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:690", "source": "app/Http/Controllers/ReceiptOrderController.php:690", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=690", "ajax": false, "filename": "ReceiptOrderController.php", "line": "690"}, "connection": "ty", "start_percent": 79.751, "width_percent": 4.129}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = '5' limit 1", "type": "query", "params": [], "bindings": ["8", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 478}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 698}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.729488, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:478", "source": "app/Http/Controllers/ReceiptOrderController.php:478", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=478", "ajax": false, "filename": "ReceiptOrderController.php", "line": "478"}, "connection": "ty", "start_percent": 83.88, "width_percent": 3.467}, {"sql": "update `warehouse_products` set `quantity` = 10, `warehouse_products`.`updated_at` = '2025-06-16 15:23:26' where `id` = 4", "type": "query", "params": [], "bindings": ["10", "2025-06-16 15:23:26", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 494}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 698}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.740383, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:494", "source": "app/Http/Controllers/ReceiptOrderController.php:494", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=494", "ajax": false, "filename": "ReceiptOrderController.php", "line": "494"}, "connection": "ty", "start_percent": 87.348, "width_percent": 3.415}, {"sql": "select * from `product_expiry_dates` where (`product_id` = '5' and `warehouse_id` = '8') limit 1", "type": "query", "params": [], "bindings": ["5", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 716}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7511108, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:716", "source": "app/Http/Controllers/ReceiptOrderController.php:716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=716", "ajax": false, "filename": "ReceiptOrderController.php", "line": "716"}, "connection": "ty", "start_percent": 90.762, "width_percent": 3.917}, {"sql": "update `receipt_orders` set `total_amount` = 90, `receipt_orders`.`updated_at` = '2025-06-16 15:23:26' where `id` = 1", "type": "query", "params": [], "bindings": ["90", "2025-06-16 15:23:26", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 732}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.761745, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:732", "source": "app/Http/Controllers/ReceiptOrderController.php:732", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=732", "ajax": false, "filename": "ReceiptOrderController.php", "line": "732"}, "connection": "ty", "start_percent": 94.68, "width_percent": 5.32}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 734}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.790017, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:734", "source": "app/Http/Controllers/ReceiptOrderController.php:734", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=734", "ajax": false, "filename": "ReceiptOrderController.php", "line": "734"}, "connection": "ty", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\WarehouseProduct": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\ReceiptOrderProduct": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FReceiptOrderProduct.php&line=1", "ajax": false, "filename": "ReceiptOrderProduct.php", "line": "?"}}, "App\\Models\\ProductExpiryDate": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductExpiryDate.php&line=1", "ajax": false, "filename": "ProductExpiryDate.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ReceiptOrder": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FReceiptOrder.php&line=1", "ajax": false, "filename": "ReceiptOrder.php", "line": "?"}}}, "count": 11, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1564994790 data-indent-pad=\"  \"><span class=sf-dump-note>manage warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1564994790\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.498908, "xdebug_link": null}]}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order/1/edit\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "تم تحديث أمر الاستلام بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/receipt-order/1", "status_code": "<pre class=sf-dump id=sf-dump-300138310 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-300138310\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-363023856 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-363023856\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2018793993 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>vendor_id</span>\" => \"<span class=sf-dump-str>4</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>invoice_number</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8475</span>\"\n  \"<span class=sf-dump-key>invoice_total</span>\" => \"<span class=sf-dump-str title=\"6 characters\">900.00</span>\"\n  \"<span class=sf-dump-key>invoice_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-08</span>\"\n  \"<span class=sf-dump-key>notes</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>products</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>unit_cost</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1.00</span>\"\n      \"<span class=sf-dump-key>expiry_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-18</span>\"\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n      \"<span class=sf-dump-key>unit_cost</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8.00</span>\"\n      \"<span class=sf-dump-key>expiry_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-24</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2018793993\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1824663885 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">455</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; XSRF-TOKEN=eyJpdiI6IjBRWEI2Y1ZReWxVQmdOK2IxdUh5OHc9PSIsInZhbHVlIjoidGs3V0drOG03ODBzcnkxL3JRWXBkQ3pJcUNIWXVhWGZJZWFmazdRLzdIUEhFL043ekpENXRaVVRFVkN5WnNOMG1kdDgyRjZnRHc5VS9SNVBVRWg4OEFabGNNYlhXWXVydkkyY3RuSm16Z3R0ZWJPaEMzWGNiM2xYcFNXaDBWY2RGWFE1VzB0ejNIeGRCalMrRUE3RkVzUGVFK2pHL1EvMFg2SFZoUk1IRzdHVnhwWGV5R0U5MlpGMHo2VVFTTWJXRXpzQzZVSERGWjl2ODNhYVpqY2xDMkpMS2ovWmJGcGRrc2VGdWxRQzYzcDVwck5NR1UyTDVjYlpDcHBrdWRscndJUTdqaWRmaVdyV3NaY0xOaldtTFB1c2pOejR5b0V0Mnhkanl2empNalNIRGNXUzBKTTBZZzZHcnQ1RnQzVUc1b0k5aWw2anRxTEFVMndkZjhnb1VFTkNJQjhJQ215UWlvS3M3Y1pKN1dUam5JK0p3UEltTjB2Y1FHMEVQTDZ4WGxEYlFHSE42dHVmaUpSbTQ5SS9uR2dtaEpTZ3dpTS92SWJ0dXl1TkpPQUw3R1hxeGZ6RXFyRnhuUGVoazdqeFp5NHh1eGZBZkgvR00vSUZFV0NVOUxBRTkreFJlRkQ0dkN6TWV2VTlXdGRxV3h1SmE3UzNHTlpDd1p2Z0lHVlYiLCJtYWMiOiJmZjczY2ZhNjM1MjBiNzkyYmY1OGViMDliMjc3ZTMwMGI3ZjE0NzVkZTY5ODZjZDlkNjE0OWYwZmE2YzhkYTQ3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkUyUHVieWFEZ1F0TXdHTHBGRGxxd2c9PSIsInZhbHVlIjoicFpXMW1QQmhTTHNNeVlac2U3UDdWVWlySUNQNGRBY0xZRVl3UXdEZWZVamE4M3hSc0xXbXhnd3VQVW80VWFDbXFvTDlMNlN4RlR6dytmSVRjVlFqN01Ia1JoTnpDRHR5eTNWaVE1UTR2Z3RrT1FLRUI1d0lFVE01SVhSak5JNHFmeDNxWG5DTFArS3NXR0V3QlJ2SGJFZFhveTBCMjRpZlRMaHNRblJQSXUxTVg0dDM5RlM0NmdxWm1ZMk9KaCsrbUV3VmkxZUxSM3Q4K09iRVo0T3g4QTE4ZFU3Zld2ZmVZOWF5aU1ybFBIVUJaeU9yMHJIREhleG9SWXFqOVA3aklGRDNlSEFaRytWTEp3MG1xMDdJb3Rvd2FVRXVKcC8wWHBVSXRMRzJHMkxGYmExczJVTjV1Wld4TGY0UC9XVFloNUxpZk9oVkFleEFkc3Awa3BRamdjMkJXbVg4M2ZZRXgzVStWbEdtSTVmVVFVMFBFbUlZM0UrSU5QTnhoNGFhdkFFcHZpVDVOY05TSmpzdEJyTTZzOFA5VEJ6SkUxa0JKc3FTTjlVc0RaWkVnMTVkNXhyQThWS3lwVDlBc1UxUDdMT01aWVR5UHN3QnEwSDFZUVpDRHpleW84UjJoM1VOQ0gxaGNtQlI1ZG1uYzc3U1JSMGpuU1pWaUlqaFM5TFgiLCJtYWMiOiJmYWRiN2Y5ZTc4ZmE5MGY0NGMwMDY2ZDMxZDdhMDg5ZDUwOWE0MzliMjc0YmZmMWU3ZmMyNzVjNmMyNjRjNTAzIiwidGFnIjoiIn0%3D; _clsk=8f5bqk%7C1750087400433%7C9%7C1%7Cl.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1824663885\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-62329662 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzcvmOKIgVMCCvEHD9HLB5trqF9yJ9GcIs8Pndo9</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-62329662\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:23:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost/receipt-order/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imttem1jdU9Sd28vaXo0b0hycUhPalE9PSIsInZhbHVlIjoiNWwzbWM4eXp5S3d5U1dyYzVOWjg3SG9UUmJqdGtpLzFKZkEzTktLT2MwWGRoZldmWVpZUmk4NmhGSmpsSUtaakhpcnRxTHhRTE55azhLYnFLb3RzS04rS1M5YmVFeHNNZWxMZE5jbEk2cjQ1VEtUc3pRYU45UDZ0T04rSkx1ZG5hQWNob1VLNStzM2RvMW84V3UzbUhTS3RGbXFTdkd4Sjh6dVBJSVRLa3dVQmRJU1FOSmVTdnZFRnNiRmJpODEzTGVWUHc5NTVqd09WR296WXBCd1VUd2VPMS8xaWNtNUFXVFVIU2kzL2lqNHRHZTB6TGxrUG5tT2huMDNZTmM4SE5CR1lJcUxIM2hsdmlHYjZUN1ZkcmpvdDRHR2o1TU9CU1ErempKQVlrRnNUbTJGaE5iUnJzOVlXanpFb2lqUUpGcWVsVFdRZEVsZTdBVG5Ba1pmQXhHWUpFUndqQjZRYjc4MTZIOEY0eFJMMUxJSlpnTExLN3dwL3lIYWcxVWZhMFZHemgzTUFHMFZpNXZ6UWpvQTBBSSttOUJURVVleU9kazY4L3kzNGpES1ZCeEdNa01COTdWSFlhYUNRdnk0VnRaa3NxSXBteHJGTll4RUdhc2VYajFpZVlWVS9UVnRrUHNkTHlUYmZQblAvLzhYbncxb1ZGa0d0MVZpUlNOakUiLCJtYWMiOiI0ODBiOTJjMjE2NDRjYWZjYjZlNmZjZDAzNDk1ZGNjM2QyNTkwZDZlYTI5NjZiZTc4NmVmYTBlYWRhMmNkNDU3IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImpEZHhKK1pTU0ZtUGgvRmZNR2E1OFE9PSIsInZhbHVlIjoibEJTS3lydEd5Zm1jYlFVYXlvdWZ3MlZqMzZ0eUNEUEhPckFaSEFUbXZGMUNYczFRT3kvMkNPbVlhV2N6VFZYbFNjcGU0T0VLWDBBdnZ6MkVtbjB4UldRMlNzZHYzTFAra3g3THVVMnZpdFh6UWdwMWVmV013UWtKbWhrOHA2RGJCYzdNbGFNVnhsNlFSYVJhSnBMcURrdjZ3SWZpTlZOOEg1VDRJVUt5Z3Izb1VoWlk2WHBqQWRVdEFCSCtDOG9TV1BjSHFFRWlnd01MMWpOQU1TQlNsKzcvVEc2dG9hSlJBbXpidUw3ZzhaSjBVdXV2YXhLV1hLeGFhK3VCZHIyRkUveVE3RUp5eUZCM1l3QXJ0R1F3dmlLVS9qeGdWZVZsMUVmUlJEeXI2YTdKeDZzU1Z4NlU2bFI4Yzd5N3FWT29lUFB3SmNoK1h3bDNxRTJpSlVTOXgwdWRseHp6YmRoZ1BJTlI4bDVFYytSSnF3YmxhRUY0K2RWcVhtSTFTUCtqQUQzazBXWEE1UGtoVFNOV0Y1Y2FDOHJkUEJwbm9JK2VEYktPbDBmdDFBcjRiSWlnY0lEUjBEOUhMZFhHZlFpcG9QcUFtL2RBc0Q5QU1aSGxZczNmNTFvTENIdEVRTWRkYTNtT0xsT0pKbnVnaENWaE55MnFySWJFQkQzaFpkUUMiLCJtYWMiOiJhNzhhZTQ4YWIzYmVkMTY4YWViM2U3YzRlYzM5NzA0OTYzODM5ZTJiMDhlM2U5ZDgzN2IzZjQ5OGExZWVmYzI2IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imttem1jdU9Sd28vaXo0b0hycUhPalE9PSIsInZhbHVlIjoiNWwzbWM4eXp5S3d5U1dyYzVOWjg3SG9UUmJqdGtpLzFKZkEzTktLT2MwWGRoZldmWVpZUmk4NmhGSmpsSUtaakhpcnRxTHhRTE55azhLYnFLb3RzS04rS1M5YmVFeHNNZWxMZE5jbEk2cjQ1VEtUc3pRYU45UDZ0T04rSkx1ZG5hQWNob1VLNStzM2RvMW84V3UzbUhTS3RGbXFTdkd4Sjh6dVBJSVRLa3dVQmRJU1FOSmVTdnZFRnNiRmJpODEzTGVWUHc5NTVqd09WR296WXBCd1VUd2VPMS8xaWNtNUFXVFVIU2kzL2lqNHRHZTB6TGxrUG5tT2huMDNZTmM4SE5CR1lJcUxIM2hsdmlHYjZUN1ZkcmpvdDRHR2o1TU9CU1ErempKQVlrRnNUbTJGaE5iUnJzOVlXanpFb2lqUUpGcWVsVFdRZEVsZTdBVG5Ba1pmQXhHWUpFUndqQjZRYjc4MTZIOEY0eFJMMUxJSlpnTExLN3dwL3lIYWcxVWZhMFZHemgzTUFHMFZpNXZ6UWpvQTBBSSttOUJURVVleU9kazY4L3kzNGpES1ZCeEdNa01COTdWSFlhYUNRdnk0VnRaa3NxSXBteHJGTll4RUdhc2VYajFpZVlWVS9UVnRrUHNkTHlUYmZQblAvLzhYbncxb1ZGa0d0MVZpUlNOakUiLCJtYWMiOiI0ODBiOTJjMjE2NDRjYWZjYjZlNmZjZDAzNDk1ZGNjM2QyNTkwZDZlYTI5NjZiZTc4NmVmYTBlYWRhMmNkNDU3IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImpEZHhKK1pTU0ZtUGgvRmZNR2E1OFE9PSIsInZhbHVlIjoibEJTS3lydEd5Zm1jYlFVYXlvdWZ3MlZqMzZ0eUNEUEhPckFaSEFUbXZGMUNYczFRT3kvMkNPbVlhV2N6VFZYbFNjcGU0T0VLWDBBdnZ6MkVtbjB4UldRMlNzZHYzTFAra3g3THVVMnZpdFh6UWdwMWVmV013UWtKbWhrOHA2RGJCYzdNbGFNVnhsNlFSYVJhSnBMcURrdjZ3SWZpTlZOOEg1VDRJVUt5Z3Izb1VoWlk2WHBqQWRVdEFCSCtDOG9TV1BjSHFFRWlnd01MMWpOQU1TQlNsKzcvVEc2dG9hSlJBbXpidUw3ZzhaSjBVdXV2YXhLV1hLeGFhK3VCZHIyRkUveVE3RUp5eUZCM1l3QXJ0R1F3dmlLVS9qeGdWZVZsMUVmUlJEeXI2YTdKeDZzU1Z4NlU2bFI4Yzd5N3FWT29lUFB3SmNoK1h3bDNxRTJpSlVTOXgwdWRseHp6YmRoZ1BJTlI4bDVFYytSSnF3YmxhRUY0K2RWcVhtSTFTUCtqQUQzazBXWEE1UGtoVFNOV0Y1Y2FDOHJkUEJwbm9JK2VEYktPbDBmdDFBcjRiSWlnY0lEUjBEOUhMZFhHZlFpcG9QcUFtL2RBc0Q5QU1aSGxZczNmNTFvTENIdEVRTWRkYTNtT0xsT0pKbnVnaENWaE55MnFySWJFQkQzaFpkUUMiLCJtYWMiOiJhNzhhZTQ4YWIzYmVkMTY4YWViM2U3YzRlYzM5NzA0OTYzODM5ZTJiMDhlM2U5ZDgzN2IzZjQ5OGExZWVmYzI2IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"27 characters\">&#1578;&#1605; &#1578;&#1581;&#1583;&#1610;&#1579; &#1571;&#1605;&#1585; &#1575;&#1604;&#1575;&#1587;&#1578;&#1604;&#1575;&#1605; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}