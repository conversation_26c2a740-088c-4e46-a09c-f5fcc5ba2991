{"__meta": {"id": "Xc59f5e5bcde98d19195c2a83b1a8ac15", "datetime": "2025-06-16 15:22:50", "utime": **********.946936, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087369.22519, "end": **********.94698, "duration": 1.721790075302124, "duration_str": "1.72s", "measures": [{"label": "Booting", "start": 1750087369.22519, "relative_start": 0, "end": **********.739822, "relative_end": **********.739822, "duration": 1.514631986618042, "duration_str": "1.51s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.739852, "relative_start": 1.5146620273590088, "end": **********.946985, "relative_end": 5.0067901611328125e-06, "duration": 0.20713305473327637, "duration_str": "207ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45152544, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00878, "accumulated_duration_str": "8.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.859536, "duration": 0.0058, "duration_str": "5.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 66.059}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.895969, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 66.059, "width_percent": 14.123}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.916549, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 80.182, "width_percent": 19.818}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order/1\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1195496540 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1195496540\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-160311831 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-160311831\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1521108447 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1521108447\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost/receipt-order/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087347611%7C6%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IklEelN4S1hSN1ZZbjlETm9EMkRYS2c9PSIsInZhbHVlIjoiNzlheGovSHJZWWIyNEdFYkd1NDh1blBrK0NkaGdmOEdnMDBhdktkRGdKako4RlBMMkp4V1l6clBGNWF1a3BxaGwyamNVbDF2dVBQMXRueElPejFIdXlsb0VjQ1hxMmhPV1ZjYnZUSnoyT28vRUNEM1RKd1A0bnFRcndsVm14WTRtOHF1cHU3Ukl0NXFIZFMyanZraStybHdrK0FPTWVPUzU4S05aMUpTM0hOeTlvQWd3aWwxRlh1Tlk5d1ozN3QxaHkwWEVxRnhOTjdwZnVZU0QwR0JMbkQrSW5HWllNbWw0OVBFOXVkYnpWWWpScE5lYzR1WmE2K0xxalVhWVovc2dTbVYvQXhZZkxndkU1M2g0ZXB3SWxOY1FoVm9ENDlMbVBHSDNDWkNrakZwSStVVkUwcSs0clVPUStlVGhMbytlT3oxelhwNW5NL2crVENJbTlsSzdMTVhndlc4SnBpaHFxYWxDY0thcEVIYXVoVncrOHNqT1JCZ1BVNHhSQlZJeGhlL2lTcEU5TjQwUXJIY2dsckxINE9BaE9WeXJVMHRyRzNEaFdzRkZUSWcxc2d0dzRnU1AzS3ExRVBIS0w0SWFDT0N3aFBVWTdLeUYrVkNRcmRRVVJZZ2tjOGtpVTQzcGw5ZW40SDZGcVBNbUJ2bmhXNEROK0hBZnhabGFOTC8iLCJtYWMiOiJkNjc4MTI2NjdlNjNjNGNjZGQ2M2YzY2QxYTIzYjQwMmQxNGM3NWI2MmJlNDgyNzg0NWM3ZmRhNWU3ODI0ODk3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZESGRpcERSUHhLUENybWNwVi9zNUE9PSIsInZhbHVlIjoibTVDU255Qkh1MzBYaHdVaDlvTC9ybUtMSG45bXRsTjRNWUQ5endYNXhNMkZ2T0lEN1k1cmhLb0FSZU96Z3dyWnV0dXFYT0tlUmQ1NTFQNjlxZUorSkpaMHhESHovajUvSzhrRTVKMHo5TVZtZzVtQXN3bXRYNVNSY3VaQ1M3d0RYMUV6bEVyZ05wTUJIMlE5SGVDcXBlYWZZVGRTMm4zN0lLdkZmQUxlc3djQTJudVdsQ0M5d21kbDk3cjZHeGJGWWNic3lHK2R3bkxxbmFNZERFSnNKM1JxeDBWc3g4Vi9xSFJHMXY5TGV3ZUcveVVjbkkzczZXTnY3eVllcEZsb09aU0pRQml0U00rWkk1QWlZK0w5SkpJSFUzeGxmZjNyaS9FQWlqM0RqWHhiSzFiQW9oQ1RxWjY2YXh3YzRVREtKT1h2alJ1b0lEUDJ6Rm1GOEc0YU9sV1Z2SDhPUWcrbEVKUEV0NzZJNENHd1F5UCtwUm9EelhEd09DUnhsWlowKzIrN2RkeG8zcDBaQkx1SGI5MDF5NW4xSWJlMG1jMGpxWGZOd05YV3VtUmpGQlZENGc2UFRlbjdrUEducjY1Vm05ei9GRExibWRpZllneUluOXBsck1mdytIemJ5b2d0ZjRXRHE3bDdxeW44d1VRcDJrMFNOeTBzSHBlSEFLNCsiLCJtYWMiOiJlNWJjYzY0M2YzMzQ5ZDdlMzMzNzBkZmQyZWZiZWZhNTQ0MTJiY2Y1MGIwZjVjNzczN2ViNTMyZjMzZTU3MjBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzcvmOKIgVMCCvEHD9HLB5trqF9yJ9GcIs8Pndo9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1540395481 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:22:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFCTnZZNEJNL3pQR2FVMy9JSFZIWHc9PSIsInZhbHVlIjoiS1B4YTFJUzZtdE11d0ZxVzRPVVI5WVpQTHVtTUFabDhNSTNIdm9nQmw1UGFrTlRROEttNldmVG1UV1VBSEwxNy8vY3lZUXM0dWNkVTVLZGx1SlgyK1hSbDlPcmxkclFHK1ZnL0lwYzlyalFFMVNZNG1Rc2poSE5VMXQrWWhJai81Q0FLNnppV3FZWGM1eUFOZGdUTVZPeFNEOFdqMnNnQWRKeUhsUGV3c3Y0b2pMbWI5d0hNRGsvb3krZU5zUkFBZDJPY2lmVkU2blJ0bERNWXRxanlkaGVlcEtIWERQcEgxa3gzMlpxeUVqejIwTG8vSllRZUNKWVM5cFRoRU5lZ0JxT2VtM1ZqeitFUDYxV1FGOHNpNHFZSWlOb0ZJcDhqSmg0bXExYTBmRloveUhwb3Q0cGlrVWhRaitKOHlURi9rYlJEVHZyYXdlWHVDZ0ZsSHJkYlB4dG5BdlBHWGxlRjhvYTNLTUZTblRPWnA3UzVPd1BmSGptVkFrUVV3cC9nb2xqTzBhdkRQMVUySnFhaENXNmZja3RpTEFYbFp5Q1YvZXJPZFZKWnlIVlRGdHZYRG54NFpIcjZlMHRaZHZEcXBHc1RjMTVhUDZxcjRjQ2NCb0VHN3IrV2U3RDVxSkpxM2xjcG5ZRFNHcjVSbzlHZDg1N2g1eFJvNTM5VXFBRTUiLCJtYWMiOiJjZjJjN2NiMmEzNDljMzdmNzRlZjlkNzU5YmRkYWIwNTBmMTI4NWFhYmY2M2U3NDc4OTE2NjAxNmVmMTIwMGU3IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:22:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImxNYnIwNEFJN29UZ25uSkhCeTdCdVE9PSIsInZhbHVlIjoieGhLak1GbEtkbTgvZ1pPdXc0UDNRbEp1bFQ1SWUzNzhyU2dlTnJtTjlHcExsakhtaFMwWXlhcDRvSlNHVXFPQ28wN2FOK3hUZTFWRFdQZERYZDZDSTdmV21XZEVUVWYrNFp4TXRIcVhWKzdaTDFqUVViWTA3ZGJFd1JSMkdRRUJPRVptOEFOVVp1UkpUZDZtK3BSVk14QTJmSlc3aXR2TFBmcFBTRy9vNVZJU3JSQ2Y5dGhjbzNEaUlIUXUvMmIraTFURFc4NmhaN3UweG5CMlZ5UDZOWHAxRHNkdlNsbytjcWRabTFZRGNvZ045ZHd6NTFnTkpvYVFzWW91VitzUzNtNU44TElmTE1PWGRxQkR4elN6YWhMclRpbEV3STBmaU9pak1vY1M5VFcxMnNXUnVMSGxDMXlTbHl3NWpHaExYdGtHTDZJeW5IK1YrSUhrZmlaQmFhUVJKMytvQzdSTDAvaUhCYnB3ZUZybkpFVm9rcFhuU0tmMHkzVVpLaWd2ODBZWTljckJ0SCtFSGs5QkhRK29wMkFtSHlkY1BmSlBDbGFqTlVUeHhnTlN2ZzlrWjQ0STVyYUhEUGpXNUxKbUJwQTVQdlc5MXJ4eVQ0b09UaTA2bmdsd2hmSzhnTlFDME9SUEdHUkRlb0ZKYy82Y21MMzhNNlZXNWtMK3FqVEkiLCJtYWMiOiJhZTNjMDU3ZjI3MWY5OWQ2NGUyNTkwZDBjNjBjOTc2M2IzYmE4NmY0NDgyNTFiMGVkZmY3Y2QyNzI5ZTlhOGFiIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:22:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFCTnZZNEJNL3pQR2FVMy9JSFZIWHc9PSIsInZhbHVlIjoiS1B4YTFJUzZtdE11d0ZxVzRPVVI5WVpQTHVtTUFabDhNSTNIdm9nQmw1UGFrTlRROEttNldmVG1UV1VBSEwxNy8vY3lZUXM0dWNkVTVLZGx1SlgyK1hSbDlPcmxkclFHK1ZnL0lwYzlyalFFMVNZNG1Rc2poSE5VMXQrWWhJai81Q0FLNnppV3FZWGM1eUFOZGdUTVZPeFNEOFdqMnNnQWRKeUhsUGV3c3Y0b2pMbWI5d0hNRGsvb3krZU5zUkFBZDJPY2lmVkU2blJ0bERNWXRxanlkaGVlcEtIWERQcEgxa3gzMlpxeUVqejIwTG8vSllRZUNKWVM5cFRoRU5lZ0JxT2VtM1ZqeitFUDYxV1FGOHNpNHFZSWlOb0ZJcDhqSmg0bXExYTBmRloveUhwb3Q0cGlrVWhRaitKOHlURi9rYlJEVHZyYXdlWHVDZ0ZsSHJkYlB4dG5BdlBHWGxlRjhvYTNLTUZTblRPWnA3UzVPd1BmSGptVkFrUVV3cC9nb2xqTzBhdkRQMVUySnFhaENXNmZja3RpTEFYbFp5Q1YvZXJPZFZKWnlIVlRGdHZYRG54NFpIcjZlMHRaZHZEcXBHc1RjMTVhUDZxcjRjQ2NCb0VHN3IrV2U3RDVxSkpxM2xjcG5ZRFNHcjVSbzlHZDg1N2g1eFJvNTM5VXFBRTUiLCJtYWMiOiJjZjJjN2NiMmEzNDljMzdmNzRlZjlkNzU5YmRkYWIwNTBmMTI4NWFhYmY2M2U3NDc4OTE2NjAxNmVmMTIwMGU3IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:22:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImxNYnIwNEFJN29UZ25uSkhCeTdCdVE9PSIsInZhbHVlIjoieGhLak1GbEtkbTgvZ1pPdXc0UDNRbEp1bFQ1SWUzNzhyU2dlTnJtTjlHcExsakhtaFMwWXlhcDRvSlNHVXFPQ28wN2FOK3hUZTFWRFdQZERYZDZDSTdmV21XZEVUVWYrNFp4TXRIcVhWKzdaTDFqUVViWTA3ZGJFd1JSMkdRRUJPRVptOEFOVVp1UkpUZDZtK3BSVk14QTJmSlc3aXR2TFBmcFBTRy9vNVZJU3JSQ2Y5dGhjbzNEaUlIUXUvMmIraTFURFc4NmhaN3UweG5CMlZ5UDZOWHAxRHNkdlNsbytjcWRabTFZRGNvZ045ZHd6NTFnTkpvYVFzWW91VitzUzNtNU44TElmTE1PWGRxQkR4elN6YWhMclRpbEV3STBmaU9pak1vY1M5VFcxMnNXUnVMSGxDMXlTbHl3NWpHaExYdGtHTDZJeW5IK1YrSUhrZmlaQmFhUVJKMytvQzdSTDAvaUhCYnB3ZUZybkpFVm9rcFhuU0tmMHkzVVpLaWd2ODBZWTljckJ0SCtFSGs5QkhRK29wMkFtSHlkY1BmSlBDbGFqTlVUeHhnTlN2ZzlrWjQ0STVyYUhEUGpXNUxKbUJwQTVQdlc5MXJ4eVQ0b09UaTA2bmdsd2hmSzhnTlFDME9SUEdHUkRlb0ZKYy82Y21MMzhNNlZXNWtMK3FqVEkiLCJtYWMiOiJhZTNjMDU3ZjI3MWY5OWQ2NGUyNTkwZDBjNjBjOTc2M2IzYmE4NmY0NDgyNTFiMGVkZmY3Y2QyNzI5ZTlhOGFiIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:22:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1540395481\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://localhost/receipt-order/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}