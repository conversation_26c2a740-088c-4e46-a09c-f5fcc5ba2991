{"__meta": {"id": "X65b76d5531c946dd3e5c6ce69905714d", "datetime": "2025-06-16 15:22:03", "utime": **********.209113, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087321.598515, "end": **********.209153, "duration": 1.610637903213501, "duration_str": "1.61s", "measures": [{"label": "Booting", "start": 1750087321.598515, "relative_start": 0, "end": **********.014644, "relative_end": **********.014644, "duration": 1.4161288738250732, "duration_str": "1.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.014671, "relative_start": 1.4161560535430908, "end": **********.209158, "relative_end": 5.0067901611328125e-06, "duration": 0.1944868564605713, "duration_str": "194ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43915008, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02988, "accumulated_duration_str": "29.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1250021, "duration": 0.02855, "duration_str": "28.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.549}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.168519, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "ty", "start_percent": 95.549, "width_percent": 4.451}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-19188214 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-19188214\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1186006078 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1186006078\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; XSRF-TOKEN=eyJpdiI6IlVCNDh6dzRMSHg1RWRtVngvQy85S0E9PSIsInZhbHVlIjoiM00vVXoyVTJqRWFVbDVaTDZueWU2WCtzdVVxVlc2K3NxUG93Q2pSN3BoQmF5VGpDdmErdFhIaXV1UVJtZ000bld5d05IWHZsY1RUVzFUSEV1MmY4S3Y3RDdUdHFYblROQ24rSVRrMUV6ODJOQ2loWjdvemxiY2hkQkhjNmlVL09DWVhjUGxsU0lyUGx2OWw1dEdGMWJOdHFJTzhUWmZUT0ZsUG45U1pOcTlwckRjMzRPb1czYjEweW94dzRwVktCdXB6a1JXVExDdHZWd3FMWDBOamNGRDFqMERKTmFVeS9EOWtVb2l2SEV5OW9QMTlJZ2taZ1kvUGZabFVsRUhRR3JmZ3ZSeUdQTnZjTmVPbEtvTnpWNzVUa2d3VmRrclZxaEw1eVZZenAvSHZhVUJnOXIvNlZnUmtTYlFERHJpUTVkNXh6ZHpNL25jUmpud3lZbEptUExjejhmUXZoY3Y3OUI4VXIwZ3kzai9BVnU5Z3ZpRlBtL2NtOU1sTGU3UlFrMnorRlE3OVRHa01tdkQ3SmRWRmgyN3UwcnpDWXhMWUIyR2ppT2M5ZThYd2I2Y040aW1uR3RFYjhmSnZvNDBPMTNpazNxU2lFUUdWSitJZ3RESXcvUUFEVktzZmlZNG1LM2tKYmN5dldRMEhBdXM5clRxVTVnbVpBSlhibU44NFkiLCJtYWMiOiI0OTJhM2ZkN2QzZDczYzVlMjg1M2RiODBmNjYzMDE5NTViODkyMTJkYjVmNDg5ZDI5MjY3OWU1N2RlY2I1MWRkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjEvRE4vVG9zalVGZHRGKzgvc0lEK2c9PSIsInZhbHVlIjoiTngrYnhVQ0ZvaWxncHpaZUdKUW5rL092VEZGZ1BSdjBBTGNmZitMUGlhdGoyYnEvRjRLNkhLaVk4U1ZJSVNCTDVSOUJ6Z0ZGaXN1OEs1MDd6Tk90ZkxaN3JWZXl5TW5sTWhHUUJsN0tOYkhKL0dvQy9TMGhHUSthbmh3VHk5S2trRmxlWCs1OG5TTkRRY3NWSXlocW9lSGsrTEorUmhBSXhXUWtEMmVNRm9UUzA5S0JPQzFXMjAwejVuQ09zVExHN0p0WXVKYkovcTY4dmcrcFlhbjBUOHJoR2ttYWlaRjVpNC9Fa2FXWHExa1JSdjh1ZWNOUS80WWlGeEYwNTMvblI4d2ZpWmMzN2dnUXo2QWE0R1B5Tno2WnVyakhBcmdhZnZIeHN1Y2hFY2xmQU5ONytVN25EN3c3djhHaDY3S1RlL2dGbjRQQlB5UkcyZ3JWNWFRQ1RBS2NhY0pvWnVSaUZobE1zS2x1cDU3N25wN3lRRGZjMGxFMzFGajNISU1naVpiV1BHdkFqMkJjdlBmbTFlYnQ5RHJOTDBCME5PRGk0K2ZzblB4Z1ArdEdHUllnMmVtSEdQM3UwV2dYQlk4WWNsVFFrSzFGZGlmM21kOVpyUEFUZU5yMVdmYVVFSUNOZ1l4NXczeVRWWVNQVFlCc29DUy9qQTRIZU9DUmRqVmwiLCJtYWMiOiIxZGIwNmRmMGYxNWMxYjAzNjMwYzU4ZTE1YzYxOTEzN2Y4NDBlMTg4ZmM0MDBjMTY2OGJmNTVjZjgyYTRjMmFlIiwidGFnIjoiIn0%3D; _clsk=8f5bqk%7C1750087320647%7C3%7C1%7Cl.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1181866700 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WjiNcn9cZJfMB3HvNRV2ADeuSdnp1JlxvgqAKjT1</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1181866700\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1493281453 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:22:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZQTEZqbm1LZDZMY2FST3F5eHQwK3c9PSIsInZhbHVlIjoiT2UrUC8rdlJKT0JkbVJpYXVBcDB2TzZxNlN4OVRrVzBYK2pYbUxnb2ZKak4rWGZmcUxCL2lpRTFRRFVVUHh0eEk4YmpsODc1alhXSk9HYktHUmpaeitPRUd0TkJrdTFtZSt3MEVLamZnY2tGNSs5WE5Jdm90YTR1ODdKQkpzSlZiVTlnMmJtTFZLUlpVaVFyR0Q0Qk9TNmUrMHFEUjlwdS9ONWVZYnBWM2VmQkpaK2xTZFAzdzlCRy9VekVsdWtsT08wOXdOd1pIWWFRc2NWN2k4VzUxUXpLY3lsVlFoMXpaa2pUdmFIQ3hPdVZ0ckl1TWRyaVgyZ056WExRMHk0VGkwQ3N5VGpzTFlEVVBMWkRyNmdBQ09iaWlYaHc5dW9JZUk0cExCekplSFhYV1FwRnB4bE9HV0pJNDJjM2EzL1VkMmRkZDRGNVk0TDBObENtSTdIQ1pSVTlwQnljaVJ2TVdCWUhJZnBnajJORmJ2aDZoMnBwbzhmVHZaZjVDUHVPQVNTME83VDF2dmF2VkxEQmFJaENxRFdyMk1OL0lLSDVBckdIc2FlYlkyb0RZbDFsSjNjcXI0Zm1VakR5d0s1em5rRGZDTFFBVU8wTXJhYkNacThIOUxEYVkxRG9QeFlXNkFNaHlWZ0tNTm95cjl0Tm8yMzUrSFNBNlJkQVZ4bTEiLCJtYWMiOiIzYTI2YzE1MDc4MWYzYmFlMDI3YTUzOWY0ZjdlZTVlYjk1Nzk0MDIyMGUxNTg4MjljODcyYzIyNjRiZjkzYTkzIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:22:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlltY2pZMGVFQXNXQkdaclBDbzdBRXc9PSIsInZhbHVlIjoiRE5aSHU1eTEzYSt6T3J6eVY0bHcvU2RWMmNoT29lMWR1RmlHTGxiK0p6OGdaUGhHSnRmRVFLR0xIVTBNbmFRSlV2K2lISnNYZzRWR2d0M1JiR1RyekwyTTMyQVA1UUhRUGwrUVhnSEFDS0ZXM0FQTW42SEFlRGRTb0VTdTlkT0k1YlZlRjNxbGJLT1JZSGl6T0RQeGtiV2k5UTZseElNU1hXdVFMd0kxdWJHYm41elhQSWp2U2NWaHRvRlBOODFGWW1ER213cVVYalJtOHJEZHkvWG9uaERLZ3hNTCthZGd4TU92Y0RnZFJhN0wvN0ZTanMvQkpzYVdZTy9hdUhobUVRclVmV3hPTzZ0Yi9tZWtCMkROaEpUaG4wdnhESXB4b1Jvem1WTFFmeVJuYnV3Nk52UGVHbmx5MXRZZ1dXb0Q5aHpqZHlUVmp3bnlUTkRqdUQzNVM0YWxGbENiMDd4VmRJRDBwak1zSU93N2NCVkxCM0JPYk14Y3VMZ1hHWDlENWxwQmpvbGhtR09YMC9JNHVJQysvK3lFREpHYnB4NXM4TEFBZ1lXRnRzVTJlQm8rcUJ1Y3ZNWlJrVUJTTXlveTVob1JUcGtYYWE5ZUtVZWtRVnNsa0t0Y2JJb2QwUFBwQ0pZbmlsdE1HeldUVHpkNXBaaE11MHdUZ0RwWjI2bXQiLCJtYWMiOiJkYmE5MzZkZTA0ZDU4YjQ3MjBkMzE3NzlhODU1MTQ1OTFjMzAwMDg4MWE4ZWI1MTY5ZjI5ODdiZWEwYzlmYjk0IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:22:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZQTEZqbm1LZDZMY2FST3F5eHQwK3c9PSIsInZhbHVlIjoiT2UrUC8rdlJKT0JkbVJpYXVBcDB2TzZxNlN4OVRrVzBYK2pYbUxnb2ZKak4rWGZmcUxCL2lpRTFRRFVVUHh0eEk4YmpsODc1alhXSk9HYktHUmpaeitPRUd0TkJrdTFtZSt3MEVLamZnY2tGNSs5WE5Jdm90YTR1ODdKQkpzSlZiVTlnMmJtTFZLUlpVaVFyR0Q0Qk9TNmUrMHFEUjlwdS9ONWVZYnBWM2VmQkpaK2xTZFAzdzlCRy9VekVsdWtsT08wOXdOd1pIWWFRc2NWN2k4VzUxUXpLY3lsVlFoMXpaa2pUdmFIQ3hPdVZ0ckl1TWRyaVgyZ056WExRMHk0VGkwQ3N5VGpzTFlEVVBMWkRyNmdBQ09iaWlYaHc5dW9JZUk0cExCekplSFhYV1FwRnB4bE9HV0pJNDJjM2EzL1VkMmRkZDRGNVk0TDBObENtSTdIQ1pSVTlwQnljaVJ2TVdCWUhJZnBnajJORmJ2aDZoMnBwbzhmVHZaZjVDUHVPQVNTME83VDF2dmF2VkxEQmFJaENxRFdyMk1OL0lLSDVBckdIc2FlYlkyb0RZbDFsSjNjcXI0Zm1VakR5d0s1em5rRGZDTFFBVU8wTXJhYkNacThIOUxEYVkxRG9QeFlXNkFNaHlWZ0tNTm95cjl0Tm8yMzUrSFNBNlJkQVZ4bTEiLCJtYWMiOiIzYTI2YzE1MDc4MWYzYmFlMDI3YTUzOWY0ZjdlZTVlYjk1Nzk0MDIyMGUxNTg4MjljODcyYzIyNjRiZjkzYTkzIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:22:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlltY2pZMGVFQXNXQkdaclBDbzdBRXc9PSIsInZhbHVlIjoiRE5aSHU1eTEzYSt6T3J6eVY0bHcvU2RWMmNoT29lMWR1RmlHTGxiK0p6OGdaUGhHSnRmRVFLR0xIVTBNbmFRSlV2K2lISnNYZzRWR2d0M1JiR1RyekwyTTMyQVA1UUhRUGwrUVhnSEFDS0ZXM0FQTW42SEFlRGRTb0VTdTlkT0k1YlZlRjNxbGJLT1JZSGl6T0RQeGtiV2k5UTZseElNU1hXdVFMd0kxdWJHYm41elhQSWp2U2NWaHRvRlBOODFGWW1ER213cVVYalJtOHJEZHkvWG9uaERLZ3hNTCthZGd4TU92Y0RnZFJhN0wvN0ZTanMvQkpzYVdZTy9hdUhobUVRclVmV3hPTzZ0Yi9tZWtCMkROaEpUaG4wdnhESXB4b1Jvem1WTFFmeVJuYnV3Nk52UGVHbmx5MXRZZ1dXb0Q5aHpqZHlUVmp3bnlUTkRqdUQzNVM0YWxGbENiMDd4VmRJRDBwak1zSU93N2NCVkxCM0JPYk14Y3VMZ1hHWDlENWxwQmpvbGhtR09YMC9JNHVJQysvK3lFREpHYnB4NXM4TEFBZ1lXRnRzVTJlQm8rcUJ1Y3ZNWlJrVUJTTXlveTVob1JUcGtYYWE5ZUtVZWtRVnNsa0t0Y2JJb2QwUFBwQ0pZbmlsdE1HeldUVHpkNXBaaE11MHdUZ0RwWjI2bXQiLCJtYWMiOiJkYmE5MzZkZTA0ZDU4YjQ3MjBkMzE3NzlhODU1MTQ1OTFjMzAwMDg4MWE4ZWI1MTY5ZjI5ODdiZWEwYzlmYjk0IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:22:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1493281453\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1111731625 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1111731625\", {\"maxDepth\":0})</script>\n"}}