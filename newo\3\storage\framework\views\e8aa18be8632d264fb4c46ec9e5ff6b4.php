<?php
    use App\Models\Utility;
    $setting = \App\Models\Utility::settings();
    $logo = \App\Models\Utility::get_file('uploads/logo');

    $company_logo = $setting['company_logo_dark'] ?? '';
    $company_logos = $setting['company_logo_light'] ?? '';
    $company_small_logo = $setting['company_small_logo'] ?? '';

    $emailTemplate = \App\Models\EmailTemplate::emailTemplateData();
    $lang = Auth::user()->lang;

    $userPlan = \App\Models\Plan::getPlan(\Auth::user()->show_dashboard());
    // $is_sale_session_new = Auth::user()['is_sale_session_new'] && Auth::user()['type']== 'sales';
    $is_sale_session_new = Auth::user()['is_sale_session_new'] && Auth::user()->can('manage pos');

    // Check if user has specific roles
    $isCashier = Auth::user()->hasRole('Cashier');
    $isDelivery = Auth::user()->hasRole('delivery') || Auth::user()->can('manage delevery');
    $isCompany = Auth::user()->hasRole('company');
    $isHR = Auth::user()->hasRole('Human resources');
    $isAccountant = Auth::user()->hasRole('accountant');
    $isSuperFiesr = Auth::user()->hasRole('SUPER FIESR');
    $isSuperFiesrBig = Auth::user()->hasRole('SUPER FIESR BIG');
    $isPricing = Auth::user()->hasRole('Pricing');

    // التحقق من أن المستخدم لديه دور Pricing فقط (بدون أدوار أخرى)
    $isPricingOnly = $isPricing && !$isCompany && !$isAccountant && !$isSuperFiesr && !$isSuperFiesrBig && !$isCashier && !$isHR && !$isDelivery;

    // Check if user has access to new menu sections
    $hasAccessToNewSections = $isCompany || $isHR || $isAccountant;
?>

<?php if(isset($setting['cust_theme_bg']) && $setting['cust_theme_bg'] == 'on'): ?>
    <nav class="dash-sidebar light-sidebar transprent-bg">
    <?php else: ?>
        <nav class="dash-sidebar light-sidebar">
<?php endif; ?>
<div class="navbar-wrapper">
    <div class="m-header main-logo">
        <a href="#" class="b-brand">
            

            <?php if($setting['cust_darklayout'] && $setting['cust_darklayout'] == 'on'): ?>
                <img src="<?php echo e($logo . '/' . (isset($company_logos) && !empty($company_logos) ? $company_logos : 'logo-dark.png') . '?' . time()); ?>"
                    alt="<?php echo e(config('app.name', 'ERPGo-SaaS')); ?>" class="logo logo-lg">
            <?php else: ?>
                <img src="<?php echo e($logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-light.png') . '?' . time()); ?>"
                    alt="<?php echo e(config('app.name', 'ERPGo-SaaS')); ?>" class="logo logo-lg">
            <?php endif; ?>

        </a>
    </div>
    <div class="navbar-content">
        <?php if(\Auth::user()->type != 'client'): ?>
            <ul class="dash-navbar">
                
                <?php if($isPricingOnly): ?>
                    
                    <li class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'pricing.index' || Request::route()->getName() == 'inventory.management' ? 'active dash-trigger' : ''); ?>">
                        <a href="#!" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-currency-dollar"></i></span>
                            <span class="dash-mtext"><?php echo e(__('التسعير')); ?></span>
                            <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                        </a>
                        <ul class="dash-submenu">
                            
                            <li class="dash-item <?php echo e(Request::route()->getName() == 'pricing.index' ? 'active' : ''); ?>">
                                <a class="dash-link" href="<?php echo e(route('pricing.index')); ?>"><?php echo e(__('إدارة التسعير')); ?></a>
                            </li>

                            
                            <li class="dash-item <?php echo e(Request::route()->getName() == 'inventory.management' ? 'active' : ''); ?>">
                                <a class="dash-link" href="<?php echo e(route('inventory.management')); ?>"><?php echo e(__('إدارة المخزون ومراقبة المخزون')); ?></a>
                            </li>
                        </ul>
                    </li>
                <?php else: ?>
                    
                    <!--------------------- Start Dashboard ----------------------------------->
                    <?php if(Gate::check('show hrm dashboard') ||
                            Gate::check('show project dashboard') ||
                            (Gate::check('show account dashboard') && !$isSuperFiesrBig) ||
                            Gate::check('show crm dashboard') ||
                            (Gate::check('show pos dashboard') && !$isSuperFiesrBig)): ?>
                    <li
                        class="dash-item dash-hasmenu
                                <?php echo e(Request::segment(1) == null ||
                                (!$isSuperFiesrBig && Request::segment(1) == 'account-dashboard') ||
                                (!$isSuperFiesrBig && Request::segment(1) == 'income report') ||
                                (!$isSuperFiesrBig && Request::segment(1) == 'report') ||
                                (!$isSuperFiesrBig && Request::segment(1) == 'reports-monthly-cashflow') ||
                                (!$isSuperFiesrBig && Request::segment(1) == 'reports-quarterly-cashflow') ||
                                Request::segment(1) == 'reports-payroll' ||
                                Request::segment(1) == 'reports-leave' ||
                                Request::segment(1) == 'reports-monthly-attendance' ||
                                Request::segment(1) == 'reports-lead' ||
                                Request::segment(1) == 'reports-deal' ||
                                (!$isSuperFiesrBig && Request::segment(1) == 'pos-dashboard') ||
                                (!$isSuperFiesrBig && Request::segment(1) == 'reports-warehouse') ||
                                (!$isSuperFiesrBig && Request::segment(1) == 'reports-daily-purchase') ||
                                (!$isSuperFiesrBig && Request::segment(1) == 'reports-monthly-purchase') ||
                                (!$isSuperFiesrBig && Request::segment(1) == 'reports-daily-pos') ||
                                (!$isSuperFiesrBig && Request::segment(1) == 'reports-monthly-pos') ||
                                (!$isSuperFiesrBig && Request::segment(1) == 'reports-pos-vs-purchase')
                                    ? 'active dash-trigger'
                                    : ''); ?>">
                        <a href="#!" class="dash-link">
                            <span class="dash-micon">
                                <i class="ti ti-home"></i>
                            </span>
                            <span class="dash-mtext"><?php echo e(__('Dashboard')); ?></span>
                            <span class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                        <ul class="dash-submenu">
                            <?php if($userPlan->account == 1 && Gate::check('show account dashboard') && !$isSuperFiesrBig): ?>
                                <li
                                    class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == null || Request::segment(1) == 'account-dashboard' || Request::segment(1) == 'report' || Request::segment(1) == 'reports-monthly-cashflow' || Request::segment(1) == 'reports-quarterly-cashflow' ? ' active dash-trigger' : ''); ?>">
                                    <a class="dash-link" href="#"><?php echo e(__('Accounting ')); ?><span
                                            class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                    <ul class="dash-submenu">
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('show account dashboard')): ?>
                                            <li
                                                class="dash-item <?php echo e(Request::segment(1) == null || Request::segment(1) == 'account-dashboard' ? ' active' : ''); ?>">
                                                <a class="dash-link"
                                                    href="<?php echo e(route('dashboard')); ?>"><?php echo e(__(' Overview')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if(Gate::check('income report') ||
                                                Gate::check('expense report') ||
                                                Gate::check('income vs expense report') ||
                                                Gate::check('tax report') ||
                                                Gate::check('loss & profit report') ||
                                                Gate::check('invoice report') ||
                                                Gate::check('bill report') ||
                                                Gate::check('stock report') ||
                                                Gate::check('invoice report') ||
                                                Gate::check('manage transaction') ||
                                                Gate::check('statement report')): ?>
                                            <li
                                                class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'report' || Request::segment(1) == 'reports-monthly-cashflow' || Request::segment(1) == 'reports-quarterly-cashflow' ? 'active dash-trigger ' : ''); ?>">
                                                <a class="dash-link" href="#"><?php echo e(__('Reports')); ?><span
                                                        class="dash-arrow"><i
                                                            data-feather="chevron-right"></i></span></a>
                                                <ul class="dash-submenu">
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('statement report')): ?>
                                                        <li
                                                            class="dash-item <?php echo e(Request::route()->getName() == 'report.account.statement' ? ' active' : ''); ?>">
                                                            <a class="dash-link"
                                                                href="<?php echo e(route('report.account.statement')); ?>"><?php echo e(__('Account Statement')); ?></a>
                                                        </li>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('invoice report')): ?>
                                                        <li
                                                            class="dash-item <?php echo e(Request::route()->getName() == 'report.invoice.summary' ? ' active' : ''); ?>">
                                                            <a class="dash-link"
                                                                href="<?php echo e(route('report.invoice.summary')); ?>"><?php echo e(__('Invoice Summary')); ?></a>
                                                        </li>
                                                    <?php endif; ?>
                                                    <li
                                                        class="dash-item <?php echo e(Request::route()->getName() == 'report.sales' ? ' active' : ''); ?>">
                                                        <a class="dash-link"
                                                            href="<?php echo e(route('report.sales')); ?>"><?php echo e(__('Sales Report')); ?></a>
                                                    </li>
                                                    <li
                                                        class="dash-item <?php echo e(Request::route()->getName() == 'report.receivables' ? ' active' : ''); ?>">
                                                        <a class="dash-link"
                                                            href="<?php echo e(route('report.receivables')); ?>"><?php echo e(__('Receivables')); ?></a>
                                                    </li>
                                                    <li
                                                        class="dash-item <?php echo e(Request::route()->getName() == 'report.payables' ? ' active' : ''); ?>">
                                                        <a class="dash-link"
                                                            href="<?php echo e(route('report.payables')); ?>"><?php echo e(__('Payables')); ?></a>
                                                    </li>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('bill report')): ?>
                                                        <li
                                                            class="dash-item <?php echo e(Request::route()->getName() == 'report.bill.summary' ? ' active' : ''); ?>">
                                                            <a class="dash-link"
                                                                href="<?php echo e(route('report.bill.summary')); ?>"><?php echo e(__('Bill Summary')); ?></a>
                                                        </li>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('stock report')): ?>
                                                        <li
                                                            class="dash-item <?php echo e(Request::route()->getName() == 'report.product.stock.report' ? ' active' : ''); ?>">
                                                            <a href="<?php echo e(route('report.product.stock.report')); ?>"
                                                                class="dash-link"><?php echo e(__('Product Stock')); ?></a>
                                                        </li>
                                                    <?php endif; ?>

                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('loss & profit report')): ?>
                                                        <li
                                                            class="dash-item <?php echo e(request()->is('reports-monthly-cashflow') || request()->is('reports-quarterly-cashflow') ? 'active' : ''); ?>">
                                                            <a class="dash-link"
                                                                href="<?php echo e(route('report.monthly.cashflow')); ?>"><?php echo e(__('Cash Flow')); ?></a>
                                                        </li>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage transaction')): ?>
                                                        <li
                                                            class="dash-item <?php echo e(Request::route()->getName() == 'transaction.index' || Request::route()->getName() == 'transfer.create' || Request::route()->getName() == 'transaction.edit' ? ' active' : ''); ?>">
                                                            <a class="dash-link"
                                                                href="<?php echo e(route('transaction.index')); ?>"><?php echo e(__('Transaction')); ?></a>
                                                        </li>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('income report')): ?>
                                                        <li
                                                            class="dash-item <?php echo e(Request::route()->getName() == 'report.income.summary' ? ' active' : ''); ?>">
                                                            <a class="dash-link"
                                                                href="<?php echo e(route('report.income.summary')); ?>"><?php echo e(__('Income Summary')); ?></a>
                                                        </li>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('expense report')): ?>
                                                        <li
                                                            class="dash-item <?php echo e(Request::route()->getName() == 'report.expense.summary' ? ' active' : ''); ?>">
                                                            <a class="dash-link"
                                                                href="<?php echo e(route('report.expense.summary')); ?>"><?php echo e(__('Expense Summary')); ?></a>
                                                        </li>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('income vs expense report')): ?>
                                                        <li
                                                            class="dash-item <?php echo e(Request::route()->getName() == 'report.income.vs.expense.summary' ? ' active' : ''); ?>">
                                                            <a class="dash-link"
                                                                href="<?php echo e(route('report.income.vs.expense.summary')); ?>"><?php echo e(__('Income VS Expense')); ?></a>
                                                        </li>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('tax report')): ?>
                                                        <li
                                                            class="dash-item <?php echo e(Request::route()->getName() == 'report.tax.summary' ? ' active' : ''); ?>">
                                                            <a class="dash-link"
                                                                href="<?php echo e(route('report.tax.summary')); ?>"><?php echo e(__('Tax Summary')); ?></a>
                                                        </li>
                                                    <?php endif; ?>
                                                </ul>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </li>
                            <?php endif; ?>

                            <?php if($userPlan->hrm == 1): ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('show hrm dashboard')): ?>
                                    <li
                                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'hrm-dashboard' || Request::segment(1) == 'reports-payroll' ? ' active dash-trigger' : ''); ?>">
                                        <a class="dash-link" href="#"><?php echo e(__('HRM ')); ?><span class="dash-arrow"><i
                                                    data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            <li
                                                class="dash-item <?php echo e(\Request::route()->getName() == 'hrm.dashboard' ? ' active' : ''); ?>">
                                                <a class="dash-link"
                                                    href="<?php echo e(route('hrm.dashboard')); ?>"><?php echo e(__(' Overview')); ?></a>
                                            </li>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage report')): ?>
                                                <li class="dash-item dash-hasmenu
                                                                    <?php echo e(Request::segment(1) == 'reports-monthly-attendance' ||
                                                                    Request::segment(1) == 'reports-leave' ||
                                                                    Request::segment(1) == 'reports-payroll'
                                                                        ? 'active dash-trigger'
                                                                        : ''); ?>"
                                                    href="#hr-report" data-toggle="collapse" role="button"
                                                    aria-expanded="<?php echo e(Request::segment(1) == 'reports-monthly-attendance' || Request::segment(1) == 'reports-leave' || Request::segment(1) == 'reports-payroll' ? 'true' : 'false'); ?>">
                                                    <a class="dash-link" href="#"><?php echo e(__('Reports')); ?><span
                                                            class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                                    <ul class="dash-submenu">
                                                        <li
                                                            class="dash-item <?php echo e(request()->is('reports-payroll') ? 'active' : ''); ?>">
                                                            <a class="dash-link"
                                                                href="<?php echo e(route('report.payroll')); ?>"><?php echo e(__('Payroll')); ?></a>
                                                        </li>
                                                        <li
                                                            class="dash-item <?php echo e(request()->is('reports-leave') ? 'active' : ''); ?>">
                                                            <a class="dash-link"
                                                                href="<?php echo e(route('report.leave')); ?>"><?php echo e(__('Leave')); ?></a>
                                                        </li>
                                                        <li
                                                            class="dash-item <?php echo e(request()->is('reports-monthly-attendance') ? 'active' : ''); ?>">
                                                            <a class="dash-link"
                                                                href="<?php echo e(route('report.monthly.attendance')); ?>"><?php echo e(__('Monthly Attendance')); ?></a>
                                                        </li>
                                                    </ul>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php if($userPlan->crm == 1): ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('show crm dashboard')): ?>
                                    <li
                                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'crm-dashboard' || Request::segment(1) == 'reports-lead' || Request::segment(1) == 'reports-deal' ? ' active dash-trigger' : ''); ?>">
                                        <a class="dash-link" href="#"><?php echo e(__('CRM')); ?><span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            <li
                                                class="dash-item <?php echo e(\Request::route()->getName() == 'crm.dashboard' ? ' active' : ''); ?>">
                                                <a class="dash-link"
                                                    href="<?php echo e(route('crm.dashboard')); ?>"><?php echo e(__(' Overview')); ?></a>
                                            </li>
                                            <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'reports-lead' || Request::segment(1) == 'reports-deal' ? 'active dash-trigger' : ''); ?>"
                                                href="#crm-report" data-toggle="collapse" role="button"
                                                aria-expanded="<?php echo e(Request::segment(1) == 'reports-lead' || Request::segment(1) == 'reports-deal' ? 'true' : 'false'); ?>">
                                                <a class="dash-link" href="#"><?php echo e(__('Reports')); ?><span
                                                        class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                                <ul class="dash-submenu">
                                                    <li
                                                        class="dash-item <?php echo e(request()->is('reports-lead') ? 'active' : ''); ?>">
                                                        <a class="dash-link"
                                                            href="<?php echo e(route('report.lead')); ?>"><?php echo e(__('Lead')); ?></a>
                                                    </li>
                                                    <li
                                                        class="dash-item <?php echo e(request()->is('reports-deal') ? 'active' : ''); ?>">
                                                        <a class="dash-link"
                                                            href="<?php echo e(route('report.deal')); ?>"><?php echo e(__('Deal')); ?></a>
                                                    </li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php if($userPlan->project == 1): ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('show project dashboard')): ?>
                                    <li
                                        class="dash-item <?php echo e(Request::route()->getName() == 'project.dashboard' ? ' active' : ''); ?>">
                                        <a class="dash-link"
                                            href="<?php echo e(route('project.dashboard')); ?>"><?php echo e(__('Project ')); ?></a>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php if($userPlan->pos == 1 && !$isSuperFiesrBig): ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('show pos dashboard')): ?>
                                    <li
                                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'pos-dashboard' || Request::segment(1) == 'reports-warehouse' || Request::segment(1) == 'reports-daily-purchase' || Request::segment(1) == 'reports-monthly-purchase' || Request::segment(1) == 'reports-daily-pos' || Request::segment(1) == 'reports-monthly-pos' || Request::segment(1) == 'reports-pos-vs-purchase' ? ' active dash-trigger' : ''); ?>">
                                        <a class="dash-link" href="#"><?php echo e(__('POS')); ?><span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            <li
                                                class="dash-item <?php echo e(\Request::route()->getName() == 'pos.dashboard' ? ' active' : ''); ?>">
                                                <a class="dash-link"
                                                    href="<?php echo e(route('pos.dashboard')); ?>"><?php echo e(__(' Overview')); ?></a>
                                            </li>
                                            <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'reports-warehouse' || Request::segment(1) == 'reports-daily-purchase' || Request::segment(1) == 'reports-monthly-purchase' || Request::segment(1) == 'reports-daily-pos' || Request::segment(1) == 'reports-monthly-pos' || Request::segment(1) == 'reports-pos-vs-purchase' ? 'active dash-trigger' : ''); ?>"
                                                href="#crm-report" data-toggle="collapse" role="button"
                                                aria-expanded="<?php echo e(Request::segment(1) == 'reports-warehouse' || Request::segment(1) == 'reports-daily-purchase' || Request::segment(1) == 'reports-monthly-purchase' || Request::segment(1) == 'reports-daily-pos' || Request::segment(1) == 'reports-monthly-pos' || Request::segment(1) == 'reports-pos-vs-purchase' ? 'true' : 'false'); ?>">
                                                <a class="dash-link" href="#"><?php echo e(__('Reports')); ?><span
                                                        class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                                <ul class="dash-submenu">
                                                    <li
                                                        class="dash-item <?php echo e(request()->is('reports-warehouse') ? 'active' : ''); ?>">
                                                        <a class="dash-link"
                                                            href="<?php echo e(route('report.warehouse')); ?>"><?php echo e(__('Warehouse Report')); ?></a>
                                                    </li>
                                                    <li
                                                        class="dash-item <?php echo e(request()->is('reports-daily-purchase') || request()->is('reports-monthly-purchase') ? 'active' : ''); ?>">
                                                        <a class="dash-link"
                                                            href="<?php echo e(route('report.daily.purchase')); ?>"><?php echo e(__('Purchase Daily/Monthly Report')); ?></a>
                                                    </li>
                                                    <li
                                                        class="dash-item <?php echo e(request()->is('reports-daily-pos') || request()->is('reports-monthly-pos') ? 'active' : ''); ?>">
                                                        <a class="dash-link"
                                                            href="<?php echo e(route('report.daily.pos')); ?>"><?php echo e(__('POS Daily/Monthly Report')); ?></a>
                                                    </li>
                                                    <li
                                                        class="dash-item <?php echo e(request()->is('reports-pos-vs-purchase') ? 'active' : ''); ?>">
                                                        <a class="dash-link"
                                                            href="<?php echo e(route('report.pos.vs.purchase')); ?>"><?php echo e(__('Pos VS Purchase Report')); ?></a>
                                                    </li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>

                        </ul>
                    </li>
                <?php endif; ?>
                <!--------------------- End Dashboard ----------------------------------->

                <!--------------------- Start Forms System ----------------------------------->
                
                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'forms' ? 'active dash-trigger' : ''); ?>">
                    <a href="#!" class="dash-link">
                        <span class="dash-micon">
                            <i class="ti ti-file-text"></i>
                        </span>
                        <span class="dash-mtext"><?php echo e(__('النماذج')); ?></span>
                        <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                    </a>
                    <ul class="dash-submenu">
                        
                        <li class="dash-item <?php echo e(Request::route()->getName() == 'forms.index' ? 'active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('forms.index')); ?>"><?php echo e(__('عرض النماذج')); ?></a>
                        </li>

                        
                        <?php if(Auth::user()->type == 'company'): ?>
                            <li class="dash-item <?php echo e(Request::route()->getName() == 'forms.create' ? 'active' : ''); ?>">
                                <a class="dash-link" href="<?php echo e(route('forms.create')); ?>"><?php echo e(__('إنشاء نموذج جديد')); ?></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
                <!--------------------- End Forms System ----------------------------------->

                <!--------------------- Start HRM ----------------------------------->

                <?php if(!empty($userPlan) && $userPlan->hrm == 1): ?>
                    <?php if(Gate::check('manage employee') || Gate::check('manage setsalary')): ?>
                        <li
                            class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'holiday-calender' ||
                            Request::segment(1) == 'leavetype' ||
                            Request::segment(1) == 'leave' ||
                            Request::segment(1) == 'attendanceemployee' ||
                            Request::segment(1) == 'document-upload' ||
                            Request::segment(1) == 'document' ||
                            Request::segment(1) == 'performanceType' ||
                            Request::segment(1) == 'branch' ||
                            Request::segment(1) == 'department' ||
                            Request::segment(1) == 'designation' ||
                            Request::segment(1) == 'employee' ||
                            Request::segment(1) == 'leave_requests' ||
                            Request::segment(1) == 'holidays' ||
                            Request::segment(1) == 'policies' ||
                            Request::segment(1) == 'leave_calender' ||
                            Request::segment(1) == 'award' ||
                            Request::segment(1) == 'transfer' ||
                            Request::segment(1) == 'resignation' ||
                            Request::segment(1) == 'training' ||
                            Request::segment(1) == 'travel' ||
                            Request::segment(1) == 'promotion' ||
                            Request::segment(1) == 'complaint' ||
                            Request::segment(1) == 'warning' ||
                            Request::segment(1) == 'termination' ||
                            Request::segment(1) == 'announcement' ||
                            Request::segment(1) == 'job' ||
                            Request::segment(1) == 'job-application' ||
                            Request::segment(1) == 'candidates-job-applications' ||
                            Request::segment(1) == 'job-onboard' ||
                            Request::segment(1) == 'custom-question' ||
                            Request::segment(1) == 'interview-schedule' ||
                            Request::segment(1) == 'career' ||
                            Request::segment(1) == 'holiday' ||
                            Request::segment(1) == 'setsalary' ||
                            Request::segment(1) == 'payslip' ||
                            Request::segment(1) == 'paysliptype' ||
                            Request::segment(1) == 'company-policy' ||
                            Request::segment(1) == 'job-stage' ||
                            Request::segment(1) == 'job-category' ||
                            Request::segment(1) == 'terminationtype' ||
                            Request::segment(1) == 'awardtype' ||
                            Request::segment(1) == 'trainingtype' ||
                            Request::segment(1) == 'goaltype' ||
                            Request::segment(1) == 'paysliptype' ||
                            Request::segment(1) == 'allowanceoption' ||
                            Request::segment(1) == 'competencies' ||
                            Request::segment(1) == 'loanoption' ||
                            Request::segment(1) == 'deductionoption'
                                ? 'active dash-trigger'
                                : ''); ?>">
                            <a href="#!" class="dash-link">
                                <span class="dash-micon">
                                    <i class="ti ti-user"></i>
                                </span>
                                <span class="dash-mtext">
                                    <?php echo e(__('HRM System')); ?>

                                </span>
                                <span class="dash-arrow">
                                    <i data-feather="chevron-right"></i>
                                </span>
                            </a>
                            <ul class="dash-submenu">
                                <li
                                    class="dash-item  <?php echo e(Request::segment(1) == 'employee' ? 'active dash-trigger' : ''); ?>   ">
                                    <?php if(\Auth::user()->type == 'Employee'): ?>
                                        <?php
                                            $employee = App\Models\Employee::where('user_id', \Auth::user()->id)->first();
                                        ?>
                                        <a class="dash-link"
                                            href="<?php echo e(route('employee.show', \Illuminate\Support\Facades\Crypt::encrypt($employee->id))); ?>"><?php echo e(__('Employee')); ?></a>
                                    <?php else: ?>
                                        <a href="<?php echo e(route('employee.index')); ?>" class="dash-link">
                                            <?php echo e(__('Employee Setup')); ?>

                                        </a>
                                    <?php endif; ?>
                                </li>

                                <?php if(Gate::check('manage set salary') || Gate::check('manage pay slip')): ?>
                                    <li
                                        class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == 'setsalary' || Request::segment(1) == 'payslip' ? 'active dash-trigger' : ''); ?>">
                                        <a class="dash-link" href="#"><?php echo e(__('Payroll Setup')); ?><span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage set salary')): ?>
                                                <li
                                                    class="dash-item <?php echo e(request()->is('setsalary*') ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('setsalary.index')); ?>"><?php echo e(__('Set salary')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage pay slip')): ?>
                                                <li class="dash-item <?php echo e(request()->is('payslip*') ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('payslip.index')); ?>"><?php echo e(__('Payslip')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </li>
                                <?php endif; ?>

                                <?php if(Gate::check('manage leave') || Gate::check('manage attendance')): ?>
                                    <li
                                        class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == 'leave' || Request::segment(1) == 'attendanceemployee' ? 'active dash-trigger' : ''); ?>">
                                        <a class="dash-link" href="#"><?php echo e(__('Leave Management Setup')); ?><span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage leave')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'leave.index' ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('leave.index')); ?>"><?php echo e(__('Manage Leave')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage attendance')): ?>
                                                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'attendanceemployee' ? 'active dash-trigger' : ''); ?>"
                                                    href="#navbar-attendance" data-toggle="collapse" role="button"
                                                    aria-expanded="<?php echo e(Request::segment(1) == 'attendanceemployee' ? 'true' : 'false'); ?>">
                                                    <a class="dash-link" href="#"><?php echo e(__('Attendance')); ?><span
                                                            class="dash-arrow"><i
                                                                data-feather="chevron-right"></i></span></a>
                                                    <ul class="dash-submenu">
                                                        <li
                                                            class="dash-item <?php echo e(Request::route()->getName() == 'attendanceemployee.index' ? 'active' : ''); ?>">
                                                            <a class="dash-link"
                                                                href="<?php echo e(route('attendanceemployee.index')); ?>"><?php echo e(__('Mark Attendance')); ?></a>
                                                        </li>
                                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create attendance')): ?>
                                                            <li
                                                                class="dash-item <?php echo e(Request::route()->getName() == 'attendanceemployee.bulkattendance' ? 'active' : ''); ?>">
                                                                <a class="dash-link"
                                                                    href="<?php echo e(route('attendanceemployee.bulkattendance')); ?>"><?php echo e(__('Bulk Attendance')); ?></a>
                                                            </li>
                                                        <?php endif; ?>
                                                    </ul>
                                                </li>
                                            <?php endif; ?>
                                            
                                        </ul>
                                    </li>
                                <?php endif; ?>

                                

                                

                                

                                <?php if(Gate::check('manage award') ||
                                        Gate::check('manage transfer') ||
                                        Gate::check('manage resignation') ||
                                        Gate::check('manage travel') ||
                                        Gate::check('manage promotion') ||
                                        Gate::check('manage complaint') ||
                                        Gate::check('manage warning') ||
                                        Gate::check('manage termination') ||
                                        Gate::check('manage announcement') ||
                                        Gate::check('manage holiday')): ?>
                                    <li
                                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'holiday-calender' || Request::segment(1) == 'holiday' || Request::segment(1) == 'policies' || Request::segment(1) == 'award' || Request::segment(1) == 'transfer' || Request::segment(1) == 'resignation' || Request::segment(1) == 'travel' || Request::segment(1) == 'promotion' || Request::segment(1) == 'complaint' || Request::segment(1) == 'warning' || Request::segment(1) == 'termination' || Request::segment(1) == 'announcement' || Request::segment(1) == 'competencies' ? 'active dash-trigger' : ''); ?>">
                                        <a class="dash-link" href="#"><?php echo e(__('HR Admin Setup')); ?><span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage award')): ?>
                                                <li class="dash-item <?php echo e(request()->is('award*') ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('award.index')); ?>"><?php echo e(__('Award')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage transfer')): ?>
                                                <li
                                                    class="dash-item  <?php echo e(request()->is('transfer*') ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('transfer.index')); ?>"><?php echo e(__('Transfer')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage resignation')): ?>
                                                <li
                                                    class="dash-item <?php echo e(request()->is('resignation*') ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('resignation.index')); ?>"><?php echo e(__('Resignation')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage travel')): ?>
                                                <li class="dash-item <?php echo e(request()->is('travel*') ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('travel.index')); ?>"><?php echo e(__('Trip')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage promotion')): ?>
                                                <li
                                                    class="dash-item <?php echo e(request()->is('promotion*') ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('promotion.index')); ?>"><?php echo e(__('Promotion')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage complaint')): ?>
                                                <li
                                                    class="dash-item <?php echo e(request()->is('complaint*') ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('complaint.index')); ?>"><?php echo e(__('Complaints')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage warning')): ?>
                                                <li class="dash-item <?php echo e(request()->is('warning*') ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('warning.index')); ?>"><?php echo e(__('Warning')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage termination')): ?>
                                                <li
                                                    class="dash-item <?php echo e(request()->is('termination*') ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('termination.index')); ?>"><?php echo e(__('Termination')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage announcement')): ?>
                                                <li
                                                    class="dash-item <?php echo e(request()->is('announcement*') ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('announcement.index')); ?>"><?php echo e(__('Announcement')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage holiday')): ?>
                                                <li
                                                    class="dash-item <?php echo e(request()->is('holiday*') || request()->is('holiday-calender') ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('holiday.index')); ?>"><?php echo e(__('Holidays')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </li>
                                <?php endif; ?>

                                
                                
                                
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage document')): ?>
                                    <li class="dash-item <?php echo e(request()->is('document-upload*') ? 'active' : ''); ?>">
                                        <a class="dash-link"
                                            href="<?php echo e(route('document-upload.index')); ?>"><?php echo e(__('Document Setup')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage company policy')): ?>
                                    <li class="dash-item <?php echo e(request()->is('company-policy*') ? 'active' : ''); ?>">
                                        <a class="dash-link"
                                            href="<?php echo e(route('company-policy.index')); ?>"><?php echo e(__('Company policy')); ?></a>
                                    </li>
                                <?php endif; ?>

                                <?php if(\Auth::user()->type == 'company' || \Auth::user()->type == 'HR'): ?>
                                    <li
                                        class="dash-item <?php echo e(Request::segment(1) == 'leavetype' ||
                                        Request::segment(1) == 'document' ||
                                        Request::segment(1) == 'performanceType' ||
                                        Request::segment(1) == 'branch' ||
                                        Request::segment(1) == 'department' ||
                                        Request::segment(1) == 'designation' ||
                                        Request::segment(1) == 'job-stage' ||
                                        Request::segment(1) == 'performanceType' ||
                                        Request::segment(1) == 'job-category' ||
                                        Request::segment(1) == 'terminationtype' ||
                                        Request::segment(1) == 'awardtype' ||
                                        Request::segment(1) == 'trainingtype' ||
                                        Request::segment(1) == 'goaltype' ||
                                        Request::segment(1) == 'paysliptype' ||
                                        Request::segment(1) == 'allowanceoption' ||
                                        Request::segment(1) == 'loanoption' ||
                                        Request::segment(1) == 'deductionoption'
                                            ? 'active dash-trigger'
                                            : ''); ?>">
                                        <a class="dash-link"
                                            href="<?php echo e(route('branch.index')); ?>"><?php echo e(__('HRM System Setup')); ?></a>
                                    </li>
                                <?php endif; ?>


                        </ul>
                    </li>
                <?php endif; ?>
            <?php endif; ?>

            <!--------------------- End HRM ----------------------------------->

            <!--------------------- Start Account ----------------------------------->

            <?php if(!empty($userPlan) &&  $userPlan->account == 1 && !$isSuperFiesr && !$isSuperFiesrBig): ?>
                <?php if(Gate::check('manage customer') ||
                        Gate::check('manage vender') ||
                        Gate::check('manage customer') ||
                        Gate::check('manage vender') ||
                        Gate::check('manage proposal') ||
                        Gate::check('manage bank account') ||
                        Gate::check('manage bank transfer') ||
                        Gate::check('manage invoice') ||
                        Gate::check('manage revenue') ||
                        Gate::check('manage credit note') ||
                        Gate::check('manage bill') ||
                        Gate::check('manage payment') ||
                        Gate::check('manage debit note') ||
                        Gate::check('manage chart of account') ||
                        Gate::check('manage journal entry') ||
                        Gate::check('balance sheet report') ||
                        Gate::check('ledger report') ||
                        Gate::check('trial balance report')): ?>
                    <li
                        class="dash-item dash-hasmenu
                                     <?php echo e(Request::route()->getName() == 'print-setting' ||
                                     Request::segment(1) == 'customer' ||
                                     Request::segment(1) == 'vender' ||
                                     Request::segment(1) == 'proposal' ||
                                     Request::segment(1) == 'bank-account' ||
                                     Request::segment(1) == 'bank-transfer' ||
                                     Request::segment(1) == 'invoice' ||
                                     Request::segment(1) == 'revenue' ||
                                     Request::segment(1) == 'credit-note' ||
                                     Request::segment(1) == 'taxes' ||
                                     Request::segment(1) == 'product-category' ||
                                     Request::segment(1) == 'product-unit' ||
                                     Request::segment(1) == 'payment-method' ||
                                     Request::segment(1) == 'custom-field' ||
                                     Request::segment(1) == 'chart-of-account-type' ||
                                     (Request::segment(1) == 'transaction' &&
                                         Request::segment(2) != 'ledger' &&
                                         Request::segment(2) != 'balance-sheet' &&
                                         Request::segment(2) != 'trial-balance') ||
                                     Request::segment(1) == 'goal' ||
                                     Request::segment(1) == 'budget' ||
                                     Request::segment(1) == 'chart-of-account' ||
                                     Request::segment(1) == 'journal-entry' ||
                                     Request::segment(2) == 'ledger' ||
                                     Request::segment(2) == 'balance-sheet' ||
                                     Request::segment(2) == 'trial-balance' ||
                                     Request::segment(2) == 'profit-loss' ||
                                     Request::segment(1) == 'bill' ||
                                     Request::segment(1) == 'expense' ||
                                     Request::segment(1) == 'payment' ||
                                     Request::segment(1) == 'debit-note'
                                         ? ' active dash-trigger'
                                         : ''); ?>">
                        <a href="#!" class="dash-link"><span class="dash-micon"><i
                                    class="ti ti-box"></i></span><span
                                class="dash-mtext"><?php echo e(__('Accounting System ')); ?>

                            </span><span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                        </a>
                        <ul class="dash-submenu">

                            <?php if(Gate::check('manage bank account') || Gate::check('manage bank transfer')): ?>
                                <li
                                    class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'bank-account' || Request::segment(1) == 'bank-transfer' ? 'active dash-trigger' : ''); ?>">
                                    <a class="dash-link" href="#"><?php echo e(__('Banking')); ?><span
                                            class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                    <ul class="dash-submenu">
                                        <li
                                            class="dash-item <?php echo e(Request::route()->getName() == 'bank-account.index' || Request::route()->getName() == 'bank-account.create' || Request::route()->getName() == 'bank-account.edit' ? ' active' : ''); ?>">
                                            <a class="dash-link"
                                                href="<?php echo e(route('bank-account.index')); ?>"><?php echo e(__('Account')); ?></a>
                                        </li>
                                        <li
                                            class="dash-item <?php echo e(Request::route()->getName() == 'bank-transfer.index' || Request::route()->getName() == 'bank-transfer.create' || Request::route()->getName() == 'bank-transfer.edit' ? ' active' : ''); ?>">
                                            <a class="dash-link"
                                                href="<?php echo e(route('bank-transfer.index')); ?>"><?php echo e(__('Transfer')); ?></a>
                                        </li>
                                    </ul>
                                </li>
                            <?php endif; ?>
                            <?php if(Gate::check('manage customer') ||
                                    Gate::check('manage proposal') ||
                                    Gate::check('manage invoice') ||
                                    Gate::check('manage revenue') ||
                                    Gate::check('manage credit note')): ?>
                                <li
                                    class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'customer' || Request::segment(1) == 'proposal' || Request::segment(1) == 'invoice' || Request::segment(1) == 'revenue' || Request::segment(1) == 'credit-note' ? 'active dash-trigger' : ''); ?>">
                                    <a class="dash-link" href="#"><?php echo e(__('Sales')); ?><span
                                            class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                    <ul class="dash-submenu">
                                        <?php if(Gate::check('manage customer')): ?>
                                            <li
                                                class="dash-item <?php echo e(Request::segment(1) == 'customer' ? 'active' : ''); ?>">
                                                <a class="dash-link"
                                                    href="<?php echo e(route('customer.index')); ?>"><?php echo e(__('Customer')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if(Gate::check('manage proposal')): ?>
                                            <li
                                                class="dash-item <?php echo e(Request::segment(1) == 'proposal' ? 'active' : ''); ?>">
                                                <a class="dash-link"
                                                    href="<?php echo e(route('proposal.index')); ?>"><?php echo e(__('Estimate')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <li
                                            class="dash-item <?php echo e(Request::route()->getName() == 'invoice.index' || Request::route()->getName() == 'invoice.create' || Request::route()->getName() == 'invoice.edit' || Request::route()->getName() == 'invoice.show' ? ' active' : ''); ?>">
                                            <a class="dash-link"
                                                href="<?php echo e(route('invoice.index')); ?>"><?php echo e(__('Invoice')); ?></a>
                                        </li>
                                        <li
                                            class="dash-item <?php echo e(Request::route()->getName() == 'revenue.index' || Request::route()->getName() == 'revenue.create' || Request::route()->getName() == 'revenue.edit' ? ' active' : ''); ?>">
                                            <a class="dash-link"
                                                href="<?php echo e(route('revenue.index')); ?>"><?php echo e(__('Revenue')); ?></a>
                                        </li>
                                        <li
                                            class="dash-item <?php echo e(Request::route()->getName() == 'credit.note' ? ' active' : ''); ?>">
                                            <a class="dash-link"
                                                href="<?php echo e(route('credit.note')); ?>"><?php echo e(__('Credit Note')); ?></a>
                                        </li>
                                    </ul>
                                </li>
                            <?php endif; ?>
                            <?php if(Gate::check('manage vender') ||
                                    Gate::check('manage bill') ||
                                    Gate::check('manage payment') ||
                                    Gate::check('manage debit note')): ?>
                                <li
                                    class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'bill' || Request::segment(1) == 'vender' || Request::segment(1) == 'expense' || Request::segment(1) == 'payment' || Request::segment(1) == 'debit-note' ? 'active dash-trigger' : ''); ?>">
                                    <a class="dash-link" href="#"><?php echo e(__('Purchases')); ?><span
                                            class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                    <ul class="dash-submenu">
                                        <?php if(Gate::check('manage vender')): ?>
                                            <li
                                                class="dash-item <?php echo e(Request::segment(1) == 'vender' ? 'active' : ''); ?>">
                                                <a class="dash-link"
                                                    href="<?php echo e(route('vender.index')); ?>"><?php echo e(__('Suppiler')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <li
                                            class="dash-item <?php echo e(Request::route()->getName() == 'bill.index' || Request::route()->getName() == 'bill.create' || Request::route()->getName() == 'bill.edit' || Request::route()->getName() == 'bill.show' ? ' active' : ''); ?>">
                                            <a class="dash-link"
                                                href="<?php echo e(route('bill.index')); ?>"><?php echo e(__('Bill')); ?></a>
                                        </li>
                                        <li
                                            class="dash-item <?php echo e(Request::route()->getName() == 'expense.index' || Request::route()->getName() == 'expense.create' || Request::route()->getName() == 'expense.edit' || Request::route()->getName() == 'expense.show' ? ' active' : ''); ?>">
                                            <a class="dash-link"
                                                href="<?php echo e(route('expense.index')); ?>"><?php echo e(__('Expense')); ?></a>
                                        </li>
                                        <li
                                            class="dash-item <?php echo e(Request::route()->getName() == 'payment.index' || Request::route()->getName() == 'payment.create' || Request::route()->getName() == 'payment.edit' ? ' active' : ''); ?>">
                                            <a class="dash-link"
                                                href="<?php echo e(route('payment.index')); ?>"><?php echo e(__('Payment')); ?></a>
                                        </li>
                                        <li
                                            class="dash-item  <?php echo e(Request::route()->getName() == 'debit.note' ? ' active' : ''); ?>">
                                            <a class="dash-link"
                                                href="<?php echo e(route('debit.note')); ?>"><?php echo e(__('Debit Note')); ?></a>
                                        </li>
                                    </ul>
                                </li>
                            <?php endif; ?>
                            <?php if(Gate::check('manage chart of account') ||
                                    Gate::check('manage journal entry') ||
                                    Gate::check('balance sheet report') ||
                                    Gate::check('ledger report') ||
                                    Gate::check('trial balance report')): ?>
                                <li
                                    class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'chart-of-account' ||
                                    Request::segment(1) == 'journal-entry' ||
                                    Request::segment(2) == 'profit-loss' ||
                                    Request::segment(2) == 'ledger' ||
                                    Request::segment(2) == 'balance-sheet' ||
                                    Request::segment(2) == 'trial-balance'
                                        ? 'active dash-trigger'
                                        : ''); ?>">
                                    <a class="dash-link" href="#"><?php echo e(__('Double Entry')); ?><span
                                            class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                    <ul class="dash-submenu">
                                        <li
                                            class="dash-item <?php echo e(Request::route()->getName() == 'chart-of-account.index' || Request::route()->getName() == 'chart-of-account.show' ? ' active' : ''); ?>">
                                            <a class="dash-link"
                                                href="<?php echo e(route('chart-of-account.index')); ?>"><?php echo e(__('Chart of Accounts')); ?></a>
                                        </li>
                                        <li
                                            class="dash-item <?php echo e(Request::route()->getName() == 'journal-entry.edit' ||
                                            Request::route()->getName() == 'journal-entry.create' ||
                                            Request::route()->getName() == 'journal-entry.index' ||
                                            Request::route()->getName() == 'journal-entry.show'
                                                ? ' active'
                                                : ''); ?>">
                                            <a class="dash-link"
                                                href="<?php echo e(route('journal-entry.index')); ?>"><?php echo e(__('Journal Account')); ?></a>
                                        </li>
                                        <li
                                            class="dash-item <?php echo e(Request::route()->getName() == 'report.ledger' ? ' active' : ''); ?>">
                                            <a class="dash-link"
                                                href="<?php echo e(route('report.ledger', 0)); ?>"><?php echo e(__('Ledger Summary')); ?></a>
                                        </li>
                                        <li
                                            class="dash-item <?php echo e(Request::route()->getName() == 'report.balance.sheet' ? ' active' : ''); ?>">
                                            <a class="dash-link"
                                                href="<?php echo e(route('report.balance.sheet')); ?>"><?php echo e(__('Balance Sheet')); ?></a>
                                        </li>
                                        <li
                                            class="dash-item <?php echo e(Request::route()->getName() == 'report.profit.loss' ? ' active' : ''); ?>">
                                            <a class="dash-link"
                                                href="<?php echo e(route('report.profit.loss')); ?>"><?php echo e(__('Profit & Loss')); ?></a>
                                        </li>

                                        <li
                                            class="dash-item <?php echo e(Request::route()->getName() == 'trial.balance' ? ' active' : ''); ?>">
                                            <a class="dash-link"
                                                href="<?php echo e(route('trial.balance')); ?>"><?php echo e(__('Trial Balance')); ?></a>
                                        </li>
                                    </ul>
                                </li>
                            <?php endif; ?>
                            <?php if(\Auth::user()->type == 'company'): ?>
                                <li class="dash-item <?php echo e(Request::segment(1) == 'budget' ? 'active' : ''); ?>">
                                    <a class="dash-link"
                                        href="<?php echo e(route('budget.index')); ?>"><?php echo e(__('Budget Planner')); ?></a>
                                </li>
                            <?php endif; ?>
                            <?php if(Gate::check('manage goal')): ?>
                                <li class="dash-item <?php echo e(Request::segment(1) == 'goal' ? 'active' : ''); ?>">
                                    <a class="dash-link"
                                        href="<?php echo e(route('goal.index')); ?>"><?php echo e(__('Financial Goal')); ?></a>
                                </li>
                            <?php endif; ?>
                            <?php if(Gate::check('manage constant tax') ||
                                    Gate::check('manage constant category') ||
                                    Gate::check('manage constant unit') ||
                                    Gate::check('manage constant payment method') ||
                                    Gate::check('manage constant custom field')): ?>
                                <li
                                    class="dash-item <?php echo e(Request::segment(1) == 'taxes' || Request::segment(1) == 'product-category' || Request::segment(1) == 'product-unit' || Request::segment(1) == 'payment-method' || Request::segment(1) == 'custom-field' || Request::segment(1) == 'chart-of-account-type' ? 'active dash-trigger' : ''); ?>">
                                    <a class="dash-link"
                                        href="<?php echo e(route('taxes.index')); ?>"><?php echo e(__('Accounting Setup')); ?></a>
                                </li>
                            <?php endif; ?>

                            <?php if(Gate::check('manage print settings') && !$isCashier): ?>
                                <li
                                    class="dash-item <?php echo e(Request::route()->getName() == 'print-setting' ? ' active' : ''); ?>">
                                    <a class="dash-link"
                                        href="<?php echo e(route('print.setting')); ?>"><?php echo e(__('Print Settings')); ?></a>
                                </li>
                            <?php endif; ?>

                        </ul>
                    </li>
                <?php endif; ?>
            <?php endif; ?>

            <!--------------------- End Account ----------------------------------->

            <!--------------------- Start CRM ----------------------------------->

            

        <!--------------------- End CRM ----------------------------------->

        <!--------------------- Start Project ----------------------------------->

        

        <!--------------------- End Project ----------------------------------->



        <!--------------------- Start User Managaement System ----------------------------------->

        <?php if(
            \Auth::user()->type != 'super admin' &&
                (Gate::check('manage user') || Gate::check('manage role') || Gate::check('manage client'))): ?>
            <li
                class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'users' ||
                Request::segment(1) == 'roles' ||
                Request::segment(1) == 'clients' ||
                Request::segment(1) == 'userlogs'
                    ? ' active dash-trigger'
                    : ''); ?>">

                <a href="#!" class="dash-link"><span class="dash-micon"><i
                            class="ti ti-users"></i></span><span
                        class="dash-mtext"><?php echo e(__('User Management')); ?></span><span class="dash-arrow"><i
                            data-feather="chevron-right"></i></span></a>
                <ul class="dash-submenu">
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage user')): ?>
                        <li
                            class="dash-item <?php echo e(Request::route()->getName() == 'users.index' || Request::route()->getName() == 'users.create' || Request::route()->getName() == 'users.edit' || Request::route()->getName() == 'user.userlog' ? ' active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('users.index')); ?>"><?php echo e(__('User')); ?></a>
                        </li>
                    <?php endif; ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage role')): ?>

                        <li
                            class="dash-item <?php echo e(Request::route()->getName() == 'roles.index' || Request::route()->getName() == 'roles.create' || Request::route()->getName() == 'roles.edit' ? ' active' : ''); ?> ">
                            <a class="dash-link" href="<?php echo e(route('roles.index')); ?>"><?php echo e(__('Role')); ?></a>
                        </li>
                    <?php endif; ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage client')): ?>
                        <li
                            class="dash-item <?php echo e(Request::route()->getName() == 'clients.index' || Request::segment(1) == 'clients' || Request::route()->getName() == 'clients.edit' ? ' active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('clients.index')); ?>"><?php echo e(__('Client')); ?></a>
                        </li>
                    <?php endif; ?>
                    
                    
                    
                    
                    
                </ul>
            </li>
        <?php endif; ?>

        <!--------------------- End User Managaement System----------------------------------->


        <!--------------------- Start Products System ----------------------------------->

        <?php if((Gate::check('manage product & service') || Gate::check('manage product & service')) && !$isCashier && !$isSuperFiesr && !$isSuperFiesrBig): ?>
            <li class="dash-item dash-hasmenu">
                <a href="#!" class="dash-link">
                    <span class="dash-micon"><i class="ti ti-shopping-cart"></i></span><span
                        class="dash-mtext"><?php echo e(__('Products System')); ?></span><span class="dash-arrow">
                        <i data-feather="chevron-right"></i></span>
                </a>
                <ul class="dash-submenu">
                    <?php if(Gate::check('manage product & service')): ?>
                        <li class="dash-item <?php echo e(Request::segment(1) == 'productservice' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('productservice.index')); ?>"
                                class="dash-link"><?php echo e(__('Product & Services')); ?>

                            </a>
                        </li>
                    <?php endif; ?>
                    <?php if(Gate::check('manage product & service')): ?>
                        <li class="dash-item <?php echo e(Request::route()->getName() == 'pricing.products' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('pricing.products')); ?>"
                                class="dash-link"><?php echo e(__('التسعير')); ?>

                            </a>
                        </li>
                    <?php endif; ?>
                    <?php if(Gate::check('manage product & service')): ?>
                        <li class="dash-item <?php echo e(Request::segment(1) == 'productstock' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('productstock.index')); ?>"
                                class="dash-link"><?php echo e(__('Product Stock')); ?>

                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </li>
        <?php endif; ?>

        <!--------------------- End Products System ----------------------------------->


        <!--------------------- Start POs System ----------------------------------->
        <?php if(!empty($userPlan) &&  $userPlan->pos == 1 && !$isSuperFiesr && !$isSuperFiesrBig && !($isDelivery && !$isCompany && !$isAccountant)): ?>
            <?php if(Gate::check('show warehouse') ||
                    Gate::check('show purchase') ||
                    Gate::check('show pos') ||
                    Gate::check('show print settings')): ?>
                <li
                    class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'pos.barcode' || Request::route()->getName() == 'pos.print' || Request::route()->getName() == 'pos.show' || Request::route()->getName() == 'pos.financial.record' || Request::segment(1) == 'receipt-order' ? ' active dash-trigger' : ''); ?>">
                    <a href="#!" class="dash-link"><span class="dash-micon"><i
                                class="ti ti-layers-difference"></i></span><span
                            class="dash-mtext"><?php echo e(__('POS System')); ?></span><span class="dash-arrow"><i
                                data-feather="chevron-right"></i></span></a>
                    <ul
                        class="dash-submenu <?php echo e(Request::route()->getName() == 'pos.barcode' ||
                        Request::route()->getName() == 'pos.print' ||
                        Request::route()->getName() == 'pos.show' ||
                        Request::segment(1) == 'receipt-order'
                            ? 'show'
                            : ''); ?>">
                        
                        
                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage pos')): ?>
                            <li class="dash-item <?php echo e(Request::route()->getName() == 'pos.index' ? ' active' : ''); ?>">
                                <a class="dash-link" data-fmodel="<?php echo e($is_sale_session_new ? 'true' : ''); ?>" data-title="<?php echo e(__(' Add POS')); ?>"
                                    data-url="<?php echo e($is_sale_session_new ? route('pos.financial.opening.balance') : route('pos.index')); ?>"
                                 href="<?php echo e(!$is_sale_session_new ? route('pos.index') : '#'); ?>"><?php echo e(__(' Add POS')); ?></a>
                            </li>
                            
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('show pos')): ?>
                            <li
                                class="dash-item <?php echo e(Request::route()->getName() == 'pos.report' || Request::route()->getName() == 'pos.show' ? ' active' : ''); ?>">
                                <a class="dash-link" data-fmodel="<?php echo e($is_sale_session_new ? 'true' : ''); ?>"
                                    data-url="<?php echo e($is_sale_session_new ? route('pos.financial.opening.balance') : route('pos.report')); ?>" data-title="<?php echo e(__('Set Opening Balance')); ?>"
                                    href="<?php echo e(!$is_sale_session_new ? route('pos.report') : '#'); ?>"><?php echo e(__('POS')); ?></a>
                            </li>
                        <?php endif; ?>

                        
                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create barcode')): ?>
                            <li
                                class="dash-item <?php echo e(Request::route()->getName() == 'pos.barcode' || Request::route()->getName() == 'pos.print' ? ' active' : ''); ?>">
                                <a class="dash-link" data-fmodel="<?php echo e($is_sale_session_new ? 'true' : ''); ?>" href="<?php echo e(!$is_sale_session_new ? route('pos.barcode'): '#'); ?> "
                                    data-url="<?php echo e($is_sale_session_new ? route('pos.financial.opening.balance') : route('pos.barcode')); ?>" data-title="<?php echo e(__('Set Opening Balance')); ?>"><?php echo e(__('Print Barcode')); ?></a>
                            </li>
                        <?php endif; ?>
                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('show financial record')): ?>
                            <li
                                class="dash-item <?php echo e(Request::route()->getName() == 'pos.financial.record' ? ' active' : ''); ?>" >
                                <a class="dash-link" data-fmodel="<?php echo e($is_sale_session_new ? 'true' : ''); ?>" data-title="<?php echo e(__('Financial Record')); ?>" data-url="<?php echo e(route('pos.financial.record')); ?>"
                                   href="<?php echo e(!$is_sale_session_new ? route('pos.financial.record') : '#'); ?>"><?php echo e(__('Financial Record')); ?></a>
                            </li>
                        <?php endif; ?>

                        <?php if(Auth::user()->hasRole('Cashier')): ?>
                            <li class="dash-item <?php echo e(Request::route()->getName() == 'product.expiry.index' || Request::route()->getName() == 'product.expiry.edit' ? ' active' : ''); ?>">
                                <a class="dash-link" data-fmodel="<?php echo e($is_sale_session_new ? 'true' : ''); ?>"
                                   data-url="<?php echo e($is_sale_session_new ? route('pos.financial.opening.balance') : route('product.expiry.index')); ?>"
                                   data-title="<?php echo e(__('تاريخ الصلاحية للمنتجات')); ?>"
                                   href="<?php echo e(!$is_sale_session_new ? route('product.expiry.index') : '#'); ?>"><?php echo e(__('تاريخ الصلاحية للمنتجات')); ?></a>
                            </li>

                            <li class="dash-item <?php echo e(Request::route()->getName() == 'receipt-order.index' ? ' active' : ''); ?>">
                                <a class="dash-link" data-fmodel="<?php echo e($is_sale_session_new ? 'true' : ''); ?>"
                                   data-url="<?php echo e($is_sale_session_new ? route('pos.financial.opening.balance') : route('receipt-order.index')); ?>"
                                   data-title="<?php echo e(__('أوامر الاستلام')); ?>"
                                   href="<?php echo e(!$is_sale_session_new ? route('receipt-order.index') : '#'); ?>"><?php echo e(__('أوامر الاستلام')); ?></a>
                            </li>
                        <?php endif; ?>

                        

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage warehouse')): ?>
                            <li class="dash-item <?php echo e(Request::route()->getName() == 'receipt-order.index' || Request::route()->getName() == 'receipt-order.create' || Request::route()->getName() == 'receipt-order.edit' ? ' active' : ''); ?>">
                                <a class="dash-link" data-fmodel="<?php echo e($is_sale_session_new ? 'true' : ''); ?>"
                                   data-url="<?php echo e($is_sale_session_new ? route('pos.financial.opening.balance') : route('receipt-order.index')); ?>"
                                   data-title="<?php echo e(__('Receipt Orders')); ?>"
                                   href="<?php echo e(!$is_sale_session_new ? route('receipt-order.index') : '#'); ?>"><?php echo e(__('Receipt Orders')); ?></a>
                            </li>
                        <?php endif; ?>

                    </ul>
                </li>
            <?php endif; ?>
        <?php endif; ?>
        <!--------------------- End POs System ----------------------------------->

        <!--------------------- Start Delivery System ----------------------------------->
        <?php if($isDelivery && !$isCompany && !$isAccountant && !$isSuperFiesr && !$isSuperFiesrBig): ?>
            <li class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'pos.index' || Request::route()->getName() == 'pos.report' ? 'active dash-trigger' : ''); ?>">
                <a href="#!" class="dash-link">
                    <span class="dash-micon"><i class="ti ti-truck-delivery"></i></span>
                    <span class="dash-mtext"><?php echo e(__('Delivery System')); ?></span>
                    <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                </a>
                <ul class="dash-submenu <?php echo e(Request::route()->getName() == 'pos.index' || Request::route()->getName() == 'pos.report' ? 'show' : ''); ?>">
                    <!-- POS للدليفري -->
                    <li class="dash-item <?php echo e(Request::route()->getName() == 'pos.index' ? 'active' : ''); ?>">
                        <a class="dash-link" data-fmodel="<?php echo e($is_sale_session_new ? 'true' : ''); ?>" data-title="<?php echo e(__('POS - Delivery')); ?>"
                            data-url="<?php echo e($is_sale_session_new ? route('pos.financial.opening.balance') : route('pos.index')); ?>"
                         href="<?php echo e(!$is_sale_session_new ? route('pos.index') : '#'); ?>"><?php echo e(__('POS - Delivery')); ?></a>
                    </li>

                    <!-- تقارير POS -->
                    <li class="dash-item <?php echo e(Request::route()->getName() == 'pos.report' || Request::route()->getName() == 'pos.show' ? 'active' : ''); ?>">
                        <a class="dash-link" data-fmodel="<?php echo e($is_sale_session_new ? 'true' : ''); ?>"
                            data-url="<?php echo e($is_sale_session_new ? route('pos.financial.opening.balance') : route('pos.report')); ?>" data-title="<?php echo e(__('Set Opening Balance')); ?>"
                            href="<?php echo e(!$is_sale_session_new ? route('pos.report') : '#'); ?>"><?php echo e(__('POS Reports')); ?></a>
                    </li>
                </ul>
            </li>
        <?php endif; ?>
        <!--------------------- End Delivery System ----------------------------------->

        <?php if(\Auth::user()->type != 'super admin'): ?>
            <?php if($isSuperFiesr || $isSuperFiesrBig || $isCompany || $isAccountant): ?>
                <!-- قسم إدارة عمليات الفروع - يظهر لمستخدمي SUPER FIESR و SUPER FIESR BIG و company و accountant -->
                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'bill' ||
                                                    Request::route()->getName() == 'branch.inventory.management' ? 'active dash-trigger' : ''); ?>">
                    <a href="#!" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-building-store"></i></span>
                        <span class="dash-mtext">إدارة عمليات الفروع</span>
                        <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                    </a>
                    <ul class="dash-submenu">
                        

                        

                        



                        <!-- 4. Bill (الفواتير) -->
                        <li class="dash-item <?php echo e(Request::route()->getName() == 'bill.index' || Request::route()->getName() == 'bill.create' || Request::route()->getName() == 'bill.edit' || Request::route()->getName() == 'bill.show' ? ' active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('bill.index')); ?>">الفواتير</a>
                        </li>

                        

                        

                        <!-- 7. Branch Inventory Management (إدارة مخزون الفروع) - فقط لـ SUPER FIESR BIG -->
                        <?php if($isSuperFiesrBig): ?>
                        <li class="dash-item <?php echo e(Request::route()->getName() == 'branch.inventory.management' ? ' active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('branch.inventory.management')); ?>">إدارة مخزون الفروع</a>
                        </li>
                        <?php endif; ?>

                        <!-- 8. Vendor Representatives Management (تسجيل المندوبين والمردين) - لـ SUPER FIESR و company -->
                        <?php if($isSuperFiesr || $isCompany): ?>
                        <li class="dash-item <?php echo e(Request::route()->getName() == 'vendor.representatives.index' || Request::route()->getName() == 'vendor.representatives.create' || Request::route()->getName() == 'vendor.representatives.edit' || Request::route()->getName() == 'vendor.representatives.show' ? ' active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('vendor.representatives.index')); ?>">تسجيل المندوبين والمردين</a>
                        </li>
                        <?php endif; ?>

                        
                    </ul>
                </li>

                <!-- قسم إدارة العمليات المالية - يظهر فقط لمستخدمي company و accountant -->
                <?php if($isCompany || $isAccountant): ?>
                <li class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'inventory.management' || Request::route()->getName() == 'financial.productservice.index' || Request::route()->getName() == 'invoice.processing.invoice.processor' || Request::route()->getName() == 'branch.cash.management' || Request::route()->getName() == 'warehouse.index' || Request::route()->getName() == 'product.expiry.index' || Request::route()->getName() == 'advanced-cash-management.index' ? 'active dash-trigger' : ''); ?>">
                    <a href="#!" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-cash"></i></span>
                        <span class="dash-mtext">إدارة العمليات المالية</span>
                        <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                    </a>
                    <ul class="dash-submenu <?php echo e(Request::route()->getName() == 'inventory.management' || Request::route()->getName() == 'financial.productservice.index' || Request::route()->getName() == 'invoice.processing.invoice.processor' || Request::route()->getName() == 'branch.cash.management' || Request::route()->getName() == 'warehouse.index' || Request::route()->getName() == 'product.expiry.index' || Request::route()->getName() == 'advanced-cash-management.index' ? 'show' : ''); ?>">
                        <!-- معالجة فواتير المبيعات link removed as requested -->
                        <!-- ملخص فواتير المبيعات removed as requested -->
                        <!-- ملخص فواتير نقاط البيع removed as requested -->
                        <!-- معالج فواتير البيع -->
                        <li class="dash-item <?php echo e(Request::route()->getName() == 'invoice.processing.invoice.processor' ? ' active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('invoice.processing.invoice.processor')); ?>">معالج فواتير البيع</a>
                        </li>

                        

                        

                        

                        <!-- معالجة النقد للفروع (إدارة النقد للفروع) -->
                        <li class="dash-item <?php echo e(Request::route()->getName() == 'branch.cash.management' ? ' active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('branch.cash.management')); ?>">معالجة النقد للفروع</a>
                        </li>

                        <!-- أوامر الاستلام - إدارة العمليات المالية -->
                        <li class="dash-item <?php echo e(Request::route()->getName() == 'receipt-order.index' || Request::route()->getName() == 'receipt-order.show' ? ' active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('receipt-order.index')); ?>">أوامر الاستلام</a>
                        </li>

                        

                        <!-- تحليل المبيعات المتقدم - إدارة العمليات المالية -->
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('show financial record')): ?>
                        <li class="dash-item <?php echo e(Request::route()->getName() == 'financial.sales.analytics.index' ? ' active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('financial.sales.analytics.index')); ?>">تحليل المبيعات المتقدم</a>
                        </li>
                        <?php endif; ?>

                        <!-- إدارة النقد المتقدمة - إدارة العمليات المالية -->
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('show financial record')): ?>
                        <li class="dash-item <?php echo e(Request::route()->getName() == 'advanced-cash-management.index' ? ' active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('advanced-cash-management.index')); ?>">إدارة النقد المتقدمة</a>
                        </li>
                        <?php endif; ?>

                        

                        

                        <!-- 3. تعديل على المخزون ومراقبة المخزون -->
                        <li class="dash-item <?php echo e(Request::route()->getName() == 'inventory.management' ? ' active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('inventory.management')); ?>">تعديل على المخزون ومراقبة المخزون</a>
                        </li>

                        <!-- 4. تاريخ الصلاحية للمنتجات (تاريخ انتهاء صلاحية المنتجات) - مخفي عن SUPER FIESR BIG -->
                        <?php if(!$isSuperFiesrBig): ?>
                        <li class="dash-item <?php echo e(Request::route()->getName() == 'product.expiry.index' || Request::route()->getName() == 'product.expiry.edit' ? ' active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('product.expiry.index')); ?>">تاريخ الصلاحية للمنتجات</a>
                        </li>
                        <?php endif; ?>

                        <!-- 5. المستودعات (إدارة المستودعات) -->
                        <li class="dash-item <?php echo e(Request::route()->getName() == 'warehouse.index' || Request::route()->getName() == 'warehouse.show' ? ' active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('warehouse.index')); ?>">المستودعات</a>
                        </li>

                        

                        <!-- 7. إدارة المنتجات والخدمات - نسخة مطابقة للأصلية -->
                        <li class="dash-item <?php echo e(Request::route()->getName() == 'financial.productservice.index' ? ' active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('financial.productservice.index')); ?>">إدارة المنتجات والخدمات</a>
                        </li>
                    </ul>
                </li>
                <?php endif; ?>
            <?php endif; ?>

            <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'support' ? 'active' : ''); ?>">
                <a href="<?php echo e(route('support.index')); ?>" class="dash-link">
                    <span class="dash-micon"><i class="ti ti-headphones"></i></span><span
                        class="dash-mtext">نظام المراسلات بالشركة</span>
                </a>
            </li>



            

            
            
        <?php endif; ?>

        <?php if(\Auth::user()->type == 'company'): ?>
            
            <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'notification_templates' ? 'active' : ''); ?>">
                <a href="<?php echo e(route('notification-templates.index')); ?>" class="dash-link">
                    <span class="dash-micon"><i class="ti ti-notification"></i></span><span
                        class="dash-mtext"><?php echo e(__('Notification Template')); ?></span>
                </a>
            </li>
        <?php endif; ?>

        <!--------------------- Start System Setup ----------------------------------->

        <?php if(\Auth::user()->type != 'super admin'): ?>
            <?php if(Gate::check('manage company plan') || Gate::check('manage order') || Gate::check('manage company settings')): ?>
                <li
                    class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'settings' ||
                    Request::segment(1) == 'plans' ||
                    Request::segment(1) == 'stripe' ||
                    Request::segment(1) == 'order'
                        ? ' active dash-trigger'
                        : ''); ?>">
                    <a href="#!" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-settings"></i></span><span
                            class="dash-mtext"><?php echo e(__('Settings')); ?></span>
                        <span class="dash-arrow">
                            <i data-feather="chevron-right"></i></span>
                    </a>
                    <ul class="dash-submenu">
                        <?php if(Gate::check('manage company settings')): ?>
                            <li
                                class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'settings' ? ' active' : ''); ?>">
                                <a href="<?php echo e(route('settings')); ?>"
                                    class="dash-link"><?php echo e(__('System Settings')); ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if(Gate::check('manage company plan')): ?>
                            <li
                                class="dash-item<?php echo e(Request::route()->getName() == 'plans.index' || Request::route()->getName() == 'stripe' ? ' active' : ''); ?>">
                                <a href="<?php echo e(route('plans.index')); ?>"
                                    class="dash-link"><?php echo e(__('Setup Subscription Plan')); ?></a>
                            </li>
                        <?php endif; ?>
                        <li
                        class="dash-item<?php echo e(Request::route()->getName() == 'referral-program.company' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('referral-program.company')); ?>"
                            class="dash-link"><?php echo e(__('Referral Program')); ?></a>
                        </li>

                        <?php if(Gate::check('manage order') && Auth::user()->type == 'company'): ?>
                            <li class="dash-item <?php echo e(Request::segment(1) == 'order' ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('order.index')); ?>" class="dash-link"><?php echo e(__('Order')); ?></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
        <?php endif; ?>




        <!--------------------- End System Setup ----------------------------------->
                <?php endif; ?> 
        </ul>
        <?php endif; ?>
        <?php if(\Auth::user()->type == 'client'): ?>
            <ul class="dash-navbar">
                <?php if(Gate::check('manage client dashboard')): ?>
                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'dashboard' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('client.dashboard.view')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-home"></i></span><span
                                class="dash-mtext"><?php echo e(__('Dashboard')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if(Gate::check('manage deal')): ?>
                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'deals' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('deals.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-rocket"></i></span><span
                                class="dash-mtext"><?php echo e(__('Deals')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if(Gate::check('manage contract')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'contract.index' || Request::route()->getName() == 'contract.show' ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('contract.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-rocket"></i></span><span
                                class="dash-mtext"><?php echo e(__('Contract')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if(Gate::check('manage project')): ?>
                    <li class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == 'projects' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('projects.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-share"></i></span><span
                                class="dash-mtext"><?php echo e(__('Project')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if(Gate::check('manage project')): ?>
                    <li
                        class="dash-item  <?php echo e(Request::route()->getName() == 'project_report.index' || Request::route()->getName() == 'project_report.show' ? 'active' : ''); ?>">
                        <a class="dash-link" href="<?php echo e(route('project_report.index')); ?>">
                            <span class="dash-micon"><i class="ti ti-chart-line"></i></span><span
                                class="dash-mtext"><?php echo e(__('Project Report')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if(Gate::check('manage project task')): ?>
                    <li class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == 'taskboard' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('taskBoard.view', 'list')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-list-check"></i></span><span
                                class="dash-mtext"><?php echo e(__('Tasks')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if(Gate::check('manage bug report')): ?>
                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'bugs-report' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('bugs.view', 'list')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-bug"></i></span><span
                                class="dash-mtext"><?php echo e(__('Bugs')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if(Gate::check('manage timesheet')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'timesheet-list' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('timesheet.list')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-clock"></i></span><span
                                class="dash-mtext"><?php echo e(__('Timesheet')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if(Gate::check('manage project task')): ?>
                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'calendar' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('task.calendar', ['all'])); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-calendar"></i></span><span
                                class="dash-mtext"><?php echo e(__('Task Calender')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'support' || Request::segment(1) == 'messages' ? 'active dash-trigger' : ''); ?>">
                    <a href="#!" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-headphones"></i></span>
                        <span class="dash-mtext"><?php echo e(__('Support System')); ?></span>
                        <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                    </a>
                    <ul class="dash-submenu">
                        <li class="dash-item <?php echo e(Request::segment(1) == 'support' && Request::segment(2) != 'reports' ? 'active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('support.index')); ?>"><?php echo e(__('Support Tickets')); ?></a>
                        </li>
                        <li class="dash-item <?php echo e(Request::segment(1) == 'support' && Request::segment(2) == 'reports' ? 'active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('support.reports')); ?>"><?php echo e(__('Support Reports')); ?></a>
                        </li>
                        <li class="dash-item <?php echo e(Request::segment(1) == 'messages' ? 'active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('messages.index')); ?>"><?php echo e(__('Internal Messages')); ?></a>
                        </li>
                    </ul>
                </li>
            </ul>
        <?php endif; ?>
        <?php if(\Auth::user()->type == 'super admin'): ?>
            <ul class="dash-navbar">
                <?php if(Gate::check('manage super admin dashboard')): ?>
                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'dashboard' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('client.dashboard.view')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-home"></i></span><span
                                class="dash-mtext"><?php echo e(__('Dashboard')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>


                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage user')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'users.index' || Request::route()->getName() == 'users.create' || Request::route()->getName() == 'users.edit' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('users.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-users"></i></span><span
                                class="dash-mtext"><?php echo e(__('Companies')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if(Gate::check('manage plan')): ?>
                    <li class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == 'plans' ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('plans.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-trophy"></i></span><span
                                class="dash-mtext"><?php echo e(__('Plan')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if(\Auth::user()->type == 'super admin'): ?>
                <li class="dash-item dash-hasmenu <?php echo e(request()->is('plan_request*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('plan_request.index')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-arrow-up-right-circle"></i></span><span
                            class="dash-mtext"><?php echo e(__('Plan Request')); ?></span>
                    </a>
                </li>
            <?php endif; ?>

                <li class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == '' ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('referral-program.index')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-discount-2"></i></span><span
                            class="dash-mtext"><?php echo e(__('Referral Program')); ?></span>
                    </a>
                </li>


                <?php if(Gate::check('manage coupon')): ?>
                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'coupons' ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('coupons.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-gift"></i></span><span
                                class="dash-mtext"><?php echo e(__('Coupon')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if(Gate::check('manage order')): ?>
                    <li class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == 'orders' ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('order.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-shopping-cart-plus"></i></span><span
                                class="dash-mtext"><?php echo e(__('Order')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
                
                <li
                    class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'email_template' || Request::route()->getName() == 'manage.email.language' ? ' active dash-trigger' : 'collapsed'); ?>">
                    <a href="<?php echo e(route('email_template.index')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-template"></i></span>
                        <span class="dash-mtext"><?php echo e(__('Email Template')); ?></span>
                    </a>
                </li>

                <?php if(\Auth::user()->type == 'super admin'): ?>
                    <?php echo $__env->make('landingpage::menu.landingpage', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php endif; ?>

                <?php if(Gate::check('manage system settings')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'systems.index' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('systems.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-settings"></i></span><span
                                class="dash-mtext"><?php echo e(__('Settings')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

            </ul>
        <?php endif; ?>


        
    </div>
</div>
</nav>

<?php $__env->startSection('custom-js'); ?>
    <script>
        $(document).ready(function () {
            $('a[data-fmodel="true"]').click(function () {
                console.log("URL:",$(this).data("url"));
                var data = {};
                var title1 = $(this).data("title");

                var title2 = $(this).data("bs-original-title");
                var title3 = $(this).data("original-title");
                var title = (title1 != undefined) ? title1 : title2;
                var title=(title != undefined) ? title : title3;

                $('#commonModal .modal-dialog').removeClass('modal-sm modal-md modal-lg modal-xl modal-xxl');
                var size = ($(this).data('size') == '') ? 'md' : $(this).data('size');

                var url = $(this).data('url');
                $("#commonModal .modal-title").html(title);
                $("#commonModal .modal-dialog").addClass('modal-' + size);

                if ($('#vc_name_hidden').length > 0) {
                    data['vc_name'] = $('#vc_name_hidden').val();
                }
                if ($('#warehouse_name_hidden').length > 0) {
                    data['warehouse_name'] = $('#warehouse_name_hidden').val();
                }
                if ($('#discount_hidden').length > 0) {
                    data['discount'] = $('#discount_hidden').val();
                }
                if ($('#quotation_id').length > 0) {
                    data['quotation_id'] = $('#quotation_id').val();
                }
                $.ajax({
                    url: url,
                    data: data,
                    success: function (data) {
                        if (data && data.trim() !== '') {
                            $('#commonModal .modal-body').html(data);
                            $("#commonModal").modal('show');
                            taskCheckbox();
                            common_bind("#commonModal");
                            validation();
                            commonLoader();
                        } else {
                            show_toastr('Error', 'Empty response received', 'error');
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log('AJAX Error:', xhr, status, error);
                        var errorMessage = 'An error occurred';

                        if (xhr.responseJSON && xhr.responseJSON.error) {
                            errorMessage = xhr.responseJSON.error;
                        } else if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }

                        show_toastr('Error', errorMessage, 'error');
                    }
                });

            });
        });
    </script>
<?php $__env->stopSection(); ?>
<?php /**PATH C:\laragon\www\to\newo\3\resources\views/partials/admin/menu.blade.php ENDPATH**/ ?>