{"__meta": {"id": "Xfc18ef52cf6730d4af9f4af41385c8c5", "datetime": "2025-06-17 06:29:58", "utime": 1750141798.021155, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750141796.360122, "end": 1750141798.021199, "duration": 1.6610770225524902, "duration_str": "1.66s", "measures": [{"label": "Booting", "start": 1750141796.360122, "relative_start": 0, "end": **********.786028, "relative_end": **********.786028, "duration": 1.4259059429168701, "duration_str": "1.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.786059, "relative_start": 1.4259369373321533, "end": 1750141798.021205, "relative_end": 5.9604644775390625e-06, "duration": 0.23514604568481445, "duration_str": "235ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45171288, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01242, "accumulated_duration_str": "12.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9117389, "duration": 0.00743, "duration_str": "7.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 59.823}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.954755, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 59.823, "width_percent": 12.48}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.983037, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 72.303, "width_percent": 27.697}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1090223817 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1090223817\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-648769415 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-648769415\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-980556691 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-980556691\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1c3qljs%7C1750141708567%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJSak1odU5od3NJaHpMNDZtV1FNK3c9PSIsInZhbHVlIjoiV2dPZlFZVXoycS9VNkJnV2R5TElsTllHbmIwaTdzY3NlRVVBNXJISlVPWnl0MWl0WWhwVU1WKzcvaVhNSlhxRnU1WVUraHJvOE9aYzdRZ2k2VzlCenJvVEovWkhTSzZtejlVU3FCQkFFS1hEZUNNMno0VnMxMlpsN2hKTUZ0NCtUalZQVFExVGc4Y1F6K1RrOTZ0YnQvQ0psV04yUG1qSmtpckUzNWtVTm1oMEk4eUJlZ2JTdVM4WTUvUUV2eFR5ZnJHcHJIYmgrSC9tRzdrOHQzVjdHK3ZKYVhmdzh1dSs1WDB4WVQrOXA4Y2pBSWVsdUJYTXJCdmFRS2lKeDZZWm1zWTNpN3F5emtuWWtwK1ZPTEN0RU1TRWVzcFZJd21YMVBHMkgzbEYvU213NEx2MmFUa1pDaFRJdjlPclJQbTFTcXVrWFdMSDA4ZGNQK1ZiTFlFTTRTdzJoVW45NG80citxMXFqdm5jVC9MMG8rN1V2T3N5a1d3ekdDR1pVZlpJVjg5dkhnWG9VVHpHV0ovYVd6UUYwL2t3aU5JcGdzd09VKy85b2ZHMUtSVW5oN2sxT2RKdjR0VzJTNFJMUXF1TXk4U0xYVFdZZEtmdlhYcTExQUhaYjJLRkY5ZVZKN0I1S3hlcVhSZHFHU0g2YnNVd0VYL294dDdCOGtHK0FiN1kiLCJtYWMiOiJmOTFhNDdjOThjY2Q0NWVmNjdjZjE0YzNmZTNjNTI1YWVhNGRiZTcyYWZiZmMxNTYxOGVhN2ZhYmZhZTQ2YTJhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImhzcEtZeXp2K0gwNGl3K2FPb29pcmc9PSIsInZhbHVlIjoiVmZTcDN4S2MyYjYwaXo5dE80MW5rSjhpQnU4VHNEV1NBK3V3RzVJSmJLMGk3QkhUZno5MGVYcTlGZW81Um5JYjE1NEY4UzR5aFNrZjZJaDI5SmVTVXVkcFdTcXE4V3pwd0lZZnZtRzJUOXBEMVdDNnF6MnRoK252L3NkZno5NTVNVkJLditNbFMxWC9ySFdhbG92U3VnQVkzbWxhYmczaHhENzIyL3Q1b3RWRkZOTnUvZDc4MkZPRVZUQ0pUdlBmT3dqRkZNR2RNTFVuL3RFRE9DRW53aElsalhyaHR5UlYreXF2YWNvN09UalkrMFdOK2RHcmovdW1udmY3NTFqNlFndE0rc1dwTHBZTTUrTkQwbXhYZWVLbk9TWG1rMGJUeXdWaWYrV2s4WEZubHE4S0NGY3RZbDFkQXN3MjFrcVdUY3F3U0VtZ0hMWW0zTnM3b1N5SGhhNmNUWXhMNWMzQUtzL05UeXhhalNZN0RFRWhab2pUbFpjLzNzdEx6Z01lVjF5ZGw0bkRMMjI1MTZrSkoxVVRSTFN5ZXh2Um9zTnFoMEg0L1p4SjRyQTA0REZMYnNNVHVZcE45aExuR1BnTXBleUhYOGtjRXU2OHdiWU4xMGtTN1IxN0hjeDkvOCtQcW1TMWJlRXNwWU83b2tYczhUVU9EQ0RjZE5tcXU5aVEiLCJtYWMiOiI1ZDY0OWE1NWJjMjJjNjg0NzY5YzkwZmI0M2NkYTYxZDhlZjljZjBhMWY5ZDUzNzY5YjRjZDM2NTBiMGE3ODc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1125160381 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1125160381\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-101951097 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:29:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikw5ZVVoSlErRXNZdWpjRkhrYTNyVEE9PSIsInZhbHVlIjoidzB6bXlkQWhibVkyOGdyUll4c2JZTThMVDVoU2I1RFF3NDJJeWtUdlJzcDJYTmRZYXVua3BJcERGUnNzMFlVcmlibnlwTEtxWEc4WFB6NmxUdU5VYkxucjliUUhETDZuZHpSOU9BeTYrdGp5WGhwNUZES3FpOTlmZ1Y0dU1iTFJOSzBiWTFQWXQwWVpYNlN6bUx4RGpUSCtWWmdxQml5TlJJNTFjaDRFcFcwa3BNaWxabmo3SllrdjlneFFkcW5GUmU0WDFpZDFuUitxWThXT1lEMEY3QTdTRXR3UHJ2dkJ4VmpSaS9KNGt2WGlOenE3YXo0VjFZbXZlMWVFRmgrdUNwTFV0MDBIVEMxejVRMkIwZVlGU0hRZHdQeEp2WnNFSW1NSzdnTnBsckhLWndTNVFBMVBIRFBDcVhydnk3VGMwK3VTM3M2aWpqK013bk5ydGlVRmkzU1c4ZXYzNmk1TDQwRVlrdmJ1a24vaytlbktpTlZhTzFJd2hXbmNkcHlJK2liOURFQTVEa0VpRHdHcTVpSmNIZTFPTGVURi93WkRKN3BNQ1dld2dOQitlVllUUlovcksyMUlpQjFyQjhOY25BSys4WkRQNzZnQkFuNFhwd1p4cmRDakhaaFowMURoMFhmVDN1MXZRMWFCSlBLaldjRDFTUWF4WWlJa285S3IiLCJtYWMiOiJmZjdlZWQ1Njk2Mzg1NTM4N2I5YmMzNWNlOGE4OTJkZGViNTMzNDAzNjQwMDU4MTg1MjUxYjEzNWIwODllYjkwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:29:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im5Cd2NQLytJYUMvQWE3NEFyWkRUWmc9PSIsInZhbHVlIjoicXVPQlZHUCtzOXl0NHh6bEhwSXlHSm9VdXBBWmc4YlVlUzFvZlAwQ254c3FqNFNtQzJLK05hQStJRHhxNEpFMVVHQmd4SUhnSm85ZTg0bmM3YjBJVW5za29tSFpORmZycFUxM2pWL2l4MG4veCtjQkcxOStOai9qYm5nK21FelFUMUNSVjkraUxKcXZza0cvR29qWEo5RFFkWnZIUVFLUW9JemswTWRhVU1sbzdTaGpNemVvRUJmR0pXdXFIcDFNS0ZHNDR0bFd0V242dkNCVlh3QXZsbHhOSW5YelVWNGdKbkZtODZTUGRWSEc1TEtiZ0hRcDQxcTBLTm1jeVNOdXlhNWEwby9XVnZXQXZoWnhNdm8zMWdobDNkSWQ0RjVXdVp1L3oya2V2bUdCanZkQUQvenFkTE8vNnhETk5NRVpwMm9HNlBNTi9CTlRTU3dxZ0ZUTkpFUlVRUEJRSTFBV280TXVmZ3g0N25MbW1oK0NTcDI0YWJCQldybXBaM2szeGtQQmo4bFRpcXBPNXRaZEt1Vyt5Skg0TVl2VWVHMHJnTTQrUDF4UVcwNlVPNTNrMitCcVAxYmFkQXBHb0owbGEwWGtpSExDQjZoN3ZCNlQwUlFUSlp6Y0pDMmgvTXJBOS9McjJCNXJ2eVJ3ckFKcE5UamQzY09iNmNjazljbHgiLCJtYWMiOiI5ZjcwNjE0ZGI5YzVjNGEwMGFkYmViYTY2ZmFhNjkyY2ZmZGU1ZjVmYWVlZTBmOWU4NWZhODAwMTVmY2U4OTE1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:29:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikw5ZVVoSlErRXNZdWpjRkhrYTNyVEE9PSIsInZhbHVlIjoidzB6bXlkQWhibVkyOGdyUll4c2JZTThMVDVoU2I1RFF3NDJJeWtUdlJzcDJYTmRZYXVua3BJcERGUnNzMFlVcmlibnlwTEtxWEc4WFB6NmxUdU5VYkxucjliUUhETDZuZHpSOU9BeTYrdGp5WGhwNUZES3FpOTlmZ1Y0dU1iTFJOSzBiWTFQWXQwWVpYNlN6bUx4RGpUSCtWWmdxQml5TlJJNTFjaDRFcFcwa3BNaWxabmo3SllrdjlneFFkcW5GUmU0WDFpZDFuUitxWThXT1lEMEY3QTdTRXR3UHJ2dkJ4VmpSaS9KNGt2WGlOenE3YXo0VjFZbXZlMWVFRmgrdUNwTFV0MDBIVEMxejVRMkIwZVlGU0hRZHdQeEp2WnNFSW1NSzdnTnBsckhLWndTNVFBMVBIRFBDcVhydnk3VGMwK3VTM3M2aWpqK013bk5ydGlVRmkzU1c4ZXYzNmk1TDQwRVlrdmJ1a24vaytlbktpTlZhTzFJd2hXbmNkcHlJK2liOURFQTVEa0VpRHdHcTVpSmNIZTFPTGVURi93WkRKN3BNQ1dld2dOQitlVllUUlovcksyMUlpQjFyQjhOY25BSys4WkRQNzZnQkFuNFhwd1p4cmRDakhaaFowMURoMFhmVDN1MXZRMWFCSlBLaldjRDFTUWF4WWlJa285S3IiLCJtYWMiOiJmZjdlZWQ1Njk2Mzg1NTM4N2I5YmMzNWNlOGE4OTJkZGViNTMzNDAzNjQwMDU4MTg1MjUxYjEzNWIwODllYjkwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:29:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im5Cd2NQLytJYUMvQWE3NEFyWkRUWmc9PSIsInZhbHVlIjoicXVPQlZHUCtzOXl0NHh6bEhwSXlHSm9VdXBBWmc4YlVlUzFvZlAwQ254c3FqNFNtQzJLK05hQStJRHhxNEpFMVVHQmd4SUhnSm85ZTg0bmM3YjBJVW5za29tSFpORmZycFUxM2pWL2l4MG4veCtjQkcxOStOai9qYm5nK21FelFUMUNSVjkraUxKcXZza0cvR29qWEo5RFFkWnZIUVFLUW9JemswTWRhVU1sbzdTaGpNemVvRUJmR0pXdXFIcDFNS0ZHNDR0bFd0V242dkNCVlh3QXZsbHhOSW5YelVWNGdKbkZtODZTUGRWSEc1TEtiZ0hRcDQxcTBLTm1jeVNOdXlhNWEwby9XVnZXQXZoWnhNdm8zMWdobDNkSWQ0RjVXdVp1L3oya2V2bUdCanZkQUQvenFkTE8vNnhETk5NRVpwMm9HNlBNTi9CTlRTU3dxZ0ZUTkpFUlVRUEJRSTFBV280TXVmZ3g0N25MbW1oK0NTcDI0YWJCQldybXBaM2szeGtQQmo4bFRpcXBPNXRaZEt1Vyt5Skg0TVl2VWVHMHJnTTQrUDF4UVcwNlVPNTNrMitCcVAxYmFkQXBHb0owbGEwWGtpSExDQjZoN3ZCNlQwUlFUSlp6Y0pDMmgvTXJBOS9McjJCNXJ2eVJ3ckFKcE5UamQzY09iNmNjazljbHgiLCJtYWMiOiI5ZjcwNjE0ZGI5YzVjNGEwMGFkYmViYTY2ZmFhNjkyY2ZmZGU1ZjVmYWVlZTBmOWU4NWZhODAwMTVmY2U4OTE1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:29:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-101951097\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1335129995 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1335129995\", {\"maxDepth\":0})</script>\n"}}