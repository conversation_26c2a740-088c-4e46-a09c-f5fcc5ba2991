{"__meta": {"id": "Xb3b21b5ee35ae906fefc1b273f32c06d", "datetime": "2025-06-17 07:14:18", "utime": **********.445332, "method": "POST", "uri": "/inventory-management/update-quantity", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750144457.826388, "end": **********.445352, "duration": 0.6189641952514648, "duration_str": "619ms", "measures": [{"label": "Booting", "start": 1750144457.826388, "relative_start": 0, "end": **********.299233, "relative_end": **********.299233, "duration": 0.47284507751464844, "duration_str": "473ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.299245, "relative_start": 0.4728572368621826, "end": **********.445357, "relative_end": 5.0067901611328125e-06, "duration": 0.14611196517944336, "duration_str": "146ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52037952, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST inventory-management/update-quantity", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\InventoryManagementController@updateQuantity", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "inventory.management.update.quantity", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=109\" onclick=\"\">app/Http/Controllers/InventoryManagementController.php:109-198</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.02128, "accumulated_duration_str": "21.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.341284, "duration": 0.016210000000000002, "duration_str": "16.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 76.175}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.368452, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 76.175, "width_percent": 3.102}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3769321, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 79.276, "width_percent": 3.195}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.3945131, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 82.472, "width_percent": 3.148}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 124}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.413423, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:124", "source": "app/Http/Controllers/InventoryManagementController.php:124", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=124", "ajax": false, "filename": "InventoryManagementController.php", "line": "124"}, "connection": "ty", "start_percent": 85.62, "width_percent": 0}, {"sql": "select count(*) as aggregate from `warehouse_products` where `id` = '7'", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.413737, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 85.62, "width_percent": 2.444}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 132}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.41759, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:132", "source": "app/Http/Controllers/InventoryManagementController.php:132", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=132", "ajax": false, "filename": "InventoryManagementController.php", "line": "132"}, "connection": "ty", "start_percent": 88.064, "width_percent": 2.585}, {"sql": "update `warehouse_products` set `quantity` = '0', `warehouse_products`.`updated_at` = '2025-06-17 07:14:18' where `id` = 7", "type": "query", "params": [], "bindings": ["0", "2025-06-17 07:14:18", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 136}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.420973, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:136", "source": "app/Http/Controllers/InventoryManagementController.php:136", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=136", "ajax": false, "filename": "InventoryManagementController.php", "line": "136"}, "connection": "ty", "start_percent": 90.648, "width_percent": 4.746}, {"sql": "insert into `stock_reports` (`product_id`, `quantity`, `type`, `type_id`, `description`, `created_by`, `updated_at`, `created_at`) values (7, 1, 'manual_inventory_decrease', 7, 'تقليل يدوي في المخزون من 1 إلى 0 - المستودع: 7', 15, '2025-06-17 07:14:18', '2025-06-17 07:14:18')", "type": "query", "params": [], "bindings": ["7", "1", "manual_inventory_decrease", "7", "تقليل يدوي في المخزون من 1 إلى 0 - المستودع: 7", "15", "2025-06-17 07:14:18", "2025-06-17 07:14:18"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4038}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 145}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4279602, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4038", "source": "app/Models/Utility.php:4038", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4038", "ajax": false, "filename": "Utility.php", "line": "4038"}, "connection": "ty", "start_percent": 95.395, "width_percent": 4.605}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 182}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.439041, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:182", "source": "app/Http/Controllers/InventoryManagementController.php:182", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=182", "ajax": false, "filename": "InventoryManagementController.php", "line": "182"}, "connection": "ty", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage inventory, result => null, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2103029899 data-indent-pad=\"  \"><span class=sf-dump-note>manage inventory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage inventory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2103029899\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.399186, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1399797647 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1399797647\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.400068, "xdebug_link": null}]}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/inventory-management/update-quantity", "status_code": "<pre class=sf-dump id=sf-dump-1951409212 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1951409212\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-366149700 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-366149700\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1653029391 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>warehouse_product_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1653029391\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1436444549 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">81</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=jfj0s4%7C1750144452817%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndiMEIwMmVGVHQ1aEc4WWN4azA2Y3c9PSIsInZhbHVlIjoiTlVtNmhQOGxzaDZCL3dVZFVJdm1ZOHJ6M1E5YlFLTnRxWmRyWTZwZ1RsRGVQNXRGNEpMUDVqQmRMeklITFpaMkYyaTkxNlpRQ1orWGhVK2xTRU12dTJhbk1pOEQ1YWg4WFJlaE5scmViYVcwdkxzTzNpRUpWTVE1NlhraWtmaytPaEpjUTdlbUwyN0JlanNmUHQwQnRFa29DbnpWcmU3NHNYaTU0czRyWmtxSDc1YURvR3RQdXFqMVBRMGkwN3NXK0lVclNqUTFMQzBJUUQwbm4rV1cyV1BkeU5UUFZ5bzF1VU1FQ3RkYTc4aEY2UHZqOVIvU2Z4MkxqM1pBTU9qWEl5T0NsRlpma0I3all5WEpNNEo5aXF4NkR2djRwRHhsN25lMTh3NkQwRW1yaDdUK1R3T2d4ckIyYkErUjNnZFBuOGVwc3M0M0JYQzhjcCtXYjBUc1FrOFdmNHcyNW9vZ2d1TFV0UFdUdk1seU1EUVd5YzVvTklrQkNNTmxaVGNUQ0VtVHAyOG8rRnhNSFFrR3hLM1ZhR09kUHZ2UzZQVGlDM3pwNmRSdHRiU2gyQjRGY1RPUDFFbUpLVi90TDRaVzhBZkdKVFhJTmkyNHNOVFlJSzVtdHNpQ3BNMlVqRXlRU2tLbVVlaDJkNlA2ZHRHTXhaRy95TFhwanpGOUs0ZXciLCJtYWMiOiI3MDk1MGI4NmM0ZWQ0YjRhNGE4MTY0M2EyZWUyMjAzZWFiYmE0ZGU4YzI5YjZlYzhhYTAxMTQxOGVjYThlMTg4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBRSzNIR3paaTdPL2hsWDVkaVFOTXc9PSIsInZhbHVlIjoiSkFTMmtZM2xIR3o1ZFkyUlJTajFhdE1oUVhXaDEzZ0pmcDNkL29sYllKLzRpT2dMZTIzMm5hK0h3cExNVmpWaXpFcS9TNlBOWW9aVDl1V0tRVkVUV3M4Ym4yclZua0tIMC9tR1VLTjlBK0NRUm15My9hamN5c09VZllRVEkySG0zMEJib2ljWFJoWkllTENFMTVJRnE5QzVwc1Y4SDVyRFIwdy9mNWUvOGU1Z0lDV0NOZWNnTE9qdG52Tkk3cGc0SEI3OTZtRkpDZC9xQVlTN216MHJkSWErWStjRmUxQ1NYcG1vcW1Lb1pTK3dSTzRJYTVYaHlKSTdYQldubG9YelJCeGQrSm5OTHFWMERlOGVEWmFRUDJNZGlpakVLUzV3cFZMbWFjNVpGZkNPMXMvK2laVkJubm9PZnlwMFhMd1JYd0xGemhtVmlXazBYcnVvWjU3aXp0bVFoMXJqQXRVNVRvbGxSb21CYUhZT2pVcU9MSG0vQXBtV1o2UE9yaFRIdW5wNnYzUml6OURFd1F4NU1kRUNuQnQxR1Q0N29rZjljTDRra1A5N0ZSSGM4bjNCSEU0bEwreVQxZjdLWEZQbFA4eU9xRWE4ZjdseEFTRHlBSDh2UFVFSUg3QlJyRGtGVDdrajdJL3J1SGJ0czRaYWJpbHFFdmZ5NDBUSXF2SkEiLCJtYWMiOiIzYWU0MjY2NTRiZWExNzU5MTA2ODRiNjI2N2Y1MDYwYzJlMjBkZjg2OTI5NTlhOGRjZmM3MGM3NGMxZWQ4ZTdmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1436444549\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2015490269 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2015490269\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1993623205 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:14:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdyU1hzdlc1QTJTdkp1RFMwOW9ONlE9PSIsInZhbHVlIjoibm9hcG16MFlZQmlQMHpFZzNCdGFjSkFNbWhSeVJWQmJVS3dZYjhtVHduOWNwK3ZlK2M5eGhMV2VSaGxRVmZPNUw4Zng5MWlzSDNmYnZtSENTWkxGNlFFRXZJTXRESzJ6NUM4QVh5MUR5TytJNmlnSlM5OG5lWkhlZnhJQmJHQWFQMTZJRi9PWWJEOG52NEROaFg5Qm9QT3prbzNuOHlXbnV2L0dVMzIwdUFYVWNDcDJ5aWFLYzg0MC9pNU9VaFI1bVVwcFdEcm9hdW5YR0FiZEVIMFg2em1CeGZFZGZWRHNHNnZySnY4NEZvbHNZNWdEVnJyWGtqNkxiVlRoLzdiK2JuTVRQZ2RkV1pmNk5kd3dzaWRpc0VLclZsTGxQNHRTZGhMTHA1cUh1UlkwNHBIaDAzd2xqSnpMRCtiNWZvZGVsamJhTjdpVTZEUHBMVTRUQU5vQnZmN1kvbVY1dmJaVnowbHREbU44UEJ1SFNCL1kvSHVwVHZpVVhBVkRmb3NHdFFDU0hTZ3J5QXo2ZDZ3SkZDOVFNcEJmOElTOGtQQ3VzK2ZtTWhiU1dUL29iNjkrV1RrdlBTVWhVQ2x2eUk0Rkc5NXdONXFoU2l4R3Rvd2FMUmx0bzNnc1RYbWJic0s3N2M3RmEyRzdycjlUeWZ6VExHcVdjU0lsTDFvWDB5SE4iLCJtYWMiOiJiMjIzNjgzYjAwYjE1OGNmMTMyZWMxNTMyM2VhMmQwYTFmNDM5MmY2MTA5YTc0OWU1MzY2MDk3MDg5OTRjZGMxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:14:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImhhRE55VUhqUTh1ME1IczNRSGJla3c9PSIsInZhbHVlIjoiaGpPRTEreE13ZHNUTWFWQUlxd2Y2MVJ3TVJXMURTbTVIRU93Ykl2T3BVSXk1SCthMkpoNXdpM2l2QmRBSS9BblNBcVZuOCtlY3hhcjRjZ0NqNzk2eUN1QlRwUHNkNFdXT25pRDNXZ3oyVkkxclVnZytxcnIwbEIyRmpIaTNoOUhXVWtta09FcEpkR2VMWWxKUGY3bG96SVhHVkZ1TnZoaHdsRlVZUklLMzlIeGVKcnZKemMvdnd2VE1wVEJETSt2Q2MzemxGYU83QjZSbW9CNm85T3RoTk5GWUtzWEIzTHVRQllCZ2NyT3dqNVdpb2RQa1RxYVRBMVlzRGZuTm5JV1VuMjVzelFKRElHc0RCRVNETVBmaThxNWZEYWVTT0twamRLOE5zOCtmUVpKb1ZhaEVTdGNoaldlR3RCclVsOFRxTXpNZ3lUd2hCanFBaXRiWm0xb1ZNNG1HSTZYR2FTa01KNElGV2FjMVBUcER2RGtYdFpnaW9YZEJibXE2bnRVRUZIYXc2LzBZV2ZUenhqYUtCZmh1UXVTeFhMYnM2MEpUV0ZpV1hCMVVBZHRzWXlRT29RU3ZQcUlpREpkVzNwVkhZOUhKUERjMDhobjdwNXp4MHA5djhiSFRDVDJQTStKQ282MTZuYmZBRy83YjlWZVNXczRhZnBTSE1xZmpNaXciLCJtYWMiOiJjMWY2MWVkMGM5NjU5YzMzNTA2MmE2ZmQ2NGFiYmY4ZGI3YzZiMmRlM2I0NGM1ODQ5NmM1YzNjOWQ3NmNjYTg1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:14:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdyU1hzdlc1QTJTdkp1RFMwOW9ONlE9PSIsInZhbHVlIjoibm9hcG16MFlZQmlQMHpFZzNCdGFjSkFNbWhSeVJWQmJVS3dZYjhtVHduOWNwK3ZlK2M5eGhMV2VSaGxRVmZPNUw4Zng5MWlzSDNmYnZtSENTWkxGNlFFRXZJTXRESzJ6NUM4QVh5MUR5TytJNmlnSlM5OG5lWkhlZnhJQmJHQWFQMTZJRi9PWWJEOG52NEROaFg5Qm9QT3prbzNuOHlXbnV2L0dVMzIwdUFYVWNDcDJ5aWFLYzg0MC9pNU9VaFI1bVVwcFdEcm9hdW5YR0FiZEVIMFg2em1CeGZFZGZWRHNHNnZySnY4NEZvbHNZNWdEVnJyWGtqNkxiVlRoLzdiK2JuTVRQZ2RkV1pmNk5kd3dzaWRpc0VLclZsTGxQNHRTZGhMTHA1cUh1UlkwNHBIaDAzd2xqSnpMRCtiNWZvZGVsamJhTjdpVTZEUHBMVTRUQU5vQnZmN1kvbVY1dmJaVnowbHREbU44UEJ1SFNCL1kvSHVwVHZpVVhBVkRmb3NHdFFDU0hTZ3J5QXo2ZDZ3SkZDOVFNcEJmOElTOGtQQ3VzK2ZtTWhiU1dUL29iNjkrV1RrdlBTVWhVQ2x2eUk0Rkc5NXdONXFoU2l4R3Rvd2FMUmx0bzNnc1RYbWJic0s3N2M3RmEyRzdycjlUeWZ6VExHcVdjU0lsTDFvWDB5SE4iLCJtYWMiOiJiMjIzNjgzYjAwYjE1OGNmMTMyZWMxNTMyM2VhMmQwYTFmNDM5MmY2MTA5YTc0OWU1MzY2MDk3MDg5OTRjZGMxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:14:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImhhRE55VUhqUTh1ME1IczNRSGJla3c9PSIsInZhbHVlIjoiaGpPRTEreE13ZHNUTWFWQUlxd2Y2MVJ3TVJXMURTbTVIRU93Ykl2T3BVSXk1SCthMkpoNXdpM2l2QmRBSS9BblNBcVZuOCtlY3hhcjRjZ0NqNzk2eUN1QlRwUHNkNFdXT25pRDNXZ3oyVkkxclVnZytxcnIwbEIyRmpIaTNoOUhXVWtta09FcEpkR2VMWWxKUGY3bG96SVhHVkZ1TnZoaHdsRlVZUklLMzlIeGVKcnZKemMvdnd2VE1wVEJETSt2Q2MzemxGYU83QjZSbW9CNm85T3RoTk5GWUtzWEIzTHVRQllCZ2NyT3dqNVdpb2RQa1RxYVRBMVlzRGZuTm5JV1VuMjVzelFKRElHc0RCRVNETVBmaThxNWZEYWVTT0twamRLOE5zOCtmUVpKb1ZhaEVTdGNoaldlR3RCclVsOFRxTXpNZ3lUd2hCanFBaXRiWm0xb1ZNNG1HSTZYR2FTa01KNElGV2FjMVBUcER2RGtYdFpnaW9YZEJibXE2bnRVRUZIYXc2LzBZV2ZUenhqYUtCZmh1UXVTeFhMYnM2MEpUV0ZpV1hCMVVBZHRzWXlRT29RU3ZQcUlpREpkVzNwVkhZOUhKUERjMDhobjdwNXp4MHA5djhiSFRDVDJQTStKQ282MTZuYmZBRy83YjlWZVNXczRhZnBTSE1xZmpNaXciLCJtYWMiOiJjMWY2MWVkMGM5NjU5YzMzNTA2MmE2ZmQ2NGFiYmY4ZGI3YzZiMmRlM2I0NGM1ODQ5NmM1YzNjOWQ3NmNjYTg1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:14:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1993623205\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1889631983 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1889631983\", {\"maxDepth\":0})</script>\n"}}