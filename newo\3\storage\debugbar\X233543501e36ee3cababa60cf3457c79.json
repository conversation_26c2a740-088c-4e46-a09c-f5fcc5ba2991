{"__meta": {"id": "X233543501e36ee3cababa60cf3457c79", "datetime": "2025-06-17 05:40:50", "utime": **********.473319, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.091075, "end": **********.473357, "duration": 1.***************, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": **********.091075, "relative_start": 0, "end": **********.26582, "relative_end": **********.26582, "duration": 1.****************, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.26584, "relative_start": 1.****************, "end": **********.47336, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "208ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02605, "accumulated_duration_str": "26.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.349885, "duration": 0.02194, "duration_str": "21.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.223}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.397835, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.223, "width_percent": 6.564}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4456592, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 90.787, "width_percent": 9.213}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750138848236%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImpPSTdsMjhFWWNLdkJRWnZkT1dIc3c9PSIsInZhbHVlIjoibnNmN29nRTl0MVphL1lnTXhxNUpMQkFlb2psK3ZXZ1RJQVk0RmtkaDNCNjdxUGFZQ0U3YkdBdU1BVXpDL2E0VzV5VmtkWUllQ3JqWklnaFlKaUhxMzgwV2ZhckowV0NVL1ZmL3hCVzBsY2NBQyt0cWh1RVZHM3oyZFZIOGpPR3d3NDlCNHB1eEkzVThWQ0RIcWtOemQ1dy9oR24vVG82amthWDVkeEg1aUxkb1FyamFjWmRLc1AzU1lnSEUxN3UrdU5MQUplSXVvWkVUdmQyMzZIdWFYa25PV2hKMzR5dVZwU2JYZWNhL1dhYkpIbFJuUExlZEFCWVdMYnhneXQrTE1CZmpXRitZaEJiNXFHQ2FWcnh1V0RhV1pHMEwrcVJnb1Z2OGgyVjl6VGlld3YwVkNQVk1nbFRmYW1VelFybHltQ3Zxa1pRQ0RGYzluVnVWZWdYaExZbHpwT21NVHdrbzBHQXRnaEowQ0pPK3dWRTNqTmRCRCtNRXl0TUMvRjZXMXlHb2d1TFdiSDRKWDhwVGRjUHdHWTQxNnlySThoN1p3UE1GK2NPWTFteGVuK1QreTdEeGVaRVhtTGZvaU1xYXFoRnVHYkhBL1A2NjlLWmpPVTRXQ2dEdXNCdngzbENWVTVPQU1MZTZWNXl0RktXaHdidkZUdCtHV2U4d1JpU1ciLCJtYWMiOiJkYzNhMjJlZjgyNDAzZjI1ZDMyOWMyY2UwOWRiMmM3NDgwNGUwZWM2ZDAwNjljZjEwNjM4ZDZjMmZiNWM0NDgyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkhkUEJYK2dJdjBEOUtVNU1oWVNLdEE9PSIsInZhbHVlIjoicW1XekZ4UUIxNmdiUklkWFdSVWl3eDIyUmFEdXRYaTFja0RvK2llTzRzaDVNRjJ5d3VVVk9PTnhPYSt6ZVFlN0RLZHhxekVVVWEwVjE0QXVNSlhReTlYUGZ0WGg5aWwrcFZmbFNkZkdXMnhTYm9ydkF6VXpGTndob0E3bWxMZ1J0NWdQUXFtUk5KNHpzcDh3aWRqRzQzaFkyRzZWa2VWNzNHZjdpNjQvMGtYdmxQWWlCTHFNMUJraURDaWROcFA0ZFlNTWFRQUpMQXZRRTR3NXhqMTNQY005SHpWc3RpREFoenNsOW9YeTJ3ZldIT0ZBdmJSRWlyN2JoemlXTC9hdTExWGJxOFZZaExtb3Z1QS9zV0pSZWFaODg1cGc3S2h2QmRON1d1UFpJdjJpSzg3bUFPV0R6dVJFY2dVeENBUU9QL01lS2hBS2tmOGdmbFJRZVlBYkxydWkxT0RSL3pRWGdLUldGZzhBaXlBOU4zc1c5RXFxSzBrWjQzYjJQYTE5aU9HMVYwNkN6QlZqczBQRURwR0FMODA3anFYNEJCMDU2Q0J1Z3NJbkFGdVFkbGpGdlBsL1B0cVdmMU81VlYzS0dBMUZtSERpM0s4NU1tQ3ZkdXF3UWFHL0ZFNk5hcTJCbUhVNnpza09LdDEyS25FMXRvTTBkdHoraWpWK1lGaEoiLCJtYWMiOiIyODdiMWY1NmIxZTc2YzEyZDQ5Zjk3YjMyODU3YjFiMWFkMDk3MjE1MTUwYzdhY2I3YzIxMjEzYjZiOTRhYmI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1101192390 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MsEpVpyAabX9l6kTbr1IUggTy4SSXp1me81GakZC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1101192390\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1249575385 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:40:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InN1NUlCbnpSRWRMTlBLTyt0QW9OUmc9PSIsInZhbHVlIjoicnNUdWtmWmozS3B6TVJKNDBpN3IrT0l3MysySGFtRTRHR2RwZE1iUiszRnk3ekhPOHdQV2RIZW03ZlBhamIybEJVN1JSOW1vYkV5dDVMa1N1UHFNUDdRb1dRYk9Bc25RWkNqTTRCY3ZVWjVyeGlEc3g0b1dFVit1cUtXVzVxbnQwZitwL28vQWUwcGlaSm5uUlo3NCtpd1lOc3I5VUQ2citxSC85OUE2dGZiei8rTGtzQk1tb3dQRU5MOUJoL21OUVBqMTd6aXVzTWxlZjNEUm1zZ3VXN0VnWlB4UUNaUVRLWkZDVEEzZjdGWDljSmZ5TlQ4SFVCcU90ZmVWc28wNE5Lc0hRbS9UdFFIWVhjUjF0Mjl1T3NDOEI4SU9tQWozVUVaTElFc2NvK0M1aTdpeURacVk1bVVOSHpzS0duRkllMC84M0U5aG1xcTVOR3kzZURZN1VaM2EwQXhwM1ZaWEFaNVVveTZpUjNUdDBIUlljVHRMMTh4VXpWeS9TY2l6Y1NKUm9LNnFTT3M4d0ZLd3o5WlZkekN1ZmlwQlRSd3haM3phMU1heE1JMVJCK1ZvbDFBbDV6WnNwY0xjUklJalVWNDVQb3RwYkN3cnhmU1BCOU5NdDZ4b0o1K2FkOWFOLzJwNUJ4a2dCMnNHVGhUZTc1dnhtbUliVGhvK09tYnQiLCJtYWMiOiI3NjFiNjFkMTExMTNjMmY2YTAyZWRkYjhiZmI5YzljYTUwZTQ3OTc1MmNlNjZiNDA3MjI5Nzk4NzgwYTlmZGQxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik1xSlFKUkhpRXh0cmt1UXdTODNvNFE9PSIsInZhbHVlIjoiZUgreHl6ODNld0o3bDZpWnpleFhQQmxJRk52WlZTdmYyTG1QQmdyYXVFb2pINzBzQlE5U2lWYnFDSlZYMWRHbDFXclRWM29qQWJSenV6cXE2Qk83OWVGV1JsendxMFVJbXhRclp0YmRESXplSEcyV1RLYVVUT3o1c2J1a0RwMjZNRTFxVG5pY1hucnRYRmRLRDhGLzhwUjd1Z0pTUjJNK0RHaHJCeVArTEFjNTlDd2V2d0NTWUt4emd2ejBOd2V3VmtmNHl5QTlFUThGYTRCMk93TGlCQ1pQTWtLTkhNS2hsSHRHZEJKTGQ1ODJNTkJGblBQQXF0VjAzSCtxWlJuN2h6Y2tHWjBxWFd2QlMzZ2U3VSsrdmJndXpkUURQZnlxMXlkaXNPMzFLNG9YRitxbmNqdnBaSGdtNytGaC9VdEpmdUxIb1hudS85UDVDcHQ2Qno5MVhIT0M5YnBFaDlCMncvUTZ3cDZpWDJCblhWY1ZkMXh0cDdEeWM1RHd6aHRBVldmQ2dZb0hmRnNWZjNUMmlyTmNsaVNyTTl1cEpCVnFxMm54VVdYbk8vOTJMYjA5T1lla21ocTUwZjRkUUUyS3hmNVBzb2FoTnA0a1FYdE1KMmtib0pCR2x5ZFBSNUthbVlyRWRQUGlaQVZWMDA5T0dZR2QrVVBET1Z6NlBFZXMiLCJtYWMiOiJkMDQ1OTA4MDMxYTcyOTlmY2Q4YzgzNTRlOGEwNjM3NzBiNDVmZjM4N2JhMWU3NmRmZjk4Y2M1NThkMjUxZWI3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InN1NUlCbnpSRWRMTlBLTyt0QW9OUmc9PSIsInZhbHVlIjoicnNUdWtmWmozS3B6TVJKNDBpN3IrT0l3MysySGFtRTRHR2RwZE1iUiszRnk3ekhPOHdQV2RIZW03ZlBhamIybEJVN1JSOW1vYkV5dDVMa1N1UHFNUDdRb1dRYk9Bc25RWkNqTTRCY3ZVWjVyeGlEc3g0b1dFVit1cUtXVzVxbnQwZitwL28vQWUwcGlaSm5uUlo3NCtpd1lOc3I5VUQ2citxSC85OUE2dGZiei8rTGtzQk1tb3dQRU5MOUJoL21OUVBqMTd6aXVzTWxlZjNEUm1zZ3VXN0VnWlB4UUNaUVRLWkZDVEEzZjdGWDljSmZ5TlQ4SFVCcU90ZmVWc28wNE5Lc0hRbS9UdFFIWVhjUjF0Mjl1T3NDOEI4SU9tQWozVUVaTElFc2NvK0M1aTdpeURacVk1bVVOSHpzS0duRkllMC84M0U5aG1xcTVOR3kzZURZN1VaM2EwQXhwM1ZaWEFaNVVveTZpUjNUdDBIUlljVHRMMTh4VXpWeS9TY2l6Y1NKUm9LNnFTT3M4d0ZLd3o5WlZkekN1ZmlwQlRSd3haM3phMU1heE1JMVJCK1ZvbDFBbDV6WnNwY0xjUklJalVWNDVQb3RwYkN3cnhmU1BCOU5NdDZ4b0o1K2FkOWFOLzJwNUJ4a2dCMnNHVGhUZTc1dnhtbUliVGhvK09tYnQiLCJtYWMiOiI3NjFiNjFkMTExMTNjMmY2YTAyZWRkYjhiZmI5YzljYTUwZTQ3OTc1MmNlNjZiNDA3MjI5Nzk4NzgwYTlmZGQxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik1xSlFKUkhpRXh0cmt1UXdTODNvNFE9PSIsInZhbHVlIjoiZUgreHl6ODNld0o3bDZpWnpleFhQQmxJRk52WlZTdmYyTG1QQmdyYXVFb2pINzBzQlE5U2lWYnFDSlZYMWRHbDFXclRWM29qQWJSenV6cXE2Qk83OWVGV1JsendxMFVJbXhRclp0YmRESXplSEcyV1RLYVVUT3o1c2J1a0RwMjZNRTFxVG5pY1hucnRYRmRLRDhGLzhwUjd1Z0pTUjJNK0RHaHJCeVArTEFjNTlDd2V2d0NTWUt4emd2ejBOd2V3VmtmNHl5QTlFUThGYTRCMk93TGlCQ1pQTWtLTkhNS2hsSHRHZEJKTGQ1ODJNTkJGblBQQXF0VjAzSCtxWlJuN2h6Y2tHWjBxWFd2QlMzZ2U3VSsrdmJndXpkUURQZnlxMXlkaXNPMzFLNG9YRitxbmNqdnBaSGdtNytGaC9VdEpmdUxIb1hudS85UDVDcHQ2Qno5MVhIT0M5YnBFaDlCMncvUTZ3cDZpWDJCblhWY1ZkMXh0cDdEeWM1RHd6aHRBVldmQ2dZb0hmRnNWZjNUMmlyTmNsaVNyTTl1cEpCVnFxMm54VVdYbk8vOTJMYjA5T1lla21ocTUwZjRkUUUyS3hmNVBzb2FoTnA0a1FYdE1KMmtib0pCR2x5ZFBSNUthbVlyRWRQUGlaQVZWMDA5T0dZR2QrVVBET1Z6NlBFZXMiLCJtYWMiOiJkMDQ1OTA4MDMxYTcyOTlmY2Q4YzgzNTRlOGEwNjM3NzBiNDVmZjM4N2JhMWU3NmRmZjk4Y2M1NThkMjUxZWI3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1249575385\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2048788645 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2048788645\", {\"maxDepth\":0})</script>\n"}}