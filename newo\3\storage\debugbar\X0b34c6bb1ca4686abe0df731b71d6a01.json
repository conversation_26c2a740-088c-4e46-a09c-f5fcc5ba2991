{"__meta": {"id": "X0b34c6bb1ca4686abe0df731b71d6a01", "datetime": "2025-06-17 05:40:57", "utime": **********.785122, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750138856.370468, "end": **********.785152, "duration": 1.4146840572357178, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1750138856.370468, "relative_start": 0, "end": **********.578698, "relative_end": **********.578698, "duration": 1.2082300186157227, "duration_str": "1.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.578729, "relative_start": 1.2082610130310059, "end": **********.785156, "relative_end": 4.0531158447265625e-06, "duration": 0.20642709732055664, "duration_str": "206ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45156232, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02523, "accumulated_duration_str": "25.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6772149, "duration": 0.02284, "duration_str": "22.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.527}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7314909, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.527, "width_percent": 5.153}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.755794, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.68, "width_percent": 4.32}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-896241361 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750138848236%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRUT20vSmpUYTNDanVJeXBNMjlaUHc9PSIsInZhbHVlIjoic2tzVldHSnozeHd2eWFFRGdBY0I1QzR0QUJxcWN3Qk9ybjNtMWVlQnJrNHJocjlNZ2RhaGNINWlTcUhWTXZCMDhLNktUMWdQNENteStlek9MZitOdlF6TEdXUTdjNmRrUVVhT21BUDRoODIxS3RDRDJySmVkakJ1a3BMczJ4cGRCMk84UmY1eEliVjRDSXRKVGl4YytPZHZzK0RObVpndXlrR25jTjhxU0NrdTZnZlBYTEdZaVRkazEvMHp6QzZDUHhrMVNoVjRUck1OQWxUTWVZdzB1UFpsVTBUN2JHaGtDSkNndEdqUEp4MFhOTld1N29zandLSDE3clJhV05nMmc3aWRjT3QzWWdobkJWbEJFak5TM1ZidzRzZXkveWR6REptaXRPQmFJTW82b29HS25VWndRKzZKbG1RVEp6cGRLUEs1Ri9zWXlvOXdLOWowditzUS8ycjRtc3p0STF1NElhRmRzMUxyRlh4UFpadnMwNmgvYitWUlI5Y0pBMFlhRFhHSmt1OThqclZSanpjWlhVNS9lZlk1cHJxVTRXMG8vckM4MkFOT0Z0TTFEU3JmVjdCTHVyTDZWS0wvUTNKZDJ2ckNkWlRhWlQ5STFDblBMRjlLZFp5bGFGSldVRDRYN25MTTM0WWZKWi9pYjlpUjVQVXNhNDdHek5McHlIaG4iLCJtYWMiOiJhY2Y4NzM0NjYxMjcyODgzY2MxYjRjODYxMDNmZTg0M2MyOTdjNWFlNjAwN2I2ZjM4ZjhjNjE4NjA5Mzg5MTgyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImRXS0d1MnhrclFJd2lXN2NpSnRLM3c9PSIsInZhbHVlIjoiRngxczJtNkVSeXk5Z2dQekdBNFZNQmlzbUpLV0ZoVnFabzdzaDJrTk9iWVhnbEhsUjU2OVFrR3hoeXpnVVlMZ2FoYk9VWk9CUXBuODc1aDBhS09wb2ZJWWV6NmlFREtubzB1ZkNadnNUTnV0TGdRWkRqOWZXVlBWVWpHbS8vUlhoSEtTZ1c3OTdvZGlyVE96YUZ6djVPUGZIOVhrQmxBbXVORTZHbmcwQ0dZdTNNSnJJbTl0K3hQRkxKcTR2M2l0RE4vZnRTK3BKa3hlLzZvcng2cTMwV09yaFpSQUI1MEpyODFRTm5wYzNnZzV6Q2tOWVUxSXp2bkptK0Y0WnQ1Z1N6Mlk3bkNhZmx3WFk5b3RWaktFMlRuZFpCMlEwMUtvbStlSG5PbmI2c1NyR1BYQWdVNVJleHFJL3NHalVEUk9Va2hMeFVYNTYySjhtU1lubkNlNHRaNlgzS0NQOWxVK2V1R3hmdzhzbTJIbStaV2RQbkl0SWJJdVYxU0hYWnhERjNpQm1ZRCt3aWVRY205TGhLS3EwenpPUW5GcVowbHd3QmNtcXhmNHpoRDVrQ3pjQktCeGxSaktXUGxvT1JYRjN4cDNoVW9zd2NBK1p5QWxpTzJHbzNjMXBrYU1ZN2JpdDJlRlY2YjhqMzM4NHkvenNWMG5IL2RVZ3poc3djS3giLCJtYWMiOiIxZDdlNmNhZjg2NmM2ODVkNDQ3M2EyMmM4N2JhZWZmNTdlNWEzZDg2MTI5YjlkNmUwZGQ5NzNlNWQ3MjBhOGFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-896241361\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1037018973 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1037018973\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:40:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJSTmVWWGZIaTc3QlJEbHhyS1BxdFE9PSIsInZhbHVlIjoiazl1Tm4wZlFUUUx6WVUvN1o4RjdhNXhWWSt4YnczY0JYZUZPRkJMQVhRZXZ6YURSN2Q3bDd6akFOWFg3UC9uaGV4dUs5UldlY25lenlrWjRzNHJsS3YxNGEvUEN6K01mQnh5bHB2WmlLLy9nY0x1TTRBSGJld1FTUTRQdDArYmRzV01GVU1pYXJPR0NSNW1oTExPVkVzQkRIalFrek45Q2hEN0phTVZUcHp0MSsrdWZlT2Fad1JXLzE1NzJXem0waDNlV2sveW56dUNoR2ovemtWSHZSTmF1V2d0bUlnYjZRZ3JZS1hzb2FjTThSd091L21sOGJrc2MvYmpweGl1Z3lLUmJ3L1RkR0lNUmpPOUptSGJjU2lrdm1sNityeWpnQkNjYk1xZHFlWDZaS3ZocmtlZmovT296VWZLV0dya0s4NUNhOW42SEJyVDlPOWFaa2pFRlhraHI5Q3BvdmJmSVBWOEs0T3Fta01Jd0hoVGN0TExRQnpqWThDTjEwWGVwVEJGam9rdFNJU1dpaithd2lGcDdJai9PNzY4RSttUlpmUFpseE5Sa3BLRXpYOFkyVDRabVNvSGhhakg4TmtoV1dFa1lIQjhTYVNCajBLbjByMkRVVDFhRFoxVlc3TUYyNWlFOFZUTFBLUG1GMVVFMDYrRktYWW8rSUJnNlZRQlkiLCJtYWMiOiI3ZDc5MTI2NWM2MjQwNjU2OGFiYTY0ZThjNjc4MDA5YzY3MDllZGI2NzQzMGQwMGYyNjYwMWI5OWY2NTI3YTU1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InZlNUJZZUJxMi9GTitYeXh2cFZMVEE9PSIsInZhbHVlIjoiWHN2dnFPNHVRRlZselNKSDVYYVQzUkgzbVBhK2hORjFzZGZlNFJBR1BsY3VDWWo3ZlZPajlaRDBPU3lUOHU2aC9RZHROZHFNNTdyV2VPejgxbWRKNzVyYXBkUmVNU1B4YkZaRU1OUStVRklHdXZFSUU2ZzZWWmhELzMyRjRGc1Ywc0xXQk5WYzBhajhSc1NkRkw5eWxZWmxjZmUyaDJ6SFcwY25WNW9PQldWZEJvS1dCdHVVQWRCNk5vaUZ0bWwxUEV0OXNrTkJhaFI1eEJseDEzNlN2WVJ2UEpHUmdvVnlsRExlUVd1S1FSWmlQa21pamNiaXMyZHZreGJxNGcwbUNTZzNOZXlsTEpQMm8veFQwNnBDVFlId0RkQ3pyRGZTTFRaK0dTRzVmdHBUeFQxMkdEdDNkZWFUWk54c2M3VldSVWNoeUU5dytYS09kdVBTWEJ6RkxZYVFvRnR4cWhUOWRFYkNPRUYvbzllYUNubm5FZGdvcXc0SzZNWmFGTHV6N0hmRjY4cGsrelpxK3lLQm1reDYrQmRXNzl2N0VUUnlUQzUvS1pJTUxORnJyb3VNdHFkd0h6TzcxODlEUWRDa1JPdHlBK3I1ZGMrMU9lMUxkNTJkcnRzSHBPWWpRMDUzMEhEVklpeGxidEhDcFJxTzZob0prUEZHZmtqcC9wTnUiLCJtYWMiOiIwNTQxNzI0MjdjNDYzMzZiM2Y1YjU3Y2Q3YzVhNjljOTc0NjkzNzdjOTU0NTc2NGVlODJiNjFjYzljZGFjMGYxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJSTmVWWGZIaTc3QlJEbHhyS1BxdFE9PSIsInZhbHVlIjoiazl1Tm4wZlFUUUx6WVUvN1o4RjdhNXhWWSt4YnczY0JYZUZPRkJMQVhRZXZ6YURSN2Q3bDd6akFOWFg3UC9uaGV4dUs5UldlY25lenlrWjRzNHJsS3YxNGEvUEN6K01mQnh5bHB2WmlLLy9nY0x1TTRBSGJld1FTUTRQdDArYmRzV01GVU1pYXJPR0NSNW1oTExPVkVzQkRIalFrek45Q2hEN0phTVZUcHp0MSsrdWZlT2Fad1JXLzE1NzJXem0waDNlV2sveW56dUNoR2ovemtWSHZSTmF1V2d0bUlnYjZRZ3JZS1hzb2FjTThSd091L21sOGJrc2MvYmpweGl1Z3lLUmJ3L1RkR0lNUmpPOUptSGJjU2lrdm1sNityeWpnQkNjYk1xZHFlWDZaS3ZocmtlZmovT296VWZLV0dya0s4NUNhOW42SEJyVDlPOWFaa2pFRlhraHI5Q3BvdmJmSVBWOEs0T3Fta01Jd0hoVGN0TExRQnpqWThDTjEwWGVwVEJGam9rdFNJU1dpaithd2lGcDdJai9PNzY4RSttUlpmUFpseE5Sa3BLRXpYOFkyVDRabVNvSGhhakg4TmtoV1dFa1lIQjhTYVNCajBLbjByMkRVVDFhRFoxVlc3TUYyNWlFOFZUTFBLUG1GMVVFMDYrRktYWW8rSUJnNlZRQlkiLCJtYWMiOiI3ZDc5MTI2NWM2MjQwNjU2OGFiYTY0ZThjNjc4MDA5YzY3MDllZGI2NzQzMGQwMGYyNjYwMWI5OWY2NTI3YTU1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InZlNUJZZUJxMi9GTitYeXh2cFZMVEE9PSIsInZhbHVlIjoiWHN2dnFPNHVRRlZselNKSDVYYVQzUkgzbVBhK2hORjFzZGZlNFJBR1BsY3VDWWo3ZlZPajlaRDBPU3lUOHU2aC9RZHROZHFNNTdyV2VPejgxbWRKNzVyYXBkUmVNU1B4YkZaRU1OUStVRklHdXZFSUU2ZzZWWmhELzMyRjRGc1Ywc0xXQk5WYzBhajhSc1NkRkw5eWxZWmxjZmUyaDJ6SFcwY25WNW9PQldWZEJvS1dCdHVVQWRCNk5vaUZ0bWwxUEV0OXNrTkJhaFI1eEJseDEzNlN2WVJ2UEpHUmdvVnlsRExlUVd1S1FSWmlQa21pamNiaXMyZHZreGJxNGcwbUNTZzNOZXlsTEpQMm8veFQwNnBDVFlId0RkQ3pyRGZTTFRaK0dTRzVmdHBUeFQxMkdEdDNkZWFUWk54c2M3VldSVWNoeUU5dytYS09kdVBTWEJ6RkxZYVFvRnR4cWhUOWRFYkNPRUYvbzllYUNubm5FZGdvcXc0SzZNWmFGTHV6N0hmRjY4cGsrelpxK3lLQm1reDYrQmRXNzl2N0VUUnlUQzUvS1pJTUxORnJyb3VNdHFkd0h6TzcxODlEUWRDa1JPdHlBK3I1ZGMrMU9lMUxkNTJkcnRzSHBPWWpRMDUzMEhEVklpeGxidEhDcFJxTzZob0prUEZHZmtqcC9wTnUiLCJtYWMiOiIwNTQxNzI0MjdjNDYzMzZiM2Y1YjU3Y2Q3YzVhNjljOTc0NjkzNzdjOTU0NTc2NGVlODJiNjFjYzljZGFjMGYxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}