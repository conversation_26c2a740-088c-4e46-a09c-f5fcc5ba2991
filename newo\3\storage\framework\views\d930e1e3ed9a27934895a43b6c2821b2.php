<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('تحرير أمر الاستلام')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('receipt-order.index')); ?>"><?php echo e(__('أوامر الاستلام')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('تحرير أمر الاستلام')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css-page'); ?>
<style>
    .product-row {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        margin-bottom: 10px;
        padding: 15px;
    }
    .product-sku {
        font-size: 0.8em;
        color: #6c757d;
    }
    .btn-remove-product {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
    }
    .btn-remove-product:hover {
        background-color: #c82333;
        border-color: #bd2130;
        color: white;
    }
    .summary-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
    }
    .summary-item {
        text-align: center;
        padding: 10px;
    }
    .summary-value {
        font-size: 1.5em;
        font-weight: bold;
        margin-bottom: 5px;
    }
    .summary-label {
        font-size: 0.9em;
        opacity: 0.8;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-6">
                        <h5 class="mb-0"><?php echo e(__('تحرير أمر الاستلام')); ?> - <?php echo e($receiptOrder->order_number); ?></h5>
                    </div>
                    <div class="col-6 text-end">
                        <span class="badge badge-<?php echo e($receiptOrder->status_color); ?>"><?php echo e($receiptOrder->status); ?></span>
                    </div>
                </div>
            </div>

            <form action="<?php echo e(route('receipt-order.update', $receiptOrder->id)); ?>" method="POST" id="receipt-order-form">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                
                <div class="card-body">
                    <!-- معلومات الأمر الأساسية -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="vendor_id" class="form-label"><?php echo e(__('المورد')); ?></label>
                                <select name="vendor_id" id="vendor_id" class="form-select">
                                    <option value=""><?php echo e(__('اختر المورد')); ?></option>
                                    <?php $__currentLoopData = $vendors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vendor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($vendor->id); ?>" 
                                            <?php echo e($receiptOrder->vendor_id == $vendor->id ? 'selected' : ''); ?>>
                                            <?php echo e($vendor->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <small class="text-muted"><?php echo e(__('اسم المورد سيظهر في تقارير العمليات المالية')); ?></small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="warehouse_id" class="form-label"><?php echo e(__('المستودع')); ?></label>
                                <select name="warehouse_id" id="warehouse_id" class="form-select" required>
                                    <?php $__currentLoopData = $warehouses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $warehouse): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($warehouse->id); ?>" 
                                            <?php echo e($receiptOrder->warehouse_id == $warehouse->id ? 'selected' : ''); ?>>
                                            <?php echo e($warehouse->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="invoice_number" class="form-label"><?php echo e(__('رقم الفاتورة')); ?></label>
                                <input type="text" name="invoice_number" id="invoice_number" 
                                       class="form-control" value="<?php echo e($receiptOrder->invoice_number); ?>">
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="invoice_total" class="form-label"><?php echo e(__('إجمالي الفاتورة')); ?></label>
                                <input type="number" name="invoice_total" id="invoice_total" 
                                       class="form-control" step="0.01" min="0" 
                                       value="<?php echo e($receiptOrder->invoice_total); ?>">
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="invoice_date" class="form-label"><?php echo e(__('تاريخ الفاتورة')); ?></label>
                                <input type="date" name="invoice_date" id="invoice_date" 
                                       class="form-control" value="<?php echo e($receiptOrder->invoice_date ? $receiptOrder->invoice_date->format('Y-m-d') : ''); ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="notes" class="form-label"><?php echo e(__('ملاحظات')); ?></label>
                                <textarea name="notes" id="notes" class="form-control" rows="3"><?php echo e($receiptOrder->notes); ?></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- قسم المنتجات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0"><?php echo e(__('المنتجات')); ?></h6>
                                    <button type="button" id="add-product-btn" class="btn btn-primary btn-sm">
                                        <i class="ti ti-plus"></i> <?php echo e(__('إضافة منتج')); ?>

                                    </button>
                                </div>
                                <div class="card-body">
                                    <div id="products-container">
                                        <?php $__currentLoopData = $receiptOrder->products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="product-row" data-index="<?php echo e($index); ?>">
                                                <div class="row align-items-end">
                                                    <div class="col-md-3">
                                                        <label class="form-label"><?php echo e(__('المنتج')); ?></label>
                                                        <select name="products[<?php echo e($index); ?>][product_id]" class="form-select product-select" required>
                                                            <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $prod): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <option value="<?php echo e($prod->id); ?>" 
                                                                    <?php echo e($product->product_id == $prod->id ? 'selected' : ''); ?>

                                                                    data-sku="<?php echo e($prod->sku); ?>"
                                                                    data-price="<?php echo e($prod->purchase_price); ?>">
                                                                    <?php echo e($prod->name); ?>

                                                                </option>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </select>
                                                        <small class="text-muted product-sku"><?php echo e($product->product->sku ?? ''); ?></small>
                                                    </div>

                                                    <div class="col-md-2">
                                                        <label class="form-label"><?php echo e(__('الكمية')); ?></label>
                                                        <input type="number" name="products[<?php echo e($index); ?>][quantity]" 
                                                               class="form-control quantity-input" 
                                                               value="<?php echo e($product->quantity); ?>" 
                                                               min="0.01" step="0.01" required>
                                                    </div>

                                                    <div class="col-md-2">
                                                        <label class="form-label"><?php echo e(__('سعر الوحدة')); ?></label>
                                                        <input type="number" name="products[<?php echo e($index); ?>][unit_cost]" 
                                                               class="form-control unit-cost-input" 
                                                               value="<?php echo e($product->unit_cost); ?>" 
                                                               min="0" step="0.01">
                                                    </div>

                                                    <div class="col-md-2">
                                                        <label class="form-label"><?php echo e(__('تاريخ الصلاحية')); ?></label>
                                                        <input type="date" name="products[<?php echo e($index); ?>][expiry_date]" 
                                                               class="form-control" 
                                                               value="<?php echo e($product->expiry_date ? $product->expiry_date->format('Y-m-d') : ''); ?>">
                                                    </div>

                                                    <div class="col-md-2">
                                                        <label class="form-label"><?php echo e(__('الإجمالي')); ?></label>
                                                        <input type="text" class="form-control row-total" readonly 
                                                               value="<?php echo e(number_format($product->total_cost, 2)); ?>">
                                                    </div>

                                                    <div class="col-md-1">
                                                        <button type="button" class="btn btn-remove-product btn-sm">
                                                            <i class="ti ti-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>

                                                <?php if($product->notes): ?>
                                                <div class="row mt-2">
                                                    <div class="col-12">
                                                        <label class="form-label"><?php echo e(__('ملاحظات')); ?></label>
                                                        <input type="text" name="products[<?php echo e($index); ?>][notes]" 
                                                               class="form-control" value="<?php echo e($product->notes); ?>">
                                                    </div>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>

                                    <!-- ملخص الإجماليات -->
                                    <div class="summary-card">
                                        <div class="row">
                                            <div class="col-md-3 summary-item">
                                                <div class="summary-value" id="total-products"><?php echo e($receiptOrder->total_products); ?></div>
                                                <div class="summary-label"><?php echo e(__('عدد المنتجات')); ?></div>
                                            </div>
                                            <div class="col-md-3 summary-item">
                                                <div class="summary-value" id="total-quantity">0</div>
                                                <div class="summary-label"><?php echo e(__('إجمالي الكمية')); ?></div>
                                            </div>
                                            <div class="col-md-3 summary-item">
                                                <div class="summary-value" id="average-cost">0</div>
                                                <div class="summary-label"><?php echo e(__('متوسط التكلفة')); ?></div>
                                            </div>
                                            <div class="col-md-3 summary-item">
                                                <div class="summary-value" id="total-amount"><?php echo e(number_format($receiptOrder->total_amount, 2)); ?></div>
                                                <div class="summary-label"><?php echo e(__('إجمالي المبلغ')); ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <div class="row">
                        <div class="col-6">
                            <a href="<?php echo e(route('receipt-order.show', $receiptOrder->id)); ?>" class="btn btn-secondary">
                                <i class="ti ti-arrow-left"></i> <?php echo e(__('إلغاء')); ?>

                            </a>
                        </div>
                        <div class="col-6 text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="ti ti-device-floppy"></i> <?php echo e(__('حفظ التغييرات')); ?>

                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
<script>
$(document).ready(function() {
    let productIndex = <?php echo e(count($receiptOrder->products)); ?>;

    // إضافة منتج جديد
    $('#add-product-btn').on('click', function() {
        const productRow = `
            <div class="product-row" data-index="${productIndex}">
                <div class="row align-items-end">
                    <div class="col-md-3">
                        <label class="form-label"><?php echo e(__('المنتج')); ?></label>
                        <select name="products[${productIndex}][product_id]" class="form-select product-select" required>
                            <option value=""><?php echo e(__('اختر المنتج')); ?></option>
                            <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($product->id); ?>"
                                    data-sku="<?php echo e($product->sku); ?>"
                                    data-price="<?php echo e($product->purchase_price); ?>">
                                    <?php echo e($product->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <small class="text-muted product-sku"></small>
                    </div>

                    <div class="col-md-2">
                        <label class="form-label"><?php echo e(__('الكمية')); ?></label>
                        <input type="number" name="products[${productIndex}][quantity]"
                               class="form-control quantity-input"
                               min="0.01" step="0.01" required>
                    </div>

                    <div class="col-md-2">
                        <label class="form-label"><?php echo e(__('سعر الوحدة')); ?></label>
                        <input type="number" name="products[${productIndex}][unit_cost]"
                               class="form-control unit-cost-input"
                               min="0" step="0.01">
                    </div>

                    <div class="col-md-2">
                        <label class="form-label"><?php echo e(__('تاريخ الصلاحية')); ?></label>
                        <input type="date" name="products[${productIndex}][expiry_date]"
                               class="form-control">
                    </div>

                    <div class="col-md-2">
                        <label class="form-label"><?php echo e(__('الإجمالي')); ?></label>
                        <input type="text" class="form-control row-total" readonly value="0.00">
                    </div>

                    <div class="col-md-1">
                        <button type="button" class="btn btn-remove-product btn-sm">
                            <i class="ti ti-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;

        $('#products-container').append(productRow);
        productIndex++;
        calculateTotals();
    });

    // حذف منتج
    $(document).on('click', '.btn-remove-product', function() {
        $(this).closest('.product-row').remove();
        calculateTotals();
    });

    // تحديث بيانات المنتج عند الاختيار
    $(document).on('change', '.product-select', function() {
        const selectedOption = $(this).find('option:selected');
        const sku = selectedOption.data('sku');
        const price = selectedOption.data('price');

        const productRow = $(this).closest('.product-row');
        productRow.find('.product-sku').text(sku || '');
        productRow.find('.unit-cost-input').val(price || 0);

        calculateRowTotal(productRow);
    });

    // حساب إجمالي الصف عند تغيير الكمية أو السعر
    $(document).on('input', '.quantity-input, .unit-cost-input', function() {
        const productRow = $(this).closest('.product-row');
        calculateRowTotal(productRow);
    });

    // حساب إجمالي الصف
    function calculateRowTotal(row) {
        const quantity = parseFloat(row.find('.quantity-input').val()) || 0;
        const unitCost = parseFloat(row.find('.unit-cost-input').val()) || 0;
        const total = quantity * unitCost;

        row.find('.row-total').val(total.toFixed(2));
        calculateTotals();
    }

    // حساب الإجماليات العامة
    function calculateTotals() {
        let totalProducts = $('.product-row').length;
        let totalQuantity = 0;
        let totalAmount = 0;

        $('.product-row').each(function() {
            const quantity = parseFloat($(this).find('.quantity-input').val()) || 0;
            const rowTotal = parseFloat($(this).find('.row-total').val()) || 0;

            totalQuantity += quantity;
            totalAmount += rowTotal;
        });

        const averageCost = totalQuantity > 0 ? (totalAmount / totalQuantity) : 0;

        $('#total-products').text(totalProducts);
        $('#total-quantity').text(totalQuantity.toFixed(2));
        $('#average-cost').text(averageCost.toFixed(2));
        $('#total-amount').text(totalAmount.toFixed(2));
    }

    // حساب الإجماليات عند تحميل الصفحة
    $('.product-row').each(function() {
        calculateRowTotal($(this));
    });

    // التحقق من صحة النموذج قبل الإرسال
    $('#receipt-order-form').on('submit', function(e) {
        const productRows = $('.product-row').length;

        if (productRows === 0) {
            e.preventDefault();
            alert('<?php echo e(__('يجب إضافة منتج واحد على الأقل')); ?>');
            return false;
        }

        // التحقق من أن جميع المنتجات لها كمية
        let hasError = false;
        $('.product-row').each(function() {
            const quantity = parseFloat($(this).find('.quantity-input').val()) || 0;
            if (quantity <= 0) {
                hasError = true;
                $(this).find('.quantity-input').addClass('is-invalid');
            } else {
                $(this).find('.quantity-input').removeClass('is-invalid');
            }
        });

        if (hasError) {
            e.preventDefault();
            alert('<?php echo e(__('يجب إدخال كمية صحيحة لجميع المنتجات')); ?>');
            return false;
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\to\newo\3\resources\views/receipt_order/edit.blade.php ENDPATH**/ ?>