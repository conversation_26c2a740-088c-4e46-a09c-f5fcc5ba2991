{"__meta": {"id": "X1341ba7405ecfd7ec451492d58e86b69", "datetime": "2025-06-17 07:14:00", "utime": **********.109805, "method": "GET", "uri": "/inventory-management/products/8?search=&status_filter=", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.41774, "end": **********.109836, "duration": 0.6920959949493408, "duration_str": "692ms", "measures": [{"label": "Booting", "start": **********.41774, "relative_start": 0, "end": **********.913921, "relative_end": **********.913921, "duration": 0.49618101119995117, "duration_str": "496ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.913938, "relative_start": 0.4961979389190674, "end": **********.109839, "relative_end": 2.86102294921875e-06, "duration": 0.19590091705322266, "duration_str": "196ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47338128, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x company_operations.inventory_management.products_table", "param_count": null, "params": [], "start": **********.088502, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/company_operations/inventory_management/products_table.blade.phpcompany_operations.inventory_management.products_table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fcompany_operations%2Finventory_management%2Fproducts_table.blade.php&line=1", "ajax": false, "filename": "products_table.blade.php", "line": "?"}, "render_count": 1, "name_original": "company_operations.inventory_management.products_table"}]}, "route": {"uri": "GET inventory-management/products/{warehouseId}", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\InventoryManagementController@getWarehouseProducts", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "inventory.management.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=39\" onclick=\"\">app/Http/Controllers/InventoryManagementController.php:39-104</a>"}, "queries": {"nb_statements": 16, "nb_failed_statements": 0, "accumulated_duration": 0.037340000000000005, "accumulated_duration_str": "37.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9729881, "duration": 0.02028, "duration_str": "20.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 54.312}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0124412, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 54.312, "width_percent": 3.214}, {"sql": "select * from `warehouses` where `warehouses`.`id` = '8' limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0176952, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:42", "source": "app/Http/Controllers/InventoryManagementController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=42", "ajax": false, "filename": "InventoryManagementController.php", "line": "42"}, "connection": "ty", "start_percent": 57.525, "width_percent": 2.625}, {"sql": "select * from `product_services` where `created_by` = 15 and `type` = 'product'", "type": "query", "params": [], "bindings": ["15", "product"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0227408, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:48", "source": "app/Http/Controllers/InventoryManagementController.php:48", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=48", "ajax": false, "filename": "InventoryManagementController.php", "line": "48"}, "connection": "ty", "start_percent": 60.15, "width_percent": 4.821}, {"sql": "select * from `product_service_categories` where `product_service_categories`.`id` in (4)", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.034885, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:48", "source": "app/Http/Controllers/InventoryManagementController.php:48", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=48", "ajax": false, "filename": "InventoryManagementController.php", "line": "48"}, "connection": "ty", "start_percent": 64.971, "width_percent": 2.491}, {"sql": "select * from `product_service_units` where `product_service_units`.`id` in (5)", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0405228, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:48", "source": "app/Http/Controllers/InventoryManagementController.php:48", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=48", "ajax": false, "filename": "InventoryManagementController.php", "line": "48"}, "connection": "ty", "start_percent": 67.461, "width_percent": 2.999}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 3 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 56}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.045375, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:56", "source": "app/Http/Controllers/InventoryManagementController.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=56", "ajax": false, "filename": "InventoryManagementController.php", "line": "56"}, "connection": "ty", "start_percent": 70.461, "width_percent": 3.535}, {"sql": "select * from `warehouse_product_limits` where `warehouse_id` = '8' and `product_id` = 3 limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 61}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.050908, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:61", "source": "app/Http/Controllers/InventoryManagementController.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=61", "ajax": false, "filename": "InventoryManagementController.php", "line": "61"}, "connection": "ty", "start_percent": 73.996, "width_percent": 4.285}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 5 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["8", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 56}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.056118, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:56", "source": "app/Http/Controllers/InventoryManagementController.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=56", "ajax": false, "filename": "InventoryManagementController.php", "line": "56"}, "connection": "ty", "start_percent": 78.281, "width_percent": 3.107}, {"sql": "select * from `warehouse_product_limits` where `warehouse_id` = '8' and `product_id` = 5 limit 1", "type": "query", "params": [], "bindings": ["8", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 61}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.060569, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:61", "source": "app/Http/Controllers/InventoryManagementController.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=61", "ajax": false, "filename": "InventoryManagementController.php", "line": "61"}, "connection": "ty", "start_percent": 81.387, "width_percent": 2.625}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 6 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["8", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 56}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.065063, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:56", "source": "app/Http/Controllers/InventoryManagementController.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=56", "ajax": false, "filename": "InventoryManagementController.php", "line": "56"}, "connection": "ty", "start_percent": 84.012, "width_percent": 2.169}, {"sql": "select * from `warehouse_product_limits` where `warehouse_id` = '8' and `product_id` = 6 limit 1", "type": "query", "params": [], "bindings": ["8", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 61}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.068484, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:61", "source": "app/Http/Controllers/InventoryManagementController.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=61", "ajax": false, "filename": "InventoryManagementController.php", "line": "61"}, "connection": "ty", "start_percent": 86.181, "width_percent": 2.785}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 7 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["8", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 56}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.073071, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:56", "source": "app/Http/Controllers/InventoryManagementController.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=56", "ajax": false, "filename": "InventoryManagementController.php", "line": "56"}, "connection": "ty", "start_percent": 88.966, "width_percent": 2.517}, {"sql": "select * from `warehouse_product_limits` where `warehouse_id` = '8' and `product_id` = 7 limit 1", "type": "query", "params": [], "bindings": ["8", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 61}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.07779, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:61", "source": "app/Http/Controllers/InventoryManagementController.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=61", "ajax": false, "filename": "InventoryManagementController.php", "line": "61"}, "connection": "ty", "start_percent": 91.484, "width_percent": 2.866}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "company_operations.inventory_management.products_table", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/company_operations/inventory_management/products_table.blade.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.092868, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 94.349, "width_percent": 2.625}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "company_operations.inventory_management.products_table", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/company_operations/inventory_management/products_table.blade.php", "line": 52}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.097277, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 96.974, "width_percent": 3.026}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\WarehouseProductLimit": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FWarehouseProductLimit.php&line=1", "ajax": false, "filename": "WarehouseProductLimit.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\ProductServiceUnit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceUnit.php&line=1", "ajax": false, "filename": "ProductServiceUnit.php", "line": "?"}}}, "count": 14, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/inventory-management/products/8", "status_code": "<pre class=sf-dump id=sf-dump-1582386317 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1582386317\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-788667340 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>status_filter</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-788667340\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1818264532 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1818264532\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-151038529 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=jfj0s4%7C1750144433135%7C6%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpHVDlxeklGdUpmakdIZ2taanJhb3c9PSIsInZhbHVlIjoiWHZUQUNGM3lOTXhBWURWRC8rYktseFVXaFRBWGY4TU5vL3ZWWEIrcXhhWklSalozUHJIaUE4d0RHb0xEN3BYeHl5TDYzcllPZm5ZOHN2Z1RuWGRxNXo4b3lBK3B2ZFZDVHVoNXh6T2o5c1dnVE80aUxwdmNVRG1saXY2bGpLdWN3Q2NnMUNQeTNrc2RSRUxEUGJGeDBWUi9ITDU1Y3lxSXYxU05DaFFyQ3pSYzZmamJ0RGFicGd3T2haVlNmRkxmSXo4cnlzbzFGYmlTQVl2b1R1eks0ckhHSW5rUWJ5N2o5MUNMWDlPRys3Nzl1Sm5SRU5HREttYzdsQytSVlNFZldDenl6YTVOTng0ZGhqSDVXaXpyQ2pVdENCMGFrdFpQd25lUTh0aG02c0VibW5abFZIODh0YkJHM0JUdks1R250NVEySHRqVUR6U3gzUmdDSzJtZ0xOSnpvNThJTG1xbGFzNDZqVnR3VThlQTNCajQxRWZBYnptd1kybHVuZ3ZjdngyKzZ1aE9qdzBoQVV5ZlVJSGViODRlVWd2ODFrZ3YrTi9OK2lyd2tBNnVqMlFpTkgrbVhZeWFvM2dLUHBYNzZDV3M5OHQyWFlVYkFCeTFZRnpqZTVTWEFrSnlCZFF0aWp0RE04SzFJR2pCSjdXOThpOXNkam5IOWpvMHZyanQiLCJtYWMiOiI2ZDhiNDc1ZGQyMGU5YjYwYzMzMzU5MGY5M2U3ODgzYzczZjg3NWI3ZWJiM2MzNTYxOTVhOWRmNTNiMDdjMjYxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlZud2Fjc1JmMjAxbUxkOGRrT2pWWEE9PSIsInZhbHVlIjoiUWZ3ZS9DcXZYQ1NzRWJPZWFNNW1rdE1YQXplaFVFMjF2ekhzcWJQWXRZU1BHR0Rwa3VKZE13dlJoMDRnb3psY2wrTWpjeThKYnJjQy9tSU03cXMvVUtOYVhMdjF6aUpqWFhObTdaQkt3aGtWa0dyZjZGcGFZNERBQ3dXMWdKUVdtOURBakoycUFYUWVPRFlDS0JjaDlMZ1kzRDVuRkswRWtTcVNpSVF4NDh3ZTNJbzNlbHo1TVp2UTFzL0ZSNXdsSDNTVDc0UFR6VkYxTWloYUUwSnpvV1paQU12c0lRNzhjL0Y3ejNUZDYyck14NTVLWXVUOXNnVDl6WktWTlIxRVlIV2p1UUVDUk8zcE4rVEdETk0zMFNDNHFoZy9RUllCbWhSY3NEaEY0WU82OWEzdW5kUWRZQllNeG9qcUZMdHFPRWpJOHVsMXJ2ODN0eTBuR1Ewd2pIYW9PcjFNR2tLQTNCUmRNb29raE04WFJSRHl4Rlh5R3dwTXJ3TkY2NUV5bG13d3ZnL0ZqTm9ZNnhLTTFvR3U4MDNVMXMvcnNWUnlud3VYUHJEbjB0Q2dYK3BodXRGSEJXWUk0TVRjdlJNL011a09rS1NwUVI4elZKcHJicVlGb1ByZE85VGEwMk1GY1ZOcm5pWGZqOTRSU0kzRUh4ZkwwenA0TlB6bFZPZmgiLCJtYWMiOiIwNDNhNjdmMDZiMzcxZjgyYzNiZmYxNjFmNTE2Zjc0YzZmN2E0NTU4ZTRkZGUzOGRkYzRkOTMzNGFiNmFjM2I5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-151038529\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-831710901 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-831710901\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-608818074 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:14:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im84Rk1CU3VmV0RMam5sMlFwRjVFOEE9PSIsInZhbHVlIjoieCtJc0RNOHUzR25LUWhjMWxmS2N3REwreFNETG9DcllVckFndHlZMTJqK0pyN2pONkJ0UkNpWUNCZUtCNXlGTk43Q3hGVmswaitEM2lRSytsRGZIT3JLREJqd1l1T1VLeFIybzA0WVlaOWFyTVcxQmt5amx5ZW4zZ3RQR1lGRm1jQWN1ZlFDL3VLOWpPWjZIajBaUHY1bFYrSVZ4U0xCQis5MU12TzlRaHNCc09KdHFLbUh5cUVFMG1PcGlubWppaUZlcnkrTFhySTJGSXNRSXlxeWd5dkJFTUw2YkhRNmpwR0t6MmtWSExXcUxsRFRtbkFPRCtrMC9WdXBEQWN3Mzl6QS9KL2NNcmp1OHRqTXZGZHdnNnRVZDRrVkloQUMyOTI1RUx0cHhYL3YwczRVcVU1Sk5tbWlNTHFFYVRiN2hxMU5DRkJ1YldmRUMzNktOT0VRYnMrRFE3TnJKQ2cwZVNjVkVxMkRFYVFUOWN6UmkxZnpVbjhsVC8zWTlDRXZsWkxOZDlFcVV1eHBzWE0wVnlHMWtEYVV3dUUzN05xYldaS0t3Q3RRYThybUNXbjZESTg0SUxMOVIrUmdGeklWS1hjNHJTUEJBUjcrV1BMalhOL3FIRVRjclRjb2ljemlHblhYQjl2TFlaU2NZSkZpek40WlpYL2Z6TG1mUnVTbkMiLCJtYWMiOiIzODk0MmQ3ZjBmZWE1MDcyODM3ODVmYzM2NjcwMGI4ZDdmMDUzZjc3YzY0ZjRjNmIzMzZhNTUxYmViMWI1OGFmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:14:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ink4UzlDcDZMK0FKLzM2a3FEaTNvL2c9PSIsInZhbHVlIjoiZDd4VTNiTkIrdzFIZDc4VlNJZEJSZXh2N0VkZTErekVlaGQ5VXNLUDZPYU1XWG9UdEZRZlBxTTU0TGtqWm83TDVoN1FnOUtTaVJPNmRuRWg0UnkxWSt1SE9ONXFoYTI0NEt1NjBETTN0cXJkWXVmV3BUVGMyMUZZWUVkSms5aituaHRKemFTbnJaaFRVa09sd0I1VUkvRHBXNGFhVGtqWXdMSkQwazNqM1ZmcXdvR2R3bmFrUVZVb1VZOEoyNk43dk1zckYycW90NmpCL3lyRWlCdll6U2xjQ0tJVWVPbXl1d0dSWld5aFZpV1Q3NnZMUzJVRkFFa29lcUJpdW5QN2cwaGk1Q2k2OVJSclJQcXJXSFdzN1JMSFJSMEJlSDdOWGo1c2FIMnYxbVU5ZkNzTm80RnRaVDVQZHl6VEx5NDZjSzZQQUVCQWVYUWtHYlk5RnhOVmppT29lTU1NbEVFYnA5Vmx5VVp3YTArSktsSDF4dGtiakkvdkxIcm81eGUwTUsrQW54UTc2RkpKcTM3Q25HU0ZWSENJVjNRZE9VOVN5UTQ4VUF0dXg3bVdXOVRvR3dLMlBkTVJRM25RQmVzczhLcmpjNHlQZjBVaG9DL2RCZTNsMkJ3OEF3T2p0T3lmNWVRdnN5czNmRVBRcHdMUkdhQS82eGtMVUZIclpXYkgiLCJtYWMiOiIwNjdkY2U5NTc3ZGNiZWRkNWYxYzllZGY2YjRjNmJlN2MwZmZlMzRjYjQxNzU1MzQyNzYzMGQyYzhjNWJhNmJiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:14:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im84Rk1CU3VmV0RMam5sMlFwRjVFOEE9PSIsInZhbHVlIjoieCtJc0RNOHUzR25LUWhjMWxmS2N3REwreFNETG9DcllVckFndHlZMTJqK0pyN2pONkJ0UkNpWUNCZUtCNXlGTk43Q3hGVmswaitEM2lRSytsRGZIT3JLREJqd1l1T1VLeFIybzA0WVlaOWFyTVcxQmt5amx5ZW4zZ3RQR1lGRm1jQWN1ZlFDL3VLOWpPWjZIajBaUHY1bFYrSVZ4U0xCQis5MU12TzlRaHNCc09KdHFLbUh5cUVFMG1PcGlubWppaUZlcnkrTFhySTJGSXNRSXlxeWd5dkJFTUw2YkhRNmpwR0t6MmtWSExXcUxsRFRtbkFPRCtrMC9WdXBEQWN3Mzl6QS9KL2NNcmp1OHRqTXZGZHdnNnRVZDRrVkloQUMyOTI1RUx0cHhYL3YwczRVcVU1Sk5tbWlNTHFFYVRiN2hxMU5DRkJ1YldmRUMzNktOT0VRYnMrRFE3TnJKQ2cwZVNjVkVxMkRFYVFUOWN6UmkxZnpVbjhsVC8zWTlDRXZsWkxOZDlFcVV1eHBzWE0wVnlHMWtEYVV3dUUzN05xYldaS0t3Q3RRYThybUNXbjZESTg0SUxMOVIrUmdGeklWS1hjNHJTUEJBUjcrV1BMalhOL3FIRVRjclRjb2ljemlHblhYQjl2TFlaU2NZSkZpek40WlpYL2Z6TG1mUnVTbkMiLCJtYWMiOiIzODk0MmQ3ZjBmZWE1MDcyODM3ODVmYzM2NjcwMGI4ZDdmMDUzZjc3YzY0ZjRjNmIzMzZhNTUxYmViMWI1OGFmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:14:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ink4UzlDcDZMK0FKLzM2a3FEaTNvL2c9PSIsInZhbHVlIjoiZDd4VTNiTkIrdzFIZDc4VlNJZEJSZXh2N0VkZTErekVlaGQ5VXNLUDZPYU1XWG9UdEZRZlBxTTU0TGtqWm83TDVoN1FnOUtTaVJPNmRuRWg0UnkxWSt1SE9ONXFoYTI0NEt1NjBETTN0cXJkWXVmV3BUVGMyMUZZWUVkSms5aituaHRKemFTbnJaaFRVa09sd0I1VUkvRHBXNGFhVGtqWXdMSkQwazNqM1ZmcXdvR2R3bmFrUVZVb1VZOEoyNk43dk1zckYycW90NmpCL3lyRWlCdll6U2xjQ0tJVWVPbXl1d0dSWld5aFZpV1Q3NnZMUzJVRkFFa29lcUJpdW5QN2cwaGk1Q2k2OVJSclJQcXJXSFdzN1JMSFJSMEJlSDdOWGo1c2FIMnYxbVU5ZkNzTm80RnRaVDVQZHl6VEx5NDZjSzZQQUVCQWVYUWtHYlk5RnhOVmppT29lTU1NbEVFYnA5Vmx5VVp3YTArSktsSDF4dGtiakkvdkxIcm81eGUwTUsrQW54UTc2RkpKcTM3Q25HU0ZWSENJVjNRZE9VOVN5UTQ4VUF0dXg3bVdXOVRvR3dLMlBkTVJRM25RQmVzczhLcmpjNHlQZjBVaG9DL2RCZTNsMkJ3OEF3T2p0T3lmNWVRdnN5czNmRVBRcHdMUkdhQS82eGtMVUZIclpXYkgiLCJtYWMiOiIwNjdkY2U5NTc3ZGNiZWRkNWYxYzllZGY2YjRjNmJlN2MwZmZlMzRjYjQxNzU1MzQyNzYzMGQyYzhjNWJhNmJiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:14:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-608818074\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2128114596 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2128114596\", {\"maxDepth\":0})</script>\n"}}