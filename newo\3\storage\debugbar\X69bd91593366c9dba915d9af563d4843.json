{"__meta": {"id": "X69bd91593366c9dba915d9af563d4843", "datetime": "2025-06-17 06:54:09", "utime": **********.437639, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143247.793394, "end": **********.43767, "duration": 1.6442759037017822, "duration_str": "1.64s", "measures": [{"label": "Booting", "start": 1750143247.793394, "relative_start": 0, "end": **********.103589, "relative_end": **********.103589, "duration": 1.310194969177246, "duration_str": "1.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.103614, "relative_start": 1.3102200031280518, "end": **********.437673, "relative_end": 3.0994415283203125e-06, "duration": 0.3340590000152588, "duration_str": "334ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46095312, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.318378, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.341553, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.408219, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.419537, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.03857000000000001, "accumulated_duration_str": "38.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.227386, "duration": 0.01602, "duration_str": "16.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 41.535}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.256391, "duration": 0.013730000000000001, "duration_str": "13.73ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 41.535, "width_percent": 35.598}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2794292, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 77.132, "width_percent": 2.567}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.3203669, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 79.699, "width_percent": 2.93}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.344624, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 82.629, "width_percent": 3.708}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.378084, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 86.337, "width_percent": 3.137}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.388304, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 89.474, "width_percent": 2.696}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.394837, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 92.17, "width_percent": 3.241}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.411536, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 95.411, "width_percent": 4.589}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XTqfILO20ESOyUrbO2PEByvY2Oxsmxq0H0QyDSRn", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1041339808 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1041339808\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-257506097 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-257506097\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-801342268 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-801342268\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=lxd95%7C1750140707036%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ind0d1FJKzJJVmVDMnc1M3BIbVZXaGc9PSIsInZhbHVlIjoiSXY2WUloL3QrdXJISzM2UmJkb0hKV1BZV0dxVHBaMzVDdmFRcDhVODhLK3daMDdtTENEK1oxbEV3Z00vSlZNaG91UE5ZY1JNa0JCdk44aXRoVVJlTnlFU1JZOXhqcS9ralFkL0FyUmNBV2hRWVE1ZHNOYi9SSk40OHJCMTF2RnpNeWExaVBJbDJrTlJRTW13cUlIeVl2eTRmbHdVTUYzN3VZeEtFL0h2MU5GbDNIZENwdStXUXFvb1RUZFZubEZoOVdYQzlFN3RWOGxMMjRBU3R2REsrNzZJN3ZsRWRNY0ZDcmNtU1RoTFJCdGR5QUZUbnUzYW9kUXJrbHJ4VENyOVZwWlg4ZDdneHdDQ0VwWGNSazIzeHYzbEM4ck9EVDh4a1lMRndsZGpKMjIrZ2wyeHdhRXJ2R3dWeTEvaDhRem5rZFR3eWFlSnpDUjFHdmRUNHNsak05cUJ3dGhyWU4zdG02a1BySGx4cDc0Tm9YYzRhN2FrbTNSVWt4K2pKSW1zdEcyY0ZuSzAyUFdUQ094Nk1DVno1Rmw0TkN4Y0d6OFBpSUR6VCtzQnVzaXJiVHVkVE0rYlRMSGxvRXI4SkFxOGcvN08yQ0FBSGZBWmdxdzY2ajJwSzRKcS94TDV2WVVMZVdmMitnSUdvdDN1YTd2bWc3WW9NeFRua2VSdEJ6cDEiLCJtYWMiOiJmY2U2Y2VmYzZjMmZhMjllZjA2ZWJiNzNmZDRlYWZjOGViZjViMjRjODNlMjVmNGJlNDY2NzM1ODZiMmU5ZjIwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ii9EOXdSdzhZVmZyVkNxUGltWE9vd0E9PSIsInZhbHVlIjoicjhFQWNscXR0NmFNYlArNG5zOEliMGJqVjg0YzNNcEVGRmRURWVzdG93ekhBUXhLNVFoRFFHU3NGcmhJNWpKdStrTXlhbW1BTUFPeWN6bm1aelllcEdMb2Z4MjVEZFRjT2hHK1NlNDFvVVpvcWNUeENLRDdkenp1c3ZRd2dPaXJ5WUdqTDZMckhWbzk3WGdWei9kOXVHUzZ2eXZyQ3c1OGx4REg0eTBuNnMyU3NvRy9lKytJeDg2dzZ1WDkrNUZzWXhhMTBMdkNnYmdCVkwwZU80VWM3NVN2Mlp5SW16MG8rRHN4LzZDSjc3VWJWWGRxemRtZ29IVm5OQ0FydXpaUWNxbnY3ZXpyQ0Q1MjZ2dTNTdEcrVU9IQW1XNUZRVHo3KzRYdGNvQzJUdG1aK2hxbTRwU1FVNXc5bTBFME0rVnA4U0ZzRTE5T0FaYmttWHMzSHlrb2t3S2drcmlGUFdkWjluUWpieCtFZE1SbUpuZlNMdnl3cWwxQmxQalh4d2xPUldTQlE0V2JZZWkrSEZNTDlnc2ZBKzBwSlR5dm5Qb05PNnI0TnZSZmZLOXpnaC8xR2RGQzBBdytZaEpldTBPRXRFNEFJSk5RN0NyUWh2dHl6NW0wZ2NLaGl5clFXTXhqRHlMLzcvbE1Ha3NneGJhVjdacnFDSXZNc3VIQ3dQSkIiLCJtYWMiOiJiYjI1NDJiMTY2M2VkOTBjMGE4OGRjOGMyMDBmZDQzZDVhYWZmZTI2ZWJmYTk0ZTc3YzkxYzJiNjVkZTQ5YWE3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XTqfILO20ESOyUrbO2PEByvY2Oxsmxq0H0QyDSRn</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8dDZ2YVZ5BNkdHQRuaGlAK2aCC8rYkxUsxd4FSmV</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-566831709 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:54:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNJS3VEVkdWN2tXTTJDcmovdldJc1E9PSIsInZhbHVlIjoicVJGL1grZExVYktZbWxZendLR0hacEpST05jRUQzSjhZOWM4UlNQdEZqOEZiOWc5cVpJOHVLYm9HMWNTdkRXYmR1ajR3aEVYN0N5cEF4U0ZZd1BoWDI1TGdXRVE3eW5Xb04vdmRtMHU1Y3NhT0dVeUdFWnpsTVJXMkRwK1YxY1JzM2lqU2NVaU5tM1dGVisyb1ZEV3hyWS9KWGV2K3k1Zzkrd0dLUk1oLzAzNDFsdWpseVY1NEdBbFlGWkRFSGQwQXJjTGhXRmFVeEU3alNVSzdNdGFxcGRUczFsSXozdnhDeUJxUGc1YW84UERXUzVocU5YM0FjRjU5NFRGSGRhZTF6YldDQndKTkhnNnVzNFk4TjNNTjNDZkVIeSsxVkVYblRydEpQTWVEWGhzMW4zRXl2aXk1WW5WaWI5K3pMdkNCYzE2d3dxK3BFSS9uZHB3Z0p4Yng4MUdFZWJ5T3oySDhhLzlsV0wzZ1NzeVRzcndBU1FnejlVVnlNcnJOaVJ3aXUrZ1hsYnBzVXMwOG5KYWZubk5NeFdkWmtXWWtaNUtveDRWbjdmOERETzNKaEZkd0NIS0NYaEtrNjJRaHl5OFAxUWNMSHdtK1VKSUtTLzdIcmd0RjcrdGtoTHI2WVc2Z0xGeVdCTC9ra0VDSzZZK0JwSDhIMEozTE4vazhBaSsiLCJtYWMiOiJiNzc1NjU4N2FiOWYyNGY1YzU3M2I4YTNiZjE0YTEzM2Q0YTNiM2QxNTEyYzNhMzdlZDdkNDI4N2JkYTA2MzBiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:54:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlZNWFVXUi9FY0I2Qmh1OElUVHhZdVE9PSIsInZhbHVlIjoiQlhsTWV5cnl2Y3M0MnhmSzV0bWdIVEgxd1k5RkFNdW9RMzdndXh2UTdOKzlWd1JLSDJFL1JoMEhRNDRXM0NtRXoyYU1zUWJXMTZpd3FiM3RkWll6RE4zM1MyU09sQnR2eXlpUjZkTVMyZ1crVTU0MmwxcW9qYkgzOFkrWUt4VXBjeXlLbDFvNzBxNTcrNzk3ZFIxK2RXa1hJcmRoaEJXdDFGUHA4V2N6TWRJM0c2UWlITU41OTgxd1lqM2c0ZEZiZXZ6TGhnNlREWml2SUliTzFjR0FRN0IranNhanZrK3h0RGNXRXhTd00wdmxHalBqK083UXgvZlBjMzhES1EyaEtEcmZueFVNMjl3ekhaaHd4QkpzUEczamFaTDBIM2dQbitxZjFWOEVDTFBuUWx0U3h5UUx4UWpQbllXS3VHcXpUWUFnL2ZaSHowMVdwNFR4emoyUzRLeFpmWGI3bHRVa1gxYS85RlhvVkIvajlIWmE2MnVNVzdFdzQwUm1HdkIzTVZsZzd5anROZGhkMk9IMlZ4clZwOXJER0dURjJiVHdXdmNlSExwYW5vMS9FSXZvMEFxOUJJaUVOQjNONmYraGtkd0FncXdjbDFXc0t5a3ZvbnUxSFJ4T0Q1UnJ4UTJ2SjVOdGthSnIxeFlabTlUQmlwK3p2V3FtUmRHZHJFSmMiLCJtYWMiOiJlOWI5NGM5MTFkNmIwZDEwOTA2ZWQzOWUxZTYyMGIyZjMyMmQ2ODQ5MDZlMzU5MDkxNzE2OWY0OGYzOWVhZGFhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:54:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNJS3VEVkdWN2tXTTJDcmovdldJc1E9PSIsInZhbHVlIjoicVJGL1grZExVYktZbWxZendLR0hacEpST05jRUQzSjhZOWM4UlNQdEZqOEZiOWc5cVpJOHVLYm9HMWNTdkRXYmR1ajR3aEVYN0N5cEF4U0ZZd1BoWDI1TGdXRVE3eW5Xb04vdmRtMHU1Y3NhT0dVeUdFWnpsTVJXMkRwK1YxY1JzM2lqU2NVaU5tM1dGVisyb1ZEV3hyWS9KWGV2K3k1Zzkrd0dLUk1oLzAzNDFsdWpseVY1NEdBbFlGWkRFSGQwQXJjTGhXRmFVeEU3alNVSzdNdGFxcGRUczFsSXozdnhDeUJxUGc1YW84UERXUzVocU5YM0FjRjU5NFRGSGRhZTF6YldDQndKTkhnNnVzNFk4TjNNTjNDZkVIeSsxVkVYblRydEpQTWVEWGhzMW4zRXl2aXk1WW5WaWI5K3pMdkNCYzE2d3dxK3BFSS9uZHB3Z0p4Yng4MUdFZWJ5T3oySDhhLzlsV0wzZ1NzeVRzcndBU1FnejlVVnlNcnJOaVJ3aXUrZ1hsYnBzVXMwOG5KYWZubk5NeFdkWmtXWWtaNUtveDRWbjdmOERETzNKaEZkd0NIS0NYaEtrNjJRaHl5OFAxUWNMSHdtK1VKSUtTLzdIcmd0RjcrdGtoTHI2WVc2Z0xGeVdCTC9ra0VDSzZZK0JwSDhIMEozTE4vazhBaSsiLCJtYWMiOiJiNzc1NjU4N2FiOWYyNGY1YzU3M2I4YTNiZjE0YTEzM2Q0YTNiM2QxNTEyYzNhMzdlZDdkNDI4N2JkYTA2MzBiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:54:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlZNWFVXUi9FY0I2Qmh1OElUVHhZdVE9PSIsInZhbHVlIjoiQlhsTWV5cnl2Y3M0MnhmSzV0bWdIVEgxd1k5RkFNdW9RMzdndXh2UTdOKzlWd1JLSDJFL1JoMEhRNDRXM0NtRXoyYU1zUWJXMTZpd3FiM3RkWll6RE4zM1MyU09sQnR2eXlpUjZkTVMyZ1crVTU0MmwxcW9qYkgzOFkrWUt4VXBjeXlLbDFvNzBxNTcrNzk3ZFIxK2RXa1hJcmRoaEJXdDFGUHA4V2N6TWRJM0c2UWlITU41OTgxd1lqM2c0ZEZiZXZ6TGhnNlREWml2SUliTzFjR0FRN0IranNhanZrK3h0RGNXRXhTd00wdmxHalBqK083UXgvZlBjMzhES1EyaEtEcmZueFVNMjl3ekhaaHd4QkpzUEczamFaTDBIM2dQbitxZjFWOEVDTFBuUWx0U3h5UUx4UWpQbllXS3VHcXpUWUFnL2ZaSHowMVdwNFR4emoyUzRLeFpmWGI3bHRVa1gxYS85RlhvVkIvajlIWmE2MnVNVzdFdzQwUm1HdkIzTVZsZzd5anROZGhkMk9IMlZ4clZwOXJER0dURjJiVHdXdmNlSExwYW5vMS9FSXZvMEFxOUJJaUVOQjNONmYraGtkd0FncXdjbDFXc0t5a3ZvbnUxSFJ4T0Q1UnJ4UTJ2SjVOdGthSnIxeFlabTlUQmlwK3p2V3FtUmRHZHJFSmMiLCJtYWMiOiJlOWI5NGM5MTFkNmIwZDEwOTA2ZWQzOWUxZTYyMGIyZjMyMmQ2ODQ5MDZlMzU5MDkxNzE2OWY0OGYzOWVhZGFhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:54:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-566831709\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1901959894 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XTqfILO20ESOyUrbO2PEByvY2Oxsmxq0H0QyDSRn</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1901959894\", {\"maxDepth\":0})</script>\n"}}