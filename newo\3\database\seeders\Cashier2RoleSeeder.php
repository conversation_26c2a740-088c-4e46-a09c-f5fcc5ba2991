<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class Cashier2RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // البحث عن شركة موجودة لإنشاء دور Cashier2 تحتها
        $company = User::where('type', 'company')->first();
        
        if (!$company) {
            $this->command->error('No company user found. Please run the main seeder first.');
            return;
        }

        // إنشاء دور Cashier2 إذا لم يكن موجوداً
        $cashier2Role = Role::firstOrCreate(
            ['name' => 'Cashier2', 'created_by' => $company->id],
            [
                'name' => 'Cashier2',
                'created_by' => $company->id,
            ]
        );

        // تحديد الصلاحيات المطلوبة لدور Cashier2
        $cashier2Permissions = [
            'manage pos',           // مطلوب للوصول لدالة create في PosController
            'show pos',             // مطلوب لعرض تقارير POS
            'show pos dashboard',   // مطلوب لعرض لوحة تحكم POS
            'manage customer',      // مطلوب للتعامل مع العملاء
            'show customer',        // مطلوب لعرض العملاء
            'manage financial record', // مطلوب لإدارة النقد
            'show financial record',   // مطلوب لعرض السجلات المالية
            'manage pricing',       // صلاحية التسعير
            'show pricing',         // عرض التسعير
            'edit pricing',         // تعديل الأسعار
        ];

        // التأكد من وجود جميع الصلاحيات المطلوبة
        foreach ($cashier2Permissions as $permissionName) {
            Permission::firstOrCreate([
                'name' => $permissionName,
                'guard_name' => 'web',
            ]);
        }

        // إعطاء الصلاحيات لدور Cashier2
        $cashier2Role->givePermissionTo($cashier2Permissions);

        // إنشاء مستخدم تجريبي بدور Cashier2 إذا لم يكن موجوداً
        $cashier2User = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Cashier2 User',
                'email' => '<EMAIL>',
                'password' => Hash::make('1234'),
                'type' => 'Cashier2',
                'default_pipeline' => 1,
                'lang' => 'en',
                'avatar' => '',
                'created_by' => $company->id,
                'email_verified_at' => now(),
            ]
        );

        // تعيين الدور للمستخدم
        if (!$cashier2User->hasRole('Cashier2')) {
            $cashier2User->assignRole($cashier2Role);
        }

        $this->command->info('Cashier2 role and user created successfully!');
        $this->command->info('Login credentials: <EMAIL> / 1234');
    }
}
