{"__meta": {"id": "Xe1f757068477aaf494cc64030e2de083", "datetime": "2025-06-16 15:23:55", "utime": 1750087435.070596, "method": "GET", "uri": "/inventory-management/products/8?search=&status_filter=", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087433.308575, "end": 1750087435.07063, "duration": 1.7620551586151123, "duration_str": "1.76s", "measures": [{"label": "Booting", "start": 1750087433.308575, "relative_start": 0, "end": **********.687006, "relative_end": **********.687006, "duration": 1.3784310817718506, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.687032, "relative_start": 1.3784570693969727, "end": 1750087435.070634, "relative_end": 3.814697265625e-06, "duration": 0.3836019039154053, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47335680, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x company_operations.inventory_management.products_table", "param_count": null, "params": [], "start": 1750087435.02247, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/company_operations/inventory_management/products_table.blade.phpcompany_operations.inventory_management.products_table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fcompany_operations%2Finventory_management%2Fproducts_table.blade.php&line=1", "ajax": false, "filename": "products_table.blade.php", "line": "?"}, "render_count": 1, "name_original": "company_operations.inventory_management.products_table"}]}, "route": {"uri": "GET inventory-management/products/{warehouseId}", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\InventoryManagementController@getWarehouseProducts", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "inventory.management.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=39\" onclick=\"\">app/Http/Controllers/InventoryManagementController.php:39-104</a>"}, "queries": {"nb_statements": 16, "nb_failed_statements": 0, "accumulated_duration": 0.03652, "accumulated_duration_str": "36.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.831919, "duration": 0.01617, "duration_str": "16.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 44.277}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.879117, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 44.277, "width_percent": 3.806}, {"sql": "select * from `warehouses` where `warehouses`.`id` = '8' limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8890631, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:42", "source": "app/Http/Controllers/InventoryManagementController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=42", "ajax": false, "filename": "InventoryManagementController.php", "line": "42"}, "connection": "ty", "start_percent": 48.083, "width_percent": 3.258}, {"sql": "select * from `product_services` where `created_by` = 15 and `type` = 'product'", "type": "query", "params": [], "bindings": ["15", "product"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.897427, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:48", "source": "app/Http/Controllers/InventoryManagementController.php:48", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=48", "ajax": false, "filename": "InventoryManagementController.php", "line": "48"}, "connection": "ty", "start_percent": 51.342, "width_percent": 4.381}, {"sql": "select * from `product_service_categories` where `product_service_categories`.`id` in (4)", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.918721, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:48", "source": "app/Http/Controllers/InventoryManagementController.php:48", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=48", "ajax": false, "filename": "InventoryManagementController.php", "line": "48"}, "connection": "ty", "start_percent": 55.723, "width_percent": 3.587}, {"sql": "select * from `product_service_units` where `product_service_units`.`id` in (5)", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9271412, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:48", "source": "app/Http/Controllers/InventoryManagementController.php:48", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=48", "ajax": false, "filename": "InventoryManagementController.php", "line": "48"}, "connection": "ty", "start_percent": 59.31, "width_percent": 3.204}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 3 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 56}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9368172, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:56", "source": "app/Http/Controllers/InventoryManagementController.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=56", "ajax": false, "filename": "InventoryManagementController.php", "line": "56"}, "connection": "ty", "start_percent": 62.514, "width_percent": 4.189}, {"sql": "select * from `warehouse_product_limits` where `warehouse_id` = '8' and `product_id` = 3 limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 61}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.945528, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:61", "source": "app/Http/Controllers/InventoryManagementController.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=61", "ajax": false, "filename": "InventoryManagementController.php", "line": "61"}, "connection": "ty", "start_percent": 66.703, "width_percent": 4.326}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 5 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["8", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 56}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.954622, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:56", "source": "app/Http/Controllers/InventoryManagementController.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=56", "ajax": false, "filename": "InventoryManagementController.php", "line": "56"}, "connection": "ty", "start_percent": 71.03, "width_percent": 3.806}, {"sql": "select * from `warehouse_product_limits` where `warehouse_id` = '8' and `product_id` = 5 limit 1", "type": "query", "params": [], "bindings": ["8", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 61}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9628708, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:61", "source": "app/Http/Controllers/InventoryManagementController.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=61", "ajax": false, "filename": "InventoryManagementController.php", "line": "61"}, "connection": "ty", "start_percent": 74.836, "width_percent": 3.505}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 6 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["8", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 56}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9719748, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:56", "source": "app/Http/Controllers/InventoryManagementController.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=56", "ajax": false, "filename": "InventoryManagementController.php", "line": "56"}, "connection": "ty", "start_percent": 78.341, "width_percent": 3.916}, {"sql": "select * from `warehouse_product_limits` where `warehouse_id` = '8' and `product_id` = 6 limit 1", "type": "query", "params": [], "bindings": ["8", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 61}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.980109, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:61", "source": "app/Http/Controllers/InventoryManagementController.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=61", "ajax": false, "filename": "InventoryManagementController.php", "line": "61"}, "connection": "ty", "start_percent": 82.256, "width_percent": 3.286}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 7 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["8", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 56}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.989072, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:56", "source": "app/Http/Controllers/InventoryManagementController.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=56", "ajax": false, "filename": "InventoryManagementController.php", "line": "56"}, "connection": "ty", "start_percent": 85.542, "width_percent": 3.204}, {"sql": "select * from `warehouse_product_limits` where `warehouse_id` = '8' and `product_id` = 7 limit 1", "type": "query", "params": [], "bindings": ["8", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 61}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.99702, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:61", "source": "app/Http/Controllers/InventoryManagementController.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=61", "ajax": false, "filename": "InventoryManagementController.php", "line": "61"}, "connection": "ty", "start_percent": 88.746, "width_percent": 3.395}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "company_operations.inventory_management.products_table", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/company_operations/inventory_management/products_table.blade.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1750087435.033727, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 92.141, "width_percent": 3.943}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "company_operations.inventory_management.products_table", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/company_operations/inventory_management/products_table.blade.php", "line": 52}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1750087435.042419, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 96.084, "width_percent": 3.916}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\WarehouseProductLimit": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FWarehouseProductLimit.php&line=1", "ajax": false, "filename": "WarehouseProductLimit.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\ProductServiceUnit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceUnit.php&line=1", "ajax": false, "filename": "ProductServiceUnit.php", "line": "?"}}}, "count": 14, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order/1/edit\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/inventory-management/products/8", "status_code": "<pre class=sf-dump id=sf-dump-3480958 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-3480958\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-194075972 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>status_filter</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-194075972\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-790581439 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-790581439\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1834367157 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; XSRF-TOKEN=eyJpdiI6Ikh5M2pkTFdGT0YyQVU5NHF0MkxkcGc9PSIsInZhbHVlIjoiMkRlMFFkR1lPaXRpS05peTFQL0R4UzVrVE5Kc2hoVHY4djVJQTNad0xML3dDNk92RUJpWGJURkZzaWg1RE1EckJTZTVZRnptQVhQTGtXTlBRTkFydTRLT3hCektwcUJXTkVSNDFnZHJtdmNML3VyckZGeGhyNWZWaWsxV00xbTNHbmsrRG5rcTZ1YWlwZTZKMkhPTjRiTnc0eGVNQk9USzUyTWZsaXVBYkgzRHV1R0szNXpqelJHV0NtWmtvTUtaUjFicUFtZGIxTThQSDFkZXlWdGZ2RnVwcm5UL05tMXVLbkM1cExlbzQ5cXo1MmdOZFRhY2hab1UvOGpaNi9ackNSN3JqenVDMmxUTXF4MWJEd3dkNkNtOHNURmdtM3hRUXNUemZrWkNDK240SDNPQUIzY3oyQWppalBRTWJrMm1RdUNINjlGYXp6ZVp4QTZNem5xWFBHRlJnTlZ0Qk5YNFl2N0lZUHZPNHUzVlRYaUF4cTdUYmJTKzRTNXUxZ21PMktJd3dHNExKY1JjRXJJMFEvYVNYV3c1T3lxTnFucFR1Z1pqZHJESG10dlBBRWtaQmd6ZDQ1L2VPaFNOWVU0T0tIUHVUY1B2SG5QUWpuUWl1ZzMvU1hZS2hXblRJdVNpTjZWdU5jenhaQTFaWUlNSnh3dmNWV0FLNm9tQk1FYXUiLCJtYWMiOiI0YzBhMzkzZWM3ZTQwODJjYzJhYzkxYjA2MjI0MTA2ZWIyYTU1Y2UyOTM3MjU3YTBiMDhkZjE3NjE5N2RiZGJlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IklMWmROYUVxN1pDcVArTVl5eEhLYlE9PSIsInZhbHVlIjoiUEU4anVQMXhCYndaVGlFMzBQc3dtZTJsalZzdllmYkhjdE56YlRJMy9YMzFMUG1xUjdLekZBVW9mTjJFVVg1cVlQZDJBMWdMZTkxUU1sdXpDVTAyeWxzVXplOTFRaVo1ZktsY29iWThiaVAyZWhKQmw3d1ZJQXVzMlV1K3lldU9zek4zdnlLcisxa3l5YW1xMnFCdWdrb2NQSU42YXNPT0NrSjJiNVFnWHR6UXBLTFF1d0N1cTJLL0Y5aXJCVzBUSXVQTk5vWGwvcHVCdEVDNm5nYUlGT05IZnhOVGJJM1YrWmlvanJ0TnFuZkJSbnQyOVdweEJTUzRSekQwOWdUb0tOdUZpbWVDcTlPYzZSTFcwbzVyTzFUWGRLeEhMQVlXQ2NkUG5QSDBhajJhRjBnMUJqNzVNT0FDYmZ0MzN2WHNGTEdaeDFXcURDOHBiU091UVNpTmIzVnJrOGZSd0wyQU9qYUVxdjlyR3F1UmNIcTdlclZHSXdvOFpSTjIrb1J6eHYvS2tFUGRLTHZsY3BTbDN4TmdHOE56NTBhRzJSUWJFVXo2ZlZHL3FxRHNUVTB4VXFESTlxaGhLZVhVWENTTGpsUFFVM0JyVkcyRVEvL3ZVRHFadmo3dTkwSGgrcGZnYllGWWlmcHZmckNiNkY5OVZiNDI4UlE3aHJ2VHlmRS8iLCJtYWMiOiIwZTMyMDEwNjM3NTY0NGFkOTliODc0ZTVhMGU5ZGFkZTQ1ZGQ0N2EzZmU3NDY5NmVlMjQwMDAxMDljNTNkNzFiIiwidGFnIjoiIn0%3D; _clsk=8f5bqk%7C1750087432471%7C12%7C1%7Cl.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1834367157\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2125672145 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzcvmOKIgVMCCvEHD9HLB5trqF9yJ9GcIs8Pndo9</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2125672145\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:23:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNQNGh6d2lHQTM1QVRvWjRKVnVDUnc9PSIsInZhbHVlIjoiV3NiU2duQk5GWG9ndUhLdUpnNUU0algwYnFTSDJrckFMRGtNWUIxN2IyTW1mb0drZHhNOVVWNlg1dDJzYXNZakw1YnFhRGErN2FFVGV0UGNJaExqRlBMa0xHZ2FTbHdYd3hnKzJBTVVqb2N3S0VMUDZaZW5pRGlQWDgvaGthdlFRTE5HWEdTN2pCY01jY3N2aHFVeC9nOHpRUWlMam13Uktzckl6eGtLV1c1L3M1ejZSQ3IyZ3JmNFdYM2x1QmhGdTUyaVVyd3MzV2swQk9iMGFjVlE0dWxkd2pqTE5PWUhmL2toZnU1aTRxMU1BSm0zTWdVL0xJMTQwMHV4V1h1ZmpTdEoydmpWb2NNY0pES0h1RTZMMlJ6NytLbmFidG1CQ3UxL2wvSHpDVzZoTEZVWlMrZUF1ZVNZbnpRUU9mSTJTRy9jTWlBVjFQTTZnc0RqTWcySGNmb3MzS1h2bVRBOFdFTkQwY09RUE01NW9QU0owMy81RjA0ZW96MENzMGRTZ1F2Q2tVRVJVZWU3VjBrcXJ4K2p4WGRhSTZXWXR5alduTXk1UnlicU0wcEh3aHRMWHpDYzR2VWpvMnZmTGdJanlCQ0hWNUprVXdJblQ2NGJaQTRRWFdCMUNyY1dPZ3M3NzJwdWJ1dXloT2ZoVGYzOEczZW1FNldWKzBoeFQ1VzYiLCJtYWMiOiIwOGUwNDMwNWQzZjMyMjU5N2U5ZmEwYjEwNDZjNmRlYzYxYzY3YWEyZjkyMTI1OGI2NWIxMzQzOWVhNWJjODY2IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlU4Q0dqeFZDRTJmcm96L3U5MmJRYlE9PSIsInZhbHVlIjoiNDRPNCs4UnFVU2FBWkxLRXhVS2FGc0dFWC91bW1QZ1c5T0pDY0FwSlZGM09sUElTemdXbEJrWDNnM3U0NVVoV2FkVXdYTjhLdlZVVEFUL0ZhQkxpM3ZkeHRHT214cjFLRnRLU2pXdTRBakRHeTkwNURaeDN0ME5XL1JsQ2w5OGhyR1lTcjB4dWxES1lHaTZ4VE5mWEh6K0VDVkZQeUxqSVRlYjRhMnFQd3VSYjZvK0ZZWnk1TUZUQUJLWm1BOWNHMUQ2K2gzTlYxVTNRWXlZbGlnQWJIdE9vU1dzMERUWm84LzNEaytSa0c5NXViaFZPbE9lMDhIaUMwcGI4MGhwWGJDZ3FPZVovS0cxNFlZTUowazdaUzc3VGduT3Z4QmxSS1JWd3JOL1hvdmRSdkVodTNHUHZNOGd0b3k4aHlPdit6Z0Y4UlRDclgrNDB3bEEwTGVKUGk0WGYzMzk2TTczZU9hSTVES0hsWmN0RFAySFNKSWxuaVlESlNSWTNVUDluSmt2OXlCbm1hOVlMYTVTM0svdENQdktDQ3lxTzE0aGhiaXExeDhScDJVM05IbXRCM2dzU0FsYTdBL3p5SnoraUVaQUlTSUZKUEJlVzlIRFJmMG1JbVh1bUZEc1pyYTBicGgvWGN5UHROcUFORlBUdVV2OHJGdnVaQ09TYVpzN2giLCJtYWMiOiI5MjM5MjFiNWI4YTViNWFmMTAxMDQzMTRjMWFjY2YyMjEyYjQ2ODRkM2ViNzAyNmQwNDlhNmE4YmE1MDU0ZDU3IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNQNGh6d2lHQTM1QVRvWjRKVnVDUnc9PSIsInZhbHVlIjoiV3NiU2duQk5GWG9ndUhLdUpnNUU0algwYnFTSDJrckFMRGtNWUIxN2IyTW1mb0drZHhNOVVWNlg1dDJzYXNZakw1YnFhRGErN2FFVGV0UGNJaExqRlBMa0xHZ2FTbHdYd3hnKzJBTVVqb2N3S0VMUDZaZW5pRGlQWDgvaGthdlFRTE5HWEdTN2pCY01jY3N2aHFVeC9nOHpRUWlMam13Uktzckl6eGtLV1c1L3M1ejZSQ3IyZ3JmNFdYM2x1QmhGdTUyaVVyd3MzV2swQk9iMGFjVlE0dWxkd2pqTE5PWUhmL2toZnU1aTRxMU1BSm0zTWdVL0xJMTQwMHV4V1h1ZmpTdEoydmpWb2NNY0pES0h1RTZMMlJ6NytLbmFidG1CQ3UxL2wvSHpDVzZoTEZVWlMrZUF1ZVNZbnpRUU9mSTJTRy9jTWlBVjFQTTZnc0RqTWcySGNmb3MzS1h2bVRBOFdFTkQwY09RUE01NW9QU0owMy81RjA0ZW96MENzMGRTZ1F2Q2tVRVJVZWU3VjBrcXJ4K2p4WGRhSTZXWXR5alduTXk1UnlicU0wcEh3aHRMWHpDYzR2VWpvMnZmTGdJanlCQ0hWNUprVXdJblQ2NGJaQTRRWFdCMUNyY1dPZ3M3NzJwdWJ1dXloT2ZoVGYzOEczZW1FNldWKzBoeFQ1VzYiLCJtYWMiOiIwOGUwNDMwNWQzZjMyMjU5N2U5ZmEwYjEwNDZjNmRlYzYxYzY3YWEyZjkyMTI1OGI2NWIxMzQzOWVhNWJjODY2IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlU4Q0dqeFZDRTJmcm96L3U5MmJRYlE9PSIsInZhbHVlIjoiNDRPNCs4UnFVU2FBWkxLRXhVS2FGc0dFWC91bW1QZ1c5T0pDY0FwSlZGM09sUElTemdXbEJrWDNnM3U0NVVoV2FkVXdYTjhLdlZVVEFUL0ZhQkxpM3ZkeHRHT214cjFLRnRLU2pXdTRBakRHeTkwNURaeDN0ME5XL1JsQ2w5OGhyR1lTcjB4dWxES1lHaTZ4VE5mWEh6K0VDVkZQeUxqSVRlYjRhMnFQd3VSYjZvK0ZZWnk1TUZUQUJLWm1BOWNHMUQ2K2gzTlYxVTNRWXlZbGlnQWJIdE9vU1dzMERUWm84LzNEaytSa0c5NXViaFZPbE9lMDhIaUMwcGI4MGhwWGJDZ3FPZVovS0cxNFlZTUowazdaUzc3VGduT3Z4QmxSS1JWd3JOL1hvdmRSdkVodTNHUHZNOGd0b3k4aHlPdit6Z0Y4UlRDclgrNDB3bEEwTGVKUGk0WGYzMzk2TTczZU9hSTVES0hsWmN0RFAySFNKSWxuaVlESlNSWTNVUDluSmt2OXlCbm1hOVlMYTVTM0svdENQdktDQ3lxTzE0aGhiaXExeDhScDJVM05IbXRCM2dzU0FsYTdBL3p5SnoraUVaQUlTSUZKUEJlVzlIRFJmMG1JbVh1bUZEc1pyYTBicGgvWGN5UHROcUFORlBUdVV2OHJGdnVaQ09TYVpzN2giLCJtYWMiOiI5MjM5MjFiNWI4YTViNWFmMTAxMDQzMTRjMWFjY2YyMjEyYjQ2ODRkM2ViNzAyNmQwNDlhNmE4YmE1MDU0ZDU3IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2140073253 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2140073253\", {\"maxDepth\":0})</script>\n"}}