{"__meta": {"id": "X3cc1bf17b4db719d4f0c8bec6b82cab5", "datetime": "2025-06-17 07:14:12", "utime": **********.745434, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750144451.940428, "end": **********.745462, "duration": 0.8050339221954346, "duration_str": "805ms", "measures": [{"label": "Booting", "start": 1750144451.940428, "relative_start": 0, "end": **********.642927, "relative_end": **********.642927, "duration": 0.7024989128112793, "duration_str": "702ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.642944, "relative_start": 0.7025160789489746, "end": **********.745465, "relative_end": 3.0994415283203125e-06, "duration": 0.10252094268798828, "duration_str": "103ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45154264, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02117, "accumulated_duration_str": "21.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.690413, "duration": 0.01948, "duration_str": "19.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.017}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.722311, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.017, "width_percent": 3.873}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.732529, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.89, "width_percent": 4.11}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-941619166 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-941619166\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-959909975 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-959909975\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1909247365 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1909247365\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1733949590 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=jfj0s4%7C1750144433135%7C6%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpsQkhBZ2pQTTNhalYyYTFOUU1wQWc9PSIsInZhbHVlIjoiS081ZEgvQVFHcHZXQjZCVlI4aEllemNlQ2FkQWQ5THh2N21ONFdQb0RiRkNwR3dxaGNMM1BpMXErR2VNUDZ1OG9tVURwNzR1ZXIraVVDbFd4aS82bWtzV29WZFRJdHhHWVloTXIxVDgwQVQ4TTFNVVI5ZmtYS1B0ZWQrVWxzaHg0YUdYZ1ZONlFPM1dtUFRQUzc1dEwwTEphU3U5M3FPUlJjYUlHZHZadVd1eVljZmIyZm1CbnI3dmJWclFGdWJsalFDWXlzd3ZDSVBGTFFZczZHQzYxS3ZTbnZPWXVJWWxQdDFHQkRkV0xyM3ViRzFsQnFCTm1kdnBtOFIrbTNLTC85dHU0T09rbys0NlR2RXlERzV4UENUVmU2ZmlKWDVoQXJyUmhaMy95QWxiKzBvM05CdEdvSEFwTVBxcTJXUXg3Q0ZJWFpoWFY0bk5OUU9WRVpBUFh1dUNsSDh0cUhDOTI3M3JwQ0tZUU9pNVhqRkJXYXJKVDhGbEd4aGlGS3pFQk1UU1duWlZQek81UGJRSXJVcUdEdS90aWlvYTZrQTROUVoveFQyTldndW40Nkhib1ozV3MvRlpKQXVhNElHTXV3QjJTMWVWMlVLdGtZd25qd2VvKzN5Z3RSTE1jbU52NjdrcUl5ZTZUU2NQZnhYT1g3MFdBc3RnQ2hQZWRKVGkiLCJtYWMiOiIwNjFmZGFmMDExYWYyODA5NzE1MDQwZjliNzY3YWI0ODI2NWFmY2M5NjE5MTI4NDliYThiMTRhMDQ4MTVkNDk4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Inc0aGxWU1ZlYWMxVFB0cmd5MmR5SlE9PSIsInZhbHVlIjoiWFIxK2dkMXlBQzBtdlpEZ3ZYb1A0TUpMTW1aR3FycmY5UUVydElkekROayt6VjcxeWl5MkIrNHRMc2hUbmZMa2xyQ093czdZSWl2K01Udkw5RGNBbzZOZ0I1OXk2NlJEWCtNLzNhUDhVUFBROVZxaEg3eVAzV3RFUjJGK1creUo1MDNCVWp1ZVV0bUJuRUxReWExcXQ0eFY4VUdFcU1HbWMxYUhLMlJLVVZIZWJEeU5tbEtZNWxCNzZlYmdvcllLeE10dWJLT0lUWDJERkZxckZnaGY0a2grSG9JZEQ1Qng3bTFVRnNZU2Fic2FjNWNDQXRxa0NaQ3FwdzA3dzQ5cVpwYjM3akNJNHVHb3E4N2pIbTROa05ENzVnWVl6OVFtYlUwTnpWNGo2MGhRNUNwMU8xYUlQMUF0UFc1Qk0wdmx3aGhpclJlSFhmTHhRY3pjMFJqbDhwU0RrQzJFUjlrbzJvd1piam9mdTNnNXZ6TzJ0Y3c5LzdES29DNjFtUjBRZkVjY2lnTk8rVjEySjJ0aXRBakZvaGlmdmdLNEN5dEN2YUxpNlJEWVZxUjVyY01mdmd0UHdLRlhYcUR4ODQ5UEJ3M05DL2ZMV3VzSFQ4VVEvb3VtemdXRzlmTGwwT0xsWDJ1MDFHS3N2OXd1bjl5bEtVL1Mrd2FlRGVoSGc3U08iLCJtYWMiOiJhNTNiZWJkNDIxYjZjZWNhNDEyYTYxN2I3MTkzYWJhOTE5OTFhMjZkNjI0NWNmNzFiOWY2YTYwOTkwMDgyY2U5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1733949590\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-392218553 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-392218553\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-708524650 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:14:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVrWlNzWTFSd1RLcTVFendZSG1McFE9PSIsInZhbHVlIjoiVDErSXNTYjljUlNYbGZMNlBpb0hQMktOcXAyNEszVDNyN1Q3Z2NESVdaMnkyaVJqUmlsbzVPZGRvcElmQlFtdWd1by9iUnRjbnNpcWZXa2w5YVJNK3dVNFV3aXkzaHJEdDJ0enV5aENrRXo3b1p1Y3psUzAzWXZBNGxISnhXSDJjcUZZK21aSW51ZWw0NDZkbHA1L2lFYmZMRFNkcWdkdmFES1dVSStRbXlpa28xSzJLSTYwUXhMQ1ZiS2p1cE13RXpkaHlIczhNdVZFd0FNQUY0S2h4UWFiWGE0UzF5bkNydlEweXhLZTkrU2NCeE9TUFU5OGlTbEFXYmlscHA5NXBLQlhIL0ttMExIa1Z1aUE0MFlGUEpYQmlLdjMzZWhPQXdYbENrMTV1QVBmYXpMZldnOFU0b1RvSXhDMW5rMzI4dmZlUnB1a0ZuTWMwQ0pLTkthc1Bqa2R5Rm1MZ0NuaVFZaTREdnN6SjlVQ1pXamMrSkJwUzNraHBVdWRpdWhIdmlPcEdSRzFyTk0zQVZUQnNtaFNxbFNDL0ljeXRjcFFoRE84SEhzOG9XRXpuVTJJRjJtRUU5MHROYVBZa1c4czdXT1RBLzRONDFpTVM5Uk5sNlNiK2dBMlBnQTdwVEpZcVVibks2cFE3WmxYb05ncFRJTDNkdG4zK2ZSTkVMeUQiLCJtYWMiOiJhMmFmNDUxNTgxOTk1YWRhN2E4NjBiZmQ4YWVhMDM1MmUyYzBmMzQ3MzlhMTFhNjQxMzY0MTc5ODQ3NWZmNTljIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:14:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImQvWmZJRnQ2YzU0S2c1K3BUY2VXbHc9PSIsInZhbHVlIjoiOFppWjhwamFpSTBIbFk1OUJ5eTFwTXlEN0xtVDdodEhYYStXVE5DWEZ3NGdNQWpjVGorYW9BWlFFK0Y0S05Fdy9xSDA1S0t4eGszc3lYWkh6ZnR4RGhFNDZVK2h3Ym95MTVIMy85ZnVGcXQreXJwQ04yOFZRRExtWi9MTmwwL2RSdzRGbEpnS2tNd1YzcTk3Mi9MU1RucS9DUUFNQTlXQnU5UXBYdmFidU9sb1crT0VNM2oyM3FpRUFHNi9CTUtxWURlcXZ4MXYyK3RvajNsLzd2a05PK0NCc2ppM2lzdG1hNVdnb3dYSEVjT2txT3orODEwM2RBbmhDS1hnWnFEYUJMcmo5QXFkQ2thT3pzN245bWZZcWxXYjRoNWRHYit4UzlTcHEyZWxmUjVxMUdodHNZMlo3SnVQMUROR1UvcHV0ZXdHdkQ3YUcrUW5XeVRWTVBuRHpVTFdmQ2xLUXd0Z0NZN0ZIbjRPWERTYWw5R0tJUTNGM3VOTGFDLzZCK1ozT1N6dlRiRWI5dmYwUE5vdHZpV29KVU9NK3NNQ0w2QkpiNjZ1RjdKNFA5NmdKbU5lenFITThFbFJNUGJYMkhKTG9HOWhiR01aaVI4d0ZDSExOQjF0VjBvWEFtUWs4YmVBQURWV2RreEVLV0ZJaFdpNTl5YW1KYUVnYmJaRlZkTEwiLCJtYWMiOiI1Y2JjMTMwOWI3YWU2NjIyZjMxZjg5ODRhNjNhMTI2OGQ1Yzc3OGNjODFjNzM0ZWE0MmUyNDI1Yzg4NWEyYzBjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:14:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVrWlNzWTFSd1RLcTVFendZSG1McFE9PSIsInZhbHVlIjoiVDErSXNTYjljUlNYbGZMNlBpb0hQMktOcXAyNEszVDNyN1Q3Z2NESVdaMnkyaVJqUmlsbzVPZGRvcElmQlFtdWd1by9iUnRjbnNpcWZXa2w5YVJNK3dVNFV3aXkzaHJEdDJ0enV5aENrRXo3b1p1Y3psUzAzWXZBNGxISnhXSDJjcUZZK21aSW51ZWw0NDZkbHA1L2lFYmZMRFNkcWdkdmFES1dVSStRbXlpa28xSzJLSTYwUXhMQ1ZiS2p1cE13RXpkaHlIczhNdVZFd0FNQUY0S2h4UWFiWGE0UzF5bkNydlEweXhLZTkrU2NCeE9TUFU5OGlTbEFXYmlscHA5NXBLQlhIL0ttMExIa1Z1aUE0MFlGUEpYQmlLdjMzZWhPQXdYbENrMTV1QVBmYXpMZldnOFU0b1RvSXhDMW5rMzI4dmZlUnB1a0ZuTWMwQ0pLTkthc1Bqa2R5Rm1MZ0NuaVFZaTREdnN6SjlVQ1pXamMrSkJwUzNraHBVdWRpdWhIdmlPcEdSRzFyTk0zQVZUQnNtaFNxbFNDL0ljeXRjcFFoRE84SEhzOG9XRXpuVTJJRjJtRUU5MHROYVBZa1c4czdXT1RBLzRONDFpTVM5Uk5sNlNiK2dBMlBnQTdwVEpZcVVibks2cFE3WmxYb05ncFRJTDNkdG4zK2ZSTkVMeUQiLCJtYWMiOiJhMmFmNDUxNTgxOTk1YWRhN2E4NjBiZmQ4YWVhMDM1MmUyYzBmMzQ3MzlhMTFhNjQxMzY0MTc5ODQ3NWZmNTljIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:14:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImQvWmZJRnQ2YzU0S2c1K3BUY2VXbHc9PSIsInZhbHVlIjoiOFppWjhwamFpSTBIbFk1OUJ5eTFwTXlEN0xtVDdodEhYYStXVE5DWEZ3NGdNQWpjVGorYW9BWlFFK0Y0S05Fdy9xSDA1S0t4eGszc3lYWkh6ZnR4RGhFNDZVK2h3Ym95MTVIMy85ZnVGcXQreXJwQ04yOFZRRExtWi9MTmwwL2RSdzRGbEpnS2tNd1YzcTk3Mi9MU1RucS9DUUFNQTlXQnU5UXBYdmFidU9sb1crT0VNM2oyM3FpRUFHNi9CTUtxWURlcXZ4MXYyK3RvajNsLzd2a05PK0NCc2ppM2lzdG1hNVdnb3dYSEVjT2txT3orODEwM2RBbmhDS1hnWnFEYUJMcmo5QXFkQ2thT3pzN245bWZZcWxXYjRoNWRHYit4UzlTcHEyZWxmUjVxMUdodHNZMlo3SnVQMUROR1UvcHV0ZXdHdkQ3YUcrUW5XeVRWTVBuRHpVTFdmQ2xLUXd0Z0NZN0ZIbjRPWERTYWw5R0tJUTNGM3VOTGFDLzZCK1ozT1N6dlRiRWI5dmYwUE5vdHZpV29KVU9NK3NNQ0w2QkpiNjZ1RjdKNFA5NmdKbU5lenFITThFbFJNUGJYMkhKTG9HOWhiR01aaVI4d0ZDSExOQjF0VjBvWEFtUWs4YmVBQURWV2RreEVLV0ZJaFdpNTl5YW1KYUVnYmJaRlZkTEwiLCJtYWMiOiI1Y2JjMTMwOWI3YWU2NjIyZjMxZjg5ODRhNjNhMTI2OGQ1Yzc3OGNjODFjNzM0ZWE0MmUyNDI1Yzg4NWEyYzBjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:14:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708524650\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1370183160 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1370183160\", {\"maxDepth\":0})</script>\n"}}