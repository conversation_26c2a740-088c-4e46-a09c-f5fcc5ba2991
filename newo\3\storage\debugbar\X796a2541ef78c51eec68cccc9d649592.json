{"__meta": {"id": "X796a2541ef78c51eec68cccc9d649592", "datetime": "2025-06-17 06:54:28", "utime": **********.47601, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143266.739084, "end": **********.476051, "duration": 1.7369670867919922, "duration_str": "1.74s", "measures": [{"label": "Booting", "start": 1750143266.739084, "relative_start": 0, "end": **********.242185, "relative_end": **********.242185, "duration": 1.503101110458374, "duration_str": "1.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.242213, "relative_start": 1.503129005432129, "end": **********.476057, "relative_end": 5.9604644775390625e-06, "duration": 0.23384404182434082, "duration_str": "234ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45695256, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00859, "accumulated_duration_str": "8.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3719609, "duration": 0.006, "duration_str": "6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 69.849}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.420135, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 69.849, "width_percent": 14.435}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4458761, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.284, "width_percent": 15.716}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1774335274 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1774335274\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-958048368 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-958048368\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-740020515 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-740020515\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-76703370 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=lxd95%7C1750140707036%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlgvTTltdloxL3hvVi9TT1JBR1d6UHc9PSIsInZhbHVlIjoiTlZzbWgxb0hVZU9MTTVZZHFNT1hpQmJFTGFuOTZXTm0zK1ZrMjBaYnZMZU9mU0MyaFVaSzN0clJwUTN2NjJMZ1V1dlBZeXVqcmxmLy9zTnFMZ0dXbENqYmRzSktwNWdKTVE3K1BSSGxsblIycUd1MXpvR1ZpRUR3MnRuYVJqMzhZNWJUZml0YnJYaSt2bmg3NVl1S0E4NVVmQzJkTlMwMWFWZTVGOVJwNDJtSUJ3ajZCYjRTTkpTckdvbzgrWEVZY252VWpBTmFMTnlCWlVTcHhHVm1zZ2s0YnphL1J2ZDlVYjBkclNGTjBVZVVYeXlXRlJwcTVLM29vZGJ3RGkxM2J6bE9KRHcwbWozYk5VQmlvcUFIaExOd1Y2K0MzOCs1M05JTVJXZWpya0pXRkFZcngyWGN5YmxmOE1hNUpiWVJFSHNzaGg0Q0FpQUh5M2Fzdnd6eVFhMXI2bXd2T2RCVmFIcnV6M1NNVTVzYndxRHVhWjJ1NkNSL1ZPMllrYU16VjBraGJMcXFsMjRMOUg3M1BwOEhXamRBbUhXOFNaRTh5R2RkRWFmbFQzVmVnYTkvN3RERE9mNEJsK2VicFFQTGJDVit1OEszemxiM09sL0c2UkN5bzBNZ0RzcmdyRFdyYVh2UzF4OGw5SFNkakRnMTZQVXBhekw0Ty9lQzUyZHgiLCJtYWMiOiI1YzYwZTZiMTMzZjEzYTY5NWQ0MGM0ZmVlZmY3OTUxZjg0ZTJhM2RjOWJlNDNmOGNhYWE5Y2ZkMGMzMGUzNzE2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjlnT2p5dEx6OHh0K0RBc1RGbkpjOWc9PSIsInZhbHVlIjoiTkxrMnBmamVPSDRVcmNIeTZ5RFFUbGNRNGVUMGVKcysyUjJ4TnpWNHdNZ0prMllvODN5L2dNVGhJdTlEc1dNbmZNRXdQZkNNMDNyZWV3THdmdllGY3lhSkxVMDBzbW90QlRPVFpGZDdBc1N0NUdvSFFSNWZWM0FDV2JXVUZLYXRxc2RzMHpMeHkxajVsbkthc2NiNlBManBiTktsWXBGTmxOaXFmNlRlNC96bkVOQnNsREVodHZtaks5T0RpTDhSRkV6ZHdrdXlJNWc5bXFqanNITmJGdHlmTVRyd3U3UnR2alo0d05mMjI3THFobCtkVjFEMWp6OS9udzljaWZNejVySWdhU0ZnL01jN2dQaTg3RlpXd0VrcVZCQUtwWUNQMDJvN2tmZDZPQ1ltV2ZLOGxNdXQxbkpRTDJLTUtIcDN5QjJMd1BsQXV4VTR5djFLOHd2WnRGZXJsem1XTTJ1SG9Bdi9WZ0NXb1BSUTZTdnJxUGFjeDFIL09ZbktMYklYZDNnM01WWmcvQ2pObzJGcGNab0Q4RGxYZGhNMGZ2V0QxRzF4MmI3OFp4NEVDT2N5QkdER0FYQTdyOE9HQ2xXc0pQQ2UxMm11eW1oUjZ6UE5ESkUzWTJLcWRpbnoyNWphNis4bHMycldpdkZ2VHlocWdxL3FjRVF3aStYQVVFdjIiLCJtYWMiOiJiOTUzYTBkMTEyOGVmNGZjOGUxNDU1OTNiYmJjZDE1YWU3MzJmNmVjOTcxNWUzOGNlMzhhOTFhYjZlM2Y2NGYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-76703370\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-59062922 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDWN9h6R9gAIdzZaXBzXfq0WGpauVfrUj70kKWA2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-59062922\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-736787352 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:54:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBKQVhMSU5ZUDh5RjIwU0laTUNockE9PSIsInZhbHVlIjoiQzV4Nmtsbmc0ODltOWZ2S1FlRElNTHJoVzNtYklhR2wzb2dHUkgyeW1WUDFXL0tuK1h0cVFhRjVuZGF5L3ZmakJQOXE4WmhHeDRnQ2dMRkJQWVhuTnBneTJaZGxaWEd0cDlwcGc4MXB1Qyt1SVJPSFpSSVkyZXFVM1pHeHRrWlh6T3ZYb1RDMmtiZWVIWE1DZUdLK1pDRTJOb1NndStHTDhvYmJma1BUeE1maitIQ3pSTzlSWmhVUE1HbTh0OHBGaXp4KzFKT2c2RFpjMTNvenJES05Kb09DcDdvcTVOWFJnc21zUlExNDB0YWxFZUxZQ0QrZWZLUUZhTmgvdGpWMWhKSFAvNzJ3TEZqMnR4NUZDTkNieXAwQXdsd2tLMForR1JjQmNMdzFLeHZNMGo4Q24wejlrU3hLOWFScXN2R1A4ZGVhWlpOd0IxSENiaWJqZEliSldwcUJZVFZDa3NZRTBvRUltZzNTUWowU0wydTVaT093Nks5Z1NwN2NuMXVkdXhyVUUrMkdlcVdUd0Y4VWMzS1lSNythSUtaZC94L2dvTzVRN2VRNm5WU3I3TVl2YXBneXZVRkU0ZXAvN0lBYVNibVlNR1V6ZGdNZGt5Z0tGL1h4ZUQ3YkVib2NIL295d0Fzb0hnS2FPcmdkSXdwSjdQTVBhVWJwRGJsZVBDSUYiLCJtYWMiOiIxYzZjYWM0NmI4MjIyZDZlMDNkYWQ4MzA4MmEzZmFlMTk1NWZkZjE3NDliMjBkMjYxODMyYTI3NmY0YjYzM2EzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:54:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im5HdVhtMjVoWjk5UWIvZWJzdzRmdnc9PSIsInZhbHVlIjoiVGJaSmxYSjI4VXpkUlYzbFMzOGlwZmM0ZThpNHFmVG0zVjgxWjR5K0JWbk9ZMW8wYWlKSHlrdHRPdTg4UldhalU4RTJOY0dlZGlEd0RCNkN6aEE5STRYNDh3WEJiUXpBbnM4Z3Nud1kwY2RCdm9zci95NDA4enJEUyt5ald5WlUwWFRpKzI3a0cyUS9XWXlKV3c5YWFFWUt5bGJ5ays1QzZtQTRMYXBoOVlrM1RtOE9pMXQwSm9YaVhsTkxNS1lYeGFmekZGTVFGdCt5emx1R1YzcVZSM2UyR2NHaFZTa3JMTUJSTEk0bllSa3EvcTNRV0JkWEgyRHRpUVVMU3hKcVo3Zm5aVG0vc2Qwa3dnRjdvUXc2QUswV1RkdDNvYUVaeTdTOVFLeTAyOElCNXQ5MkVXRXM5Vk1qNE5tdWIvR3J5U2tib1U5V2FGSWhOK09XbGlTT0xtNTZIVDh5dmVmQlBUbWNJQ3RyTjF3YjlaUis0ZTlndGY1dVJ1ZndtL0xHc25jY3ovK2N1cUVZV0hCcW9ZTW9xZE1vQkhjb3ppUVEzMlJ1THlSbUt0Y3J3Nm9XOXg0TGtRb2VxNzN6UXlidHY2TkFsQnp5OXNPaEhRV0lFQlE2aGVOZWhad1JwWVc1MGRjOUFnTVBRclRJcFJJZEVjV3psUzFadjBFRFZOOHIiLCJtYWMiOiI2NWZjYWVjMGE0NWJlZWRmNzI4ZjY0YWUxN2I2ZDYxZDVjMWU3NzczYTE0MmMxMmE2YmMzY2JlNTQ1NjBjZTdhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:54:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBKQVhMSU5ZUDh5RjIwU0laTUNockE9PSIsInZhbHVlIjoiQzV4Nmtsbmc0ODltOWZ2S1FlRElNTHJoVzNtYklhR2wzb2dHUkgyeW1WUDFXL0tuK1h0cVFhRjVuZGF5L3ZmakJQOXE4WmhHeDRnQ2dMRkJQWVhuTnBneTJaZGxaWEd0cDlwcGc4MXB1Qyt1SVJPSFpSSVkyZXFVM1pHeHRrWlh6T3ZYb1RDMmtiZWVIWE1DZUdLK1pDRTJOb1NndStHTDhvYmJma1BUeE1maitIQ3pSTzlSWmhVUE1HbTh0OHBGaXp4KzFKT2c2RFpjMTNvenJES05Kb09DcDdvcTVOWFJnc21zUlExNDB0YWxFZUxZQ0QrZWZLUUZhTmgvdGpWMWhKSFAvNzJ3TEZqMnR4NUZDTkNieXAwQXdsd2tLMForR1JjQmNMdzFLeHZNMGo4Q24wejlrU3hLOWFScXN2R1A4ZGVhWlpOd0IxSENiaWJqZEliSldwcUJZVFZDa3NZRTBvRUltZzNTUWowU0wydTVaT093Nks5Z1NwN2NuMXVkdXhyVUUrMkdlcVdUd0Y4VWMzS1lSNythSUtaZC94L2dvTzVRN2VRNm5WU3I3TVl2YXBneXZVRkU0ZXAvN0lBYVNibVlNR1V6ZGdNZGt5Z0tGL1h4ZUQ3YkVib2NIL295d0Fzb0hnS2FPcmdkSXdwSjdQTVBhVWJwRGJsZVBDSUYiLCJtYWMiOiIxYzZjYWM0NmI4MjIyZDZlMDNkYWQ4MzA4MmEzZmFlMTk1NWZkZjE3NDliMjBkMjYxODMyYTI3NmY0YjYzM2EzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:54:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im5HdVhtMjVoWjk5UWIvZWJzdzRmdnc9PSIsInZhbHVlIjoiVGJaSmxYSjI4VXpkUlYzbFMzOGlwZmM0ZThpNHFmVG0zVjgxWjR5K0JWbk9ZMW8wYWlKSHlrdHRPdTg4UldhalU4RTJOY0dlZGlEd0RCNkN6aEE5STRYNDh3WEJiUXpBbnM4Z3Nud1kwY2RCdm9zci95NDA4enJEUyt5ald5WlUwWFRpKzI3a0cyUS9XWXlKV3c5YWFFWUt5bGJ5ays1QzZtQTRMYXBoOVlrM1RtOE9pMXQwSm9YaVhsTkxNS1lYeGFmekZGTVFGdCt5emx1R1YzcVZSM2UyR2NHaFZTa3JMTUJSTEk0bllSa3EvcTNRV0JkWEgyRHRpUVVMU3hKcVo3Zm5aVG0vc2Qwa3dnRjdvUXc2QUswV1RkdDNvYUVaeTdTOVFLeTAyOElCNXQ5MkVXRXM5Vk1qNE5tdWIvR3J5U2tib1U5V2FGSWhOK09XbGlTT0xtNTZIVDh5dmVmQlBUbWNJQ3RyTjF3YjlaUis0ZTlndGY1dVJ1ZndtL0xHc25jY3ovK2N1cUVZV0hCcW9ZTW9xZE1vQkhjb3ppUVEzMlJ1THlSbUt0Y3J3Nm9XOXg0TGtRb2VxNzN6UXlidHY2TkFsQnp5OXNPaEhRV0lFQlE2aGVOZWhad1JwWVc1MGRjOUFnTVBRclRJcFJJZEVjV3psUzFadjBFRFZOOHIiLCJtYWMiOiI2NWZjYWVjMGE0NWJlZWRmNzI4ZjY0YWUxN2I2ZDYxZDVjMWU3NzczYTE0MmMxMmE2YmMzY2JlNTQ1NjBjZTdhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:54:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-736787352\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2141780979 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2141780979\", {\"maxDepth\":0})</script>\n"}}