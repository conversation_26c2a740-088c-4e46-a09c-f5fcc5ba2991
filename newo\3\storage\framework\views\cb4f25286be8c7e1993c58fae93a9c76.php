<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('عرض أمر الاستلام')); ?> - <?php echo e($receiptOrder->order_number); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('css-page'); ?>
<style>
    @media print {
        .no-print { display: none !important; }
        .print-only { display: block !important; }
        body { font-size: 12px; }
        .card { border: none; box-shadow: none; }
        .invoice-header { border-bottom: 2px solid #000; }
        .invoice-footer { border-top: 1px solid #000; }
        .table th, .table td { border: 1px solid #000 !important; }
        .company-logo { max-height: 80px; }
    }
    
    .print-only { display: none; }
    .invoice-header { padding: 20px 0; }
    .company-info { text-align: center; }
    .company-logo { max-height: 100px; margin-bottom: 10px; }
    .order-details { background: #f8f9fa; padding: 15px; border-radius: 5px; }
    .table-products th { background: #e9ecef; }
    .signature-section { margin-top: 50px; }
    .signature-box { border-top: 1px solid #000; text-align: center; padding-top: 5px; margin-top: 40px; }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('receipt-order.index')); ?>"><?php echo e(__('أوامر الاستلام')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('عرض الأمر')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header no-print">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0"><?php echo e(__('تفاصيل أمر الاستلام')); ?></h5>
                    </div>
                    <div class="col-auto">
                        <?php if(Auth::user()->can('manage warehouse') || Auth::user()->can('show financial record') || Auth::user()->hasRole('company')): ?>
                            <a href="<?php echo e(route('receipt-order.edit', $receiptOrder->id)); ?>" class="btn btn-warning">
                                <i class="ti ti-edit"></i> <?php echo e(__('تحرير')); ?>

                            </a>
                        <?php endif; ?>
                        <button onclick="window.print()" class="btn btn-primary">
                            <i class="ti ti-printer"></i> <?php echo e(__('طباعة')); ?>

                        </button>
                        <a href="<?php echo e(route('receipt-order.index')); ?>" class="btn btn-secondary">
                            <i class="ti ti-arrow-left"></i> <?php echo e(__('العودة')); ?>

                        </a>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <!-- رأس الفاتورة -->
                <div class="invoice-header">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="company-info">
                                <?php
                                    $logo = \App\Models\Utility::get_file('uploads/logo/');
                                    $company_logo = \App\Models\Utility::getValByName('company_logo_dark');
                                    $img = $logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png');
                                ?>
                                <img src="<?php echo e($img); ?>" alt="شعار الشركة" class="company-logo">
                                <h4><?php echo e(\App\Models\Utility::getValByName('company_name') ?: 'اسم الشركة'); ?></h4>
                                <p class="mb-0"><?php echo e(\App\Models\Utility::getValByName('company_address') ?: 'عنوان الشركة'); ?></p>
                                <p class="mb-0"><?php echo e(\App\Models\Utility::getValByName('company_phone') ?: 'هاتف الشركة'); ?></p>
                                <p class="mb-0"><?php echo e(\App\Models\Utility::getValByName('company_email') ?: 'بريد الشركة'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <h2 class="text-primary">
                                <?php if($receiptOrder->order_type === 'استلام بضاعة'): ?>
                                    <?php echo e(__('فاتورة استلام بضاعة')); ?>

                                <?php elseif($receiptOrder->order_type === 'نقل بضاعة'): ?>
                                    <?php echo e(__('أمر نقل بضاعة')); ?>

                                <?php else: ?>
                                    <?php echo e(__('أمر إخراج بضاعة')); ?>

                                <?php endif; ?>
                            </h2>
                            <p class="text-muted"><?php echo e(__('رقم الأمر')); ?>: <strong><?php echo e($receiptOrder->order_number); ?></strong></p>
                        </div>
                        <div class="col-md-4 text-end">
                            <p><strong><?php echo e(__('التاريخ')); ?>:</strong> <?php echo e(\App\Models\Utility::getDateFormated($receiptOrder->invoice_date ?: $receiptOrder->created_at)); ?></p>
                            <p><strong><?php echo e(__('الوقت')); ?>:</strong> <?php echo e($receiptOrder->created_at->format('H:i')); ?></p>
                            <?php if($receiptOrder->invoice_number): ?>
                                <p><strong><?php echo e(__('رقم الفاتورة')); ?>:</strong> <?php echo e($receiptOrder->invoice_number); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الأمر -->
                <div class="order-details mt-4">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><?php echo e(__('معلومات الأمر')); ?></h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong><?php echo e(__('نوع الأمر')); ?>:</strong></td>
                                    <td>
                                        <?php if($receiptOrder->order_type === 'استلام بضاعة'): ?>
                                            <span class="badge bg-success"><?php echo e($receiptOrder->order_type); ?></span>
                                        <?php elseif($receiptOrder->order_type === 'نقل بضاعة'): ?>
                                            <span class="badge bg-info"><?php echo e($receiptOrder->order_type); ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-warning"><?php echo e($receiptOrder->order_type); ?></span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php if($receiptOrder->vendor): ?>
                                    <tr>
                                        <td><strong><?php echo e(__('المورد')); ?>:</strong></td>
                                        <td><?php echo e($receiptOrder->vendor->name); ?></td>
                                    </tr>
                                <?php endif; ?>
                                <tr>
                                    <td><strong><?php echo e(__('المستودع')); ?>:</strong></td>
                                    <td><?php echo e($receiptOrder->warehouse->name ?? 'غير محدد'); ?></td>
                                </tr>
                                <?php if($receiptOrder->fromWarehouse): ?>
                                    <tr>
                                        <td><strong><?php echo e(__('من مستودع')); ?>:</strong></td>
                                        <td><?php echo e($receiptOrder->fromWarehouse->name); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6><?php echo e(__('معلومات إضافية')); ?></h6>
                            <table class="table table-sm">
                                <?php if($receiptOrder->exit_reason): ?>
                                    <tr>
                                        <td><strong><?php echo e(__('سبب الإخراج')); ?>:</strong></td>
                                        <td><?php echo e($receiptOrder->exit_reason); ?></td>
                                    </tr>
                                <?php endif; ?>
                                <?php if($receiptOrder->exit_date): ?>
                                    <tr>
                                        <td><strong><?php echo e(__('تاريخ الإخراج')); ?>:</strong></td>
                                        <td><?php echo e(\App\Models\Utility::getDateFormated($receiptOrder->exit_date)); ?></td>
                                    </tr>
                                <?php endif; ?>
                                <?php if($receiptOrder->responsible_person): ?>
                                    <tr>
                                        <td><strong><?php echo e(__('الشخص المسؤول')); ?>:</strong></td>
                                        <td><?php echo e($receiptOrder->responsible_person); ?></td>
                                    </tr>
                                <?php endif; ?>
                                <tr>
                                    <td><strong><?php echo e(__('المنشئ')); ?>:</strong></td>
                                    <td><?php echo e(isset($creator) && $creator ? $creator->name : 'غير محدد'); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- جدول المنتجات -->
                <div class="mt-4">
                    <h6><?php echo e(__('المنتجات')); ?></h6>
                    <div class="table-responsive">
                        <table class="table table-bordered table-products">
                            <thead>
                                <tr>
                                    <th width="5%">#</th>
                                    <th width="15%"><?php echo e(__('رمز المنتج')); ?></th>
                                    <th width="30%"><?php echo e(__('اسم المنتج')); ?></th>
                                    <th width="10%"><?php echo e(__('الكمية')); ?></th>
                                    <?php if($receiptOrder->order_type === 'استلام بضاعة'): ?>
                                        <th width="12%"><?php echo e(__('سعر الوحدة')); ?></th>
                                        <th width="12%"><?php echo e(__('الإجمالي')); ?></th>
                                    <?php endif; ?>
                                    <?php if($receiptOrder->products->where('expiry_date', '!=', null)->count() > 0): ?>
                                        <th width="12%"><?php echo e(__('تاريخ الصلاحية')); ?></th>
                                    <?php endif; ?>
                                    <th width="20%"><?php echo e(__('ملاحظات')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $totalAmount = 0; ?>
                                <?php $__currentLoopData = $receiptOrder->products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($index + 1); ?></td>
                                        <td><?php echo e($item->product->sku ?? 'غير محدد'); ?></td>
                                        <td><?php echo e($item->product->name ?? 'غير محدد'); ?></td>
                                        <td><?php echo e(number_format($item->quantity, 2)); ?></td>
                                        <?php if($receiptOrder->order_type === 'استلام بضاعة'): ?>
                                            <td><?php echo e(number_format($item->unit_cost, 2)); ?></td>
                                            <td><?php echo e(number_format($item->total_cost, 2)); ?></td>
                                            <?php $totalAmount += $item->total_cost; ?>
                                        <?php endif; ?>
                                        <?php if($receiptOrder->products->where('expiry_date', '!=', null)->count() > 0): ?>
                                            <td>
                                                <?php if($item->expiry_date): ?>
                                                    <?php echo e(\App\Models\Utility::getDateFormated($item->expiry_date)); ?>

                                                <?php else: ?>
                                                    -
                                                <?php endif; ?>
                                            </td>
                                        <?php endif; ?>
                                        <td>
                                            <?php if($item->is_return): ?>
                                                <span class="badge bg-danger"><?php echo e(__('مرتجع')); ?></span>
                                            <?php endif; ?>
                                            <?php echo e($item->notes); ?>

                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                            <?php if($receiptOrder->order_type === 'استلام بضاعة' && $totalAmount > 0): ?>
                                <tfoot>
                                    <tr class="table-active">
                                        <td colspan="<?php echo e($receiptOrder->products->where('expiry_date', '!=', null)->count() > 0 ? '5' : '4'); ?>" class="text-end">
                                            <strong><?php echo e(__('الإجمالي')); ?>:</strong>
                                        </td>
                                        <td><strong><?php echo e(number_format($totalAmount, 2)); ?></strong></td>
                                        <td></td>
                                    </tr>
                                </tfoot>
                            <?php endif; ?>
                        </table>
                    </div>
                </div>

                <!-- الملاحظات -->
                <?php if($receiptOrder->notes): ?>
                    <div class="mt-4">
                        <h6><?php echo e(__('ملاحظات')); ?></h6>
                        <div class="alert alert-info">
                            <?php echo e($receiptOrder->notes); ?>

                        </div>
                    </div>
                <?php endif; ?>

                <!-- ملخص الأمر -->
                <div class="mt-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6><?php echo e(__('ملخص الأمر')); ?></h6>
                                    <p class="mb-1"><strong><?php echo e(__('إجمالي المنتجات')); ?>:</strong> <?php echo e($receiptOrder->total_products); ?></p>
                                    <?php if($receiptOrder->order_type === 'استلام بضاعة'): ?>
                                        <p class="mb-1"><strong><?php echo e(__('إجمالي المبلغ')); ?>:</strong> <?php echo e(number_format($receiptOrder->total_amount, 2)); ?></p>
                                    <?php endif; ?>
                                    <p class="mb-0"><strong><?php echo e(__('الحالة')); ?>:</strong> 
                                        <span class="badge bg-<?php echo e($receiptOrder->status_color); ?>"><?php echo e($receiptOrder->status); ?></span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التوقيعات -->
                <div class="signature-section no-print">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="signature-box">
                                <?php echo e(__('توقيع المستلم')); ?>

                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="signature-box">
                                <?php echo e(__('توقيع المسؤول')); ?>

                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="signature-box">
                                <?php echo e(__('ختم الشركة')); ?>

                            </div>
                        </div>
                    </div>
                </div>

                <!-- فوتر الطباعة -->
                <div class="print-only invoice-footer mt-5 pt-3">
                    <div class="row">
                        <div class="col-6">
                            <small><?php echo e(__('تاريخ الطباعة')); ?>: <?php echo e(now()->format('Y-m-d H:i')); ?></small>
                        </div>
                        <div class="col-6 text-end">
                            <small><?php echo e(__('طُبع بواسطة')); ?>: <?php echo e(Auth::user()->name); ?></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if(request()->get('print') == '1'): ?>
    <script>
        window.onload = function() {
            window.print();
        }
    </script>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\to\newo\3\resources\views/receipt_order/show.blade.php ENDPATH**/ ?>