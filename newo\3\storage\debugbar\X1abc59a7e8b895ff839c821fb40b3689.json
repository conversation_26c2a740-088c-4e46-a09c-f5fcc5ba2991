{"__meta": {"id": "X1abc59a7e8b895ff839c821fb40b3689", "datetime": "2025-06-17 06:54:28", "utime": **********.313677, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143266.739084, "end": **********.313712, "duration": 1.5746278762817383, "duration_str": "1.57s", "measures": [{"label": "Booting", "start": 1750143266.739084, "relative_start": 0, "end": **********.122051, "relative_end": **********.122051, "duration": 1.3829669952392578, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.122076, "relative_start": 1.3829920291900635, "end": **********.313716, "relative_end": 4.0531158447265625e-06, "duration": 0.19163990020751953, "duration_str": "192ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45707960, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.008790000000000001, "accumulated_duration_str": "8.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.224902, "duration": 0.00596, "duration_str": "5.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.804}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.259799, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.804, "width_percent": 14.903}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.283215, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 82.708, "width_percent": 17.292}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-931508134 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-931508134\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1858289311 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1858289311\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-673625422 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-673625422\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1917004230 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=lxd95%7C1750140707036%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlgvTTltdloxL3hvVi9TT1JBR1d6UHc9PSIsInZhbHVlIjoiTlZzbWgxb0hVZU9MTTVZZHFNT1hpQmJFTGFuOTZXTm0zK1ZrMjBaYnZMZU9mU0MyaFVaSzN0clJwUTN2NjJMZ1V1dlBZeXVqcmxmLy9zTnFMZ0dXbENqYmRzSktwNWdKTVE3K1BSSGxsblIycUd1MXpvR1ZpRUR3MnRuYVJqMzhZNWJUZml0YnJYaSt2bmg3NVl1S0E4NVVmQzJkTlMwMWFWZTVGOVJwNDJtSUJ3ajZCYjRTTkpTckdvbzgrWEVZY252VWpBTmFMTnlCWlVTcHhHVm1zZ2s0YnphL1J2ZDlVYjBkclNGTjBVZVVYeXlXRlJwcTVLM29vZGJ3RGkxM2J6bE9KRHcwbWozYk5VQmlvcUFIaExOd1Y2K0MzOCs1M05JTVJXZWpya0pXRkFZcngyWGN5YmxmOE1hNUpiWVJFSHNzaGg0Q0FpQUh5M2Fzdnd6eVFhMXI2bXd2T2RCVmFIcnV6M1NNVTVzYndxRHVhWjJ1NkNSL1ZPMllrYU16VjBraGJMcXFsMjRMOUg3M1BwOEhXamRBbUhXOFNaRTh5R2RkRWFmbFQzVmVnYTkvN3RERE9mNEJsK2VicFFQTGJDVit1OEszemxiM09sL0c2UkN5bzBNZ0RzcmdyRFdyYVh2UzF4OGw5SFNkakRnMTZQVXBhekw0Ty9lQzUyZHgiLCJtYWMiOiI1YzYwZTZiMTMzZjEzYTY5NWQ0MGM0ZmVlZmY3OTUxZjg0ZTJhM2RjOWJlNDNmOGNhYWE5Y2ZkMGMzMGUzNzE2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjlnT2p5dEx6OHh0K0RBc1RGbkpjOWc9PSIsInZhbHVlIjoiTkxrMnBmamVPSDRVcmNIeTZ5RFFUbGNRNGVUMGVKcysyUjJ4TnpWNHdNZ0prMllvODN5L2dNVGhJdTlEc1dNbmZNRXdQZkNNMDNyZWV3THdmdllGY3lhSkxVMDBzbW90QlRPVFpGZDdBc1N0NUdvSFFSNWZWM0FDV2JXVUZLYXRxc2RzMHpMeHkxajVsbkthc2NiNlBManBiTktsWXBGTmxOaXFmNlRlNC96bkVOQnNsREVodHZtaks5T0RpTDhSRkV6ZHdrdXlJNWc5bXFqanNITmJGdHlmTVRyd3U3UnR2alo0d05mMjI3THFobCtkVjFEMWp6OS9udzljaWZNejVySWdhU0ZnL01jN2dQaTg3RlpXd0VrcVZCQUtwWUNQMDJvN2tmZDZPQ1ltV2ZLOGxNdXQxbkpRTDJLTUtIcDN5QjJMd1BsQXV4VTR5djFLOHd2WnRGZXJsem1XTTJ1SG9Bdi9WZ0NXb1BSUTZTdnJxUGFjeDFIL09ZbktMYklYZDNnM01WWmcvQ2pObzJGcGNab0Q4RGxYZGhNMGZ2V0QxRzF4MmI3OFp4NEVDT2N5QkdER0FYQTdyOE9HQ2xXc0pQQ2UxMm11eW1oUjZ6UE5ESkUzWTJLcWRpbnoyNWphNis4bHMycldpdkZ2VHlocWdxL3FjRVF3aStYQVVFdjIiLCJtYWMiOiJiOTUzYTBkMTEyOGVmNGZjOGUxNDU1OTNiYmJjZDE1YWU3MzJmNmVjOTcxNWUzOGNlMzhhOTFhYjZlM2Y2NGYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1917004230\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDWN9h6R9gAIdzZaXBzXfq0WGpauVfrUj70kKWA2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-649700952 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:54:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxpQVowbTcrcmM1MUxQOElHZHdXT0E9PSIsInZhbHVlIjoiS3VzblVqaHlWblVaUlVseC9YdzVicTNLWWRybUl3amhDdStjRHhrVVVSU3NKYXpITGU4R0JIWU9mR1d6V1pVNFhnSU95eW5NaVZDbjhzbXFsQjFaRzFVK1NGaXc5d2loTXdkMGc2RUJ3dktsYTg5VDVpaGxDTy9TQTgybFA2MzFTWDlTYUpralZjaHVOckc4bWUvV0xIN2xqdko4T3BLZG81Mk9ZSDN1U3FDUmNYZ3FEVHJNMEo5Y2pWc01maHpld3N1aGZYL3BhRU5QcFM2R1A4QkRURTNvRVhFS1hkbDdvNW9FK08vL1o1RTFCVGJ5cGFtcjQ1TTExc2h1T0wvUEQvQ2gzcnh0WW1pbDNGN2JyQXNyWXRwOXRqTDQ1NmswVm9lTW1EcldnMllRc00vS3NUa0VWcit4ckY1aFdFQjBQRXdUTkRDS2Q1Qk9FdDRjQ2w5RHlBdzgzSlZERG8zY0p0K3NwdTlITXUzVGRLS0VpVWYyTmFVUkZGYVVUQ3Q4THVyTDJMeXo4UmtZOWhvVnkydnJ6b3FJUml4RUhyUG5LeXRjZGZ0aGFrSGs2ZXRxL1VXd1JZSjVXOGVSYjdKKzd5YVFOekdpNHZRUUlVN0hueWtCUldlclBoYnMvcUtuWWV3Q1RrQjJQa3JoQzQ0MzlNTVRRSnpKZ2MrUm5kb1UiLCJtYWMiOiJmNWYwYmYzMjYwNDEyNmRlZGExNGY3MzcxNGE0OGY5MzMxNjhlNDNiMWViMjhjMmQzYzcxZjU2N2M1ZWU0YmNiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:54:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjdSUWJjK3hzS2dyU3pIeWdHWUFab0E9PSIsInZhbHVlIjoiQU1qbjhaUGhGcXFtUnh3M3dPQWE1N1JtVUFUSUpPMUVOS2FIT3NPai9NOWs4bnRwaitrZk1XbnJXM1FNbUxDVEZDODRidFhWNGVNYXBJUVZueURZTFM3ZGtHVjZ2QURYcXIxT1BvNDVCelF6TXYxYWNGbnBieDFTNWJPYWowU2pxKzdjT1NNMWU2c3l1NUJyQjNZemt0VEtEUTJvRmlnSnd5NUJFTmJXOXhVVUdOQ0ZFYmgrWFUrL2dSSFFjYUw3cFpLUlhSSVJiOHlsa2REbTg0UW96ZG85TDhKU2JJZFQ3V2hlMTFKd1JvMFZWelpYd2tDbVBzQkRzdnRpQXhjZDB3ZXJTV0JkVUp5amZMZHh3YUNiUmxLNTBVMUlzWDlaQkdwTGh3MzNaZEYrVGZvS0lyTVN3cFI0WlVjUjFuWFhUWCtzRmRyZnY1MUdEQ2VjTUFqWjRlbXgxbWhXRk8rbUpMZ094cHp2by9CNmkvTkRNN1lBMCtNemNpekk2UGFqODVCSnpRWlVZM1UyS241ZUVJZW9iUWI5WnYwZmdmVUcxNis1K25mek4rQjlrY0RLclExUUFyeUg3amx2cjk4RUhPdXlWbmxCR0FCbHNVRmZtRk9vM21xNGJYL3hHcHFtQXdVZGkvaVladGtNYTdOSmJhNXJZSXk4cWtmMUdneXgiLCJtYWMiOiI2YTJkNTIyYjE1M2VhZDljZmViZjQzMmU0Y2EyMzUwMjM5ODUyMzEzOGIxYjBlM2U2ZDAwZGJkM2Q2ODJmZWFlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:54:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxpQVowbTcrcmM1MUxQOElHZHdXT0E9PSIsInZhbHVlIjoiS3VzblVqaHlWblVaUlVseC9YdzVicTNLWWRybUl3amhDdStjRHhrVVVSU3NKYXpITGU4R0JIWU9mR1d6V1pVNFhnSU95eW5NaVZDbjhzbXFsQjFaRzFVK1NGaXc5d2loTXdkMGc2RUJ3dktsYTg5VDVpaGxDTy9TQTgybFA2MzFTWDlTYUpralZjaHVOckc4bWUvV0xIN2xqdko4T3BLZG81Mk9ZSDN1U3FDUmNYZ3FEVHJNMEo5Y2pWc01maHpld3N1aGZYL3BhRU5QcFM2R1A4QkRURTNvRVhFS1hkbDdvNW9FK08vL1o1RTFCVGJ5cGFtcjQ1TTExc2h1T0wvUEQvQ2gzcnh0WW1pbDNGN2JyQXNyWXRwOXRqTDQ1NmswVm9lTW1EcldnMllRc00vS3NUa0VWcit4ckY1aFdFQjBQRXdUTkRDS2Q1Qk9FdDRjQ2w5RHlBdzgzSlZERG8zY0p0K3NwdTlITXUzVGRLS0VpVWYyTmFVUkZGYVVUQ3Q4THVyTDJMeXo4UmtZOWhvVnkydnJ6b3FJUml4RUhyUG5LeXRjZGZ0aGFrSGs2ZXRxL1VXd1JZSjVXOGVSYjdKKzd5YVFOekdpNHZRUUlVN0hueWtCUldlclBoYnMvcUtuWWV3Q1RrQjJQa3JoQzQ0MzlNTVRRSnpKZ2MrUm5kb1UiLCJtYWMiOiJmNWYwYmYzMjYwNDEyNmRlZGExNGY3MzcxNGE0OGY5MzMxNjhlNDNiMWViMjhjMmQzYzcxZjU2N2M1ZWU0YmNiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:54:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjdSUWJjK3hzS2dyU3pIeWdHWUFab0E9PSIsInZhbHVlIjoiQU1qbjhaUGhGcXFtUnh3M3dPQWE1N1JtVUFUSUpPMUVOS2FIT3NPai9NOWs4bnRwaitrZk1XbnJXM1FNbUxDVEZDODRidFhWNGVNYXBJUVZueURZTFM3ZGtHVjZ2QURYcXIxT1BvNDVCelF6TXYxYWNGbnBieDFTNWJPYWowU2pxKzdjT1NNMWU2c3l1NUJyQjNZemt0VEtEUTJvRmlnSnd5NUJFTmJXOXhVVUdOQ0ZFYmgrWFUrL2dSSFFjYUw3cFpLUlhSSVJiOHlsa2REbTg0UW96ZG85TDhKU2JJZFQ3V2hlMTFKd1JvMFZWelpYd2tDbVBzQkRzdnRpQXhjZDB3ZXJTV0JkVUp5amZMZHh3YUNiUmxLNTBVMUlzWDlaQkdwTGh3MzNaZEYrVGZvS0lyTVN3cFI0WlVjUjFuWFhUWCtzRmRyZnY1MUdEQ2VjTUFqWjRlbXgxbWhXRk8rbUpMZ094cHp2by9CNmkvTkRNN1lBMCtNemNpekk2UGFqODVCSnpRWlVZM1UyS241ZUVJZW9iUWI5WnYwZmdmVUcxNis1K25mek4rQjlrY0RLclExUUFyeUg3amx2cjk4RUhPdXlWbmxCR0FCbHNVRmZtRk9vM21xNGJYL3hHcHFtQXdVZGkvaVladGtNYTdOSmJhNXJZSXk4cWtmMUdneXgiLCJtYWMiOiI2YTJkNTIyYjE1M2VhZDljZmViZjQzMmU0Y2EyMzUwMjM5ODUyMzEzOGIxYjBlM2U2ZDAwZGJkM2Q2ODJmZWFlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:54:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-649700952\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2107875442 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2107875442\", {\"maxDepth\":0})</script>\n"}}