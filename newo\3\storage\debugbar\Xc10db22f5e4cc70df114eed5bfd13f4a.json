{"__meta": {"id": "Xc10db22f5e4cc70df114eed5bfd13f4a", "datetime": "2025-06-17 07:13:14", "utime": **********.343853, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750144393.741515, "end": **********.343875, "duration": 0.6023600101470947, "duration_str": "602ms", "measures": [{"label": "Booting", "start": 1750144393.741515, "relative_start": 0, "end": **********.209899, "relative_end": **********.209899, "duration": 0.4683840274810791, "duration_str": "468ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.209914, "relative_start": 0.4683990478515625, "end": **********.343878, "relative_end": 3.0994415283203125e-06, "duration": 0.13396406173706055, "duration_str": "134ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48268120, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02359, "accumulated_duration_str": "23.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.265173, "duration": 0.01682, "duration_str": "16.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 71.301}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.293044, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 71.301, "width_percent": 2.798}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.3155642, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 74.099, "width_percent": 3.518}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.318811, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 77.618, "width_percent": 3.306}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.325818, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 80.924, "width_percent": 16.405}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3333752, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 97.329, "width_percent": 2.671}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-432024627 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432024627\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.324442, "xdebug_link": null}]}, "session": {"_token": "TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1216750094 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1216750094\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2039793312 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2039793312\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-640928301 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-640928301\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=17n9b1e%7C1750144374665%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkQ3YVR3dUpIL1hOYTZ5cVNqaHlXVmc9PSIsInZhbHVlIjoiQVpLVWVWMC9wTVEyOHM5djkzTmxoTmd3SXQvcFJ2NUVRQW9ySG0wVVdBMWsrb1BrSUYwNE9sakVXZThlR1ZlVmFWSVJVTVFEaGFxUXRhVldZemdJRGs5QkJFcElCTXpBcmJ3ZnFjclBTVVZYTnBhSEdzTGVhSzZYL3JhOXpRYkNUdFNwSUtLMy9MSTV5Nk5ncmxidkdKSk9pMXNGK0JDdGZob0h6anJGcGc0MVpPb3J0bjZsWnI0RXpBZUpmalB4N0ZFSW5aTnNEakxieHRvYVh1Z0Z5VUpWUFY1SlQ3NmhDY1JBdk13WnA5cGlETU9sbm1UdEJCN1pKZ0V5ZUhnUWprYjlJSllIQUVLbG1OdkM0dllvTFlBQ0NNaFBNVWtBWWZ2cDQyMk5EbXdZK3I3ZFVtWE04RWhmaUNLbWE1OURwcUhDUFlXZ1ZlSzdWRlk2bWVqZnBteFNvZjRvcU1rcnBudmV2bTV1dGJLL25pdGlKbmh0RXJ5Y1E3REdDd0FRQkpLa1AyTzlkKzJwZUJYekl6dEYwRDdqUW5JU0kveHh4RzlQYVNKWHI0OFhrZXNPUXR3SUVWMStld2xXaWtmYmtHNmlnMGJHb1NwZFRaY3l0MXdtaWtKa21aSG5INjI5anhjY1pOSGRPR0Nuam1RWWg2WHNZSytUTlltUlNONmsiLCJtYWMiOiJlMGI5NmI0OWQ5YmE2YzU2MDdkYjVkZDkxYjUyMWIxZDIxNzY2MGI0NDgwM2EzZjVjNjNlYjFkZTczM2IwMTliIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjR2MW8xMkE5UStrcXRkVUt3K2ZnWkE9PSIsInZhbHVlIjoidDhWdnhtUTVyVlNwNlc2UmlTUVRXanJmUUlqTitCRERQSVZ0c3lETFBrUHZwN29XWElFM3ZtK2FDMUwzNmw1aHBNcC84TVEwR25GUkM4ekhidVByMDFTK2dNN00rVHZ1Qm81Rk1WR0hWa3VQSlpya2lNNHIvQjRUTldXd3lxc0pCeW9vb1JiL1c5YzBWOHc2WlN3Yjc3bFV5WUxlcjdFY0JPKzZJK3FXVGRIZnc4Vmp1bzdlR0NhQmNOejNUbzF0STJkN2dld1RROGNKck5yRzZ1akpxOXVJSmJsaU5oMmRhRGs5Mk5OenNiai9BSEhzaWhJVmk0TTE4YW9HYU5PZkN5Y0wvN0xFZmNub2ZEcW40VkJpYWZ4QmQvQUNSZEZwZFUvSkczQVMyVW5QQVRFUHI5SDhhUm9OQkFjZkNZYmFzMzg5MXlLVC9TZWRvTkYwck8zN0sxd0xMS3NmbTdIVC84ZllYcVY2VmVwQnpqN0RYb1RCeVhuNGVFUGU2ZGh2YitPcisvYUx6WXV6S1huOC9yWDFtVmFHV2ZYc0hSZXQ3SXZmcGhqZmJKVmgvNElyWno2V1dFQWNrZVo2UUZDYkpTVTZFWFF5MjNHQ1dQbC9IZFlyNFZRVjJCNVhMR0dXQmhZV3dTamltZTdwWnY2QVJWNyszSkNwdmdGZk5lSnoiLCJtYWMiOiJhYzY3MGQxMWJiZmM0ODcyMTMzNTAxYzJhOWE4MDE4MGUyNjkwNjE5ODY2MmUzZGJjZGRiMGE0Nzg0YTM2YWE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDWN9h6R9gAIdzZaXBzXfq0WGpauVfrUj70kKWA2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1030552526 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:13:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRGbjFidHIxVy9tZ3hWSEJvZkxoOHc9PSIsInZhbHVlIjoiSW9KK3U0L1JUbGtSd3FhdXdoeHBtN0VwN092VWdqOTRHcXpsQkhUeGVLS3ExN09JaEd2d2NWcVdyaGhGTTVrOUlXem1SaDNGM2UrTG1kYi9CU2dweko5WGo1L3BCZWo0MTlNQ2xvQkhDN0xWMTBEQnhQY3pSVUt3T1dIVmJVWWprZHJRMWJkcVcrc3ZCT0pDM1A5TjhSU25jQnAwRUwvTVMxSVhiaTdOZHVpMGExdDRXeFVYRlhkUmxHM1NXQ0pRc25hZnRNQ0ZHd3ZjMVVod3EvWGZTRk9td2k1VnJwRkNsOWlBeW03ZVJMeXNaMmZ6N2hWU0Q4UmJkT1B1bnVWNGQ1a1AxQzQzVVk3NFlOelJWcEFmRjZZMmt4YU5xZ05ib1BiYlQwRkVWRExIRDMvUVJVUEVXc0ZDWE9iYktwVkVLd3ZRK0YrdENxL2w5YnhoRFdLVXB0bCsyZUkzMzBEczhBc3FSakY1UUFQT0NhSlNha1VkUjVvQmF4eE0yL01UWFo2dDR5WkZLaFpjUHpoMjY2cENPRGMwZHcxdVQvRkc1U3FoNDZFU2xOaWhxNzF2aXhvRk1QLzZRZVBQQTM4UUdqMkxxSkVCZE45NEdSOGxKNEJybDV1Snp4ZzI2WlhoeTlRNlJ3RFBCQ1lVZXdycWM2NU1Bc2xMSXBLaEc4VkgiLCJtYWMiOiIxN2I4YTEyNDNkNWE0ZDNlOTdiNjVkNTU2MzE0MmY1OGUyMDI0NTFkNmY1ZTUzMGQ0OGM3YjNmMTFkNjNhMzI5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:13:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik55ZTR1UTA5REZva0FnZHlMZVJZTlE9PSIsInZhbHVlIjoiWkJPTWp5NXgvbXVTbkcvMDdVd0RobEdWUitOWVVWSEZRS0JkSHFDK05pWmFXbDhxd1dsYTg1dzFrNThGYy84eE1sd2NJbjJxOEtLWUJKd1FnSXBQTGV5akZmeHFheGQ3MENIaUpDMEZEMk5jcHBJZlRUaEwvb1cyOWVhaGo4eUczR3VwejVtYUk4RkFKTUVNWWlKMG5ZWnMrVmxBUGlxdHpKLzVaYy8vREdZZ0FFb2hJTjkwOUlsU09MdWhiSC90OVBrYzAxSmpiMFVtenRHcWRXRUw0UFBPeE9Yc3l2TzR0MWxzTDVZc3I5MVFOamw5emIxMncyMTRpQSszNXJrVnBHVlo3NFpLWFNnQnI2L3JPT3M4QVluaCtCVnlqejhPVDdGV1Z3VmpFcjNRQitaTk1OV3Q4UHA5ZzdZcGFyZGIzQ3JKYnlGNmVONDhDYlBBNXp5aytEMTh2czB5dlY5U01Ndy9aN09laTdZRE1MWm1ickhQVjJERllzTk1HaGdNUWh6QlJMcWVLRC9KMjcvS3psZEFWTWpnQjB4M2V1V0QwazFDVzhJdDJkUmMxdEVJUmFuRFVOd2pSTVBwTGV1RXFXdFpDWjRxTkVldUxRQ1BUcGVYLzZhSnQzN2FETDRXOG9yOURENERCM2UyUVUwMVREOVBzS0ZtZktKeUpwQjQiLCJtYWMiOiJiNzYyNjNiNWVhZjZiM2Y5YjE2MWQyZGM4NmU3ZGEzZGM2Nzk0MGEzYzI2ZWE5MzQ4YjM4OTM1ZWViYTdjYjUxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:13:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRGbjFidHIxVy9tZ3hWSEJvZkxoOHc9PSIsInZhbHVlIjoiSW9KK3U0L1JUbGtSd3FhdXdoeHBtN0VwN092VWdqOTRHcXpsQkhUeGVLS3ExN09JaEd2d2NWcVdyaGhGTTVrOUlXem1SaDNGM2UrTG1kYi9CU2dweko5WGo1L3BCZWo0MTlNQ2xvQkhDN0xWMTBEQnhQY3pSVUt3T1dIVmJVWWprZHJRMWJkcVcrc3ZCT0pDM1A5TjhSU25jQnAwRUwvTVMxSVhiaTdOZHVpMGExdDRXeFVYRlhkUmxHM1NXQ0pRc25hZnRNQ0ZHd3ZjMVVod3EvWGZTRk9td2k1VnJwRkNsOWlBeW03ZVJMeXNaMmZ6N2hWU0Q4UmJkT1B1bnVWNGQ1a1AxQzQzVVk3NFlOelJWcEFmRjZZMmt4YU5xZ05ib1BiYlQwRkVWRExIRDMvUVJVUEVXc0ZDWE9iYktwVkVLd3ZRK0YrdENxL2w5YnhoRFdLVXB0bCsyZUkzMzBEczhBc3FSakY1UUFQT0NhSlNha1VkUjVvQmF4eE0yL01UWFo2dDR5WkZLaFpjUHpoMjY2cENPRGMwZHcxdVQvRkc1U3FoNDZFU2xOaWhxNzF2aXhvRk1QLzZRZVBQQTM4UUdqMkxxSkVCZE45NEdSOGxKNEJybDV1Snp4ZzI2WlhoeTlRNlJ3RFBCQ1lVZXdycWM2NU1Bc2xMSXBLaEc4VkgiLCJtYWMiOiIxN2I4YTEyNDNkNWE0ZDNlOTdiNjVkNTU2MzE0MmY1OGUyMDI0NTFkNmY1ZTUzMGQ0OGM3YjNmMTFkNjNhMzI5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:13:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik55ZTR1UTA5REZva0FnZHlMZVJZTlE9PSIsInZhbHVlIjoiWkJPTWp5NXgvbXVTbkcvMDdVd0RobEdWUitOWVVWSEZRS0JkSHFDK05pWmFXbDhxd1dsYTg1dzFrNThGYy84eE1sd2NJbjJxOEtLWUJKd1FnSXBQTGV5akZmeHFheGQ3MENIaUpDMEZEMk5jcHBJZlRUaEwvb1cyOWVhaGo4eUczR3VwejVtYUk4RkFKTUVNWWlKMG5ZWnMrVmxBUGlxdHpKLzVaYy8vREdZZ0FFb2hJTjkwOUlsU09MdWhiSC90OVBrYzAxSmpiMFVtenRHcWRXRUw0UFBPeE9Yc3l2TzR0MWxzTDVZc3I5MVFOamw5emIxMncyMTRpQSszNXJrVnBHVlo3NFpLWFNnQnI2L3JPT3M4QVluaCtCVnlqejhPVDdGV1Z3VmpFcjNRQitaTk1OV3Q4UHA5ZzdZcGFyZGIzQ3JKYnlGNmVONDhDYlBBNXp5aytEMTh2czB5dlY5U01Ndy9aN09laTdZRE1MWm1ickhQVjJERllzTk1HaGdNUWh6QlJMcWVLRC9KMjcvS3psZEFWTWpnQjB4M2V1V0QwazFDVzhJdDJkUmMxdEVJUmFuRFVOd2pSTVBwTGV1RXFXdFpDWjRxTkVldUxRQ1BUcGVYLzZhSnQzN2FETDRXOG9yOURENERCM2UyUVUwMVREOVBzS0ZtZktKeUpwQjQiLCJtYWMiOiJiNzYyNjNiNWVhZjZiM2Y5YjE2MWQyZGM4NmU3ZGEzZGM2Nzk0MGEzYzI2ZWE5MzQ4YjM4OTM1ZWViYTdjYjUxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:13:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1030552526\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1642311768 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1642311768\", {\"maxDepth\":0})</script>\n"}}