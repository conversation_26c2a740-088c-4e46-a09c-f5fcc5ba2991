{"__meta": {"id": "X7ba1434400d226c3cb852c08fbaeab37", "datetime": "2025-06-16 15:22:28", "utime": **********.958813, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087347.268779, "end": **********.958858, "duration": 1.6900789737701416, "duration_str": "1.69s", "measures": [{"label": "Booting", "start": 1750087347.268779, "relative_start": 0, "end": **********.741098, "relative_end": **********.741098, "duration": 1.4723188877105713, "duration_str": "1.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.741129, "relative_start": 1.4723498821258545, "end": **********.958863, "relative_end": 5.0067901611328125e-06, "duration": 0.21773409843444824, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45152536, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02113, "accumulated_duration_str": "21.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8553782, "duration": 0.0184, "duration_str": "18.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.08}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.909082, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.08, "width_percent": 5.726}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.932364, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.806, "width_percent": 7.194}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1678000293 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1678000293\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1231304279 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1231304279\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1560539326 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1560539326\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-289157131 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087342824%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjgySUF5Zy9tL1pFUTFyUFZzYWEwV3c9PSIsInZhbHVlIjoibVZodmZGQU1FOVBFazdIUTZpeEsrQmJ1UzZOand4TnhLY1FWQUVKR0ZURlI5M0pkdUxkb3ZzZ1Q1R1E1OTRMN3haUEprWGhOWWlhN25HalpxMCs2b09tNTlZVmowVnl6SjlhcmhzeS96VmFRMDNMOVdJNWpWa01tZ0JzcDM2aXBRREtlSHpRYW5leWxiNXFJREFWaWc0WUJDSzlJN2JIZU1WbWZESitXR1FCbWpFajRlNEE1YjAzNHFMYzFUek91N3VlNFp5ZzZjWjE1WFdka2xZUHpvd0NPMUxwR1l3bnBVNXArWHg5Z2tIeDBSR3dBMk5qd1RobGNwelZnOW54aHNJTEwzUGw0Rkp3WlY0emh0Q3AyU1JlOXRYUW9scWdZYnhNejYxQ2FpbHVvcGhjRzBzZzB5RmJwNndxZUY0MHRTRHAvWTJvbkRNbWhmR1BNdDVlYVpUcEovYTBPOXNiY3R2TVVPRVBpem1oVkY4dkRiNEZiR055MzdIYnRJQlZqNGVPUTRwejRTSmM3QkU4UFFuUmtpQXA2YVBra3lMSXFkL205V0NrMElCeXkrY3A4UjdjQXpveE5idVhiSlFaalBtdlBTdVZpUUdsanpFSFpscmwvRHZhNWlldTRYSXQ1YVkrRDRaOHQ4ejNNV004bUVVUWJHQ0x0RWNEdnVhODIiLCJtYWMiOiI2NjMzYmMyNDMwMDJlMTAwZDZjMGE5YzZlMzM4ZTZlZGRjMTk3YjAyYWJlMzNhYTNhZDY2NTE5Mzg1NzllNDIzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjV6c0JUUW45RFRHVVRmSXRCK293cWc9PSIsInZhbHVlIjoiZFBUbnhmUXNob0loQ0NUb01lRjAwS2xOME1VUFQzK2R1TFArNDFVNzVVeElCOXpDU0x4SDYwSVRteXc4eXlVOTlJNjY3S3lIaXlNUEp5bDd3c29TbFN5c0ZyT3dUOEdIWnRYRTAvU1U3VVNZN0FGM2ZKZmJQbHN4dEJvdjFVL3ZGZ2VZOXZ6OXVRS29LS2lDZUVNWm5IR0U0ODBKVDBDOHBHaUN5Tm0xRUVhWFFBYkEwZ2RLam5rMHlHUGdXWk5QREJzaG9VTGNmVkFUdnU2OWRqenN1T1BhZnFWK1ljRnd2NjhoQTY2OUc2WlppeG5rSEQyckRhQTdFYTNuWjdZTzRSVDZHVk81Sm00SkFYb2MyMENtNkN0SDd0SG5Oc2crZVRWUW1ZRjk0czFHbFQ0aWN2czJOU1g2cXFVbU5IQ0RCaFNrOFYvOGcyQnhnVjRxQnhySW1oVmlwUzRnOExCYzh2eTFwcDAyYm42THZrV0xyWW5lUXhuS01GS2NQcGVtMTBDeThKZE5VVVpzTXc2bGg1TUdQTllweUVqVmRsTVZQaDdhQ0RYcmtPR3JSOS9aNndycXpPMFluWUtYZnd3NTExS3Z6U0VlemFWZHNuZTJGdGVvL1RjZHNUS3NvZzJJUDRPNDdEakoxeWw2WWgyNElGc1F1dG5ZRzNKOHdESDAiLCJtYWMiOiI5NjRmNDEzNjc4MzhhOWE3MTY4MWM1YjI1NDE0ZWFmZjhjYjkyOGU0Y2M2OTY3MWQ1MGU4ZmZkMjU0MzY3NDRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-289157131\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1714303129 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzcvmOKIgVMCCvEHD9HLB5trqF9yJ9GcIs8Pndo9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1714303129\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1996488500 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:22:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZlVEJtQVdOZVlTSEcyVFh2WjJ0QUE9PSIsInZhbHVlIjoiVG9keTdzUXM0dkZka2laWU1FSmRFT3ZxZmFlWWk1Vm9FOXQ2VkZuSDJEdStmR2FpeDZod0J0bkNmelJuakR3dHN3aW0yNzZyV3NsZ0FiRjc0N2VlQjRNTlFMZXlvQ2ozLzZ4Tmk1R0FpZzBGbm90QlZyemNLUWFOYkpHSkgvYWM5V2RHOEpWTmIycFhZZkRORTN4Y0pzdjdFR1FoZ1NrdU1TUUhKR1JiQlZyVFlZVWx4VkE4QW12QnNNMkNSbmhjVkVYRUE4OUgyTXZKdllrTzR0U0xpNDVJR3RQdEVJK0lScWU5WUpkdEJiVkhEeWdlaXdxd3pzSzhVVXJCVkFwOExCTmcxbFB3WEtMV0RVaWVhUXNQZnh0TzJRaFZ0UmdQcjRkSVN3Zkp1cHpTWWkzbWM2VnJWV00xRTRKQ0JNY2s2dXVuK2ovYWNlZldiTDh5M3lCMFhmVURzMHlycEljeFAvRENQT2ZBZTMxbmI2b1BXMjVUWUQxUlhHb0RoQnh4aXpwVVhFS2dDandNcU1FaDBzdGxVb1FpalZIbUdCRHRKVDJvTzJ3eGx5eTN1MU4xM3BzbjdjMFlCaWlPVXByUmt6UXN6RzYvVkV4Z054R1ZoZXRjNjFmR1M0L0ovRnd5VWpvQ3RqVWNhNmtHM3VZNm9RUVJ2dGdKNWI3bHQ3S3oiLCJtYWMiOiIwYzcxYTc3Y2JiNzJlZTE4NjBmN2RkYWNmY2Y3ZDJmMjgyZjc0YTlhN2FkMjllZjY4NjQ3NmMwMjk4MTUxZjBiIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:22:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklzWlhjSjhMSmNhdjEzM1lRNk9xWWc9PSIsInZhbHVlIjoibHNHa1R6dUYxSThERlJzK0FDdjZWZzBVRmdQNndlL0RHM2gyZFFMcXBNMXBQMlB6UjNNcWpXSnF6QW1wRGpGSGJ5dldHaDZHeWxwVytuU1hkWldpQ0J0KzV4UjdBd0ovT2xBUnpvcnNDZ0VUU3Jjelh2bC8xSU5lMTNLMDdUdC9FNEwwc1AxQ21FMHVyWkFCNDNyZTBwT2xCcHZEWndEai9FTjUydGRIQXBFbHpSd2MvT2tTdXE2cHlHcjhvZFFXRWV4cG5PQ05XS0pZaGMvdXU3bmNBUEFPLzN0c29xdHkwRzdCNElwUTJWRTVMcWViWDU0SzQ1MHk4cEVnUzVTV2ZXS0IvRUlzTzVsVzBGa1Z3SXcrKzlmTnNqamRtU1lPcHIzV00yb2JSNldjVUNMM1NXNE1CRjVscHp1d0RFeEhBVzBxS0NRSmJjUjQrRDRoMnlnb0F0RXpJRXNsZEIwRlFGY1c3Y2FPanl1VmkxeUNNOUE1WmF4Z0JoK1UvV005aXJHZGp6UVptRGJWcGhaL2YxdXQ2T0MrQUpndlNUanpqcXpGeTYyS1RoWldiUFNaRkg2Tk1CWHFuNXp5Z1U4N2dkL1ZXclRrRVRRT2NwUmZnWEdtQUlPMzVPSWU0ZTg4YmpsM2ZuUS9reGN2Tm5wZHVyZUtIM2R5YWVlRkxyZlciLCJtYWMiOiI2ZjY2ZGY0YmU4OGM0YmI0NDhiZGIwMmE4MWQwNWJhMGQ0NzMzZGVlNzdmMzQzODZjNDI5YmUzNTY5MDMzMjA3IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:22:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZlVEJtQVdOZVlTSEcyVFh2WjJ0QUE9PSIsInZhbHVlIjoiVG9keTdzUXM0dkZka2laWU1FSmRFT3ZxZmFlWWk1Vm9FOXQ2VkZuSDJEdStmR2FpeDZod0J0bkNmelJuakR3dHN3aW0yNzZyV3NsZ0FiRjc0N2VlQjRNTlFMZXlvQ2ozLzZ4Tmk1R0FpZzBGbm90QlZyemNLUWFOYkpHSkgvYWM5V2RHOEpWTmIycFhZZkRORTN4Y0pzdjdFR1FoZ1NrdU1TUUhKR1JiQlZyVFlZVWx4VkE4QW12QnNNMkNSbmhjVkVYRUE4OUgyTXZKdllrTzR0U0xpNDVJR3RQdEVJK0lScWU5WUpkdEJiVkhEeWdlaXdxd3pzSzhVVXJCVkFwOExCTmcxbFB3WEtMV0RVaWVhUXNQZnh0TzJRaFZ0UmdQcjRkSVN3Zkp1cHpTWWkzbWM2VnJWV00xRTRKQ0JNY2s2dXVuK2ovYWNlZldiTDh5M3lCMFhmVURzMHlycEljeFAvRENQT2ZBZTMxbmI2b1BXMjVUWUQxUlhHb0RoQnh4aXpwVVhFS2dDandNcU1FaDBzdGxVb1FpalZIbUdCRHRKVDJvTzJ3eGx5eTN1MU4xM3BzbjdjMFlCaWlPVXByUmt6UXN6RzYvVkV4Z054R1ZoZXRjNjFmR1M0L0ovRnd5VWpvQ3RqVWNhNmtHM3VZNm9RUVJ2dGdKNWI3bHQ3S3oiLCJtYWMiOiIwYzcxYTc3Y2JiNzJlZTE4NjBmN2RkYWNmY2Y3ZDJmMjgyZjc0YTlhN2FkMjllZjY4NjQ3NmMwMjk4MTUxZjBiIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:22:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklzWlhjSjhMSmNhdjEzM1lRNk9xWWc9PSIsInZhbHVlIjoibHNHa1R6dUYxSThERlJzK0FDdjZWZzBVRmdQNndlL0RHM2gyZFFMcXBNMXBQMlB6UjNNcWpXSnF6QW1wRGpGSGJ5dldHaDZHeWxwVytuU1hkWldpQ0J0KzV4UjdBd0ovT2xBUnpvcnNDZ0VUU3Jjelh2bC8xSU5lMTNLMDdUdC9FNEwwc1AxQ21FMHVyWkFCNDNyZTBwT2xCcHZEWndEai9FTjUydGRIQXBFbHpSd2MvT2tTdXE2cHlHcjhvZFFXRWV4cG5PQ05XS0pZaGMvdXU3bmNBUEFPLzN0c29xdHkwRzdCNElwUTJWRTVMcWViWDU0SzQ1MHk4cEVnUzVTV2ZXS0IvRUlzTzVsVzBGa1Z3SXcrKzlmTnNqamRtU1lPcHIzV00yb2JSNldjVUNMM1NXNE1CRjVscHp1d0RFeEhBVzBxS0NRSmJjUjQrRDRoMnlnb0F0RXpJRXNsZEIwRlFGY1c3Y2FPanl1VmkxeUNNOUE1WmF4Z0JoK1UvV005aXJHZGp6UVptRGJWcGhaL2YxdXQ2T0MrQUpndlNUanpqcXpGeTYyS1RoWldiUFNaRkg2Tk1CWHFuNXp5Z1U4N2dkL1ZXclRrRVRRT2NwUmZnWEdtQUlPMzVPSWU0ZTg4YmpsM2ZuUS9reGN2Tm5wZHVyZUtIM2R5YWVlRkxyZlciLCJtYWMiOiI2ZjY2ZGY0YmU4OGM0YmI0NDhiZGIwMmE4MWQwNWJhMGQ0NzMzZGVlNzdmMzQzODZjNDI5YmUzNTY5MDMzMjA3IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:22:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1996488500\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2090201900 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2090201900\", {\"maxDepth\":0})</script>\n"}}