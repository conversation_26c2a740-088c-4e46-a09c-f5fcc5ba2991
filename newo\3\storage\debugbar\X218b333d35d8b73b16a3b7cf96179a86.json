{"__meta": {"id": "X218b333d35d8b73b16a3b7cf96179a86", "datetime": "2025-06-16 15:23:24", "utime": 1750087404.27565, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087399.989675, "end": 1750087404.275699, "duration": 4.286023855209351, "duration_str": "4.29s", "measures": [{"label": "Booting", "start": 1750087399.989675, "relative_start": 0, "end": 1750087402.997462, "relative_end": 1750087402.997462, "duration": 3.007786989212036, "duration_str": "3.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750087402.997484, "relative_start": 3.0078089237213135, "end": 1750087404.275704, "relative_end": 5.0067901611328125e-06, "duration": 1.2782199382781982, "duration_str": "1.28s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45169568, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.026840000000000003, "accumulated_duration_str": "26.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.367903, "duration": 0.01651, "duration_str": "16.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 61.513}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.797611, "duration": 0.00822, "duration_str": "8.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 61.513, "width_percent": 30.626}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750087404.148356, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.139, "width_percent": 7.861}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order/1/edit\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-63677640 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-63677640\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1225051196 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1225051196\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1737246296 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1737246296\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-185862185 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087382715%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBRWEI2Y1ZReWxVQmdOK2IxdUh5OHc9PSIsInZhbHVlIjoidGs3V0drOG03ODBzcnkxL3JRWXBkQ3pJcUNIWXVhWGZJZWFmazdRLzdIUEhFL043ekpENXRaVVRFVkN5WnNOMG1kdDgyRjZnRHc5VS9SNVBVRWg4OEFabGNNYlhXWXVydkkyY3RuSm16Z3R0ZWJPaEMzWGNiM2xYcFNXaDBWY2RGWFE1VzB0ejNIeGRCalMrRUE3RkVzUGVFK2pHL1EvMFg2SFZoUk1IRzdHVnhwWGV5R0U5MlpGMHo2VVFTTWJXRXpzQzZVSERGWjl2ODNhYVpqY2xDMkpMS2ovWmJGcGRrc2VGdWxRQzYzcDVwck5NR1UyTDVjYlpDcHBrdWRscndJUTdqaWRmaVdyV3NaY0xOaldtTFB1c2pOejR5b0V0Mnhkanl2empNalNIRGNXUzBKTTBZZzZHcnQ1RnQzVUc1b0k5aWw2anRxTEFVMndkZjhnb1VFTkNJQjhJQ215UWlvS3M3Y1pKN1dUam5JK0p3UEltTjB2Y1FHMEVQTDZ4WGxEYlFHSE42dHVmaUpSbTQ5SS9uR2dtaEpTZ3dpTS92SWJ0dXl1TkpPQUw3R1hxeGZ6RXFyRnhuUGVoazdqeFp5NHh1eGZBZkgvR00vSUZFV0NVOUxBRTkreFJlRkQ0dkN6TWV2VTlXdGRxV3h1SmE3UzNHTlpDd1p2Z0lHVlYiLCJtYWMiOiJmZjczY2ZhNjM1MjBiNzkyYmY1OGViMDliMjc3ZTMwMGI3ZjE0NzVkZTY5ODZjZDlkNjE0OWYwZmE2YzhkYTQ3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkUyUHVieWFEZ1F0TXdHTHBGRGxxd2c9PSIsInZhbHVlIjoicFpXMW1QQmhTTHNNeVlac2U3UDdWVWlySUNQNGRBY0xZRVl3UXdEZWZVamE4M3hSc0xXbXhnd3VQVW80VWFDbXFvTDlMNlN4RlR6dytmSVRjVlFqN01Ia1JoTnpDRHR5eTNWaVE1UTR2Z3RrT1FLRUI1d0lFVE01SVhSak5JNHFmeDNxWG5DTFArS3NXR0V3QlJ2SGJFZFhveTBCMjRpZlRMaHNRblJQSXUxTVg0dDM5RlM0NmdxWm1ZMk9KaCsrbUV3VmkxZUxSM3Q4K09iRVo0T3g4QTE4ZFU3Zld2ZmVZOWF5aU1ybFBIVUJaeU9yMHJIREhleG9SWXFqOVA3aklGRDNlSEFaRytWTEp3MG1xMDdJb3Rvd2FVRXVKcC8wWHBVSXRMRzJHMkxGYmExczJVTjV1Wld4TGY0UC9XVFloNUxpZk9oVkFleEFkc3Awa3BRamdjMkJXbVg4M2ZZRXgzVStWbEdtSTVmVVFVMFBFbUlZM0UrSU5QTnhoNGFhdkFFcHZpVDVOY05TSmpzdEJyTTZzOFA5VEJ6SkUxa0JKc3FTTjlVc0RaWkVnMTVkNXhyQThWS3lwVDlBc1UxUDdMT01aWVR5UHN3QnEwSDFZUVpDRHpleW84UjJoM1VOQ0gxaGNtQlI1ZG1uYzc3U1JSMGpuU1pWaUlqaFM5TFgiLCJtYWMiOiJmYWRiN2Y5ZTc4ZmE5MGY0NGMwMDY2ZDMxZDdhMDg5ZDUwOWE0MzliMjc0YmZmMWU3ZmMyNzVjNmMyNjRjNTAzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-185862185\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1883966125 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzcvmOKIgVMCCvEHD9HLB5trqF9yJ9GcIs8Pndo9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1883966125\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-943443910 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:23:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkF0Z05YQm1yQmkxMTZGZVBkZ01ja1E9PSIsInZhbHVlIjoiaDdSVERZYXVaNDVxRWNtK1N3RHZPaDRtcXBvQXdXVEFBZEhYdS83ZEpMY1I5WXc1RXh1anI4Y2JLWkJtTkhjWCtEclRMSEk5VVQyamJCL2xIWm9BWDdFOGkrbVVMOGFxeGlxeDlJa0NlZFNrVk1HMithelJyK2YwNDlQN3dNajB5cjg3dVd3NElwL0FiL1NoOXZFQTdoUmd3M3NUM1NNazJBYy95K2xjUlJCbUsyQTZzc3MyTzlFcERGcVA0UThYWDZycUlkZnNBTDl0N2RVOVhjWFdrWVpPdVJoOXZwWERyM0VSejNTMzdqZWJnMFBxVWphd0krdjl1T2lZTFZNM3BtWVQ0TTVXYWcwR3NXbnZWYXJLM1BoRVJoNmRrWm5tOWdBdDFPdFM4VUVjVnJHYVlDTEUyOFczWTJqTTB0ZDRudTEwL2U0MHg3dlA4MG9ORG5MVm9hZG5LV1N5akJaSmVGcFNKdEY4K1R2bGd1M2EwMEdNRzNISG1jVXZ5bnZkVTlUQmQrSXBnMkFNVmZKR3Q2WVRUck92TzYrWE03QUtTOTZ6ZTZnNU82MmNlNXh2QWxuU0VxOGw4YnAxcEcrWFZXQUZZZkRTZWZKS0RIYTRsNE1pV3hQQWRFMGl5b21kUmFLcVFkR1YvQS80d2NRUWhxYnF3bnlybGczODM0Y0MiLCJtYWMiOiJjZTIyNWNjMjAyMDA1ZDc2YWE3ODk1ZDVmZWIxN2Q1MTI1MjE4YmYxMWM0ZGQzNzlmZTU3NDQ3ZTM4MzU3MTlhIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImVvbjliUi9INXBPOFVRVU9qWmpFV2c9PSIsInZhbHVlIjoicHNyU0xCS2xNMm1uV2M3ckViQXdPd21FRUoyVmpUTnRROCtmQ3Z1YTRUaWdaZUpCOHJ1YmxraWFaZjB6cGtUMVpERGt0WXpSSnFkNmhLWXRxNWlhQllmOURBU2M0UHAyOEZTWU1YSHRHSUJDMVozSWJtdkYzMlFMN1ZFakpZMFJYM2t1SmZKVmxQTUtnYm5zSjkwZEc5K282N0ZaMGZNNFVwVi9HWXdobEU2UVg5empBWnBJUkcxZXB0NVNWSmh2eHJKdllrRjcxTmtJVkgrUzlnT1p0eGNadExMY2xjZHNsem8veElaWERweDhnYkJ3YUZ4dmdsbnVoQy9QdU9xbVBIcmdROUFCN1JUTkExa2YrVU83Mkl1RG1ITUdrWEZZOUpoRmZyNWJ1WlBzRUxGQzlqVFQ0dVM1N2pLYVJ6VzJOd2VHTitBN3dwVWhueVpWa2tSVkZzb09kT21uekN1L1NUSkZ6NDM5ZnZkdFFkalcrS040TmdHc0NVZTFSajNjSXRuNmFNWHk5TWFKR3Y4TlltRzBDOWcrbHJwelozMEM2eWlGMmdtV0QvQXYvaUFwUFNpTDBJMnFNblVBeFk0ZWlNMDQwTVMxTXgrT3dxelRLZzhRSFFqdkZQeFZtNXFLVzlrblhyYnRXcm1BclV6TXpML2IyMEovZlVCSXprekIiLCJtYWMiOiI3MGE0ZDVlOGJkMDNjODVhYjIxYmJhOWYzMDg4M2I1ODJlZDhmZGU3NmY2YzI5NmFmZGVjZmMyNWNiNzJmY2U1IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkF0Z05YQm1yQmkxMTZGZVBkZ01ja1E9PSIsInZhbHVlIjoiaDdSVERZYXVaNDVxRWNtK1N3RHZPaDRtcXBvQXdXVEFBZEhYdS83ZEpMY1I5WXc1RXh1anI4Y2JLWkJtTkhjWCtEclRMSEk5VVQyamJCL2xIWm9BWDdFOGkrbVVMOGFxeGlxeDlJa0NlZFNrVk1HMithelJyK2YwNDlQN3dNajB5cjg3dVd3NElwL0FiL1NoOXZFQTdoUmd3M3NUM1NNazJBYy95K2xjUlJCbUsyQTZzc3MyTzlFcERGcVA0UThYWDZycUlkZnNBTDl0N2RVOVhjWFdrWVpPdVJoOXZwWERyM0VSejNTMzdqZWJnMFBxVWphd0krdjl1T2lZTFZNM3BtWVQ0TTVXYWcwR3NXbnZWYXJLM1BoRVJoNmRrWm5tOWdBdDFPdFM4VUVjVnJHYVlDTEUyOFczWTJqTTB0ZDRudTEwL2U0MHg3dlA4MG9ORG5MVm9hZG5LV1N5akJaSmVGcFNKdEY4K1R2bGd1M2EwMEdNRzNISG1jVXZ5bnZkVTlUQmQrSXBnMkFNVmZKR3Q2WVRUck92TzYrWE03QUtTOTZ6ZTZnNU82MmNlNXh2QWxuU0VxOGw4YnAxcEcrWFZXQUZZZkRTZWZKS0RIYTRsNE1pV3hQQWRFMGl5b21kUmFLcVFkR1YvQS80d2NRUWhxYnF3bnlybGczODM0Y0MiLCJtYWMiOiJjZTIyNWNjMjAyMDA1ZDc2YWE3ODk1ZDVmZWIxN2Q1MTI1MjE4YmYxMWM0ZGQzNzlmZTU3NDQ3ZTM4MzU3MTlhIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImVvbjliUi9INXBPOFVRVU9qWmpFV2c9PSIsInZhbHVlIjoicHNyU0xCS2xNMm1uV2M3ckViQXdPd21FRUoyVmpUTnRROCtmQ3Z1YTRUaWdaZUpCOHJ1YmxraWFaZjB6cGtUMVpERGt0WXpSSnFkNmhLWXRxNWlhQllmOURBU2M0UHAyOEZTWU1YSHRHSUJDMVozSWJtdkYzMlFMN1ZFakpZMFJYM2t1SmZKVmxQTUtnYm5zSjkwZEc5K282N0ZaMGZNNFVwVi9HWXdobEU2UVg5empBWnBJUkcxZXB0NVNWSmh2eHJKdllrRjcxTmtJVkgrUzlnT1p0eGNadExMY2xjZHNsem8veElaWERweDhnYkJ3YUZ4dmdsbnVoQy9QdU9xbVBIcmdROUFCN1JUTkExa2YrVU83Mkl1RG1ITUdrWEZZOUpoRmZyNWJ1WlBzRUxGQzlqVFQ0dVM1N2pLYVJ6VzJOd2VHTitBN3dwVWhueVpWa2tSVkZzb09kT21uekN1L1NUSkZ6NDM5ZnZkdFFkalcrS040TmdHc0NVZTFSajNjSXRuNmFNWHk5TWFKR3Y4TlltRzBDOWcrbHJwelozMEM2eWlGMmdtV0QvQXYvaUFwUFNpTDBJMnFNblVBeFk0ZWlNMDQwTVMxTXgrT3dxelRLZzhRSFFqdkZQeFZtNXFLVzlrblhyYnRXcm1BclV6TXpML2IyMEovZlVCSXprekIiLCJtYWMiOiI3MGE0ZDVlOGJkMDNjODVhYjIxYmJhOWYzMDg4M2I1ODJlZDhmZGU3NmY2YzI5NmFmZGVjZmMyNWNiNzJmY2U1IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-943443910\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1873926044 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1873926044\", {\"maxDepth\":0})</script>\n"}}