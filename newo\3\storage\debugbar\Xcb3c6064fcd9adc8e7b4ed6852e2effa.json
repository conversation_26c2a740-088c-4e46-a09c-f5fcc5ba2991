{"__meta": {"id": "Xcb3c6064fcd9adc8e7b4ed6852e2effa", "datetime": "2025-06-17 06:55:05", "utime": **********.917839, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143304.301203, "end": **********.917881, "duration": 1.61667799949646, "duration_str": "1.62s", "measures": [{"label": "Booting", "start": 1750143304.301203, "relative_start": 0, "end": **********.687743, "relative_end": **********.687743, "duration": 1.3865399360656738, "duration_str": "1.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.687762, "relative_start": 1.386559009552002, "end": **********.917886, "relative_end": 5.0067901611328125e-06, "duration": 0.23012399673461914, "duration_str": "230ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45170824, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.035809999999999995, "accumulated_duration_str": "35.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.794307, "duration": 0.03252, "duration_str": "32.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.813}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8673131, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.813, "width_percent": 4.273}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.889609, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.085, "width_percent": 4.915}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-6331130 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-6331130\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-881629978 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-881629978\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1508322098 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1508322098\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1007279476 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=1rkbmmi%7C1750143299616%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlducVBzb2VDMGpnd0pCVU1TZ1kxeUE9PSIsInZhbHVlIjoickhlOVNHbGdDdjlOV1hpZDkwUHZyNW9yN2Q3NUd0SkVid1JtYTE2Mjc5d3NJSVF4TE5OYkR0RTd5YXZ3SEVrUjQxSS9TbXF2ak5zamhRQ1Fjc0RyNVQwdVBxNUpjdDROeE1YUlY5V0M4bmhLWWtPT0VOeXVIaXBjU0ovUlVWUHZrQzl0MVErTitWY0wxdHZVU2FiODBMMkMzeklKVnppMkZRY25JVk8wa1llaGhXWEwvZ1RWZk1vVXpVYkN1dlBhUVRLNlBPZUt5UE5JZlQ2dGJtdlVQS1hlTU1uSUJWZDY1eTliTm9OOGdUM243blRMdmp6Qm5wc3g4SUxDZG8vbXBzWDF3NHlLaDB4b1FySll2UUxtRlIzVGVxQ1dJc3UxRDNyRGZtR29MRHdwWmVGRmhERTg1NW04Q3lCSDZZdU1sQXlwRWwvRWM1dm9TNHc4OE5zVWlNM1dWbEgzTWxRdS9PdFhvMmd3MVlkMjF5dnZMUi9BM0JKRDdDSXZVTTBRbmQ4YW9NUVVvd2kycEJZdys1NlJaU0k4a3JQSXVTbTBkT3l4bGVBTWxIM01ET2J4RmN4OU9OdmNjdkJ0cVRML05GRkI3TGJHQmk1bmZDVWkvTnREVkR4NG5qOFVRR1NLdzZMcE1SSkl2a2RZMjlLeWtZVUFTQ2pZcVlMUjlIOU4iLCJtYWMiOiI1OTI5MDczMmZkNTJlMmVkNjMwZDI0ZDAxNjY0YTc4OTVmYWE0MTYxYWEzYTFkNTA1YWRhZWI0MDVlMWNkOTY4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkhpbjRTaS9oTXJSQjJxWDBSNHhodGc9PSIsInZhbHVlIjoiQW5oQU9pOUZIVFdCSXF1WW5GSktHZXpEWXIzVnJZc3NEMnFTRzIxYk5CY29heGc2MnBTYURiZnRlVGRrUld2NWd1VmhFakloYXhDOFRzOStiS1VobDI5ZHlpWFU5TXpEOUNwZDJUSzRKajFVV25NOEcxdnJXR0xmK2dKaHFENmppTTJLaFdEdkNnS2x0V0RYYjQzVUl2NUV0c3lUMzRhL2FRRG5WVGtIMkcxancyWnYvVm5WUnAxUzlOdU1LbEpaOWZHTS9HcVFGN3ZaYTJ1cjBueTM3ZXY2WjNxcEx2bEROczRjWVhzemdaZUxmbFZkalBpUm85MnFzUFJlb3dGSzNiVVYxcVZ3c1FzZ1hVOTVFS0MwdXoyTEdQV0hGbnFkQThkYlYwYlN6MTR5MmxQYzhqRGJXMkFUQjAxKzR5d0xpL05TSTNVa1F2aTVKckExbHc4TzV1RnR3eEJwQjZacDIzSitvamlWRU9YVE02cGJXTGdkbDdDOEZ4YlBSMkkyb05UNEtvTDNTZmYyYXVzWFlLaHpFd2R1NURqSDM2UDRkWDlvNnRqT0Rhai9IV3M5cFZnNnZuTmFpclAxVUhOa1AyY2UrTlN1SjhlYW5qS0p4YTlyWEZVaU5KYUg5dDI5eXBwaHIyRGZTcDh0Wm5RbUo0OFRMeDlHSHE5NUtxVlEiLCJtYWMiOiJmZjA3MDI1MDBjYzRjMDk0YmQ3NDAzNjU5NjgyNzQxMTVmNDdlNzM4NTgyNDU0ZDdlZDUzYzkwYzYwMGZjMDkxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1007279476\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-910633679 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDWN9h6R9gAIdzZaXBzXfq0WGpauVfrUj70kKWA2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-910633679\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-735625666 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:55:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ims1WHJyNXBZbG1DZXVGQlVYbjZVR2c9PSIsInZhbHVlIjoiWGFHM0hlaHY2WHJJUWd6TkhIQUw0bzU4VEo2elY2amllVFFTa2tOZzk2U0NmM1lzRjZDN3BRenVFM0ZvUVhFdnhLL2F3TElEaVdmNlQ4LzZXSFlnUVRJM2VBVHhtc0JFR0dRR25NMEl6amVENko4clVUTzVobkhOQUh0Qm1ublVkRWZVQXRJaXFraFVjaE40VEtTN0d1djBnRFNRNFpXeGZGbENLbW1ONmdTVkFZSEF3YTBHZnhTUGNJUXhIL3cxaExuK253SzlBdlAzN2Vsb1ZRQzUwSFBnRVFYSkUralNEMWRYVmZMUUVjUEFYV3FhUEdrbzZHcEFMMXczSGhRNXNNWjdvd3E5UlhzTEEzaU43NzJ3ZHdnOVBzekNURnFDSEtUVlJOUjRRdEw4c0szT2V3M0pkU2huUkhsbktjR0tPZmV1TDFnMit2a0s1emRmTWxhMXBsMEtIMkVVL21Qd3BDVFJuTk1FSlJmTW91YnV3cEJ0K3lWemhLSDJxTFhiSitzMzZaTThZMGJmZUFWcUtENHYrR09DNlpIUFNqVi80QlNVc0preWZnaUZOenBUUENtRXltdFRyZ2FJeE1uT1pkKy8rZFpLc0RMZ0tHaU5ISkNUQjlId0p5UG1Nbms0aEJsSVV6RHpTVEZHMFo0YnNUL0ZEdEkyeGo0QVFXaFciLCJtYWMiOiI5N2E3ZTFmOTNjOWY4ZWFiNzMyYTJhMGY0Y2Q2NTk0YWM0Zjk5ZTE1OGJhYWQ0OGE3NjYyNjA5MjQ2NjQ2ZTU1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:55:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ino2SDBGWnBOWHhndEprSjNnLzNHTmc9PSIsInZhbHVlIjoiQ2ZRa3daTnJRRUg2WXFDS3JMUW1TV0FjeCtDWGNIMHdpWlREOEh5Wk5vWUlPR2RaL0JwYTNJam45czdNTktPZHFZcTlLVFRXLzRlalpUNFB1UXE4QTAzZjlUYXY0ZDJadzJjWm1UYk1GNkNsdS8vOStSZDVlQXBmWXZGdktNSWxPQ2pHa294Umk2MFd3NVFManhVbzdRVEZTNklLcUNzSG4vV2pVWEVuaHd0NlRjMHU5VE5lcHNlY3Q5TDRWaVZZVXlBVnBoTXFXSVcrNUZGSFpJUUpMNmxQaTV6Zjdrd3BVREh0UVpEd2Y3dVRVVlEySTdCWTRrOU41KzY3b0doUHNGcmlESDBrUVZLV1YxSEpwN2p4eWRWcUFySnBZUEcvSEhGcTlaOW1icmt5VVhVU3ZselhrTnhlOFhvK3hVcnRtL2ZHcXBCdi9TVnhvOGVpY3ByRFNqRWZjZkpjSzE0VHVLcExycE83N0FDVWlWdjMyOFAyOEk2ZGR6RExIQVpMSGthd2JiNFhvdGpCTUFnb3pxZDdTWFhGVy9UOStBR1BjbGwwQVJLNVBOai9RQTJRbjZ2bnFGS0R0WFhtc3dyN3o0NnF6R3dqc1VpZmw5U0RYUjR4UjVpMUdFM3FxcGFtQzU2YXBhMVFZYlJiWUplaU1teUFqdm1iRkc0Nko5eUQiLCJtYWMiOiJkZjViNjYxOTYyMzNjNTk2YjFjNGE2NjU0NjUyNjExNWNhYjA3MGU2NmI4ZjFkYjVkZmNjMTc0MDIyMDVlMGMwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:55:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ims1WHJyNXBZbG1DZXVGQlVYbjZVR2c9PSIsInZhbHVlIjoiWGFHM0hlaHY2WHJJUWd6TkhIQUw0bzU4VEo2elY2amllVFFTa2tOZzk2U0NmM1lzRjZDN3BRenVFM0ZvUVhFdnhLL2F3TElEaVdmNlQ4LzZXSFlnUVRJM2VBVHhtc0JFR0dRR25NMEl6amVENko4clVUTzVobkhOQUh0Qm1ublVkRWZVQXRJaXFraFVjaE40VEtTN0d1djBnRFNRNFpXeGZGbENLbW1ONmdTVkFZSEF3YTBHZnhTUGNJUXhIL3cxaExuK253SzlBdlAzN2Vsb1ZRQzUwSFBnRVFYSkUralNEMWRYVmZMUUVjUEFYV3FhUEdrbzZHcEFMMXczSGhRNXNNWjdvd3E5UlhzTEEzaU43NzJ3ZHdnOVBzekNURnFDSEtUVlJOUjRRdEw4c0szT2V3M0pkU2huUkhsbktjR0tPZmV1TDFnMit2a0s1emRmTWxhMXBsMEtIMkVVL21Qd3BDVFJuTk1FSlJmTW91YnV3cEJ0K3lWemhLSDJxTFhiSitzMzZaTThZMGJmZUFWcUtENHYrR09DNlpIUFNqVi80QlNVc0preWZnaUZOenBUUENtRXltdFRyZ2FJeE1uT1pkKy8rZFpLc0RMZ0tHaU5ISkNUQjlId0p5UG1Nbms0aEJsSVV6RHpTVEZHMFo0YnNUL0ZEdEkyeGo0QVFXaFciLCJtYWMiOiI5N2E3ZTFmOTNjOWY4ZWFiNzMyYTJhMGY0Y2Q2NTk0YWM0Zjk5ZTE1OGJhYWQ0OGE3NjYyNjA5MjQ2NjQ2ZTU1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:55:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ino2SDBGWnBOWHhndEprSjNnLzNHTmc9PSIsInZhbHVlIjoiQ2ZRa3daTnJRRUg2WXFDS3JMUW1TV0FjeCtDWGNIMHdpWlREOEh5Wk5vWUlPR2RaL0JwYTNJam45czdNTktPZHFZcTlLVFRXLzRlalpUNFB1UXE4QTAzZjlUYXY0ZDJadzJjWm1UYk1GNkNsdS8vOStSZDVlQXBmWXZGdktNSWxPQ2pHa294Umk2MFd3NVFManhVbzdRVEZTNklLcUNzSG4vV2pVWEVuaHd0NlRjMHU5VE5lcHNlY3Q5TDRWaVZZVXlBVnBoTXFXSVcrNUZGSFpJUUpMNmxQaTV6Zjdrd3BVREh0UVpEd2Y3dVRVVlEySTdCWTRrOU41KzY3b0doUHNGcmlESDBrUVZLV1YxSEpwN2p4eWRWcUFySnBZUEcvSEhGcTlaOW1icmt5VVhVU3ZselhrTnhlOFhvK3hVcnRtL2ZHcXBCdi9TVnhvOGVpY3ByRFNqRWZjZkpjSzE0VHVLcExycE83N0FDVWlWdjMyOFAyOEk2ZGR6RExIQVpMSGthd2JiNFhvdGpCTUFnb3pxZDdTWFhGVy9UOStBR1BjbGwwQVJLNVBOai9RQTJRbjZ2bnFGS0R0WFhtc3dyN3o0NnF6R3dqc1VpZmw5U0RYUjR4UjVpMUdFM3FxcGFtQzU2YXBhMVFZYlJiWUplaU1teUFqdm1iRkc0Nko5eUQiLCJtYWMiOiJkZjViNjYxOTYyMzNjNTk2YjFjNGE2NjU0NjUyNjExNWNhYjA3MGU2NmI4ZjFkYjVkZmNjMTc0MDIyMDVlMGMwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:55:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-735625666\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-456616534 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-456616534\", {\"maxDepth\":0})</script>\n"}}