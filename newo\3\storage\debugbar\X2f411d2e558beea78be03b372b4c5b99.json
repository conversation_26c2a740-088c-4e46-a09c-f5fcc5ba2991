{"__meta": {"id": "X2f411d2e558beea78be03b372b4c5b99", "datetime": "2025-06-16 15:23:37", "utime": **********.571084, "method": "POST", "uri": "/inventory-management/update-quantity", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087415.688441, "end": **********.571129, "duration": 1.882688045501709, "duration_str": "1.88s", "measures": [{"label": "Booting", "start": 1750087415.688441, "relative_start": 0, "end": **********.137812, "relative_end": **********.137812, "duration": 1.4493708610534668, "duration_str": "1.45s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.13786, "relative_start": 1.4494190216064453, "end": **********.571147, "relative_end": 1.7881393432617188e-05, "duration": 0.4332869052886963, "duration_str": "433ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52022392, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST inventory-management/update-quantity", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\InventoryManagementController@updateQuantity", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "inventory.management.update.quantity", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=109\" onclick=\"\">app/Http/Controllers/InventoryManagementController.php:109-198</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.03725, "accumulated_duration_str": "37.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.275426, "duration": 0.0246, "duration_str": "24.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 66.04}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.332688, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 66.04, "width_percent": 4.188}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.358451, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 70.228, "width_percent": 4.268}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.412859, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 74.497, "width_percent": 6.55}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 124}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.476672, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:124", "source": "app/Http/Controllers/InventoryManagementController.php:124", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=124", "ajax": false, "filename": "InventoryManagementController.php", "line": "124"}, "connection": "ty", "start_percent": 81.047, "width_percent": 0}, {"sql": "select count(*) as aggregate from `warehouse_products` where `id` = '4'", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.4774141, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 81.047, "width_percent": 3.705}, {"sql": "select * from `warehouse_products` where `warehouse_products`.`id` = '4' limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 132}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.491061, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:132", "source": "app/Http/Controllers/InventoryManagementController.php:132", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=132", "ajax": false, "filename": "InventoryManagementController.php", "line": "132"}, "connection": "ty", "start_percent": 84.752, "width_percent": 3.383}, {"sql": "update `warehouse_products` set `quantity` = '20', `warehouse_products`.`updated_at` = '2025-06-16 15:23:37' where `id` = 4", "type": "query", "params": [], "bindings": ["20", "2025-06-16 15:23:37", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 136}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5027368, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:136", "source": "app/Http/Controllers/InventoryManagementController.php:136", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=136", "ajax": false, "filename": "InventoryManagementController.php", "line": "136"}, "connection": "ty", "start_percent": 88.134, "width_percent": 4.403}, {"sql": "insert into `stock_reports` (`product_id`, `quantity`, `type`, `type_id`, `description`, `created_by`, `updated_at`, `created_at`) values (5, 10, 'manual_inventory_increase', 4, 'زيادة يدوية في المخزون من 10 إلى 20 - المستودع: 8', 15, '2025-06-16 15:23:37', '2025-06-16 15:23:37')", "type": "query", "params": [], "bindings": ["5", "10", "manual_inventory_increase", "4", "زيادة يدوية في المخزون من 10 إلى 20 - المستودع: 8", "15", "2025-06-16 15:23:37", "2025-06-16 15:23:37"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4038}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 145}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.517213, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4038", "source": "app/Models/Utility.php:4038", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4038", "ajax": false, "filename": "Utility.php", "line": "4038"}, "connection": "ty", "start_percent": 92.537, "width_percent": 7.463}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 182}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.550584, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:182", "source": "app/Http/Controllers/InventoryManagementController.php:182", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=182", "ajax": false, "filename": "InventoryManagementController.php", "line": "182"}, "connection": "ty", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage inventory, result => null, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1035058857 data-indent-pad=\"  \"><span class=sf-dump-note>manage inventory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage inventory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1035058857\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.430108, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1159575675 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1159575675\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.432779, "xdebug_link": null}]}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order/1\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/inventory-management/update-quantity", "status_code": "<pre class=sf-dump id=sf-dump-2078967070 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2078967070\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-914092714 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-914092714\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1533578935 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n  \"<span class=sf-dump-key>warehouse_product_id</span>\" => \"<span class=sf-dump-str>4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1533578935\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2130622579 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">82</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087411438%7C10%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkQrU0N6YUlQdGFObmNzMnM4UysweWc9PSIsInZhbHVlIjoiYTF3a2JIZW5sTmlVajJKbXVOTUVwOE1XZjV6UUhLTXlKTW1ueFRyU3JzNG11T25QSFNpSUdtTGR3citKK0VQRzgyNkNEQjRnb0xtdm05MVFrbWYwR09oV21JQVI1SUdNMFFvSTVCdGRaSTVac3BocUxPY2dIUE93N0VwRG9GSGJuR2Y5WXdROGliRmJZMEtORklidWxOWkxPQ2NvZ0ptTkhMQWdaWklsTGdtb2pjYjJqc2pCMzFIRVFqSGY2UEdzVVlDR092VHMrZkc5MVlmWEJGTko1U1ZTMXc0WCtDVXJ4elNaMXJLS3FSN2FXQlZCOWdGdUJoS2YwVVVOMnNXRGxBV1FjYmxCOVlqQjIwN1lEWmttZmpMYTdsZkFoVkRLZlpNZHdTcTBHdU1yZk8rWkt0YUN2T2libUNNUm96QnlXYThFN2lRNW9lUk5FbGM2d0kwaEZpd0pNOFJyV3NmdmNJTU5Qb2pHazJXeE05Yk5OVWdoUEVXWWg3ODZjYWZPclN5MTR4d3RTMGhUMEE0ZHJ6UFJ3d0hGY1F5WGVRZ1kxWkp6MGZNbUxsUUYveWFGMll2Nk0ya3Z5eE44NklVNExITHROSHBkcDJSWnhjV0FPNVBtUnA4eGtaWG5iWnhwYlY5RUtNQ2R2ZTJWTE12NFdTTHg5NVVObU5IbkRJN2MiLCJtYWMiOiIxYWY5NTVhODcyZDJhYjNmM2UyYWM1NjkxYWJmNWUwNWRmYzVkNDRjNGI3YWUyNWQ2MGZkNGI3NWMwNjM4ODViIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImEwT2k5VmxBRVlZMGxDNEFyaVVmYVE9PSIsInZhbHVlIjoiNWRITEpwVUVkN2QrV2VnWXVaSC9zRDZ5OUdYZGRNS0JZZ2IvM3dRQXZsZWxzUUNNT1RNQXB6T1dZMS9WOUk3d3hBbTQ3OXQ5dkNwYjlhbWx2ZTY5cW5SMVNpNjhZYnV2UDZGelFVY1BXVmpkbTJ5NVFVbnY2TEM1YlhaMXYwSFlKbi9VRTFySmV6MU5Xd3BvRDRDaGtFU1RMT2ZoQTk0ZERMQVQyTGUrTERBUEZHdklKdTlWclR2ZldabUdzTjNYWCt5L0tQeG1Yc0VPWWhReWIybW5MRUdDL3R3NUJibVFxUnRqK1ZpYkcwVlEvSG44OEtLQmU2Y0JheVJKY0gzNWpmWTcvTk9DbHJLL2NlRklDS1VwWm5uN1dJZnVkTDRJekV4TG1LcytTQThxcXFDZ0Qwa3ZrbERBN0lXeHNTdDI5Y2pueUhNS3IzTTJzdEJkS3NWNEQ2WlEvd3ZoS0xOYVVseGFJV1NJR3VxK3hkaU1raUxRbUdHZklwU0VDOVlQR3ExZUkxV2dTcEUwdDV5WjlnMFlTeFJ2RXdVUVkyamIzak4vanpRL0I1ejMyYTFBbTY4QjF2cGs3NmJPZDhnb0dqUEVtUzRUNFlZWUwrQ3pTZ2hVQnZrRU1xbC9WbnY0eTRaQ3dPTkZVWHd2MmVVZjFwOXFWZFFSQVZsZmVZajQiLCJtYWMiOiI4NTdmNTBlZjY1MzMxYjU4ZDgwZGZmMWEzODA4N2U3OTEyYzE5Njc2NzU1MTBiZTA1ZjNmNDA5NjU4M2FiMjkxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2130622579\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1933818611 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzcvmOKIgVMCCvEHD9HLB5trqF9yJ9GcIs8Pndo9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1933818611\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-728362579 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:23:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhvQTFWeFNxOVlZVjEyOGhmNU5xQlE9PSIsInZhbHVlIjoiblFQRG1hNmZkYTBCVjZFLzF6MnpZT3VGUVZlUUpQZWNwMEJYbHdEVWhYSG1sbElMY3VTaWVETmdYdlZ3OWFwZlkvWUlwV0JIZFNlYlIvWGQ1VXo2UmtZR1lTZGdMMEFFOFRwVTJZeW84a1RZTXBLcGhmT1d5bVhYTHNwNCtEbnlUUnNWcVRxTzB0Z0gybkZTcWllekR0dE1EN2lJUDNURnd3Ny9JUjhCK3ZzZXc2ZGdHSGVXWHBMaFF4ZlZUbmF2VGtsV29ieVVoODY3YjBhemw2Q1dSVHdNb1d5cnpBeFFoTElOUC9Sc1VJcFZ5NTc5YnYzNnQxOHBGaVR0SDJFTkx1cHVUU1FVYUl3R0tDMElJNGhYQUZTWVlVWGtrQnBESFpwc3BYNUJzVy85V3lLYXR1cjkyU0RjWTdPNGNTQ1gyV1JRakR0WlQrRUoveG5iM3h4c3RKYkEzanMyMWFJVUZPVTNCY3FSa3FrMDNQRHBoRUpOQVMrODArWFhRdW1OZ2IzQjRqMk1uQ3FJc2h1ejR0OFpKck52RHZoSVdBOUR0MncyazJ0WDR5UlE0c1hUTUpRK2d1Q3lnbEhtOG02TEJlSFlqY0NkdURKd2xWV25EYzk2SFFnNXovRkpBeER2Zm9UbmFTZ1VWdEVDcFY4Nk1lVW5UTVhmOW5JcXlyamkiLCJtYWMiOiJjZmM4YjcwZWU2NmU2MTRjNDJiNTI4NjBhOWFjOTI1ZmY3ZGVkNzE0YTljMWU0MzU5ZjUzOTMyYmFhYjg5NDQ2IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlROdU9BSUxkamZ0clpOZlRGYWcyd0E9PSIsInZhbHVlIjoiakNVNnl4WG1TOW9jRjduT0dpL1IvZStwWmsydHNoay9sNklad3B1MWZ1TDVuVUFBZ2Jsa2dBclZrVTlQdnNQWnpNNTg5ZzVXMk9oZjNkODREN3BUQU1FTzg3VityL3BoMVdUcVI4WUFEYVJEblF5NWl5cDkzN1ZMazBCTUZXcm1nRzVzekZtSER5ZWdvaCtGeDlTdEhyc3dEUGtqeFpHSFQrUU1YVXljVUJlWFFOWXRBQTVvY1BrUFRuY2l0bFdIbHlGRC9uVjdOVGlmSTVQL2JESm9UWERjYmkrcjBNck0wdlBlN0RlcWJ3QVMzdy9wSGJhL3R4V2c2d0pScGpyZFVUdFd1VGtWbHhvQm41cnJRbkhhakVmaDRnbGJOUzNsT3ByOEJRQ0FMTjN3TjVLeDFnRFNvdkhHVEdlYWxRNmN6eTN0MXRQUlhWaGVVNEQrTFFIRGdja3dGRjJUdmx2VmdZVHczZHMyOXFtSUcxVzQ0NEtTYW9BdnlGNWdaYUVLT0NxOERqYWx1V0ZObWlQT3FhL3dZQXJVZ0hjN3lCQURpa3RNSWhCV2JXVEJxdXhKKzlRd3FBWmo1SFM4Q3BzcGxNakJrcmZhRDYxNkZnSE1VNDJCd3NhbEV3OHlWTll5K0hTeUZCcFVXWEJYakZCZ0JqOXNCNTZ4ZnF4RXdxOTUiLCJtYWMiOiIzMDgyMzExYWYwMWMzZmU1OTU5YTA2NjY0YjQ5MDQwMDM0YzQyNGIzZTdiNWI1NzQ0MzM2YTVkNTMyODViZjVkIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:23:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhvQTFWeFNxOVlZVjEyOGhmNU5xQlE9PSIsInZhbHVlIjoiblFQRG1hNmZkYTBCVjZFLzF6MnpZT3VGUVZlUUpQZWNwMEJYbHdEVWhYSG1sbElMY3VTaWVETmdYdlZ3OWFwZlkvWUlwV0JIZFNlYlIvWGQ1VXo2UmtZR1lTZGdMMEFFOFRwVTJZeW84a1RZTXBLcGhmT1d5bVhYTHNwNCtEbnlUUnNWcVRxTzB0Z0gybkZTcWllekR0dE1EN2lJUDNURnd3Ny9JUjhCK3ZzZXc2ZGdHSGVXWHBMaFF4ZlZUbmF2VGtsV29ieVVoODY3YjBhemw2Q1dSVHdNb1d5cnpBeFFoTElOUC9Sc1VJcFZ5NTc5YnYzNnQxOHBGaVR0SDJFTkx1cHVUU1FVYUl3R0tDMElJNGhYQUZTWVlVWGtrQnBESFpwc3BYNUJzVy85V3lLYXR1cjkyU0RjWTdPNGNTQ1gyV1JRakR0WlQrRUoveG5iM3h4c3RKYkEzanMyMWFJVUZPVTNCY3FSa3FrMDNQRHBoRUpOQVMrODArWFhRdW1OZ2IzQjRqMk1uQ3FJc2h1ejR0OFpKck52RHZoSVdBOUR0MncyazJ0WDR5UlE0c1hUTUpRK2d1Q3lnbEhtOG02TEJlSFlqY0NkdURKd2xWV25EYzk2SFFnNXovRkpBeER2Zm9UbmFTZ1VWdEVDcFY4Nk1lVW5UTVhmOW5JcXlyamkiLCJtYWMiOiJjZmM4YjcwZWU2NmU2MTRjNDJiNTI4NjBhOWFjOTI1ZmY3ZGVkNzE0YTljMWU0MzU5ZjUzOTMyYmFhYjg5NDQ2IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlROdU9BSUxkamZ0clpOZlRGYWcyd0E9PSIsInZhbHVlIjoiakNVNnl4WG1TOW9jRjduT0dpL1IvZStwWmsydHNoay9sNklad3B1MWZ1TDVuVUFBZ2Jsa2dBclZrVTlQdnNQWnpNNTg5ZzVXMk9oZjNkODREN3BUQU1FTzg3VityL3BoMVdUcVI4WUFEYVJEblF5NWl5cDkzN1ZMazBCTUZXcm1nRzVzekZtSER5ZWdvaCtGeDlTdEhyc3dEUGtqeFpHSFQrUU1YVXljVUJlWFFOWXRBQTVvY1BrUFRuY2l0bFdIbHlGRC9uVjdOVGlmSTVQL2JESm9UWERjYmkrcjBNck0wdlBlN0RlcWJ3QVMzdy9wSGJhL3R4V2c2d0pScGpyZFVUdFd1VGtWbHhvQm41cnJRbkhhakVmaDRnbGJOUzNsT3ByOEJRQ0FMTjN3TjVLeDFnRFNvdkhHVEdlYWxRNmN6eTN0MXRQUlhWaGVVNEQrTFFIRGdja3dGRjJUdmx2VmdZVHczZHMyOXFtSUcxVzQ0NEtTYW9BdnlGNWdaYUVLT0NxOERqYWx1V0ZObWlQT3FhL3dZQXJVZ0hjN3lCQURpa3RNSWhCV2JXVEJxdXhKKzlRd3FBWmo1SFM4Q3BzcGxNakJrcmZhRDYxNkZnSE1VNDJCd3NhbEV3OHlWTll5K0hTeUZCcFVXWEJYakZCZ0JqOXNCNTZ4ZnF4RXdxOTUiLCJtYWMiOiIzMDgyMzExYWYwMWMzZmU1OTU5YTA2NjY0YjQ5MDQwMDM0YzQyNGIzZTdiNWI1NzQ0MzM2YTVkNTMyODViZjVkIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:23:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-728362579\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1846731886 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://localhost/receipt-order/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1846731886\", {\"maxDepth\":0})</script>\n"}}