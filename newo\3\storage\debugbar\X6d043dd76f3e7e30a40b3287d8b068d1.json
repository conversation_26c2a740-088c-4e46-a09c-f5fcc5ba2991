{"__meta": {"id": "X6d043dd76f3e7e30a40b3287d8b068d1", "datetime": "2025-06-17 07:12:53", "utime": **********.826269, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750144372.985032, "end": **********.82629, "duration": 0.8412578105926514, "duration_str": "841ms", "measures": [{"label": "Booting", "start": 1750144372.985032, "relative_start": 0, "end": **********.703848, "relative_end": **********.703848, "duration": 0.718815803527832, "duration_str": "719ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.70386, "relative_start": 0.7188279628753662, "end": **********.826292, "relative_end": 2.1457672119140625e-06, "duration": 0.12243199348449707, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45170128, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019969999999999998, "accumulated_duration_str": "19.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.766726, "duration": 0.01724, "duration_str": "17.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.329}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8024392, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.329, "width_percent": 4.857}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.814132, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.187, "width_percent": 8.813}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-151038090 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-151038090\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-25605754 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-25605754\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1793755506 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793755506\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-149166275 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1874 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; XSRF-TOKEN=eyJpdiI6IklxZiswcFlVQTFXRW5oSUNZRnBEUEE9PSIsInZhbHVlIjoiR0h5WGZJbVczM2ZCdnhtUGFCbXBVNzZsL0hDQnF0K3lLWk00UEdGMzQySkZnM3U4V2JQZU8wUThXMlVLclg0SmZ3TGR5WXBzSkVXVWVPQldZMllyYVhpOHZwdW11V2FMSGdhTWxXbGhxTG40SGZhRVNiMzZ5VzIreUd5dk9MSXZsZkQ4bnQzVFBCUk40MHAzUnRhUVdwcTMzcEwwajI4Nm45MDVoOGRCZEJqZ3l2c2p4UVVzMWV3MU9la29aZkNTd3hRcnlwM0d3RllJMTEwMlpuTkFxQ0ZhaTIxRDVYSlBNOHpLMmgyaklKcS9iYzdMRWhWU1NFSGhidjY5MDdHcmppS0NvMWhFalRRNVdiaEhxb2pYdlFLd3h0UmVuS2NROU9QMm9LM2ZadU5hMWdic1JLM3o3clN3QlhwVzJURFdTZzVFdjVvb054WmtHK1FqMlExRWppVGhkTHpKcmhtdXFMaENYeCs0YU9INEtPK3hjRnBmSkNLSHB6WWZWVzVYMFJma1pYMCtNUTNjZDVkWk1uVDlOMkUxNmd0dTIwNGg5YWcrMDN5S2RKWlRnZnJEVW9aTnVoZmtPL3JOb0lIejdJL1BNZGFDdXJoRGxxNnV3Qk8yam4rWnBObkRQSmdSUnc0OHZDOUFBNGJQK25yK3VJdkFDczg2NUdWODBlekkiLCJtYWMiOiJjZjI3ZjgxMTQyYmNlNzdlZWVhZGU1ODhjMDBiODI0Y2VlZjNjYjExOGIwOTA1NjE1NzgwN2NiYzY2MTIxOTcwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InpJajl4eVU3cElZR0RGQW1hbmJjK2c9PSIsInZhbHVlIjoiRGQ3MTlyWmlOR0hiQVgzZVRIWXI1WGRiZ1VkdFdWbzlnVndoUkx6TXY3cmR3TkFJRDB4amc3NDRxZEtOVE9WaldQQ3Joajd5ZEtNdXh3RjUweUFZUnN3NVd3MmlIN2VLZDlyVmJ5VVBjalhwaEc4QTBhb2lDMXRZejJRUkx0OG8wUUErWmpwNmpUZU1sUy8xdVVRYmpLNmxoQXFLWFMzSXJUbWxkM0dOTm56ODJlTWplZVFSdmoxdzZQWkpSd2ZPd2JnZ0E3R003VWlRNWxFWHkyd2g3NmxjR0VjaDZWeU4vbFk1NXF4UDVlQXRTc0lLYWxRdVZxRDdIUUVzS2tLK3JnT2dkdWN0OXhDU2o1L1VwUTFwQVlMZlZXQU9GTTczU244S21raEpPd3orV2JqY09sWlJCbGN0MENBOEw4S2NqNGxxdFA3NXdiQisrdnFMVCs2WmVTU29tS3RlTlZpbkpLSWtmTHdMYk1lNW91U0RHbTNTbmcvSnZ6RFVaQWhMRDR1UEtNdVNucmhwSnM5MGxTdCt3MkNQTGVUUjdBMkUxOWdraDJzWHhJbitGYUtTVFFreFRsNS9hQlVaUHdJa0J6a1RrWERwZ1NhcnNQQTgrbDVtMk1LRUlEdjA2enM0dzZBc1lvR05iNnk5dEpqcnIrRmplbVA5NzJxdEd2ZmoiLCJtYWMiOiI4MTU4ZGQ3YTIyOGU4MjUwNzk3YWQ2Mzk1MjM5ZmM0ZWM4NDcxOTczMzkwMDZlNWU4ZTBjZGJhM2M4NTM3ZjRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-149166275\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1492572101 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDWN9h6R9gAIdzZaXBzXfq0WGpauVfrUj70kKWA2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492572101\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1840646286 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:12:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdYMm5Wa1JmQnRTbmRES0ZkN0RzWHc9PSIsInZhbHVlIjoiZEIwUDNYZjl0cHc0U0R3Nm5CYWhTVWRmVjZMTkZaREsvdlUyS1BrMmVPYmFQd3ZrQjdtdHFOQWlqMHlUdnpYMGwxVzBTNUlqdThJa1EwcnlOSisyU3o0NVlSK00xSWhtVjFSNzB2WEo0dTZEMTlCR3BQSTdsZU1HL0xkVlI5eFBpcktVV0V5cndBSHIxc2c2YitTbHFIbHQ5dGRLWkVtaHRyUDdkaTVrSE5Gd1lXSTF3M1VuSmdLNHRrRWFKem5NYlZLNlIvZVdkYWR6SWM5Um5lOGYyZTlSNk9Bbk50SmU2ckI1Y1pTQW94UEN5a2ZPVzNSb1YzaVRjTjQxTTNxZE05MllZQ1lOcWZ3bnZrelUvcjNIemQ1M1RMOU5UUFNJbXhIelFBS0d2UEt4dXo1SXV6KzFBeWJBdE1kUThwTTBRSVFvdG1NK3R6NStScUU2ZmZaeXJpbEwyU01IbzlyTHlQeU1semVUV0g4U3g0TXZrSnFzbjN3VzZ0dDRrK3hhTWRtOVN4Y2psSyt2WVpnSVRqZUY3T0FybHpVb0JKZ1hhTXlZVFMwZXdrUHg1NEVyTlJXUStSTXFTTFJVR2RMS21PN0QwSHl3SHJ0ektOZmoxNFRmbHU0aFc1a0xXZG9ONnAzWFNZa01OMGx5eTBabm5KUlQ1KzNlV3dCU0ZoK04iLCJtYWMiOiI2OGViMTY3MjM4MDQ5ZDI0YmVhYWZhNzYwYzllZWJiYTQ2ZjRjM2Y5NDc5ZmI4MTAxMjMzYWVjMTE1MjQ0NDZkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:12:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InpUbUgwTjdNZkNiOVBiVTVpWGRpanc9PSIsInZhbHVlIjoiTzRHcHFXelJpSno2MmNPMUxYNHZtMEpyZ0E3WWVIcWh5YUtOK2p5ZER2N0xuam1XelRFZjFEcHh3bnhKTmUyN2drZklHM0pWbWFKNE9HUEdseW9WKy95aHpiQnE1SVV4UkFpSnB1bHpYajNzYVN6dWFTZlpiUEtMbC9LU0JuVHlncjJDYWczSE43alRCa0k0a0VqVjBRYzRxNGxtV2s4S0xIS0lwTGRINlVKYTE0R2FGT3hzMFoxZlpOdStMMldqVm9ITVBJay82M0tDM1BGTEphYkp4TXcrbXVrdjFjQ3pPN2pvL3JlY2YwbXZBeUFXaVBOaHQvaEJqMCs0QmtmMFg2QUw5dFB5YTkwcDZBc3NjU1ByaWFrb2UxOVMvY2puS0cwaVg5b3FwWWQxaXE5TFpLMW1tU3d2REZkcElGK211WWRJSWRKTnYwV1B6RWtjcHdmY1Z1L1NKUEtKVHlsNHpSTSs4OVRxS2w5M1RZQWpmL1Vqc0dBZ3F3bFBUSFdUVnhJYUl5aGgyMzdXNXM3cTJvV0lrUGk4NWh0a0RZUmhNVUJVUUJiRGZqK2JTc2RSSGhHdnEvcERSNDZsTk9xZEo5ZTZHZFlWd25MM0JQaHpyNnlvNDlBRmNrdjlCV1BYTjdJWnBzOVhjNzVqcEgzZnBTR3ZjaU5Ebzc4TmZyVG4iLCJtYWMiOiI5YTEzZWJjN2VkODQwZDhmNWI4ZDcyM2I0NTk0ZTBlNjMxNGRiM2RkMDY2OTcwNTNkMzIzM2QzZDIwY2UxOGY4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:12:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdYMm5Wa1JmQnRTbmRES0ZkN0RzWHc9PSIsInZhbHVlIjoiZEIwUDNYZjl0cHc0U0R3Nm5CYWhTVWRmVjZMTkZaREsvdlUyS1BrMmVPYmFQd3ZrQjdtdHFOQWlqMHlUdnpYMGwxVzBTNUlqdThJa1EwcnlOSisyU3o0NVlSK00xSWhtVjFSNzB2WEo0dTZEMTlCR3BQSTdsZU1HL0xkVlI5eFBpcktVV0V5cndBSHIxc2c2YitTbHFIbHQ5dGRLWkVtaHRyUDdkaTVrSE5Gd1lXSTF3M1VuSmdLNHRrRWFKem5NYlZLNlIvZVdkYWR6SWM5Um5lOGYyZTlSNk9Bbk50SmU2ckI1Y1pTQW94UEN5a2ZPVzNSb1YzaVRjTjQxTTNxZE05MllZQ1lOcWZ3bnZrelUvcjNIemQ1M1RMOU5UUFNJbXhIelFBS0d2UEt4dXo1SXV6KzFBeWJBdE1kUThwTTBRSVFvdG1NK3R6NStScUU2ZmZaeXJpbEwyU01IbzlyTHlQeU1semVUV0g4U3g0TXZrSnFzbjN3VzZ0dDRrK3hhTWRtOVN4Y2psSyt2WVpnSVRqZUY3T0FybHpVb0JKZ1hhTXlZVFMwZXdrUHg1NEVyTlJXUStSTXFTTFJVR2RMS21PN0QwSHl3SHJ0ektOZmoxNFRmbHU0aFc1a0xXZG9ONnAzWFNZa01OMGx5eTBabm5KUlQ1KzNlV3dCU0ZoK04iLCJtYWMiOiI2OGViMTY3MjM4MDQ5ZDI0YmVhYWZhNzYwYzllZWJiYTQ2ZjRjM2Y5NDc5ZmI4MTAxMjMzYWVjMTE1MjQ0NDZkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:12:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InpUbUgwTjdNZkNiOVBiVTVpWGRpanc9PSIsInZhbHVlIjoiTzRHcHFXelJpSno2MmNPMUxYNHZtMEpyZ0E3WWVIcWh5YUtOK2p5ZER2N0xuam1XelRFZjFEcHh3bnhKTmUyN2drZklHM0pWbWFKNE9HUEdseW9WKy95aHpiQnE1SVV4UkFpSnB1bHpYajNzYVN6dWFTZlpiUEtMbC9LU0JuVHlncjJDYWczSE43alRCa0k0a0VqVjBRYzRxNGxtV2s4S0xIS0lwTGRINlVKYTE0R2FGT3hzMFoxZlpOdStMMldqVm9ITVBJay82M0tDM1BGTEphYkp4TXcrbXVrdjFjQ3pPN2pvL3JlY2YwbXZBeUFXaVBOaHQvaEJqMCs0QmtmMFg2QUw5dFB5YTkwcDZBc3NjU1ByaWFrb2UxOVMvY2puS0cwaVg5b3FwWWQxaXE5TFpLMW1tU3d2REZkcElGK211WWRJSWRKTnYwV1B6RWtjcHdmY1Z1L1NKUEtKVHlsNHpSTSs4OVRxS2w5M1RZQWpmL1Vqc0dBZ3F3bFBUSFdUVnhJYUl5aGgyMzdXNXM3cTJvV0lrUGk4NWh0a0RZUmhNVUJVUUJiRGZqK2JTc2RSSGhHdnEvcERSNDZsTk9xZEo5ZTZHZFlWd25MM0JQaHpyNnlvNDlBRmNrdjlCV1BYTjdJWnBzOVhjNzVqcEgzZnBTR3ZjaU5Ebzc4TmZyVG4iLCJtYWMiOiI5YTEzZWJjN2VkODQwZDhmNWI4ZDcyM2I0NTk0ZTBlNjMxNGRiM2RkMDY2OTcwNTNkMzIzM2QzZDIwY2UxOGY4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:12:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1840646286\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-213653448 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-213653448\", {\"maxDepth\":0})</script>\n"}}