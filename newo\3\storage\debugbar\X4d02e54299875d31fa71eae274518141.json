{"__meta": {"id": "X4d02e54299875d31fa71eae274518141", "datetime": "2025-06-17 07:13:19", "utime": **********.297984, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750144398.667424, "end": **********.298004, "duration": 0.630579948425293, "duration_str": "631ms", "measures": [{"label": "Booting", "start": 1750144398.667424, "relative_start": 0, "end": **********.216635, "relative_end": **********.216635, "duration": 0.5492110252380371, "duration_str": "549ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.216649, "relative_start": 0.5492250919342041, "end": **********.298006, "relative_end": 2.1457672119140625e-06, "duration": 0.08135700225830078, "duration_str": "81.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45405360, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.019110000000000002, "accumulated_duration_str": "19.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.258073, "duration": 0.01844, "duration_str": "18.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 96.494}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.287827, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 96.494, "width_percent": 3.506}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1073198865 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1073198865\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-753332602 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-753332602\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1857239484 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1857239484\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1845667138 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=17n9b1e%7C1750144374665%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InRGbjFidHIxVy9tZ3hWSEJvZkxoOHc9PSIsInZhbHVlIjoiSW9KK3U0L1JUbGtSd3FhdXdoeHBtN0VwN092VWdqOTRHcXpsQkhUeGVLS3ExN09JaEd2d2NWcVdyaGhGTTVrOUlXem1SaDNGM2UrTG1kYi9CU2dweko5WGo1L3BCZWo0MTlNQ2xvQkhDN0xWMTBEQnhQY3pSVUt3T1dIVmJVWWprZHJRMWJkcVcrc3ZCT0pDM1A5TjhSU25jQnAwRUwvTVMxSVhiaTdOZHVpMGExdDRXeFVYRlhkUmxHM1NXQ0pRc25hZnRNQ0ZHd3ZjMVVod3EvWGZTRk9td2k1VnJwRkNsOWlBeW03ZVJMeXNaMmZ6N2hWU0Q4UmJkT1B1bnVWNGQ1a1AxQzQzVVk3NFlOelJWcEFmRjZZMmt4YU5xZ05ib1BiYlQwRkVWRExIRDMvUVJVUEVXc0ZDWE9iYktwVkVLd3ZRK0YrdENxL2w5YnhoRFdLVXB0bCsyZUkzMzBEczhBc3FSakY1UUFQT0NhSlNha1VkUjVvQmF4eE0yL01UWFo2dDR5WkZLaFpjUHpoMjY2cENPRGMwZHcxdVQvRkc1U3FoNDZFU2xOaWhxNzF2aXhvRk1QLzZRZVBQQTM4UUdqMkxxSkVCZE45NEdSOGxKNEJybDV1Snp4ZzI2WlhoeTlRNlJ3RFBCQ1lVZXdycWM2NU1Bc2xMSXBLaEc4VkgiLCJtYWMiOiIxN2I4YTEyNDNkNWE0ZDNlOTdiNjVkNTU2MzE0MmY1OGUyMDI0NTFkNmY1ZTUzMGQ0OGM3YjNmMTFkNjNhMzI5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik55ZTR1UTA5REZva0FnZHlMZVJZTlE9PSIsInZhbHVlIjoiWkJPTWp5NXgvbXVTbkcvMDdVd0RobEdWUitOWVVWSEZRS0JkSHFDK05pWmFXbDhxd1dsYTg1dzFrNThGYy84eE1sd2NJbjJxOEtLWUJKd1FnSXBQTGV5akZmeHFheGQ3MENIaUpDMEZEMk5jcHBJZlRUaEwvb1cyOWVhaGo4eUczR3VwejVtYUk4RkFKTUVNWWlKMG5ZWnMrVmxBUGlxdHpKLzVaYy8vREdZZ0FFb2hJTjkwOUlsU09MdWhiSC90OVBrYzAxSmpiMFVtenRHcWRXRUw0UFBPeE9Yc3l2TzR0MWxzTDVZc3I5MVFOamw5emIxMncyMTRpQSszNXJrVnBHVlo3NFpLWFNnQnI2L3JPT3M4QVluaCtCVnlqejhPVDdGV1Z3VmpFcjNRQitaTk1OV3Q4UHA5ZzdZcGFyZGIzQ3JKYnlGNmVONDhDYlBBNXp5aytEMTh2czB5dlY5U01Ndy9aN09laTdZRE1MWm1ickhQVjJERllzTk1HaGdNUWh6QlJMcWVLRC9KMjcvS3psZEFWTWpnQjB4M2V1V0QwazFDVzhJdDJkUmMxdEVJUmFuRFVOd2pSTVBwTGV1RXFXdFpDWjRxTkVldUxRQ1BUcGVYLzZhSnQzN2FETDRXOG9yOURENERCM2UyUVUwMVREOVBzS0ZtZktKeUpwQjQiLCJtYWMiOiJiNzYyNjNiNWVhZjZiM2Y5YjE2MWQyZGM4NmU3ZGEzZGM2Nzk0MGEzYzI2ZWE5MzQ4YjM4OTM1ZWViYTdjYjUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1845667138\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1965711458 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDWN9h6R9gAIdzZaXBzXfq0WGpauVfrUj70kKWA2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1965711458\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1214746789 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:13:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNTcThuayt2dWR2eHZXUlBxam9mWXc9PSIsInZhbHVlIjoiZ1AyOXp6cG5kSDhZK1VkZFZ3aWFXaTcrVnRGa3VJanB2VzF2QTZ6akdsWEtMWFk2Zm5MTFR3TWpnWFppTllkZWdLN3NIcWpMM2hmdVlWODhRQ05nSUFvSHllaGRmSWJYNmp0SWNGSU40bXRqOE8wbVlHYStvcDlPbHhTQ3J2TllocHJZWnhBdDJpVFJJcUNTcWJCUkpOYXFKT2NhMlFIUEZmRVJScUdsMWExS1NYR1B2VWRlK2JERlFrbkx3eGwvbGlZb2FkanA1V294anJtRlJCZTJVM3R4MldNMHM5MlJWZlR2TWhLdTNZVm5MeG1JV0MxRDRKaWJhRFplU05SSklYN3V1eTl4U2xuV1d3TjdFZFQxZjZzd0JJckZRK3pza013enk3S0xTYTY0djBvaVQ1QmRLeHhzK3l1L0p5dGpNM1Vya1VGZU9Zd0VSdFdaaG5JaHFwTnUxRDdROUp3TG43QlRyM0ZJWFBjVjFqTzdxQUViRCtFY2F5cTRTMEFpNEN6VERJUC9YMkppWEhjMDNWQkxKN29DMWt2NzVXUkE5OU1QS1JDclVuZGtuWXl4a25xNnlHcDNKd1FUZEF5Rko3dGs0cEgvcHJFNjZQSzFXZHpNQWZ6bEhiM3lHdzh1U2ZwMng1RkRNbXprWWpNRnBDUXlseWZXbEhlNy9xRkEiLCJtYWMiOiJiZDE3ZGI1YzA2YmYxODgwZTVjZjcxOTI0OGU3MTVjMGU3NzczMDVkNmIzZWY4NGFjZDhiMWI4OGFlNDQxNTJhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:13:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik5ueXBxWlVUZlF6dGtxVnpldldxRHc9PSIsInZhbHVlIjoiNnNUMnp1QW85ZUdLV29rbHdNSFBwZG55UGxzZkVDamNyYVNpZGN5YjlRWFZsOS9NZ1k1Y0FKQ3VXalBZMFY5cU5hUTZIcEdzK1M5cUoxSldpSXhDRy9jZFFqb0xCZGl5aEt3M2k4SVdJTnptTkdVQkt0R1YvSTdkT0ppWUNvVnV3SWdLWFhwMFE5Sy9NWVNxRUxLOFA5c0Zjb2FyVlQ1eGtuOHI3YUFweGN5MUwrV1JHbmxCcm1EODRWL1l4VjJSUlJPSVRGZysybEZWemxybjhISmhWSzY4Wml3bU00K1BzZjMreWQ4WHF5eURZcGtTb056eGFHd2tFZDBPR2w5Zk5jd2xUMWkwYnJYdDk3T3FCQkZ5OEhVaWcvUVExWXhEZ2lDU2pza1NqRlcwbDFmYmxvUmVneUhwbG5kSEgxTmtCZTNFOUI3RkRkZEE4QjRyaW5iN051V1IwVVNGVWoxekdNQjI3N3VVck1tYk9tcW9UVDVyN3ZSRHZEbm0wUFljZ2tuR2lwMW0zME1hakZRaXRZa2xtc1dHb0V0d0hvT2cyMC9iZ25GVDZyVUthMUUwbFhDSUgrb2NBa3NuOTFNMkV5UHlSaHZIK2F0bUFuMERoazduTXNGckVPeHlydkV4djBJcUdndzRvbkdlNjNSV1FEL3hSVnZ4MUpRMzNQdGQiLCJtYWMiOiI2OTg0YzBmMzM1NGY0Y2IxMzdhNzE5YzRiZWNlNTg4OWI2ZjhhZWVjYTBlNjk4YTgzNDMxZWZiNjM5ZjdkNDFiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:13:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNTcThuayt2dWR2eHZXUlBxam9mWXc9PSIsInZhbHVlIjoiZ1AyOXp6cG5kSDhZK1VkZFZ3aWFXaTcrVnRGa3VJanB2VzF2QTZ6akdsWEtMWFk2Zm5MTFR3TWpnWFppTllkZWdLN3NIcWpMM2hmdVlWODhRQ05nSUFvSHllaGRmSWJYNmp0SWNGSU40bXRqOE8wbVlHYStvcDlPbHhTQ3J2TllocHJZWnhBdDJpVFJJcUNTcWJCUkpOYXFKT2NhMlFIUEZmRVJScUdsMWExS1NYR1B2VWRlK2JERlFrbkx3eGwvbGlZb2FkanA1V294anJtRlJCZTJVM3R4MldNMHM5MlJWZlR2TWhLdTNZVm5MeG1JV0MxRDRKaWJhRFplU05SSklYN3V1eTl4U2xuV1d3TjdFZFQxZjZzd0JJckZRK3pza013enk3S0xTYTY0djBvaVQ1QmRLeHhzK3l1L0p5dGpNM1Vya1VGZU9Zd0VSdFdaaG5JaHFwTnUxRDdROUp3TG43QlRyM0ZJWFBjVjFqTzdxQUViRCtFY2F5cTRTMEFpNEN6VERJUC9YMkppWEhjMDNWQkxKN29DMWt2NzVXUkE5OU1QS1JDclVuZGtuWXl4a25xNnlHcDNKd1FUZEF5Rko3dGs0cEgvcHJFNjZQSzFXZHpNQWZ6bEhiM3lHdzh1U2ZwMng1RkRNbXprWWpNRnBDUXlseWZXbEhlNy9xRkEiLCJtYWMiOiJiZDE3ZGI1YzA2YmYxODgwZTVjZjcxOTI0OGU3MTVjMGU3NzczMDVkNmIzZWY4NGFjZDhiMWI4OGFlNDQxNTJhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:13:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik5ueXBxWlVUZlF6dGtxVnpldldxRHc9PSIsInZhbHVlIjoiNnNUMnp1QW85ZUdLV29rbHdNSFBwZG55UGxzZkVDamNyYVNpZGN5YjlRWFZsOS9NZ1k1Y0FKQ3VXalBZMFY5cU5hUTZIcEdzK1M5cUoxSldpSXhDRy9jZFFqb0xCZGl5aEt3M2k4SVdJTnptTkdVQkt0R1YvSTdkT0ppWUNvVnV3SWdLWFhwMFE5Sy9NWVNxRUxLOFA5c0Zjb2FyVlQ1eGtuOHI3YUFweGN5MUwrV1JHbmxCcm1EODRWL1l4VjJSUlJPSVRGZysybEZWemxybjhISmhWSzY4Wml3bU00K1BzZjMreWQ4WHF5eURZcGtTb056eGFHd2tFZDBPR2w5Zk5jd2xUMWkwYnJYdDk3T3FCQkZ5OEhVaWcvUVExWXhEZ2lDU2pza1NqRlcwbDFmYmxvUmVneUhwbG5kSEgxTmtCZTNFOUI3RkRkZEE4QjRyaW5iN051V1IwVVNGVWoxekdNQjI3N3VVck1tYk9tcW9UVDVyN3ZSRHZEbm0wUFljZ2tuR2lwMW0zME1hakZRaXRZa2xtc1dHb0V0d0hvT2cyMC9iZ25GVDZyVUthMUUwbFhDSUgrb2NBa3NuOTFNMkV5UHlSaHZIK2F0bUFuMERoazduTXNGckVPeHlydkV4djBJcUdndzRvbkdlNjNSV1FEL3hSVnZ4MUpRMzNQdGQiLCJtYWMiOiI2OTg0YzBmMzM1NGY0Y2IxMzdhNzE5YzRiZWNlNTg4OWI2ZjhhZWVjYTBlNjk4YTgzNDMxZWZiNjM5ZjdkNDFiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:13:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1214746789\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}