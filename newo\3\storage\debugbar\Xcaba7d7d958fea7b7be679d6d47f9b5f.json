{"__meta": {"id": "Xcaba7d7d958fea7b7be679d6d47f9b5f", "datetime": "2025-06-17 05:40:41", "utime": **********.184653, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.814791, "end": **********.184688, "duration": 1.****************, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": **********.814791, "relative_start": 0, "end": **********.978144, "relative_end": **********.978144, "duration": 1.****************, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.978163, "relative_start": 1.****************, "end": **********.184691, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "207ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019230000000000004, "accumulated_duration_str": "19.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0589511, "duration": 0.01502, "duration_str": "15.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 78.107}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.106006, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 78.107, "width_percent": 6.656}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1585622, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 84.763, "width_percent": 15.237}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750138838203%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkIvWStwTm1weUEyTC92aTcvVFZSUHc9PSIsInZhbHVlIjoiZ2ZXbWgzU1R3d2kxbVpFVUtuL3BkTitQMlNvM1JBQ0xJcXF4YUVRZStvUHlmdi8rQm1WenBNeWR0QkZSeEtXZnZBU3FDWkxrZmRtQXkwQ0JXcWdqT2RtV21HQWZFbFBENC9MOEd1NHArQ1MvWCs2cjVoTm5vRUxBMjkxeVBHaWRxaXBjN2FQdU9CZjNNU2RCNmk4VEN2aWRZZVRkUXBtYU1zZ1ZwUFlxQ2JPQzlRNm5DSEJ3QnNzYTNrVW1WcnA0QUtyNURoYW1Gd2l0UnNuV2I5MnBjdUxXeXd0TW1LZ0svTGFSS2EvRUZxWnkvWUFQTWFYYmo0MkFGblBXRUs1alFxN3RrVG02MDgxWUJsWi9sVEROUWZQRzFrbi94TFhTQ29PbGtqSElYUUxlclU3QjRLYmdXRXhyOGEvVkZBaG9ndk9iY082WUwwbWN0WVF1N2JodlUzMGl6Mmk2ejFKb2NuZmozNE5IVFpKbUsyaEszeDNCejhBM2tURDE0amxtK2FqNityNTVxNjFDVFpGWWhQMGx2OUZlQUpzb0FOdm5MYllISldlMHo5MEN6UEtDeGNVbXp6NWNpVHJnYzloZDhVNWpwUEVJLytJWURnQStkdk9ISUNKZVMzc0lQSXp6ZzIvQVVyTjdqdDZWcjZEdE5VVkYxSnFEOE9WbkNpQkwiLCJtYWMiOiJhNzRhZTQ4OGJmOWZjMTYwMzAwZGRhOWZkYzVjYmYzMTljYjI3MDlhNzYwZTkzZjM4MTNhMTAxY2MwOGM3ZGQyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjdYZVhhc1dBb3N6bGJaM3NzLzhtZVE9PSIsInZhbHVlIjoiWFNFVDd2anU2d1ZSM29tcTRPbkh1MUVjUnh3anhraXlrTEhrWk5ja3VBMHFTWDNCZHI3RWZQL0RHa214NmExckFTVWJEaXkrLzVsZmxYNHFiaTZOZmZYTUVabWxURVJFSTFnaU9YNDdiUGdNaWFLREVLd1BrdE11T2ltcHlFclJTeVNKQnczVnJBalRpTVlJSjRyZ1J1TmQrYkdMSGZoV3NnY2RkRS9sUUxBb29uZjlaaUxZOVNnakxLT1R4ZU95dWxra0dlRUQrSTV5djBURjNwMFlpQksrOHIwUmFVdmVBRGwzc0FuMHQ3VnVzYWc4eEpjVFI1K2k2cStKUmEvdE5VT25CTGhZTE16TE9tSmdGdFJ6eVBDblFubFc5bHdFOFZhMGlldURDZUIxL0dMRm9PdmI2Tkl6dmsvQ1hOR0xKUU04SWRPbXROdTFocTdTY3M2ZUVsTHRjZnZwVlRibUVBck9TbFZMWmZINjl0a2ZaeEdGSDVoZThNK3ZVcG9Xd01CL3l1a0ZHdVlCaHRabFhUZVNhZ0lJNkRRTTFEc0xmWll1S2Jab2dKdzN0bFBoT29US2x2THNURGhwYmUzZXJIdHFWbG9FaXVPNSttQTB2dDZKNWVsSmlsRlloV1NPeVpvOXU4b0tEN1U1dnc4VXRNTDhTRnJtSUxDTURXZmgiLCJtYWMiOiI2YmM2MWUxYTNhNzM1NDBiZjM1OGI0MDYzZTU2Zjc5Y2QzZmIxYjk4ZmNkODFhZmJkNWFkNzJjM2VlMjEwMGFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MsEpVpyAabX9l6kTbr1IUggTy4SSXp1me81GakZC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1881116973 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:40:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlN0alBJb2E4OWp0WU5zeWRvS002Q1E9PSIsInZhbHVlIjoiVUZjRGErSHBEMEtETitzWmpWSUdQZlZ5RHRNRVc2cmV3NVM5WmhGT2dnVVZROXZaaDJncW52eHBlaDZhMEZXeVY2TlJab0NSNzE3Qkt6MnRVeTd6MXhxcC91eUtTeUcwWXNKMTNpTEkvNWk4QnFuUWFYVUFUZHN3S1hmb0JWVFllMWpYaVBaUTZEL1pwZ1FseEtpRmhHVW9raVZvMUNrd0tVUXN3Y3dXTkU5cno3eXlNV3IzTVd6Z1ZObUVDZFZXdC9ZQWJFb085ZmdlZThtd3FZakNOSlR5OHJBNERMMTlyb25rSlNVVDJTbm5KMkxreG5iNkVyeCtWOUVqT0VrbXB0YmJZM0syaWVkakptSm10VVZDWDBTcStmLzU1a1VqTW5tQXBTN1B5Y096WWRMTElsRlAvS282aVQ0MWpJcmFqTUNmaGdhalh0L3FGbXlaRW1aZ3VsWlBOZXd6VHhyRldYNXhNZHlMV1A1R2JTTmVOK3dJYjkyc3ljT1JWT1gwVGFxaUJKbjd1QnVUUUlEL2txQXB1a0NmdGRrL3VUWm55QUhkUVJJT1FxbmxnanR3azlPN3NBUkZFUnkyU0hXdUZkWWNKK2NWSzVvVktyTS9tTE5SS1VvR1VnelVtK3EwMWZuWmduSlI2SFptWGRhMTFPOU5vdnlDbUNJT0EwOFUiLCJtYWMiOiIzNjI3NDIyMGY4YzJkNTFhYzZhYWM4NDg5YmFkMWJkNjIxYTM2NTRiYjZjYmIwMTI2M2FkZjA1N2VhODczYzk5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFLcDZEZlEvLythVmVFQlpnMEk1d2c9PSIsInZhbHVlIjoiUDFRUXRpbGlCVGdOSzJTb1J4M1htRmNmYVFJMmpEQklGdlBwSkV1ZjVYdDJEZWRiT1hzK1FRdEdONkFNbDZnZ2N4ZCtFSkxnMFI5V3FrR0hVMk0rd3pWRnQ1ejlXVnhkNGNIZGM0SEFtZnFsdW1ScUxVc2VPemdzTGtFMW10dFJFWU5wbnFWdmUvRkxDcmo0aC9TSEZPYVFjWjRhbTl4NVRYbC9DTzZoZHk0QkZLMkg0RmpEMVE1QkhJYzhSMm5QZldYZmhlcXBZLzZBQzBMbWF6U0JRVnhJQXVWTDdSYm5STlc2dUFZMC9RWjlPbWowdkxJMWNzWFRtc0VPZDNIckcvRThSL3M5SG53TVlVZCtOOThGSitWL0EvU0VQVEw1UWFRRktheS9LTUhnSEFMS3A2UnQxSnhtdTRCSEpLT1RwT2NLejl2VEtTSnY4bzZ6VFR1S01Xb3hha3NWWlFNVUFXamZGT0hMVUpXeVhEZENlUnQ1dG5CY2JpSlpMYnJvSWZIV1piT2lNR29UNmxUWlFrYUhnS05IZFQxQ0NzOUhUVW4zNEVtM0VNQllTbEJNK1JpTW1zRjcxMFBMcy9INVdMQlc0SEdrNDd3bHVSY2IrRTVXL1VBZWRUcW13ZDFseFdUcXFjcUI3SDYxOXFsbjdVVDZxb2grTzU2VWxCTjAiLCJtYWMiOiIxZTRmZDdjNDI5ZDY1MWZhYWM5MmQyNjk1NzI3ZDc2OGFhYWMwNTE2OTQxOTJhOWNhNTdmZWVjNjU5MjI3MTAxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:40:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlN0alBJb2E4OWp0WU5zeWRvS002Q1E9PSIsInZhbHVlIjoiVUZjRGErSHBEMEtETitzWmpWSUdQZlZ5RHRNRVc2cmV3NVM5WmhGT2dnVVZROXZaaDJncW52eHBlaDZhMEZXeVY2TlJab0NSNzE3Qkt6MnRVeTd6MXhxcC91eUtTeUcwWXNKMTNpTEkvNWk4QnFuUWFYVUFUZHN3S1hmb0JWVFllMWpYaVBaUTZEL1pwZ1FseEtpRmhHVW9raVZvMUNrd0tVUXN3Y3dXTkU5cno3eXlNV3IzTVd6Z1ZObUVDZFZXdC9ZQWJFb085ZmdlZThtd3FZakNOSlR5OHJBNERMMTlyb25rSlNVVDJTbm5KMkxreG5iNkVyeCtWOUVqT0VrbXB0YmJZM0syaWVkakptSm10VVZDWDBTcStmLzU1a1VqTW5tQXBTN1B5Y096WWRMTElsRlAvS282aVQ0MWpJcmFqTUNmaGdhalh0L3FGbXlaRW1aZ3VsWlBOZXd6VHhyRldYNXhNZHlMV1A1R2JTTmVOK3dJYjkyc3ljT1JWT1gwVGFxaUJKbjd1QnVUUUlEL2txQXB1a0NmdGRrL3VUWm55QUhkUVJJT1FxbmxnanR3azlPN3NBUkZFUnkyU0hXdUZkWWNKK2NWSzVvVktyTS9tTE5SS1VvR1VnelVtK3EwMWZuWmduSlI2SFptWGRhMTFPOU5vdnlDbUNJT0EwOFUiLCJtYWMiOiIzNjI3NDIyMGY4YzJkNTFhYzZhYWM4NDg5YmFkMWJkNjIxYTM2NTRiYjZjYmIwMTI2M2FkZjA1N2VhODczYzk5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFLcDZEZlEvLythVmVFQlpnMEk1d2c9PSIsInZhbHVlIjoiUDFRUXRpbGlCVGdOSzJTb1J4M1htRmNmYVFJMmpEQklGdlBwSkV1ZjVYdDJEZWRiT1hzK1FRdEdONkFNbDZnZ2N4ZCtFSkxnMFI5V3FrR0hVMk0rd3pWRnQ1ejlXVnhkNGNIZGM0SEFtZnFsdW1ScUxVc2VPemdzTGtFMW10dFJFWU5wbnFWdmUvRkxDcmo0aC9TSEZPYVFjWjRhbTl4NVRYbC9DTzZoZHk0QkZLMkg0RmpEMVE1QkhJYzhSMm5QZldYZmhlcXBZLzZBQzBMbWF6U0JRVnhJQXVWTDdSYm5STlc2dUFZMC9RWjlPbWowdkxJMWNzWFRtc0VPZDNIckcvRThSL3M5SG53TVlVZCtOOThGSitWL0EvU0VQVEw1UWFRRktheS9LTUhnSEFMS3A2UnQxSnhtdTRCSEpLT1RwT2NLejl2VEtTSnY4bzZ6VFR1S01Xb3hha3NWWlFNVUFXamZGT0hMVUpXeVhEZENlUnQ1dG5CY2JpSlpMYnJvSWZIV1piT2lNR29UNmxUWlFrYUhnS05IZFQxQ0NzOUhUVW4zNEVtM0VNQllTbEJNK1JpTW1zRjcxMFBMcy9INVdMQlc0SEdrNDd3bHVSY2IrRTVXL1VBZWRUcW13ZDFseFdUcXFjcUI3SDYxOXFsbjdVVDZxb2grTzU2VWxCTjAiLCJtYWMiOiIxZTRmZDdjNDI5ZDY1MWZhYWM5MmQyNjk1NzI3ZDc2OGFhYWMwNTE2OTQxOTJhOWNhNTdmZWVjNjU5MjI3MTAxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:40:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1881116973\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1191817900 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1191817900\", {\"maxDepth\":0})</script>\n"}}