{"__meta": {"id": "Xea35129d8cc94f2ed512c4b22dbce4b1", "datetime": "2025-06-17 07:13:05", "utime": **********.805417, "method": "POST", "uri": "/pricing/update-inline", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.209136, "end": **********.805439, "duration": 0.5963029861450195, "duration_str": "596ms", "measures": [{"label": "Booting", "start": **********.209136, "relative_start": 0, "end": **********.675638, "relative_end": **********.675638, "duration": 0.46650195121765137, "duration_str": "467ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.67565, "relative_start": 0.46651387214660645, "end": **********.805441, "relative_end": 1.9073486328125e-06, "duration": 0.1297910213470459, "duration_str": "130ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52050744, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pricing/update-inline", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PricingController@updateInline", "namespace": null, "prefix": "", "where": [], "as": "pricing.update.inline", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPricingController.php&line=141\" onclick=\"\">app/Http/Controllers/PricingController.php:141-224</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.02377, "accumulated_duration_str": "23.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.713953, "duration": 0.01108, "duration_str": "11.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 46.613}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.736203, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 46.613, "width_percent": 2.945}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.757598, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 49.558, "width_percent": 3.87}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.761015, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 53.429, "width_percent": 3.534}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '7'", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.773732, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 56.963, "width_percent": 3.029}, {"sql": "select * from `product_services` where `id` = '7' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["7", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PricingController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PricingController.php", "line": 169}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7780058, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "PricingController.php:169", "source": "app/Http/Controllers/PricingController.php:169", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPricingController.php&line=169", "ajax": false, "filename": "PricingController.php", "line": "169"}, "connection": "ty", "start_percent": 59.992, "width_percent": 2.482}, {"sql": "update `product_services` set `purchase_price` = '10', `product_services`.`updated_at` = '2025-06-17 07:13:05' where `id` = 7", "type": "query", "params": [], "bindings": ["10", "2025-06-17 07:13:05", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/PricingController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PricingController.php", "line": 207}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7842062, "duration": 0.008919999999999999, "duration_str": "8.92ms", "memory": 0, "memory_str": null, "filename": "PricingController.php:207", "source": "app/Http/Controllers/PricingController.php:207", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPricingController.php&line=207", "ajax": false, "filename": "PricingController.php", "line": "207"}, "connection": "ty", "start_percent": 62.474, "width_percent": 37.526}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1363590150 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1363590150\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.76665, "xdebug_link": null}]}, "session": {"_token": "TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]"}, "request": {"path_info": "/pricing/update-inline", "status_code": "<pre class=sf-dump id=sf-dump-1092337684 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1092337684\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1168585115 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1168585115\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1136574651 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>field</span>\" => \"<span class=sf-dump-str title=\"14 characters\">purchase_price</span>\"\n  \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1136574651\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1763634091 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">82</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=17n9b1e%7C1750144374665%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijk5NFY4bmNSYlh2RnVKQVAvL2NzVUE9PSIsInZhbHVlIjoiNmdQcmRaRk1QTDNmcGN6cWhEL1hsNEcvOG5PQi93b3BGcEtCOFQxaW42dnAxOFNCaFdFQ2RMZCtWbUFwbndISlQ3Tm9sdXpQTWNlczFVU3JlTTJyTjVoemU0R0gvNUlwekxabG9sMnRObzIxSnA1QmpBSEVUdFNPcktOQ2lPdkdTVGlmK2ZrK29DdWNjb25zV2tGSEtBZWtPd1pZbVdOdzhacVExQVFtNERGOVM4OGFsSzl0UlRaZlhjQjhzREk1K0dPdlM1cjBGd09ybWNBTW1Id3lTOWMzeERVbG5yR08yZ2EyUFcrdUtkTlZRSFV2UG9UbzBHQVhObDVMaWkyQ25YK0FralNac2NjNzZ4VG5QWkZLYWprSC9Oa091T1ptR2F6K3ZQdXh5RXNKZjRVM2VqQlhNRGU5c0RwbmZjSHNLN0kxRnlLT0RFeTdpVmFjQnFSVXBSTmdqamFtLzI4aU5UUENBbkJ0K0dGZGpSSXpjdnUyc1N1MGEzSHFwSm1RQ0pYNnBPWlRtaVV6YkVXcTMrcG1nNVJ2QXMxRmcxbjNtZ1c1aC85YlNyKzI2bmFKNUtxSHdteW8ra1JrbU0rQkd2NGlZYXlqN0dBM082MW4vMUJaQUNRT3BtMjJmVzFuWlhaVVZJeVJ2QkZZL09saXJhRHVNREMwWXdKeXhZUHUiLCJtYWMiOiI2MWUwMjM4NDdmODRlZWY1N2I3MTZmZTg2MTBjN2I3OWJhMzY1OTUxN2NlMjMyNjBmMjEwNGNiNWJlZTI5MTdhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik91RnZCRDQ1cnErNHhOMjYvc0VnQUE9PSIsInZhbHVlIjoiYWRueis0Z3prOEFvM2M5TldwV2pHYzJTRUtRSm9yVUFNWnB6MkQybkFldG83VlFqbHpCRjk1S0VsQlVOdWNuT0pLdkkvUEU5NFFiQzlhblNIa0tyelZpblNLVnhqVE9Ta08wQmJGWmhEaW51ZFErMWlBSXE3OVNPYlhCcWhxbWJsQkt3bjJPL1lOdFNiMG5GNEVzSlBOam1kR2MzVmxGa1J0OTFHNVUyMk1VRnhBTEtjcGdQd3llYWorY0tpMlMrTVFQdHJHS20wdmVUNGFWNk5VT3Vqby96cU4remU5eTd2a2hkSFlJNHJkWGdmY3pTbWg4MVhvK0Q0RVduNU9lWnJtWml6d2RhTGsrLzc1Y2JUSHVqdzJzT3hYYjZ5TU82OWxvRnBoNXgrZXgzU0JOTUgxcFZQOEhoSFloUU0xelh2TGY5OUgxenN2Q1VyRkdUdm1ETUEzZDZUeUtJYXNOSnlsZGlDeXlRb2tyM0o1aExEeW9RQnlSekNXbng4dW9OUExkd2V0ZERuZnZFcnEzUjNxS2V5ZFo3Tk9iTjl3d05iU09sRkZCLzFFbzRuR2Nwam0vaFhTVm9EZFltM1cxbTVzMHhFanhmbUJnZURoc2FwaitWeWZDRnl4eWl3aDR1Ry9GeWNZOStXK0J3am04NXVYRkJIdFI3MDVUSGIrTEIiLCJtYWMiOiI4ZjJlMmZhYTVkNTk5YzdmYmY3NDcwMGYxMjQ2MDgyZTRlYzc0ZmFmM2EwMmIzMzNiYjRkY2E1NDA2OGRiYTBlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1763634091\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-776920265 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDWN9h6R9gAIdzZaXBzXfq0WGpauVfrUj70kKWA2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-776920265\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:13:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldoQUVmb0lXVDUwbFU0NG1QamRxa2c9PSIsInZhbHVlIjoiMEsrSTIwZDdJZkljVjBqekc0MTZ4eHFqRkVraE9CeW5DWUtjQmtlR3lvZVA2NW00ekRmS1BUTG9mbGo1ZExRRHR5M0xOV3ZsRVNOTlc4bHRwRXdTbDRielIybjZGOUN3SGFyUFhwbng5TGNZdWNlU1R0Wk5nWURGRHUxWlJ3aGJvcWhRUEVTd0RJMWIyOWdldi9zUWc3M3psYTlmVUtUNGNXMThTNDhEUFJlTUVSQlpyRFZBT2ZudlJ2YXhnVjRiZzRzbXd6TjdqTnJnVDI1VEdOTm42VWdzalh1N09la1lQNVJPVjZJYkNmRHlkMVprTHlKRGZvVmpQREFFNkhTNUdXVFNrYzdBVTBDbTNNZ3hRWFRuMGlwK1FqdnZBM3hoWGxxdkhhc0xVcXk1NTlmY1NhV3RDSDZXTDNUSlVGS3huQXdiMWNpSFRsQzdVSUFXVlBQRUJsM0tIYmNrRGd5dXpuWkhhdXNsUFBGZjZHR1dXbFpraExYL1o5dUpvR2ZQYUl0TmZMZWk3S3RFaHRVUGhzbS8wVGFBUHJLeGVZZ0d0aVdGOUt0MFZ2VGxxaDFBblhzYnVXUWV6d3NWSTh3SUtGWUEvOHg0T1M2S2MxWjJEQlJ4VzVXQ0xBUXVmeE1tT0paa3J0KzN0YThob2RMRVV6YVFTRlNuUXNRbjVFOW0iLCJtYWMiOiI3YjBjMWRkOGMyNWQ3MDA2MGU3MWQxNGYwMzA2Y2IzMDk5MmE0ZGQxYWY4YmZlOTZhZTU1MzU2ZWM2YjgzNDM4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:13:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InBOb3htdHgycjhJM3dJdUZ4N0VUbXc9PSIsInZhbHVlIjoiR2tMeWZDaFo4VWRqWFQvczBCVXFIajVvWG8wc3p2MnlhTVVVcEd6VytqRjFyL1BVNGkrOHFaQUkvdldNR0cvN043MmtvSVJERzc4MkFpaWZoeThSUEtTTTQ0Z0wybXBOQ2w0dGhnMDJ2U2NHaG1YK3V3QnhJV05JVTgxR3ZZQ3JXY0NZNGVMNjJZeG5DU0l2N1l5ZFNoa1k4RFRkbitGbUhLaEFNQmhqbTQ0RVBxK0svR3BZdjgzYjlISjl0SWhmbVZmd3MwT1VhbDlTR2JQTFdTSjFCYk11UXJWUXFhOUFVbTE4N0V2aDJ5WTVuV2NLQXlXMFJwMENROExuOHJPdlV6S1NTVWFCU2NZRHlMM1RqUktmSEtOczBscG9ZcmlweWRLWjhxQ0JlaGxwbFM3dFh4ZzgzNmU1TkNyZGVXMzE1OE8vQ0JhalI5U0JFZWdQek43R0NDMTZhVUNyNG5tdzIzWEZtUEtPSzdISHU1MkVHazl3QVdrdHF3TWY1UFFBZUZFREFNZWRScXp3R2I1QlcyejVWUWkzV3FGbVJzQ1RBZTFNdmdZUlZZZjFONDdEVEo2Z0pIK3dETFc0YXV5bExYUnpnV0hOTjBTSzVyZW9KYVBiNFQveVd4cklRVlNKd0puVjg2MHpRR0NrNnZiQ1E2NHFqbDc2ZFZNU2krRWYiLCJtYWMiOiIxYjVhMDFmZjYwMzhkMTk3MWVhYWI5OGU3OTZhN2ZiZThhZWJkOTRjYWM5Yzc3MzE1NmVmMzQwYzU3ZTIxMjcxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:13:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldoQUVmb0lXVDUwbFU0NG1QamRxa2c9PSIsInZhbHVlIjoiMEsrSTIwZDdJZkljVjBqekc0MTZ4eHFqRkVraE9CeW5DWUtjQmtlR3lvZVA2NW00ekRmS1BUTG9mbGo1ZExRRHR5M0xOV3ZsRVNOTlc4bHRwRXdTbDRielIybjZGOUN3SGFyUFhwbng5TGNZdWNlU1R0Wk5nWURGRHUxWlJ3aGJvcWhRUEVTd0RJMWIyOWdldi9zUWc3M3psYTlmVUtUNGNXMThTNDhEUFJlTUVSQlpyRFZBT2ZudlJ2YXhnVjRiZzRzbXd6TjdqTnJnVDI1VEdOTm42VWdzalh1N09la1lQNVJPVjZJYkNmRHlkMVprTHlKRGZvVmpQREFFNkhTNUdXVFNrYzdBVTBDbTNNZ3hRWFRuMGlwK1FqdnZBM3hoWGxxdkhhc0xVcXk1NTlmY1NhV3RDSDZXTDNUSlVGS3huQXdiMWNpSFRsQzdVSUFXVlBQRUJsM0tIYmNrRGd5dXpuWkhhdXNsUFBGZjZHR1dXbFpraExYL1o5dUpvR2ZQYUl0TmZMZWk3S3RFaHRVUGhzbS8wVGFBUHJLeGVZZ0d0aVdGOUt0MFZ2VGxxaDFBblhzYnVXUWV6d3NWSTh3SUtGWUEvOHg0T1M2S2MxWjJEQlJ4VzVXQ0xBUXVmeE1tT0paa3J0KzN0YThob2RMRVV6YVFTRlNuUXNRbjVFOW0iLCJtYWMiOiI3YjBjMWRkOGMyNWQ3MDA2MGU3MWQxNGYwMzA2Y2IzMDk5MmE0ZGQxYWY4YmZlOTZhZTU1MzU2ZWM2YjgzNDM4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:13:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InBOb3htdHgycjhJM3dJdUZ4N0VUbXc9PSIsInZhbHVlIjoiR2tMeWZDaFo4VWRqWFQvczBCVXFIajVvWG8wc3p2MnlhTVVVcEd6VytqRjFyL1BVNGkrOHFaQUkvdldNR0cvN043MmtvSVJERzc4MkFpaWZoeThSUEtTTTQ0Z0wybXBOQ2w0dGhnMDJ2U2NHaG1YK3V3QnhJV05JVTgxR3ZZQ3JXY0NZNGVMNjJZeG5DU0l2N1l5ZFNoa1k4RFRkbitGbUhLaEFNQmhqbTQ0RVBxK0svR3BZdjgzYjlISjl0SWhmbVZmd3MwT1VhbDlTR2JQTFdTSjFCYk11UXJWUXFhOUFVbTE4N0V2aDJ5WTVuV2NLQXlXMFJwMENROExuOHJPdlV6S1NTVWFCU2NZRHlMM1RqUktmSEtOczBscG9ZcmlweWRLWjhxQ0JlaGxwbFM3dFh4ZzgzNmU1TkNyZGVXMzE1OE8vQ0JhalI5U0JFZWdQek43R0NDMTZhVUNyNG5tdzIzWEZtUEtPSzdISHU1MkVHazl3QVdrdHF3TWY1UFFBZUZFREFNZWRScXp3R2I1QlcyejVWUWkzV3FGbVJzQ1RBZTFNdmdZUlZZZjFONDdEVEo2Z0pIK3dETFc0YXV5bExYUnpnV0hOTjBTSzVyZW9KYVBiNFQveVd4cklRVlNKd0puVjg2MHpRR0NrNnZiQ1E2NHFqbDc2ZFZNU2krRWYiLCJtYWMiOiIxYjVhMDFmZjYwMzhkMTk3MWVhYWI5OGU3OTZhN2ZiZThhZWJkOTRjYWM5Yzc3MzE1NmVmMzQwYzU3ZTIxMjcxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:13:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1104161375 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1104161375\", {\"maxDepth\":0})</script>\n"}}