{"__meta": {"id": "Xe001a863c0d1f1687773b1ab189dd0de", "datetime": "2025-06-16 15:22:24", "utime": 1750087344.033626, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087342.444963, "end": 1750087344.033682, "duration": 1.588719129562378, "duration_str": "1.59s", "measures": [{"label": "Booting", "start": 1750087342.444963, "relative_start": 0, "end": **********.878308, "relative_end": **********.878308, "duration": 1.433345079421997, "duration_str": "1.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.878328, "relative_start": 1.4333651065826416, "end": 1750087344.033686, "relative_end": 3.814697265625e-06, "duration": 0.15535783767700195, "duration_str": "155ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45169544, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.007640000000000001, "accumulated_duration_str": "7.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.959735, "duration": 0.00512, "duration_str": "5.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.016}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.993058, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.016, "width_percent": 17.801}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750087344.010282, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.817, "width_percent": 15.183}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-974433933 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-974433933\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1786755161 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1786755161\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-628598472 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-628598472\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1249619244 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087330436%7C4%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IklXd01DbWFoM1NITDh3RkNuRHJoSEE9PSIsInZhbHVlIjoiR05qenQwMEhwSXlpU0UvZnp6SVExRWdhRWhBTHN4L0E0M0tpWmZ2L0J6NHpNUjVIQW9GYkh1RWlzazRPMnZDbnFiV2g5K1VCK2xocG5nTnZvbUNJVHI1aVRjY2xBNXgxWFVDblhtaDlGNmRrTWdyRExqalJnblJlbEFPZTNPTHQyLzVabVQ1alpiZ1I5R2VWbGdxaG9qWW5wMDNOSndmTDdYRkVvTE1EZDFFTU9MTVZUd1YyQU1MdWFMUUxXaFljWWZxYnhlT2N6elY2czNUNmdHVCttL3hUNW5tQzBWaDhQaDR5RjJ5UGZJeEljOHNkSmNKU3RZS0k0eEp4bHBkSzgwWHBRYkRiMHFLRG1WbjZyZWtlZEJTNTIzRSt2Z1l0N1AzckpQeElYQzlaczNobVg3Z2dhV29lNFVnM2MvTHl2VzZKamxudTUreXZ1RnN0TDlVMzRITW5sL0NHV0YzY1VrbjRPc2YzbWJ4VHoyMURkNysvWjQ1SkFDNFJOczQ2OGFqRzdUenVKa3FZVHlXRDJVTk1rRzhmeXRPTzB2ZG5lZGd1MVlvWjJFcGt5aDMyNjFMUEZlbE5PMnVFc2Nna1FKdjdETU5uYm10VTVYY0t3azNoUTVWTXhVenJGUlJyajVoZjF5SVFYREZNN0VNTmVRVm5wd25saElmWS9zRlEiLCJtYWMiOiI1ODk3Nzg2NDA4ZDU2ZmE2OWQ2NzI4ZWY2NjUwMTU0YjJiOTY0Y2Q2YTI4ZWE0NzIzMzM1N2MwZGU1MWI2ZmJjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InVBVk55MGxXd0hGV2hwZDcrRTNyV0E9PSIsInZhbHVlIjoiTEZscWZxc2VLV2NPZnZFZmlpbW9PYjRCVVFCZnZjRFhtbW1TYWY1bUV0cFhZRllXK1A3QXUzVEFxK2RjZW1uRzhpcW5YcXBjUWtoTDBua0pKNDdtcmp5djRsVmNPYXBFOXU4VU9Ua2pjN3FCSnI3WUZxYWNCdE11N293c1B4c29YRWpnZ0hFSDlHdGk1cSttSlZwRjA1TXBDemFwcUcwNndrN3RuUW80VjBXZUwrR0sxdWY4SUVwZStza2EwMXR6OFVPcGdsKzdPR3RVWXRraVFqeGhoUC8xNnpSSXlld0xFOFcvckJvVEVOSHUwWnVHQnNLcE1hdnFqU0VXK3hmS2lmYUIvKzdzZUJBbWRkMVlJMUhwandpVkY3ZzZPdXEzRUEwbDJuaGtGTWxocEd2a1NjOWUweUNWR0gxRkNaa3MzSVhyNmZrY1NkWWJ0azJHS1dML0l6Z251QTJQa3hwVVBRM1V1dEQrRy8wUUozVFhrMGsvN0krUzg4dHB4aWRxYzk4YXpsZkV5U29sRGRPd3cwVmxPSGZFbUNQZ0sxTFFXY3Y1T2F3ajNGa3BIRzU4MFZXQ2Z5aExHOUI5am5OaTFVMVBzSWsxVEgxUWpYQUJoQzVKQW56SXIrWWxiTHBETzhPNmFCelJaMmx6VWpTRS83bEpCdzkxMkZ1dTNUZjQiLCJtYWMiOiIyYzJiMzI2YmE0MjcxZTA4ZmEzNDVjMGNlNzNhMDc2NTBmNTcxYWNhYTQ4NmQwNGRjY2MzOGQ3OTRlNDdmYTFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1249619244\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-388563407 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzcvmOKIgVMCCvEHD9HLB5trqF9yJ9GcIs8Pndo9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-388563407\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:22:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpnZXM1aThpYVZ3UWlzZnQ0Wkx1M2c9PSIsInZhbHVlIjoieHMvdjM2anhQano3RkZkWDhXMzZHNGp3cFNuTmo1WnJOdDJvQzhDTTRvZDlsK2ljTURRWlJDUmZoU0RYQXEyd1JrdG9PcmN3TDhwV2dzWVVxa1BqQStRWEFXUCt4TitQemZjVWh4SDVjOEdxWTRRc3FuaEZLdklzSVQ2dGZiS2o3TW1iTzV5NWlpTEpNekM4cTZLRXk4NDc2YWdYOGpTb3p0Zi90Umg5SW5DZGxHVUFIVGJ5MlVONzNpMnN3dkhHcTZmL01lRUYwS2R3ajRPZkZhMTlrbnNkTk85UjYwRFQ1ckExcGxEUHVpaFJFdUpNOEhZeXo0Yk5FSTBieCtCM1J1aHRPL3N4d2xDVUFwNmJkNE1WcjJWc1VJRzZGMVdaeFVPNVJyb3pMcDB2dnIyQUhvQXFMLzlPeFZLbVlpcWxHcnptb3RzQldDQXdQRUdDY0tUU2FKdWQ1aGtNaktReHZXdDNVcWVXMm5ZUWpXWld3R2hhYUZyclJ1ZU9KU2VFeEduNGlkNmFCbzQvUlBjVEFKSFhJZHhzeGtXbU9INGxuVW5VRVlFeW1HZldJVjZwR3pwRHZZd0R1Z2NTc1hMOWVZemw0eWdJTE9yQWhaQWQ3VnZyQ0JqRTU2Q0wzY1QvMXhDbjVNNmpHOHdXbHFuN3Z3K2czVFlUM0kzTWJvRi8iLCJtYWMiOiI0MDg5YzAwMTI1MzA0MjQzNjQ5ZWJmMmVkOGEwMTIyMWIxODM1YTdjNDVhMDVkOGU1MDM5NDAxNzdhZGFlMjRmIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:22:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ii9VT0IzOC9DL01IdnJ3YUtDR1VyR3c9PSIsInZhbHVlIjoiQWJjWithVVZWamw3RnB6a1dJbW41Q0lndENoNzdsVmJvVFF2THVQVnVtZ2FvVlpEV285U1NZbStWejhSRXFTY3dLT0tvWWFHV3lMNm9USjdZRG9YRFhkU1RQbCtDdDMvdk5OWm94ZzNpalMwV1QzRzlFK3JVMExMb1prQzVQUzdyZXlwQXdpRHIwb1k1M3hrL1BTUDMrYk52blFBUDY0dHNjOUtNTEpiWGlCVDAxMTR0QUtqY1A3NGtHK0IySXNuU3c1R1I2OGNBdG11WEVLTUdiSjNwQVFxSkZYZzI3TU1PcWVMT25MNEFPYVRDNmgwdmFNWFNpdXdLc1hlTEFuTEd5a1F4K1dNUXl6aGpsSC9id0JOWHQvM2RVSXhZcXc1VkIzN1ZnNU1TQ3pnR1p1ZTk5dVRGTjB6U0JSRmw4eFBRNk50YURGMXBqZEF3bFZBMlM3SitZeVc0MWYzcXBsOWV3NklxRXpCVjJiVW9VSVdoV2pCVllXQlpadERMMUU4clJyOWtjcnN6cE1hZjg5OFNvUFhiZWFOR2R3cExjSXptWCtsNVZmdWNpUFU0RWZzeUQ5TDJLQUhBUnp0S3hoeVk3YzJiN3JITnNqTjF2bU5tTTlDNjZyWWFKVVJ2OTZ1cUhpbm1Eem5GQldEbXdUQThvdzNwK3NXQUVRdzMzNEMiLCJtYWMiOiI4OTE2Njg3ODdhMGZlY2ZkNjFmODNhNDJkOGMxNDM0ZmZmNzRlNWE4MzJhYzljNjY1MDY1ZWZkZTEyYzk5NTIyIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:22:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpnZXM1aThpYVZ3UWlzZnQ0Wkx1M2c9PSIsInZhbHVlIjoieHMvdjM2anhQano3RkZkWDhXMzZHNGp3cFNuTmo1WnJOdDJvQzhDTTRvZDlsK2ljTURRWlJDUmZoU0RYQXEyd1JrdG9PcmN3TDhwV2dzWVVxa1BqQStRWEFXUCt4TitQemZjVWh4SDVjOEdxWTRRc3FuaEZLdklzSVQ2dGZiS2o3TW1iTzV5NWlpTEpNekM4cTZLRXk4NDc2YWdYOGpTb3p0Zi90Umg5SW5DZGxHVUFIVGJ5MlVONzNpMnN3dkhHcTZmL01lRUYwS2R3ajRPZkZhMTlrbnNkTk85UjYwRFQ1ckExcGxEUHVpaFJFdUpNOEhZeXo0Yk5FSTBieCtCM1J1aHRPL3N4d2xDVUFwNmJkNE1WcjJWc1VJRzZGMVdaeFVPNVJyb3pMcDB2dnIyQUhvQXFMLzlPeFZLbVlpcWxHcnptb3RzQldDQXdQRUdDY0tUU2FKdWQ1aGtNaktReHZXdDNVcWVXMm5ZUWpXWld3R2hhYUZyclJ1ZU9KU2VFeEduNGlkNmFCbzQvUlBjVEFKSFhJZHhzeGtXbU9INGxuVW5VRVlFeW1HZldJVjZwR3pwRHZZd0R1Z2NTc1hMOWVZemw0eWdJTE9yQWhaQWQ3VnZyQ0JqRTU2Q0wzY1QvMXhDbjVNNmpHOHdXbHFuN3Z3K2czVFlUM0kzTWJvRi8iLCJtYWMiOiI0MDg5YzAwMTI1MzA0MjQzNjQ5ZWJmMmVkOGEwMTIyMWIxODM1YTdjNDVhMDVkOGU1MDM5NDAxNzdhZGFlMjRmIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:22:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ii9VT0IzOC9DL01IdnJ3YUtDR1VyR3c9PSIsInZhbHVlIjoiQWJjWithVVZWamw3RnB6a1dJbW41Q0lndENoNzdsVmJvVFF2THVQVnVtZ2FvVlpEV285U1NZbStWejhSRXFTY3dLT0tvWWFHV3lMNm9USjdZRG9YRFhkU1RQbCtDdDMvdk5OWm94ZzNpalMwV1QzRzlFK3JVMExMb1prQzVQUzdyZXlwQXdpRHIwb1k1M3hrL1BTUDMrYk52blFBUDY0dHNjOUtNTEpiWGlCVDAxMTR0QUtqY1A3NGtHK0IySXNuU3c1R1I2OGNBdG11WEVLTUdiSjNwQVFxSkZYZzI3TU1PcWVMT25MNEFPYVRDNmgwdmFNWFNpdXdLc1hlTEFuTEd5a1F4K1dNUXl6aGpsSC9id0JOWHQvM2RVSXhZcXc1VkIzN1ZnNU1TQ3pnR1p1ZTk5dVRGTjB6U0JSRmw4eFBRNk50YURGMXBqZEF3bFZBMlM3SitZeVc0MWYzcXBsOWV3NklxRXpCVjJiVW9VSVdoV2pCVllXQlpadERMMUU4clJyOWtjcnN6cE1hZjg5OFNvUFhiZWFOR2R3cExjSXptWCtsNVZmdWNpUFU0RWZzeUQ5TDJLQUhBUnp0S3hoeVk3YzJiN3JITnNqTjF2bU5tTTlDNjZyWWFKVVJ2OTZ1cUhpbm1Eem5GQldEbXdUQThvdzNwK3NXQUVRdzMzNEMiLCJtYWMiOiI4OTE2Njg3ODdhMGZlY2ZkNjFmODNhNDJkOGMxNDM0ZmZmNzRlNWE4MzJhYzljNjY1MDY1ZWZkZTEyYzk5NTIyIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:22:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1915519248 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1915519248\", {\"maxDepth\":0})</script>\n"}}