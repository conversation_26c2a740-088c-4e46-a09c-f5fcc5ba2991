@extends('layouts.admin')
@section('page-title')
    {{ __('Receipt Orders') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('الرئيسية') }}</a></li>
    <li class="breadcrumb-item">{{ __('إدارة العمليات المالية') }}</li>
    <li class="breadcrumb-item">{{ __('أوامر الاستلام') }}</li>
@endsection

@push('css-page')
<link rel="stylesheet" href="{{ asset('css/datatable/buttons.dataTables.min.css') }}">
<style>
/* تصميم مطابق للصورة */
.page-header {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.page-title {
    color: #495057;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.stats-row {
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

/* تصميم الجدول مطابق للصورة */
.main-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.card-header-custom {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: between;
    align-items: center;
}

.table {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.table thead th {
    background: #f8f9fa;
    color: #495057;
    border-bottom: 2px solid #e9ecef;
    font-weight: 600;
    padding: 12px 8px;
    text-align: center;
    font-size: 0.85rem;
    border-top: none;
}

.table tbody td {
    padding: 10px 8px;
    vertical-align: middle;
    text-align: center;
    border-color: #e9ecef;
    font-size: 0.85rem;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
    border-radius: 0.375rem;
}

.action-btn {
    display: inline-block;
    margin: 0 2px;
}

.action-btn a {
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: white;
    font-size: 12px;
}

.btn-edit {
    background: #ffc107;
}

.btn-view {
    background: #28a745;
}

.btn-print {
    background: #17a2b8;
}

.btn-pdf {
    background: #dc3545;
}

.financial-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    padding: 8px 15px;
    border-radius: 25px;
    font-size: 0.85em;
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

/* إحصائيات ملونة مطابقة للصورة */
.stat-card.orange {
    border-left: 4px solid #ff9800;
}

.stat-card.blue {
    border-left: 4px solid #2196f3;
}

.stat-card.green {
    border-left: 4px solid #4caf50;
}

.stat-card.purple {
    border-left: 4px solid #9c27b0;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #495057;
    margin: 10px 0 5px 0;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

.create-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 0.9rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}
</style>
@endpush

@section('content')
    <!-- Header مطابق للصورة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-6">
                <h1 class="page-title">Receipt Orders</h1>
            </div>
            <div class="col-6 text-end">
                <a href="{{ route('receipt-order.create') }}" class="create-btn">
                    <i class="ti ti-plus"></i>
                    إنشاء أمر استلام
                </a>
            </div>
        </div>
    </div>

    <!-- إحصائيات مطابقة للصورة -->
    <div class="row stats-row">
        <div class="col-md-3">
            <div class="stat-card orange">
                <div class="stat-number">{{ $receiptOrders->count() }}</div>
                <div class="stat-label">إجمالي الأوامر</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card blue">
                <div class="stat-number">{{ $receiptOrders->where('order_type', 'استلام بضاعة')->count() }}</div>
                <div class="stat-label">أوامر الاستلام</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card green">
                <div class="stat-number">{{ $receiptOrders->where('order_type', 'نقل بضاعة')->count() }}</div>
                <div class="stat-label">أوامر النقل</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card purple">
                <div class="stat-number">{{ number_format($receiptOrders->sum('total_amount'), 2) }}</div>
                <div class="stat-label">إجمالي المبلغ</div>
            </div>
        </div>
    </div>

    <!-- الجدول الرئيسي مطابق للصورة -->
    <div class="main-card">
        <div class="card-header-custom">
            <div class="d-flex justify-content-between align-items-center w-100">
                <h6 class="mb-0">Receipt Orders List</h6>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge badge-info">{{ $receiptOrders->count() }} أوامر</span>
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table datatable">
                <thead>
                    <tr>
                        <th>ACTIONS</th>
                        <th>CREATED DATE</th>
                        <th>DATE</th>
                        <th>TOTAL AMOUNT</th>
                        <th>CREATED BY</th>
                        <th>WAREHOUSE</th>
                        <th>VENDOR/SOURCE</th>
                        <th>ORDER TYPE</th>
                        <th>ORDER NUMBER</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($receiptOrders as $order)
                        <tr>
                            <td>
                                <div class="action-btn">
                                    @if(Auth::user()->can('manage warehouse') || Auth::user()->can('show financial record') || Auth::user()->hasRole('company'))
                                        <a href="{{ route('receipt-order.edit', $order['id']) }}" class="btn-edit" title="تحرير العمليات المالية">
                                            <i class="ti ti-edit"></i>
                                        </a>
                                    @endif
                                    <a href="{{ route('receipt-order.show', $order['id']) }}" class="btn-view" title="عرض">
                                        <i class="ti ti-eye"></i>
                                    </a>
                                    <a href="{{ route('receipt-order.print', $order['id']) }}" class="btn-print" title="طباعة" target="_blank">
                                        <i class="ti ti-printer"></i>
                                    </a>
                                    <a href="{{ route('receipt-order.pdf', $order['id']) }}" class="btn-pdf" title="PDF" target="_blank">
                                        <i class="ti ti-file-type-pdf"></i>
                                    </a>
                                </div>
                            </td>
                            <td>{{ $order['created_at']->format('Y-m-d H:i') }}</td>
                            <td>{{ \App\Models\Utility::getDateFormated($order['date']) }}</td>
                            <td>
                                @if($order['total_amount'] > 0)
                                    <strong class="text-success">{{ number_format($order['total_amount'], 2) }}</strong>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                <span class="badge badge-info">{{ $order['creator_name'] }}</span>
                            </td>
                            <td>{{ $order['warehouse_name'] }}</td>
                            <td>{{ $order['vendor_name'] }}</td>
                            <td>
                                @if($order['type'] === 'استلام بضاعة')
                                    <span class="badge badge-success">{{ $order['type'] }}</span>
                                @elseif($order['type'] === 'نقل بضاعة')
                                    <span class="badge badge-info">{{ $order['type'] }}</span>
                                @else
                                    <span class="badge badge-warning">{{ $order['type'] }}</span>
                                @endif
                            </td>
                            <td>
                                <strong class="text-primary">{{ $order['reference_number'] }}</strong>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    @push('script-page')
    <script>
    $(document).ready(function() {
        $('.datatable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
            },
            "order": [[ 1, "desc" ]],
            "pageLength": 25,
            "responsive": true,
            "dom": 'Bfrtip',
            "buttons": [
                {
                    extend: 'excel',
                    text: '<i class="ti ti-file-spreadsheet"></i> Excel',
                    className: 'btn btn-success btn-sm'
                },
                {
                    extend: 'pdf',
                    text: '<i class="ti ti-file-type-pdf"></i> PDF',
                    className: 'btn btn-danger btn-sm'
                },
                {
                    extend: 'print',
                    text: '<i class="ti ti-printer"></i> Print',
                    className: 'btn btn-info btn-sm'
                }
            ]
        });
    });
    </script>
    @endpush
@endsection
