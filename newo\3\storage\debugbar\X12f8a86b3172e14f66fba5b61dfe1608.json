{"__meta": {"id": "X12f8a86b3172e14f66fba5b61dfe1608", "datetime": "2025-06-16 15:22:46", "utime": **********.684234, "method": "PUT", "uri": "/receipt-order/1", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087364.727485, "end": **********.684267, "duration": 1.9567821025848389, "duration_str": "1.96s", "measures": [{"label": "Booting", "start": 1750087364.727485, "relative_start": 0, "end": **********.155552, "relative_end": **********.155552, "duration": 1.4280669689178467, "duration_str": "1.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.155584, "relative_start": 1.4280991554260254, "end": **********.684271, "relative_end": 4.0531158447265625e-06, "duration": 0.5286870002746582, "duration_str": "529ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52030272, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT receipt-order/{receipt_order}", "middleware": "web, verified, auth, XSS, revalidate", "as": "receipt-order.update", "controller": "App\\Http\\Controllers\\ReceiptOrderController@update", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=602\" onclick=\"\">app/Http/Controllers/ReceiptOrderController.php:602-748</a>"}, "queries": {"nb_statements": 26, "nb_failed_statements": 0, "accumulated_duration": 0.045270000000000005, "accumulated_duration_str": "45.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.273079, "duration": 0.00684, "duration_str": "6.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 15.109}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.311716, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 15.109, "width_percent": 2.584}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.376402, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 17.694, "width_percent": 5.059}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.386039, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 22.752, "width_percent": 2.761}, {"sql": "select count(*) as aggregate from `venders` where `id` = '4'", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.4286351, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 25.514, "width_percent": 3.358}, {"sql": "select count(*) as aggregate from `warehouses` where `id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.438081, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 28.871, "width_percent": 3.181}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '3'", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.4623208, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 32.052, "width_percent": 2.496}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '5'", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.470894, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 34.548, "width_percent": 1.966}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 626}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.491138, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:626", "source": "app/Http/Controllers/ReceiptOrderController.php:626", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=626", "ajax": false, "filename": "ReceiptOrderController.php", "line": "626"}, "connection": "ty", "start_percent": 36.514, "width_percent": 0}, {"sql": "select * from `receipt_orders` where `id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 639}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4935539, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:639", "source": "app/Http/Controllers/ReceiptOrderController.php:639", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=639", "ajax": false, "filename": "ReceiptOrderController.php", "line": "639"}, "connection": "ty", "start_percent": 36.514, "width_percent": 3.645}, {"sql": "select * from `receipt_order_products` where `receipt_order_products`.`receipt_order_id` = 1 and `receipt_order_products`.`receipt_order_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 642}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.508512, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:642", "source": "app/Http/Controllers/ReceiptOrderController.php:642", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=642", "ajax": false, "filename": "ReceiptOrderController.php", "line": "642"}, "connection": "ty", "start_percent": 40.159, "width_percent": 4.462}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 3 limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 478}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 657}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.518607, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:478", "source": "app/Http/Controllers/ReceiptOrderController.php:478", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=478", "ajax": false, "filename": "ReceiptOrderController.php", "line": "478"}, "connection": "ty", "start_percent": 44.621, "width_percent": 2.739}, {"sql": "update `warehouse_products` set `quantity` = 0, `warehouse_products`.`updated_at` = '2025-06-16 15:22:46' where `id` = 2", "type": "query", "params": [], "bindings": ["0", "2025-06-16 15:22:46", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 494}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 657}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5272381, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:494", "source": "app/Http/Controllers/ReceiptOrderController.php:494", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=494", "ajax": false, "filename": "ReceiptOrderController.php", "line": "494"}, "connection": "ty", "start_percent": 47.36, "width_percent": 2.916}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 5 limit 1", "type": "query", "params": [], "bindings": ["8", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 478}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 657}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.534644, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:478", "source": "app/Http/Controllers/ReceiptOrderController.php:478", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=478", "ajax": false, "filename": "ReceiptOrderController.php", "line": "478"}, "connection": "ty", "start_percent": 50.276, "width_percent": 2.099}, {"sql": "update `warehouse_products` set `quantity` = 0, `warehouse_products`.`updated_at` = '2025-06-16 15:22:46' where `id` = 4", "type": "query", "params": [], "bindings": ["0", "2025-06-16 15:22:46", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 494}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 657}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.542419, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:494", "source": "app/Http/Controllers/ReceiptOrderController.php:494", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=494", "ajax": false, "filename": "ReceiptOrderController.php", "line": "494"}, "connection": "ty", "start_percent": 52.375, "width_percent": 3.048}, {"sql": "delete from `receipt_order_products` where `receipt_order_products`.`receipt_order_id` = 1 and `receipt_order_products`.`receipt_order_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 675}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.550807, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:675", "source": "app/Http/Controllers/ReceiptOrderController.php:675", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=675", "ajax": false, "filename": "ReceiptOrderController.php", "line": "675"}, "connection": "ty", "start_percent": 55.423, "width_percent": 7.687}, {"sql": "insert into `receipt_order_products` (`receipt_order_id`, `product_id`, `quantity`, `unit_cost`, `expiry_date`, `is_return`, `notes`, `total_cost`, `updated_at`, `created_at`) values (1, '3', '10.00', '1.00', '2025-06-18 00:00:00', 0, '', 10, '2025-06-16 15:22:46', '2025-06-16 15:22:46')", "type": "query", "params": [], "bindings": ["1", "3", "10.00", "1.00", "2025-06-18 00:00:00", "0", "", "10", "2025-06-16 15:22:46", "2025-06-16 15:22:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 690}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.561987, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:690", "source": "app/Http/Controllers/ReceiptOrderController.php:690", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=690", "ajax": false, "filename": "ReceiptOrderController.php", "line": "690"}, "connection": "ty", "start_percent": 63.11, "width_percent": 3.601}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = '3' limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 478}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 698}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.570636, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:478", "source": "app/Http/Controllers/ReceiptOrderController.php:478", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=478", "ajax": false, "filename": "ReceiptOrderController.php", "line": "478"}, "connection": "ty", "start_percent": 66.711, "width_percent": 2.54}, {"sql": "update `warehouse_products` set `quantity` = 10, `warehouse_products`.`updated_at` = '2025-06-16 15:22:46' where `id` = 2", "type": "query", "params": [], "bindings": ["10", "2025-06-16 15:22:46", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 494}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 698}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5783598, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:494", "source": "app/Http/Controllers/ReceiptOrderController.php:494", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=494", "ajax": false, "filename": "ReceiptOrderController.php", "line": "494"}, "connection": "ty", "start_percent": 69.251, "width_percent": 2.43}, {"sql": "select * from `product_expiry_dates` where (`product_id` = '3' and `warehouse_id` = '8') limit 1", "type": "query", "params": [], "bindings": ["3", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 716}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.587539, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:716", "source": "app/Http/Controllers/ReceiptOrderController.php:716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=716", "ajax": false, "filename": "ReceiptOrderController.php", "line": "716"}, "connection": "ty", "start_percent": 71.681, "width_percent": 8.107}, {"sql": "update `product_expiry_dates` set `created_by` = 15, `product_expiry_dates`.`updated_at` = '2025-06-16 15:22:46' where `id` = 1", "type": "query", "params": [], "bindings": ["15", "2025-06-16 15:22:46", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 716}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5964708, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:716", "source": "app/Http/Controllers/ReceiptOrderController.php:716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=716", "ajax": false, "filename": "ReceiptOrderController.php", "line": "716"}, "connection": "ty", "start_percent": 79.788, "width_percent": 2.827}, {"sql": "insert into `receipt_order_products` (`receipt_order_id`, `product_id`, `quantity`, `unit_cost`, `expiry_date`, `is_return`, `notes`, `total_cost`, `updated_at`, `created_at`) values (1, '5', '1000', '8.00', '2025-06-24 00:00:00', 0, '', 8000, '2025-06-16 15:22:46', '2025-06-16 15:22:46')", "type": "query", "params": [], "bindings": ["1", "5", "1000", "8.00", "2025-06-24 00:00:00", "0", "", "8000", "2025-06-16 15:22:46", "2025-06-16 15:22:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 690}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6033459, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:690", "source": "app/Http/Controllers/ReceiptOrderController.php:690", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=690", "ajax": false, "filename": "ReceiptOrderController.php", "line": "690"}, "connection": "ty", "start_percent": 82.615, "width_percent": 2.85}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = '5' limit 1", "type": "query", "params": [], "bindings": ["8", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 478}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 698}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.612533, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:478", "source": "app/Http/Controllers/ReceiptOrderController.php:478", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=478", "ajax": false, "filename": "ReceiptOrderController.php", "line": "478"}, "connection": "ty", "start_percent": 85.465, "width_percent": 2.916}, {"sql": "update `warehouse_products` set `quantity` = 1000, `warehouse_products`.`updated_at` = '2025-06-16 15:22:46' where `id` = 4", "type": "query", "params": [], "bindings": ["1000", "2025-06-16 15:22:46", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 494}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 698}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.61933, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:494", "source": "app/Http/Controllers/ReceiptOrderController.php:494", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=494", "ajax": false, "filename": "ReceiptOrderController.php", "line": "494"}, "connection": "ty", "start_percent": 88.381, "width_percent": 2.452}, {"sql": "select * from `product_expiry_dates` where (`product_id` = '5' and `warehouse_id` = '8') limit 1", "type": "query", "params": [], "bindings": ["5", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 716}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6289961, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:716", "source": "app/Http/Controllers/ReceiptOrderController.php:716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=716", "ajax": false, "filename": "ReceiptOrderController.php", "line": "716"}, "connection": "ty", "start_percent": 90.833, "width_percent": 2.651}, {"sql": "update `product_expiry_dates` set `created_by` = 15, `product_expiry_dates`.`updated_at` = '2025-06-16 15:22:46' where `id` = 2", "type": "query", "params": [], "bindings": ["15", "2025-06-16 15:22:46", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 716}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.635154, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:716", "source": "app/Http/Controllers/ReceiptOrderController.php:716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=716", "ajax": false, "filename": "ReceiptOrderController.php", "line": "716"}, "connection": "ty", "start_percent": 93.484, "width_percent": 2.408}, {"sql": "update `receipt_orders` set `total_amount` = 8010, `receipt_orders`.`updated_at` = '2025-06-16 15:22:46' where `id` = 1", "type": "query", "params": [], "bindings": ["8010", "2025-06-16 15:22:46", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 732}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6427891, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:732", "source": "app/Http/Controllers/ReceiptOrderController.php:732", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=732", "ajax": false, "filename": "ReceiptOrderController.php", "line": "732"}, "connection": "ty", "start_percent": 95.891, "width_percent": 4.109}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 734}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.662761, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:734", "source": "app/Http/Controllers/ReceiptOrderController.php:734", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=734", "ajax": false, "filename": "ReceiptOrderController.php", "line": "734"}, "connection": "ty", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\WarehouseProduct": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\ReceiptOrderProduct": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FReceiptOrderProduct.php&line=1", "ajax": false, "filename": "ReceiptOrderProduct.php", "line": "?"}}, "App\\Models\\ProductExpiryDate": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductExpiryDate.php&line=1", "ajax": false, "filename": "ProductExpiryDate.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ReceiptOrder": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FReceiptOrder.php&line=1", "ajax": false, "filename": "ReceiptOrder.php", "line": "?"}}}, "count": 11, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-429603704 data-indent-pad=\"  \"><span class=sf-dump-note>manage warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-429603704\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.402909, "xdebug_link": null}]}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "تم تحديث أمر الاستلام بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/receipt-order/1", "status_code": "<pre class=sf-dump id=sf-dump-123605670 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-123605670\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1733826716 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1733826716\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-24354348 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>vendor_id</span>\" => \"<span class=sf-dump-str>4</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>invoice_number</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8475</span>\"\n  \"<span class=sf-dump-key>invoice_total</span>\" => \"<span class=sf-dump-str title=\"6 characters\">900.00</span>\"\n  \"<span class=sf-dump-key>invoice_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-08</span>\"\n  \"<span class=sf-dump-key>notes</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>products</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>unit_cost</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1.00</span>\"\n      \"<span class=sf-dump-key>expiry_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-18</span>\"\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1000</span>\"\n      \"<span class=sf-dump-key>unit_cost</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8.00</span>\"\n      \"<span class=sf-dump-key>expiry_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-24</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-24354348\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-707556156 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">457</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087347611%7C6%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdEZXFzV0hSQi9Ha2VQNURQL2owRmc9PSIsInZhbHVlIjoiVC9JenVWN1ZyK1B3ZmY5akY4NkFlZkREdXAwZWZUNWh3dGU5b0xRWG9zSFVWTW51RUduTDlpTnBqay9zcFdXN09mUjdrQkZ0TU5mOE00N0Rib2dPcTljTHh4VjlJcTRwRkxQNDRiM1dqUjB4TlhyNHNGZDBQRzJoUDZrZ2Y4ZjRvVFNTeVhsdnNiU0JDSldFVE0wMGU0L0tUZFI1V0J1WjYvay9CRlZYZlE0NGtSdXRsNWZYY2RTY09IcVJmSndZRHhQUHR1ZG9tVDAyT3VqN2RNRmVIQlJmOFRGaWVFb1hoNFBNRVlMaEZQV2M4VE9XM1lkeVV0c2tHaVEwNUJodVFRbTVxR21Ic3VrWll1ZVpvQ3dGajFYc2d4Z09ybVhpU1VGQVlqdUsvNktwdlMyVE9ZQnRYSTlTRDZqUmZ2dGhUdDJNSDJweE9Rb3FtMEpWUmQzSUwrTnhFRmxpbjZxTEpSdGxlUUZ2c25HOXhGSkY1emxKMjROQlltMXRDTXF1aHZsZGxFblFKQ1VYT1JWSUh4bm05RFE2dkRtc3RsK1N3YTdydEhTU1dLbUdUR3Z6SkhqUnNLNHBYcVAxYlJxL0ZmbmdyVTJsQXJKeDhvTkNJeFNGNjlzUE1lYzlMVkcvbm1lUy9pSDRTTHcwb1RZVzFhaWdMWlQ4UFpUWTBjSE4iLCJtYWMiOiJiYTVhMjRiY2E3MmU5ZTgzYWRjZTM4MDI3ZGVhYzUxYmZhYzhiODFlNzk3ZGFhYTFlMGRmMzc4YTkxZWI0NGM0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IklsYmEreEQ0dGlkVisxRXpibUJxbWc9PSIsInZhbHVlIjoiVlNPTmx0QUx2NDBEdkQvR3dYbHNWTm9IcnhPY2lWcEpPME5WaitGb2MzeWI2VnlnYkx4Y2VKMm9jNkR4OU1PUkhJaFk5Mm8zb1loUXYxbDFLdTJMbkw1eEtJbEFpdG9aNjd3blpiRHJ2MFZKTHhhWnlFT3JiaktOcitRMjJBeUJZanlvakNZNjYzb1p0RlQyaVNaWDY5VmEyQUFwa1NrdkZHaUlBbCs5ODFlZkZIcVNRZTJNZGE4TWo4UnhoVjlUTHQyLy9JdWRraUtqMmZSWjJhcDBQTzlXT0pkV2pFYWZibGYrdVZFTlJGdkhFY3RUVW9zUHgyZG8wdElIZGJySkIvVVZYVDdSVUJVaDErWXR4c1I0OC8zVGt6Tmlhd050ZzZjSjA0VXVVRE1QN2JqRWNpVFpLME5MZUxXOGF1KzNwcjFPTEFhSjJDLzNReHh2VzJ5R3JuVmtHVzZ1WXNTWWd0eGRML0xRNTdkSjRyTTBzZUxEdXI2emhPNGNIM1E5U2NsN3h2MFpFRnVKMDZCb0xobk1vQ05NMHFHT2NzTWVDM0NqRmFJSkVoR2RKQTdwdW1mMEE3V3VrWXh5RG1OeHFQOVVPWHdZUjlnZWdHTWdYa1EzV3RSM3hCeFZHb0hoRC9keVZobmZTdDlvaWNhZFRlaVFoUy94RHRpRy9KYkgiLCJtYWMiOiJmYzdlN2E0ZmQxOTIyNDUwZTg3OWU3OGI2NzZjYzRkMDA1MTg4NmM5NmM1MDk2MzI0ZjdjZDUxMmQ1OTg5MTJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-707556156\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2077668589 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzcvmOKIgVMCCvEHD9HLB5trqF9yJ9GcIs8Pndo9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2077668589\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1218198886 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:22:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost/receipt-order/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNZMHFGRDB4UENHNFQzNS8ySUk4U3c9PSIsInZhbHVlIjoiRWtGRlU1dzdrTE5BcHlLT2pUckhPb2xvZEx6RVhlam1vZTlqbXFoRTZIZ0hodkVraTBPdkRJTlhrZHdoQ0kySjFibDk0TlU1QnA3MDBibERxZUlOcnlBenpBTURQNmxZSDVLQmJWbmRNQjBCK2NVODAyMFJJaFhqeWxWZmk5NWlRbWx0RXlGODNyUGZpSE9CY0UzRjBpQW4wMHorWjFYQjRucW1GRHlOR3Z3VUhCVXNTbERPNWtjVGc0YlplUEV4cG9iNDJyb1YrbHZpMXdNOWtuelgybi9EOUpLcWNqVXFIZVRRUitkUGhRSFF6K2ROb2VWaXBiRmtKMXIrTUxycVVrcStDUXo3Z1hvcVBpR0RQSnp4YjdxWXhuOElaR010NnJEaElka05RVGt0a0JKcFpFQklWUWlRRUllR1JoK2hVNHorRitXc3lVdFkreGI4TGU4bWdwRFhtbkRhSkxiaXdzVitIYzI0TWt4SXhGV2FzUFFzMkt6WlJ6V0VLNlRmUG1TUjI5dXhPSzFHZElhemRmQlh1QmRBZUxxTi9aVnNVcFpFUVVaKzNHSUhiL0t3dWRmSG1lNFpCVUFFK21lYktjaVlQRjI3UGhVUnArd2N1OTYzSG1razNtWkVKU2F4T3poK3dmdUJkcmhqUGxnNU5LcFQ5dXI1UjAyN29jYUIiLCJtYWMiOiJiZTgwMGE2NmViOTI3N2IyYWYxOWE1OTE0NTJlMzExZTViM2NmZDJhODA0ZGQwNjNiZjY5ZmZjODY3ZGQ1NTYyIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:22:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ilo2OGM1a3huVFhwbnVnOUdqSXIzYXc9PSIsInZhbHVlIjoiZklKUGhJcVlnbjJxL0pCZkxjQW53ekxuTzEyckc5STIwUHRXUDladlZGYVVaZWpyRlNZVEVhYlVuZjN3WlBaQXRxYVM2SlRLcFpDdGYwWWFKKzRrV1FkRjEzRUlPVEJ1MzBZNFY1ZDc4RXBIZEZzZzBDSStTSmEyN0VNa1l1bndlcEtMWlE5TndqWVlXSUhSWVBpR21IVEoxT2lqdm04MlFlT0xOdVRGazZ1UTh6MWY5SjZBU25Bdy9MdEFVZ292aWJhdHRZZ1pnOVhWN1R0elp2VHFySGpTdERiQlVvaGRKQ2RwdFU0ZWpIMDdnbVljSTVHZm9ITnkvOXJpNkthWWRpNXBZQjAvZ1prTHUyYXJHNkdRTDExL0w3RjRLMjRoNlllVnNmMGx3Wk9WL1diWHBVdWVwbml4cHdPcHBzNUdFdThoTU5RNnNmZ2NpZ25lU2xjQnZoYktycEMvQTh6Rkx0S20xeVc0cWhlUXNZMlFMUTdVOWJQUnd4bThDU3B2ZWxENWxCT3hxWHg0Tys3VUtPVWc3YUdWT0czbm1XV1RCQVdmREQ4SERwdGlmMHFlRDdvSHJOcGR2RkxLQnRSN0xkQkNiN3M0MnlXSWtLZG4yVGs3dmhCTW15Q205cHlNczZXY3l6TjBkaXlqalJkNEQzMUFNMTR3STFwSmduSUEiLCJtYWMiOiIzYzhkZjhhZTdmOTg4YmRlMGNhYjNlZWZjZWFiMDI4OThiNDY3ODk2MWIxNzA0YmJiZTBmNzM4ZDdlZGE3MjIzIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:22:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNZMHFGRDB4UENHNFQzNS8ySUk4U3c9PSIsInZhbHVlIjoiRWtGRlU1dzdrTE5BcHlLT2pUckhPb2xvZEx6RVhlam1vZTlqbXFoRTZIZ0hodkVraTBPdkRJTlhrZHdoQ0kySjFibDk0TlU1QnA3MDBibERxZUlOcnlBenpBTURQNmxZSDVLQmJWbmRNQjBCK2NVODAyMFJJaFhqeWxWZmk5NWlRbWx0RXlGODNyUGZpSE9CY0UzRjBpQW4wMHorWjFYQjRucW1GRHlOR3Z3VUhCVXNTbERPNWtjVGc0YlplUEV4cG9iNDJyb1YrbHZpMXdNOWtuelgybi9EOUpLcWNqVXFIZVRRUitkUGhRSFF6K2ROb2VWaXBiRmtKMXIrTUxycVVrcStDUXo3Z1hvcVBpR0RQSnp4YjdxWXhuOElaR010NnJEaElka05RVGt0a0JKcFpFQklWUWlRRUllR1JoK2hVNHorRitXc3lVdFkreGI4TGU4bWdwRFhtbkRhSkxiaXdzVitIYzI0TWt4SXhGV2FzUFFzMkt6WlJ6V0VLNlRmUG1TUjI5dXhPSzFHZElhemRmQlh1QmRBZUxxTi9aVnNVcFpFUVVaKzNHSUhiL0t3dWRmSG1lNFpCVUFFK21lYktjaVlQRjI3UGhVUnArd2N1OTYzSG1razNtWkVKU2F4T3poK3dmdUJkcmhqUGxnNU5LcFQ5dXI1UjAyN29jYUIiLCJtYWMiOiJiZTgwMGE2NmViOTI3N2IyYWYxOWE1OTE0NTJlMzExZTViM2NmZDJhODA0ZGQwNjNiZjY5ZmZjODY3ZGQ1NTYyIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:22:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ilo2OGM1a3huVFhwbnVnOUdqSXIzYXc9PSIsInZhbHVlIjoiZklKUGhJcVlnbjJxL0pCZkxjQW53ekxuTzEyckc5STIwUHRXUDladlZGYVVaZWpyRlNZVEVhYlVuZjN3WlBaQXRxYVM2SlRLcFpDdGYwWWFKKzRrV1FkRjEzRUlPVEJ1MzBZNFY1ZDc4RXBIZEZzZzBDSStTSmEyN0VNa1l1bndlcEtMWlE5TndqWVlXSUhSWVBpR21IVEoxT2lqdm04MlFlT0xOdVRGazZ1UTh6MWY5SjZBU25Bdy9MdEFVZ292aWJhdHRZZ1pnOVhWN1R0elp2VHFySGpTdERiQlVvaGRKQ2RwdFU0ZWpIMDdnbVljSTVHZm9ITnkvOXJpNkthWWRpNXBZQjAvZ1prTHUyYXJHNkdRTDExL0w3RjRLMjRoNlllVnNmMGx3Wk9WL1diWHBVdWVwbml4cHdPcHBzNUdFdThoTU5RNnNmZ2NpZ25lU2xjQnZoYktycEMvQTh6Rkx0S20xeVc0cWhlUXNZMlFMUTdVOWJQUnd4bThDU3B2ZWxENWxCT3hxWHg0Tys3VUtPVWc3YUdWT0czbm1XV1RCQVdmREQ4SERwdGlmMHFlRDdvSHJOcGR2RkxLQnRSN0xkQkNiN3M0MnlXSWtLZG4yVGs3dmhCTW15Q205cHlNczZXY3l6TjBkaXlqalJkNEQzMUFNMTR3STFwSmduSUEiLCJtYWMiOiIzYzhkZjhhZTdmOTg4YmRlMGNhYjNlZWZjZWFiMDI4OThiNDY3ODk2MWIxNzA0YmJiZTBmNzM4ZDdlZGE3MjIzIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:22:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1218198886\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1518830783 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"27 characters\">&#1578;&#1605; &#1578;&#1581;&#1583;&#1610;&#1579; &#1571;&#1605;&#1585; &#1575;&#1604;&#1575;&#1587;&#1578;&#1604;&#1575;&#1605; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1518830783\", {\"maxDepth\":0})</script>\n"}}