{"__meta": {"id": "X98a9e2ab5ed63546cbb818fe744eb9f0", "datetime": "2025-06-17 06:54:52", "utime": **********.279286, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143290.610779, "end": **********.279324, "duration": 1.6685450077056885, "duration_str": "1.67s", "measures": [{"label": "Booting", "start": 1750143290.610779, "relative_start": 0, "end": **********.011087, "relative_end": **********.011087, "duration": 1.4003078937530518, "duration_str": "1.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.011116, "relative_start": 1.4003369808197021, "end": **********.279328, "relative_end": 4.0531158447265625e-06, "duration": 0.26821208000183105, "duration_str": "268ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45171048, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022320000000000003, "accumulated_duration_str": "22.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1635509, "duration": 0.01989, "duration_str": "19.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.113}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.224281, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.113, "width_percent": 4.57}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.249501, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.683, "width_percent": 6.317}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-219373604 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-219373604\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1333475680 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1333475680\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-999980684 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-999980684\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1710863842 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1930 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; XSRF-TOKEN=eyJpdiI6IjlReTdCZEhSTDh0YmZtb3ZxbmZhSnc9PSIsInZhbHVlIjoiN2hza3RybGVVUmRzdGh4N01ETjkzcWE0WmJmRHhQT2dneFJSUlRYUEtDcmVvcGNaSCs0UEV5Q0d0M0NDMWZKQ2hyNCtaeXZXRmRPeml2RVIyTGhKU3h1bklYSWJBb3hIWGJ6YnFxWEt1cVBwZWFSSUt2M2J6ZkR3cTBGSXdVcGZqU0hQUDlDeUtoa2d1cmM4cDgweVByTjhkTDVIYk1sTmNaL3hMblNibVhTRFFHSkhXVlJveVh6R3NWQXQxL3dzd2RnRmZ6MDVHMlFQS1BwS2ppaGxXUXpLZ3V2MHVzTXhPS296ZkVJRzNTK0syalJheThwMGRQQVE5Y0k2dlpNU01VQlBKU0htaUVuSnJjZTVHUGxTMnlCZW1lMEU1MkRzdGFTVU1BdFlzMG5kdW5rNTdzWGZhVllZcXJodHU5Z0trem5wTHFTMHdJRktIckg5ZzBRamFGVGtqelZIVWJCU0xwWlExeXVIeHF2ZXJJWWVJM1FhSnY2UVBsMDdXbExvQWdmbkhMQWxPZ3ZnazdvM29rM3NKWms0UFlmYzF6WjFhdjRIS09vT2kydkZFNUVUWndVQmFENDJlYmgrcVU1ZnFudytlUko4U0I0THZkZk11Y1pKUGRIdDV2eERzc0ttTmZLb1d3Y2Z1TFVjRDJ0T0l0NlJhbnVvbS9LbUY1amIiLCJtYWMiOiJlMDNkNjhkMTkyYzY5MzIxMzFmYWQ3YWIwZDA4NDYwOTczZGE1Y2ExMjExNDlkMWU5ZmU4ZTE4NTgyODljNjVkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjQ2OGY3eHlRQ05VTjF5bWtVNUhaVXc9PSIsInZhbHVlIjoicTV1WG1KUUN5WmlidkVtQWZpcXlaSGFLU0MxMUQxTFppTW50eFcwSlRveVJIY2RSZWpXWEloVVpIMTUwL2xMYzUrV3EzRU1ZSTNSTEltUGFJdTlCWlpsSlRyajAyb0xxOUcrejFwNExGUzFiOFNESnY1a0J4dEFWSnZ2SEUxUUN3RHhlSDcvUmdiQVBKeSs3cm40UXVGYzVhZ0dBZUl5OTB1aTdQYWJRMzRrU1A0RVpkS2pvcWVNS05FYVFLcmFvRktnOFBJTFZWR3RhOElnTjFvRUtOTHNHVkpPdFpIa25tck5tQ1lGS1ZVazhxK09QbHhBQ3kvbmxFa01YdGZZWVMzNWdxRnV4K21iRmE4S255R2xRZ0o2bHJSeTJQL3l5cHRNbmlGMGpoVWJtU0VieXQ0cndWVkkzeDcyVjUwRFVLNHMyc1RvWVYwV3B5R2JOTGdHYnhza2dLc1RHa2lSOFJwaFAvUk9wcmdnM3Y0S2Y2Mi91ckVPQmFjQWwzSFo3VmI3bDlXT25JaHF4T2FGUVBlREloREtaREFNVWVOQjBWNEt0RFZhdW9oamYyZnRrdVJwMEp1dVJIb0pMb1pBWWJUajZTbTJjODNYMkxzMzhHaVpLVWtTWmFHQnNyVXRZTi9vcmZVeWRrTnVNdUJPcnRDNW5uQXNqcmN0TnFpL0wiLCJtYWMiOiIzMDI2YzA4NjVjMDU4NmE4MTI1MGFhNWI1YTQ2M2JlNTQ2OWNiZmE0ZDM3MDQzNTkwMDRkMzJlOWM3YTZiMTdhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1710863842\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-412508668 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-412508668\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1133546417 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:54:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdYUzVNUG43RTJtY3B0NWlGcWk2dEE9PSIsInZhbHVlIjoiZnJpMnA1d010aTU3aThBand5Y1ZIdjhkMzN1eU1ldlVGQ2dQenRUL2RHWjB3dXliUXFBcS9lTENpZ0FjTTZjaVc1L1owRzZxdStMRDcwOHQxZVNoL1JhVmljMTIrc0Q3WEpVdGVqZTNoanFJbnhHRHNLNDN2ZVdOL2t2NEtpRlJmOENMdVBHMjdiZWUzWlB1dmRkcFZGL2dMeU9oYkdvMGwrZzNuU3FTeDE3QUJuYWJPcktBVldxV0FXVS9Zc0hmTHQ1YnovUGRrczI5WUxlOUUvd3FUSDBEWEh3Z2h5b1BtdWhzalpNdzl4akZkWVg5bHVOZS82NFFobTNHMGpUSENGVXpWcFNXQUhLMGJ3aVZjVVV6RVFlUXZqSDNVeGVFeXppc0xGQ2lXNDVmajNCM0V0UEFKcWN4NENxQUdWMEZvazhMNHBRcjV4eXk3UUVNbXpoOS82MlRqYkxza1hIV0tOem9meThCcEMxUHlaT20zMFRLWWRlTnhYaG0zVHB0NHNsanZEaTBqend4cVlUWWZweDhkNVFSTDlROFF1TlpxNFF6QVBCUzArZmlJYi9ISDB3QmdJNFBqemtxRVZXR1F3UTJIMzE3YnZhTWxuWmJEb1ljMDluL1NCK0orb2J4NVBwZUxZSUY2ZXpWbEM3UmFqb2lzUVpPZzRob0FEbkciLCJtYWMiOiI2YmUyNjY4MjZiYTVjNWViYjBjMmU4NDFhZjc3ZjRlNjYzYzNlY2MyMDA2OTM4OTAyNjVkYzg3ZDYyYzg5MWQwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:54:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im9SZHU3K2tYc0dQSEdYbG1zRGpHUkE9PSIsInZhbHVlIjoic0dERGxWVnFHK05LcWYyZGJMTmNQUG5menNnejJpWU90WVpjUkhaVTVKNUh6VDZJSC95bTBWVmp1bXpIcFJaUjAwMkoyY3lSemZDblR6dk5kTTJpQXJOSUFydmFOOU5VNG9LeFg0STVWaENvVERsd3ltMWRSMzFlbWk2cVk3YmEvbWtXVUx4TldNYm1CTUtwbWhEeGR1elc3L0hYb2UvdnF1UVlhQit6VUlhYmkvcGVqY0RsU2lJb0Z6QWVDeDdHWFVXejVEeUlCOHdCR053cW5GL2NSSkluN3FWWjR0STlYYUdvNDNJbktzWUZqdFdvVC9LN1BKb0ozK1pEU0UyQmN5OFFaVzdmN2U2WE1TVHdZOVpTemdUNzc4cWZ2YjRUeFEzT0FIUmFISzRzaFBHQVAzL0hyVHNuVUdMQklvNlRpbHhYUmIvRkcrV1AzUGh0NFJUaWVDVDZZcXNDV0U4eGFsYm1zWS9LU21yMWxYWEdiR1pod2dKdm9VN0xCN05mMmZLemxjVVlTWGZ4cFBqZXZzdEpXSHJLUW5Oa3NXRFhWRmFFUFlJM2ZlTXNrU1BQejFySkwyOWE3bHhDMWtEUm1meGpDM2Rsbm0xZlFnZ3E5elVaOURWRjRiall0bWpGOFlUZGpMcVpHc0REbW9IcVhsOVhhV09YTkw4MDRiY1EiLCJtYWMiOiI3YTI1NDBkMzAwOGMwZmE0ZmJmZTdlZjJmM2MwNGU4NmFiYjM2ZGFlNjRiYmZiMWI4MGFkZjk4MWE1ZWMyMGQ0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:54:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdYUzVNUG43RTJtY3B0NWlGcWk2dEE9PSIsInZhbHVlIjoiZnJpMnA1d010aTU3aThBand5Y1ZIdjhkMzN1eU1ldlVGQ2dQenRUL2RHWjB3dXliUXFBcS9lTENpZ0FjTTZjaVc1L1owRzZxdStMRDcwOHQxZVNoL1JhVmljMTIrc0Q3WEpVdGVqZTNoanFJbnhHRHNLNDN2ZVdOL2t2NEtpRlJmOENMdVBHMjdiZWUzWlB1dmRkcFZGL2dMeU9oYkdvMGwrZzNuU3FTeDE3QUJuYWJPcktBVldxV0FXVS9Zc0hmTHQ1YnovUGRrczI5WUxlOUUvd3FUSDBEWEh3Z2h5b1BtdWhzalpNdzl4akZkWVg5bHVOZS82NFFobTNHMGpUSENGVXpWcFNXQUhLMGJ3aVZjVVV6RVFlUXZqSDNVeGVFeXppc0xGQ2lXNDVmajNCM0V0UEFKcWN4NENxQUdWMEZvazhMNHBRcjV4eXk3UUVNbXpoOS82MlRqYkxza1hIV0tOem9meThCcEMxUHlaT20zMFRLWWRlTnhYaG0zVHB0NHNsanZEaTBqend4cVlUWWZweDhkNVFSTDlROFF1TlpxNFF6QVBCUzArZmlJYi9ISDB3QmdJNFBqemtxRVZXR1F3UTJIMzE3YnZhTWxuWmJEb1ljMDluL1NCK0orb2J4NVBwZUxZSUY2ZXpWbEM3UmFqb2lzUVpPZzRob0FEbkciLCJtYWMiOiI2YmUyNjY4MjZiYTVjNWViYjBjMmU4NDFhZjc3ZjRlNjYzYzNlY2MyMDA2OTM4OTAyNjVkYzg3ZDYyYzg5MWQwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:54:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im9SZHU3K2tYc0dQSEdYbG1zRGpHUkE9PSIsInZhbHVlIjoic0dERGxWVnFHK05LcWYyZGJMTmNQUG5menNnejJpWU90WVpjUkhaVTVKNUh6VDZJSC95bTBWVmp1bXpIcFJaUjAwMkoyY3lSemZDblR6dk5kTTJpQXJOSUFydmFOOU5VNG9LeFg0STVWaENvVERsd3ltMWRSMzFlbWk2cVk3YmEvbWtXVUx4TldNYm1CTUtwbWhEeGR1elc3L0hYb2UvdnF1UVlhQit6VUlhYmkvcGVqY0RsU2lJb0Z6QWVDeDdHWFVXejVEeUlCOHdCR053cW5GL2NSSkluN3FWWjR0STlYYUdvNDNJbktzWUZqdFdvVC9LN1BKb0ozK1pEU0UyQmN5OFFaVzdmN2U2WE1TVHdZOVpTemdUNzc4cWZ2YjRUeFEzT0FIUmFISzRzaFBHQVAzL0hyVHNuVUdMQklvNlRpbHhYUmIvRkcrV1AzUGh0NFJUaWVDVDZZcXNDV0U4eGFsYm1zWS9LU21yMWxYWEdiR1pod2dKdm9VN0xCN05mMmZLemxjVVlTWGZ4cFBqZXZzdEpXSHJLUW5Oa3NXRFhWRmFFUFlJM2ZlTXNrU1BQejFySkwyOWE3bHhDMWtEUm1meGpDM2Rsbm0xZlFnZ3E5elVaOURWRjRiall0bWpGOFlUZGpMcVpHc0REbW9IcVhsOVhhV09YTkw4MDRiY1EiLCJtYWMiOiI3YTI1NDBkMzAwOGMwZmE0ZmJmZTdlZjJmM2MwNGU4NmFiYjM2ZGFlNjRiYmZiMWI4MGFkZjk4MWE1ZWMyMGQ0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:54:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1133546417\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1629893589 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1629893589\", {\"maxDepth\":0})</script>\n"}}