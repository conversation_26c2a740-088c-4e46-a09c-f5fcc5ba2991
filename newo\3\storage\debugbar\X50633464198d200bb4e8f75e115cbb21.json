{"__meta": {"id": "X50633464198d200bb4e8f75e115cbb21", "datetime": "2025-06-17 07:08:51", "utime": **********.936961, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.14203, "end": **********.936991, "duration": 0.7949609756469727, "duration_str": "795ms", "measures": [{"label": "Booting", "start": **********.14203, "relative_start": 0, "end": **********.818631, "relative_end": **********.818631, "duration": 0.6766009330749512, "duration_str": "677ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.818645, "relative_start": 0.6766149997711182, "end": **********.936995, "relative_end": 4.0531158447265625e-06, "duration": 0.11835002899169922, "duration_str": "118ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45156424, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.015009999999999999, "accumulated_duration_str": "15.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8809822, "duration": 0.01329, "duration_str": "13.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.541}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.911059, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.541, "width_percent": 5.197}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.922921, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.738, "width_percent": 6.262}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-212389081 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-212389081\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1408264199 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1408264199\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-979067992 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-979067992\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-722770477 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=jfj0s4%7C1750143922573%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imdmd1ZLbXJsb1JBdHJuUGJFZ0VSQlE9PSIsInZhbHVlIjoicUc0WUJHV0QvUkRHd2gxeERHdm5ZUzh1V3J5RmRiSHlLY2N5U1owaHBQVXhlZmpCVGluT2VCZVd2aEFPMXlRWndpeGdXRDFINmFrc3dSWXJWSTB5RDEwMkdjYnlkOGFYOG9lK3VYTmlMTW5aclpnbitLQ1Z0TU14NnlKd0RUWE9kRjZMbERnNkgwNzlqWEFDRmlXMjVac0hyUXFTNXRCYzh2ckJ5dks5ditNVTFkTE5vSjdkYUdaaUttdHlacXVmeWg4OXMxRlRNRVlBL3E1S2V1b2p1ZmhPZjl5N3drTThiYytkTUk2NWhMRm85RVdKRGhCeWIzNzBrRUZ1aHgyQ2VFRzYxVnJCZDFVcWVpTE04d2R6blNxdm82R2FEbXhHUklFOEp0cFRKYlRQT1VhYXRwc3BsWnZ1cU1QUStYSXhVNnVveVJBWE5IWnluNmI4T2pPODd0cFhIOGp4d3MxQXRuempqMi9MQjVSOHNGbUZIZ3N0TTBGM3ZleXdoOGtqQnA0cTRxUlNad0pBT0dJWHp0T3VJWVF0RnJTVFpJeXZWVVZCNUhhNGgzd0hIRFo5SVdGRjhob2lhL1ZwSW9LU083aTNDdkNkakFWbXlRbVJmaldkTVl0LzIzTm1WdTNnV0IrRXlPRWZXM3p3Zm5jSmRORVZDYmYyM1BPY2dCZ2wiLCJtYWMiOiJlMjEyMDY1OGYxOTU3ZjBmMGJiMDY0NGZhNmQxODE3MDQ4NDQ1YTA1ZmJmODM0ZTk3NWIxZWJhMWRjYmI0Yjk0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkxZclFqTzhwUUpwWkkzOW5LNG1nZEE9PSIsInZhbHVlIjoiekpXWTVSb0VrSktKbHRFVWttTHJzRWNIamFncmRlQ29NaE1OK1dEdklWRnZ3TER5NnN2ZkMxTzZOdUg5bGF2ZzBicUoxd0QrVkVwN1hsQXZCd1VZRUwwUTF6TUllQzlKay9yTitYUjNRTHI3TVNJc0E4ZHczb0ExVDZkT3VCc0o1dEtOYnpsSkhvTzREQXJWU0FUdEZnSHZaTGdSNElKMVNTaDg1aGVVTkwvY0s0YjR1bnk0YTYrcDhkMHFjUVVUSDgyQXZDaVNzUTVTL1IzKzFGVldlbjJuRjJPZ1gyS3d3MmxSWWF3NFVVanhMU3BhWlJCOStiNmRQRnY4MlhQLzVlaG5ySE5hcjQzWXNHRnhXSXJUWEZOTWU0bFgyb2VheTlrUkc3U1F4VVlZNXZGb2ZDckZBNGFhQWJTUXhHTXNhU2t0c3hRZWhWcllZSm1kdkljeXVydmcxbS9maTNIOVlVVFQ5UUdWUTFlUGt6YzFBV0RpNkhIYnNIb2hpUGhFTHJxQk1HSVZnMTdGcldkaFBNSEQ4MkdNOXpDK2dKSGtFSE9VcXdTQW9OZGxRQVZScnZUS1lBWEFmSC9wKzBwYjhlV21TdHgyY3ZsMlh6Z1FqYUwrdU96dDVKUGxrdFNKeWR2QlIrU0ZuK0ZNU3dXb21ISURjaGNtT0NIczVJaGYiLCJtYWMiOiI5ZDY4MTA1NDJlZjQzMTcyNDBkZDdiNzc5ZWU3NDFkNzA3NDQ5YTQ2MmFjZWFhYjdlOWZlZjQxOGI2YzI3OWRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-722770477\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-571205579 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-571205579\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-991559621 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:08:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InF0R0lGN0wzemI3VWo1MWNHM2ZNOUE9PSIsInZhbHVlIjoiMjVtek4vV0I5QXQwNy95Nnd4eGw0amtkbG9qdjVlRGh1bmw2bzI4cU9QajE4YXFXZlJZbVdtWWVQOFVXNDdsVXlyRDN6c2M0dFZ2RnAxRG9HK01wdCsxTjF5czlIRlg3RHAwK0FqTkdNL0huTkl5WGphUnJ1OVdGRWFjTHJwK1N1b1BHREVPRENpNE0wUTVhYm1SMXpLK05oZ0VjY2tHSVhNa0tVT0doTTRsYWtjcHlteEVLZDFVRGJrOWNGbkE3RDYxK2lwT3hWLzBSTXhuZ3owL3pzeTBoQTBhRTBsbDlnWXpnVG5PQ3R1ZHpxOG51UjFpV05MMGszb3FBa3AxWHd0MUN6RldRR0w1dmRGcWJUckkvcWhlMStLVWQ1eGJQcFY4STRURW1ud0ZsOEMybFlGdFZzRE1wRTFwUE1iSklNRHhvWUlIdEFHR1VtQjd4dVo3VUdmN01TbUpvK3hadTlzd3RpUEE1QW00TmRybkxZb2crVVVxUkhEcW01bzQyVHJQSHZ2bkx4YWhidmt1R0NpVTkvSm80K1NWT3lQSW1UbWdNSDA4c1ZHcEwxTTVxemp2SmtNSGFDcHcxUGc0UXJVS1paVE5YR2xmN0FQK1JWajFyU1Qwb2ZYRUVidkpyNHUzaTdNZitGenI4WUIvZkUxemY4d2tsSW50TG5pcWciLCJtYWMiOiI3MDI5NTU2NTVhODY5ODkwMDI2YjI5NGVmN2YyM2NlZDRkMmQxYTY2MWQyYjFiNTYyZDYyZjgyYzU1MDQxODI2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:08:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZ6RURTVHlFN0xySlQ2ZHlORGszT0E9PSIsInZhbHVlIjoiVTRJNWZFM0FaSVBWbzJLZWtYL1drdG1ES2wyMGZvQXFqNDNDbXBVNmQ0MGx4VDdSTmJmeGd5S0tNMHhRR1VDUGVHNjAvMmJucmFKcithMVRRY3pGY2RkaWdUOWE4WlpuUFBIWThHOGRhdlBxZkhrSCtnZmdkT2dkdE9TMVk4eHgrK0FDTWVmeVlYbkM5eDB0WUhPVzM1andDaUlvSDRob1llc2ZEVGNIQkNOT3g3Y1VScnlVdmgxSWVNOUx4WW5Tenp3VmNNSW04TUpOSmUwYXlQbC9oZUR0N3kzcFVFaEYrVGhKOXM4VTNlendlMWtIZ1RZUEhDYXR5dGRnWW5ibjB5RnFGcHc3L2JvK0w0K05JY3hpSUFQTEpUU3VLVlM5cm1LN05TSHdkRFZ5eXNlck9hZ2V2QXdEbzJBT2FWZ1pBZmNhM0JvMk45Z1RuOFVSNFJiQWRQZzh5b1pzSURveFBWcVI4b2UxTithdWFlUHJkWXcrR0t0d0dLYVhGbFBNRWZYdFpxNnRDMy85RmhGT2VnU0duT01QY0hiWWpUWXV2Z1ZCSUNoMGZPN1pSOW8yamxLeW9FYmNyVkFSTHZkVzJ3djdLTzFtOFR1QURuU0RIMUt2Q0tXSzhNaEJidVFUcU5zaVQ5ZThHdUpJMXJuc01kRHZVeW16ejJyQjhSVXAiLCJtYWMiOiIzYWJlMWE1NTQwOGQ5N2NkNmMzOWVhOTQxMWUzMjQwNWU2YjIzNTA5NjU2OWNmYTEzNWMxYjJlZWJhOWM4MTg5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:08:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InF0R0lGN0wzemI3VWo1MWNHM2ZNOUE9PSIsInZhbHVlIjoiMjVtek4vV0I5QXQwNy95Nnd4eGw0amtkbG9qdjVlRGh1bmw2bzI4cU9QajE4YXFXZlJZbVdtWWVQOFVXNDdsVXlyRDN6c2M0dFZ2RnAxRG9HK01wdCsxTjF5czlIRlg3RHAwK0FqTkdNL0huTkl5WGphUnJ1OVdGRWFjTHJwK1N1b1BHREVPRENpNE0wUTVhYm1SMXpLK05oZ0VjY2tHSVhNa0tVT0doTTRsYWtjcHlteEVLZDFVRGJrOWNGbkE3RDYxK2lwT3hWLzBSTXhuZ3owL3pzeTBoQTBhRTBsbDlnWXpnVG5PQ3R1ZHpxOG51UjFpV05MMGszb3FBa3AxWHd0MUN6RldRR0w1dmRGcWJUckkvcWhlMStLVWQ1eGJQcFY4STRURW1ud0ZsOEMybFlGdFZzRE1wRTFwUE1iSklNRHhvWUlIdEFHR1VtQjd4dVo3VUdmN01TbUpvK3hadTlzd3RpUEE1QW00TmRybkxZb2crVVVxUkhEcW01bzQyVHJQSHZ2bkx4YWhidmt1R0NpVTkvSm80K1NWT3lQSW1UbWdNSDA4c1ZHcEwxTTVxemp2SmtNSGFDcHcxUGc0UXJVS1paVE5YR2xmN0FQK1JWajFyU1Qwb2ZYRUVidkpyNHUzaTdNZitGenI4WUIvZkUxemY4d2tsSW50TG5pcWciLCJtYWMiOiI3MDI5NTU2NTVhODY5ODkwMDI2YjI5NGVmN2YyM2NlZDRkMmQxYTY2MWQyYjFiNTYyZDYyZjgyYzU1MDQxODI2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:08:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZ6RURTVHlFN0xySlQ2ZHlORGszT0E9PSIsInZhbHVlIjoiVTRJNWZFM0FaSVBWbzJLZWtYL1drdG1ES2wyMGZvQXFqNDNDbXBVNmQ0MGx4VDdSTmJmeGd5S0tNMHhRR1VDUGVHNjAvMmJucmFKcithMVRRY3pGY2RkaWdUOWE4WlpuUFBIWThHOGRhdlBxZkhrSCtnZmdkT2dkdE9TMVk4eHgrK0FDTWVmeVlYbkM5eDB0WUhPVzM1andDaUlvSDRob1llc2ZEVGNIQkNOT3g3Y1VScnlVdmgxSWVNOUx4WW5Tenp3VmNNSW04TUpOSmUwYXlQbC9oZUR0N3kzcFVFaEYrVGhKOXM4VTNlendlMWtIZ1RZUEhDYXR5dGRnWW5ibjB5RnFGcHc3L2JvK0w0K05JY3hpSUFQTEpUU3VLVlM5cm1LN05TSHdkRFZ5eXNlck9hZ2V2QXdEbzJBT2FWZ1pBZmNhM0JvMk45Z1RuOFVSNFJiQWRQZzh5b1pzSURveFBWcVI4b2UxTithdWFlUHJkWXcrR0t0d0dLYVhGbFBNRWZYdFpxNnRDMy85RmhGT2VnU0duT01QY0hiWWpUWXV2Z1ZCSUNoMGZPN1pSOW8yamxLeW9FYmNyVkFSTHZkVzJ3djdLTzFtOFR1QURuU0RIMUt2Q0tXSzhNaEJidVFUcU5zaVQ5ZThHdUpJMXJuc01kRHZVeW16ejJyQjhSVXAiLCJtYWMiOiIzYWJlMWE1NTQwOGQ5N2NkNmMzOWVhOTQxMWUzMjQwNWU2YjIzNTA5NjU2OWNmYTEzNWMxYjJlZWJhOWM4MTg5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:08:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-991559621\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1175452251 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1175452251\", {\"maxDepth\":0})</script>\n"}}