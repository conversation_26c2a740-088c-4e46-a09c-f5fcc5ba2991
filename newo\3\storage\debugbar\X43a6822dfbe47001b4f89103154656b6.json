{"__meta": {"id": "X43a6822dfbe47001b4f89103154656b6", "datetime": "2025-06-17 06:29:57", "utime": **********.997397, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750141796.358893, "end": **********.997443, "duration": 1.638550043106079, "duration_str": "1.64s", "measures": [{"label": "Booting", "start": 1750141796.358893, "relative_start": 0, "end": **********.791155, "relative_end": **********.791155, "duration": 1.4322621822357178, "duration_str": "1.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.79118, "relative_start": 1.4322869777679443, "end": **********.997448, "relative_end": 5.0067901611328125e-06, "duration": 0.2062680721282959, "duration_str": "206ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45154264, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0112, "accumulated_duration_str": "11.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.90033, "duration": 0.00822, "duration_str": "8.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 73.393}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9424691, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 73.393, "width_percent": 10.714}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.96578, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.107, "width_percent": 15.893}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2028643099 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2028643099\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1875318285 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1875318285\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1566838055 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1566838055\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1057002382 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1c3qljs%7C1750141708567%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJSak1odU5od3NJaHpMNDZtV1FNK3c9PSIsInZhbHVlIjoiV2dPZlFZVXoycS9VNkJnV2R5TElsTllHbmIwaTdzY3NlRVVBNXJISlVPWnl0MWl0WWhwVU1WKzcvaVhNSlhxRnU1WVUraHJvOE9aYzdRZ2k2VzlCenJvVEovWkhTSzZtejlVU3FCQkFFS1hEZUNNMno0VnMxMlpsN2hKTUZ0NCtUalZQVFExVGc4Y1F6K1RrOTZ0YnQvQ0psV04yUG1qSmtpckUzNWtVTm1oMEk4eUJlZ2JTdVM4WTUvUUV2eFR5ZnJHcHJIYmgrSC9tRzdrOHQzVjdHK3ZKYVhmdzh1dSs1WDB4WVQrOXA4Y2pBSWVsdUJYTXJCdmFRS2lKeDZZWm1zWTNpN3F5emtuWWtwK1ZPTEN0RU1TRWVzcFZJd21YMVBHMkgzbEYvU213NEx2MmFUa1pDaFRJdjlPclJQbTFTcXVrWFdMSDA4ZGNQK1ZiTFlFTTRTdzJoVW45NG80citxMXFqdm5jVC9MMG8rN1V2T3N5a1d3ekdDR1pVZlpJVjg5dkhnWG9VVHpHV0ovYVd6UUYwL2t3aU5JcGdzd09VKy85b2ZHMUtSVW5oN2sxT2RKdjR0VzJTNFJMUXF1TXk4U0xYVFdZZEtmdlhYcTExQUhaYjJLRkY5ZVZKN0I1S3hlcVhSZHFHU0g2YnNVd0VYL294dDdCOGtHK0FiN1kiLCJtYWMiOiJmOTFhNDdjOThjY2Q0NWVmNjdjZjE0YzNmZTNjNTI1YWVhNGRiZTcyYWZiZmMxNTYxOGVhN2ZhYmZhZTQ2YTJhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImhzcEtZeXp2K0gwNGl3K2FPb29pcmc9PSIsInZhbHVlIjoiVmZTcDN4S2MyYjYwaXo5dE80MW5rSjhpQnU4VHNEV1NBK3V3RzVJSmJLMGk3QkhUZno5MGVYcTlGZW81Um5JYjE1NEY4UzR5aFNrZjZJaDI5SmVTVXVkcFdTcXE4V3pwd0lZZnZtRzJUOXBEMVdDNnF6MnRoK252L3NkZno5NTVNVkJLditNbFMxWC9ySFdhbG92U3VnQVkzbWxhYmczaHhENzIyL3Q1b3RWRkZOTnUvZDc4MkZPRVZUQ0pUdlBmT3dqRkZNR2RNTFVuL3RFRE9DRW53aElsalhyaHR5UlYreXF2YWNvN09UalkrMFdOK2RHcmovdW1udmY3NTFqNlFndE0rc1dwTHBZTTUrTkQwbXhYZWVLbk9TWG1rMGJUeXdWaWYrV2s4WEZubHE4S0NGY3RZbDFkQXN3MjFrcVdUY3F3U0VtZ0hMWW0zTnM3b1N5SGhhNmNUWXhMNWMzQUtzL05UeXhhalNZN0RFRWhab2pUbFpjLzNzdEx6Z01lVjF5ZGw0bkRMMjI1MTZrSkoxVVRSTFN5ZXh2Um9zTnFoMEg0L1p4SjRyQTA0REZMYnNNVHVZcE45aExuR1BnTXBleUhYOGtjRXU2OHdiWU4xMGtTN1IxN0hjeDkvOCtQcW1TMWJlRXNwWU83b2tYczhUVU9EQ0RjZE5tcXU5aVEiLCJtYWMiOiI1ZDY0OWE1NWJjMjJjNjg0NzY5YzkwZmI0M2NkYTYxZDhlZjljZjBhMWY5ZDUzNzY5YjRjZDM2NTBiMGE3ODc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1057002382\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1601564406 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1601564406\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1697691318 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:29:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNZeWZlRDhVNDFYWTNxV2V3K0xjV1E9PSIsInZhbHVlIjoiNUdqM1VMQmF5RStldWd6WUNxdU5tZWRocjh4TkpaKzFVNTdtNFhuNGtGeVZvWFJFQkRSalNkWGw1R1RaRm1qclNlbCtlSGlvTWg0eGF4TEFZNGFhYko3cG5GcWUvdk01dWJIQWlsOFdoclVJUDhnUkJLSXRGN3B6bENQZmpwQXpXSmxWaHBXZGNudDczOG5SWUtER3dseGQ4Z2dqRGlHcWV6UHZNVlVINk45YktjQkRadGR5TndyVi9sWkVsUmtQYnVBLzloSkJaM1NCZXFGNXptL09ROEFnSHpYRWxhcHlIeUk0bnIzWE1zbWxMQ25mUjJxZVExRFo5dkJzUEIrMHpkR2NSdzRPTWwrdzRENy8xb2hrQVJGL2pXYVVSaU95czJCUE9kbDE4WnpyTSsvMkFTSzJtWmtFQXBDY09pS24zWmZkcXY2V3dXbmkyS1NnWDg3b2tPckNKRXJlc2VsSUVjcUtGUktmQk5SbzkwN24rNlAzZUhiVzNjclNlN0pVY21HZXZ4Ty9uL1lIVUFoWlRldmM0ZTBYdHdUenBmaDk2VGgveVAyZTRndDhkaTZxMTkyQ21XVVQ1VzVTdkNHM1hYald4TXdtSDNacDlwMEs1YlZBZjd2VGhvcHArbXNpS0ZhVlVXdzk5cVhucXpvRTBKekwyN0Q1Z3hRS0dWVlQiLCJtYWMiOiJhYWE0NGIzMjU2MzM5ZjQzYjliMGMwMjQxY2M2NTU2Y2M1Mzk5ZmE1YWM4MGQ0NDYwMmFiOWY4NzNmZWE0ZWVlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:29:57 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjhhNWUvcW1rdWVSVjNTY1ZYQVdGWWc9PSIsInZhbHVlIjoib2x2aWpyamtDa0xNSmY5M2VOeHNrY25Pb2tVWWNWZVJmLytxaHJLcUxBSWNXSE91dForTDFhK295MzRxUEduTEpvUHdpL2VRSEc5OTBDRG1hcUZtZnpML2pWUDFsRTloUWZzWmIwMFdNMTEyWGhmTVVXbnB4bW9tN3dRUEVFeThWSXM5N0U5K0VuN09rZmJIOUJ5Q0JueXMvbXN6SXRGVUx6Z2JCZ0ZjK0J2eTd4WE54OWlGWHBnTnFRVUF5WmpuS3JjYk51ajN6cVVHMHJlakFJbXJuRTUrTVhQenlyUkRaelAveVhqZ0plMENHZ055dTZSdit5QkxEMklsWWJBZGFiangxalJ1QTRTVkVZNXcvS1NTQlBDWWlJQ2lWUVBQL3lWUlpFeGdUZDRXRjNsL1oybTVQOUM4cGtISHVnblRLaWdKUFN4bG1uUzJhK3pWcXFHRzA1UzJYSG0zVVJIeHlDbFhjZjhoVkFmckZuclhUb25lVlE3c1FYaU5IbGZMVU1RN1B0K0FWYzZWTTFSUVVoKzdtMEJOVHpTL2xZUklHaFl1VW92aGlCRUg1MjdSK3JRam1kME8vYUVVT0FlL3doUWVWazlka2FGczQrdGdtY0gxa0JEaVY3TmI5ZFdpSTh5Zm9Lald2NjUySUZJZnVDUHh4K2VDMDlpYWtVTksiLCJtYWMiOiJiZTc2OGU2MjRlN2U3MjAwZjBhZWU0NjBmM2VmYzVkYzQ0MDMxOTgxYmY2OTY0ODVmNzhmMTgxZWJjMWNhYTA0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:29:57 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNZeWZlRDhVNDFYWTNxV2V3K0xjV1E9PSIsInZhbHVlIjoiNUdqM1VMQmF5RStldWd6WUNxdU5tZWRocjh4TkpaKzFVNTdtNFhuNGtGeVZvWFJFQkRSalNkWGw1R1RaRm1qclNlbCtlSGlvTWg0eGF4TEFZNGFhYko3cG5GcWUvdk01dWJIQWlsOFdoclVJUDhnUkJLSXRGN3B6bENQZmpwQXpXSmxWaHBXZGNudDczOG5SWUtER3dseGQ4Z2dqRGlHcWV6UHZNVlVINk45YktjQkRadGR5TndyVi9sWkVsUmtQYnVBLzloSkJaM1NCZXFGNXptL09ROEFnSHpYRWxhcHlIeUk0bnIzWE1zbWxMQ25mUjJxZVExRFo5dkJzUEIrMHpkR2NSdzRPTWwrdzRENy8xb2hrQVJGL2pXYVVSaU95czJCUE9kbDE4WnpyTSsvMkFTSzJtWmtFQXBDY09pS24zWmZkcXY2V3dXbmkyS1NnWDg3b2tPckNKRXJlc2VsSUVjcUtGUktmQk5SbzkwN24rNlAzZUhiVzNjclNlN0pVY21HZXZ4Ty9uL1lIVUFoWlRldmM0ZTBYdHdUenBmaDk2VGgveVAyZTRndDhkaTZxMTkyQ21XVVQ1VzVTdkNHM1hYald4TXdtSDNacDlwMEs1YlZBZjd2VGhvcHArbXNpS0ZhVlVXdzk5cVhucXpvRTBKekwyN0Q1Z3hRS0dWVlQiLCJtYWMiOiJhYWE0NGIzMjU2MzM5ZjQzYjliMGMwMjQxY2M2NTU2Y2M1Mzk5ZmE1YWM4MGQ0NDYwMmFiOWY4NzNmZWE0ZWVlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:29:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjhhNWUvcW1rdWVSVjNTY1ZYQVdGWWc9PSIsInZhbHVlIjoib2x2aWpyamtDa0xNSmY5M2VOeHNrY25Pb2tVWWNWZVJmLytxaHJLcUxBSWNXSE91dForTDFhK295MzRxUEduTEpvUHdpL2VRSEc5OTBDRG1hcUZtZnpML2pWUDFsRTloUWZzWmIwMFdNMTEyWGhmTVVXbnB4bW9tN3dRUEVFeThWSXM5N0U5K0VuN09rZmJIOUJ5Q0JueXMvbXN6SXRGVUx6Z2JCZ0ZjK0J2eTd4WE54OWlGWHBnTnFRVUF5WmpuS3JjYk51ajN6cVVHMHJlakFJbXJuRTUrTVhQenlyUkRaelAveVhqZ0plMENHZ055dTZSdit5QkxEMklsWWJBZGFiangxalJ1QTRTVkVZNXcvS1NTQlBDWWlJQ2lWUVBQL3lWUlpFeGdUZDRXRjNsL1oybTVQOUM4cGtISHVnblRLaWdKUFN4bG1uUzJhK3pWcXFHRzA1UzJYSG0zVVJIeHlDbFhjZjhoVkFmckZuclhUb25lVlE3c1FYaU5IbGZMVU1RN1B0K0FWYzZWTTFSUVVoKzdtMEJOVHpTL2xZUklHaFl1VW92aGlCRUg1MjdSK3JRam1kME8vYUVVT0FlL3doUWVWazlka2FGczQrdGdtY0gxa0JEaVY3TmI5ZFdpSTh5Zm9Lald2NjUySUZJZnVDUHh4K2VDMDlpYWtVTksiLCJtYWMiOiJiZTc2OGU2MjRlN2U3MjAwZjBhZWU0NjBmM2VmYzVkYzQ0MDMxOTgxYmY2OTY0ODVmNzhmMTgxZWJjMWNhYTA0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:29:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1697691318\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1024804478 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1024804478\", {\"maxDepth\":0})</script>\n"}}