{"__meta": {"id": "X970a0fd8e81c701d1734e547e62c50d8", "datetime": "2025-06-17 06:54:43", "utime": **********.465442, "method": "PUT", "uri": "/users/16", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143282.008361, "end": **********.465476, "duration": 1.4571149349212646, "duration_str": "1.46s", "measures": [{"label": "Booting", "start": 1750143282.008361, "relative_start": 0, "end": **********.136179, "relative_end": **********.136179, "duration": 1.1278178691864014, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.136212, "relative_start": 1.1278510093688965, "end": **********.465479, "relative_end": 2.86102294921875e-06, "duration": 0.3292667865753174, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51610416, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT users/{user}", "middleware": "web, verified, auth, XSS, revalidate", "as": "users.update", "controller": "App\\Http\\Controllers\\UserController@update", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=252\" onclick=\"\">app/Http/Controllers/UserController.php:252-315</a>"}, "queries": {"nb_statements": 13, "nb_failed_statements": 0, "accumulated_duration": 0.05112, "accumulated_duration_str": "51.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.221277, "duration": 0.02072, "duration_str": "20.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 40.532}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2687, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 40.532, "width_percent": 2.113}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.3161938, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 42.645, "width_percent": 2.171}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.322257, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 44.816, "width_percent": 2.074}, {"sql": "select * from `users` where `users`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 284}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.334996, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "UserController.php:284", "source": "app/Http/Controllers/UserController.php:284", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=284", "ajax": false, "filename": "UserController.php", "line": "284"}, "connection": "ty", "start_percent": 46.89, "width_percent": 2.191}, {"sql": "select count(*) as aggregate from `users` where `email` = '<EMAIL>' and `id` <> '16'", "type": "query", "params": [], "bindings": ["<EMAIL>", "16"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 983}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.370283, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 49.081, "width_percent": 1.761}, {"sql": "select * from `roles` where `id` = '23' and `guard_name` = 'web' limit 1", "type": "query", "params": [], "bindings": ["23", "web"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 120}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 298}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3770752, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Role.php:169", "source": "vendor/spatie/laravel-permission/src/Models/Role.php:169", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=169", "ajax": false, "filename": "Role.php", "line": "169"}, "connection": "ty", "start_percent": 50.841, "width_percent": 1.917}, {"sql": "update `users` set `type` = 'Cashier2', `users`.`updated_at` = '2025-06-17 06:54:43' where `id` = 16", "type": "query", "params": [], "bindings": ["Cashier2", "2025-06-17 06:54:43", "16"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 301}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.384864, "duration": 0.01013, "duration_str": "10.13ms", "memory": 0, "memory_str": null, "filename": "UserController.php:301", "source": "app/Http/Controllers/UserController.php:301", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=301", "ajax": false, "filename": "UserController.php", "line": "301"}, "connection": "ty", "start_percent": 52.758, "width_percent": 19.816}, {"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 3059}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 302}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4029078, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3059", "source": "app/Models/Utility.php:3059", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=3059", "ajax": false, "filename": "Utility.php", "line": "3059"}, "connection": "ty", "start_percent": 72.574, "width_percent": 2.191}, {"sql": "update `employees` set `name` = '<PERSON><PERSON>', `email` = '<EMAIL>', `employees`.`updated_at` = '2025-06-17 06:54:43' where `user_id` = 16", "type": "query", "params": [], "bindings": ["<PERSON><PERSON>", "<EMAIL>", "2025-06-17 06:54:43", "16"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 3061}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 302}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.410918, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3061", "source": "app/Models/Utility.php:3061", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=3061", "ajax": false, "filename": "Utility.php", "line": "3061"}, "connection": "ty", "start_percent": 74.765, "width_percent": 7.277}, {"sql": "select * from `model_has_roles` where `model_has_roles`.`model_id` = 16 and `model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 306}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.421086, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "UserController.php:306", "source": "app/Http/Controllers/UserController.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=306", "ajax": false, "filename": "UserController.php", "line": "306"}, "connection": "ty", "start_percent": 82.042, "width_percent": 1.976}, {"sql": "delete from `model_has_roles` where `model_has_roles`.`model_id` = 16 and `model_type` = 'App\\Models\\User' and `model_has_roles`.`role_id` in (17)", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 306}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.428787, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "UserController.php:306", "source": "app/Http/Controllers/UserController.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=306", "ajax": false, "filename": "UserController.php", "line": "306"}, "connection": "ty", "start_percent": 84.018, "width_percent": 8.138}, {"sql": "insert into `model_has_roles` (`model_id`, `model_type`, `role_id`) values (16, 'App\\Models\\User', 23)", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 306}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.438567, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "UserController.php:306", "source": "app/Http/Controllers/UserController.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=306", "ajax": false, "filename": "UserController.php", "line": "306"}, "connection": "ty", "start_percent": 92.156, "width_percent": 7.844}]}, "models": {"data": {"App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1905958598 data-indent-pad=\"  \"><span class=sf-dump-note>edit user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1905958598\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.333856, "xdebug_link": null}]}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "User successfully updated.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/16", "status_code": "<pre class=sf-dump id=sf-dump-1543045875 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1543045875\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1546698201 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1546698201\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1649954569 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\"><PERSON><PERSON>asi<PERSON></span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1649954569\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-407313216 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">135</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1930 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; XSRF-TOKEN=eyJpdiI6IjdCTW10bkFteTRNYnZuUXBZb3pOVkE9PSIsInZhbHVlIjoidm0rRUJKWGY3dFkvWTZkTm5wTHJhcHpuL1g3Vmc4T1pZTlJjazIwVTdSQXpzblFYK0w1V3EwRzVkUmRtY3JHdC9sS0VpZ0diT2hZQ0ZueXpsUWZwa1poVXUydEVmS2xCVWVjdTlXNGdEaFJxRFFHKzFqSENad1BmMUFFY3VBelFmM1dkcUtzMzVOS3gxeGxlNmNQM28xR081MCtsbldkVS8zeDh3TS9ZWkVuUzJJbnVvNGJMbnMvOWxXY29kTWpIYTdscHNYVHBpL2ZpLzYzYjVUZTFZeHVDakh5K21xa3hqNXdacWt5dzFhWlEvYXlmaDZrNlRxMklpam9KOVIrQ1d1UjBGRVdkNERzeXJVTDhaU3AyZGhpWVNwOWJKbXBTa0kxRXUrVWdwQnhwaFg2OFE2ak53QmJxREs1ZFdTRkhycmFaeHZrT0pXaUFMOTdPRk5JUHF0M3h1aUxtT25DKzFPK0NOcTF6bU9hV1Njc1dhd0lIMkJpZ1BjTURoc1RaZXNMOWpmM3pLdndVL21JM296NWIxSE1NOGd3ZDhqUENNSFQ5MmlIc0tjclpiWUlqelB5TEpJbEE5elR2ZUNyL1R1dlkyUnU2NzZFQ1JxNmw3cXgyckFleHM1RTNuVXM5VmwwRjBPUVZvU3hrV0xzY0VVOEt4NFl1NjdTTzBmUjYiLCJtYWMiOiIzMTAyYjgzNmYzNmE2Y2RhMzZiNWYxNjgxOGFiZGE2YWQzZGIwYmM2ODcyZmIxZmRkNTViOGI0OThmZGJiZTQxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkRCY1dhTTBCOW5JZGoreHdDa3lrUXc9PSIsInZhbHVlIjoiSUJUMFRUb3VEUmhBRzl3NlVnQnFVZ0o0eWtKQzlQKzd0SVFGZmZDSVRDL1hCYlAzbUVuR3oxdUZjSm5jTUN3UkRvOWYxV2tNSmd3cUEzVkJ4OXR4L0JPUG1qTzA3YTBGUy9jczVscWlhc2wyM3dhMkhmb1J2ZVVpbFNuYXBsejJuaEgzUUZRMVdXZWhhUWM5S092MzIrZTFQaWVCWVZwcWdQaXZBOTJobmFmb2JEV1dKSW8vQVhVaHFuY0VhTzhRZ1lMYTcycGl5TUFMYUZEL2dVc3NIWElzS1Z3TWlpTUN3VnVwSmtIVGtoM29DcmFNYWVXOW9lU3UyQi9RMWZIN05RMFpoclpPVVhjYWJwODJDbC9tNVNxMUI4YWlrbjlwYkplaGl0aE5valU3cUxsWGJWdTRoYUpqemtkOGhFY3MyZEhoaGFIcHJFaVBINVI3cU5oMkFaR3dZdFNSQnk5MWNTSXJNWnppZ1NoekIxU0h4WmRkbURlY28vRkVHMUs0S0lNa21tYkJmNjFSUDIzQzNQTmo4RHozNzg0VVA4a2o1WkJWL3FHV0NEaHF3U1lTZEJ1a01qaENpSzZFT2ZhcC9LZXhSVTcreWZnMjc4MVJzWGQ3N082TEp5Mms4THlTa0xCTDFoZTJDVE9Ed0FrMW1ZeE5RdkZNaGxYU3MrUHUiLCJtYWMiOiIxY2U2ZTlhZjBmMDAzYjc4NzI5Y2ZmMTFhZjk1ZGY0NGNhZTM1ZDc0OTQ2YjA5YmIyNDVjM2ZlZTRmZmUzYWRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-407313216\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1881582738 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1881582738\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-696087224 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:54:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImIxVTJURGFOemhtSXJrcElQSXU4eGc9PSIsInZhbHVlIjoiaG1JWG5UQXN6T0k2c3NITHVLWWxEcytQWTVnZDdNU2hVT1o3VFBxWjB0Ri9ralF0MjJzUitpTFoxTFFlalRTanZUaFpnM3RsdUdPTHZISkVOWmFZeEM2dlZxckFuR1doNU5CcEZlcFhrZk13eDFzZysxQjZ4VG9rSkNPMnZlM3RtaWl0ZmZld2FRMDdzSk8xZG9MeVlKLyt3QUkrMlc2dVBLekROSEp1cEhyd01TTDhoVjRXeEJXb1c2bVFpL1Z3a2VwV1FHeVFVR3NkNm5hTG40VWRHdFBLZHNuU3JVRlRLUmw5NGdjN1BRcUN5UVpWcm9KaktYc0Npek5xWEdwY0t0TTJqMGxjRFZUK09tNkU3WGJ2VCtyc1JvSTY3cUtjZWprN0NVRk5tcVBWMjkwVFk0UThhajY0RzBuT3hiWm1YU2ZKdS92dkRyUHdwbG9sWmdJL0lzbDVrZURNRUtyMnZRZVkvdVNzbmRIMXNRd2hTeGVPSEg1aklXQ1owYnN6KzFlQlN0dXBOcS84WlpmM042M1ZRQVhvRy84Y0IrcWkza1dKNGUvdnk1ZUJBMjVyUmgvQXpxdnBTMzM1T2dxbkdJT3pEenlSWjMyS04xMDJCaHAvRGlPOVlPUnRXY1JtTmsxeFdoajE2SHdHK0c1aHZmL2FIL0F0SGJVejNNZTIiLCJtYWMiOiI5NGFhYTFkODdiOGE0NmUxMTBjNGU4Yzg5ZGUwYmI3MDU4ZGU0NmFlNWExNWVjNjliZjA1YmNkMzYyZjhkY2NmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:54:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjlkeDNiRXgwekhMbGtmVWVsUXZsSkE9PSIsInZhbHVlIjoiK2Vqemcxemk5aVpXVitYRVE1RThCeTVDVk4xT0JWVmRrZERzUGVSQytWenBuRGExQmFTMCtjcS9qMlBLcW1USUpnZWJsaVFBQ1NjRlQzdHhBeEVIbDZ4Q1pCbkcxUFBxL2dtLzVvOXNnNEpUSGtqYS9oNXFxUEppUXBZU0NFWmgxOHhSbDVJTVd6LzZlTVRnVUJHL2M4U1owWFVSVVl4V05uV1RIZ1hOYWRTbENYdER3dkJPL3QyaEpZOTRPYWZBUWg4MFJlbHBPQmpSOFg4b0l6cElTZE13bUNDcjRwV2tWYWNhdmlwRDZqSEFQZEJCV0FUcDBQVUdiQ0NySlU4TXo0ejNjWE5vbEVycTFlWHQ1SjJta3lSSE5XSXN0WVg5L0FpejJvVWpoT2pxSTIvOWFyWEZzb21kaTFTbnU4dDJ5d2xVUTBWTnVrbXJvVTMrV3FJdG13OEpIZVJmTFZOdkRoY2kwYkljK2xNbGdobXlaaVVGd3c2U2VoRWZiN2J6N0szY00yUGtwUjg0bDZFY1FSK29teUNxMisvU05RWEVweXorK1hHekpsenIvMmlFQXRtL2hadWFaMTdyRE5BeEhNNWEwUDh1ZzN4cGJwOGtpcldpL05TNDZUNGRDRmlGRHV4WVdiOWNlZUdLcWhSdEhZL0hPZVFZRWJScS8wclYiLCJtYWMiOiI5Mzg2MTg3YTFiMjllMWM1ZmY5ZjM4ZWFkN2MzZTM0ZmQxZjA3Njc5OTAyOTExZTQzY2RiZWRkMjJmNDA2YjgxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:54:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImIxVTJURGFOemhtSXJrcElQSXU4eGc9PSIsInZhbHVlIjoiaG1JWG5UQXN6T0k2c3NITHVLWWxEcytQWTVnZDdNU2hVT1o3VFBxWjB0Ri9ralF0MjJzUitpTFoxTFFlalRTanZUaFpnM3RsdUdPTHZISkVOWmFZeEM2dlZxckFuR1doNU5CcEZlcFhrZk13eDFzZysxQjZ4VG9rSkNPMnZlM3RtaWl0ZmZld2FRMDdzSk8xZG9MeVlKLyt3QUkrMlc2dVBLekROSEp1cEhyd01TTDhoVjRXeEJXb1c2bVFpL1Z3a2VwV1FHeVFVR3NkNm5hTG40VWRHdFBLZHNuU3JVRlRLUmw5NGdjN1BRcUN5UVpWcm9KaktYc0Npek5xWEdwY0t0TTJqMGxjRFZUK09tNkU3WGJ2VCtyc1JvSTY3cUtjZWprN0NVRk5tcVBWMjkwVFk0UThhajY0RzBuT3hiWm1YU2ZKdS92dkRyUHdwbG9sWmdJL0lzbDVrZURNRUtyMnZRZVkvdVNzbmRIMXNRd2hTeGVPSEg1aklXQ1owYnN6KzFlQlN0dXBOcS84WlpmM042M1ZRQVhvRy84Y0IrcWkza1dKNGUvdnk1ZUJBMjVyUmgvQXpxdnBTMzM1T2dxbkdJT3pEenlSWjMyS04xMDJCaHAvRGlPOVlPUnRXY1JtTmsxeFdoajE2SHdHK0c1aHZmL2FIL0F0SGJVejNNZTIiLCJtYWMiOiI5NGFhYTFkODdiOGE0NmUxMTBjNGU4Yzg5ZGUwYmI3MDU4ZGU0NmFlNWExNWVjNjliZjA1YmNkMzYyZjhkY2NmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:54:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjlkeDNiRXgwekhMbGtmVWVsUXZsSkE9PSIsInZhbHVlIjoiK2Vqemcxemk5aVpXVitYRVE1RThCeTVDVk4xT0JWVmRrZERzUGVSQytWenBuRGExQmFTMCtjcS9qMlBLcW1USUpnZWJsaVFBQ1NjRlQzdHhBeEVIbDZ4Q1pCbkcxUFBxL2dtLzVvOXNnNEpUSGtqYS9oNXFxUEppUXBZU0NFWmgxOHhSbDVJTVd6LzZlTVRnVUJHL2M4U1owWFVSVVl4V05uV1RIZ1hOYWRTbENYdER3dkJPL3QyaEpZOTRPYWZBUWg4MFJlbHBPQmpSOFg4b0l6cElTZE13bUNDcjRwV2tWYWNhdmlwRDZqSEFQZEJCV0FUcDBQVUdiQ0NySlU4TXo0ejNjWE5vbEVycTFlWHQ1SjJta3lSSE5XSXN0WVg5L0FpejJvVWpoT2pxSTIvOWFyWEZzb21kaTFTbnU4dDJ5d2xVUTBWTnVrbXJvVTMrV3FJdG13OEpIZVJmTFZOdkRoY2kwYkljK2xNbGdobXlaaVVGd3c2U2VoRWZiN2J6N0szY00yUGtwUjg0bDZFY1FSK29teUNxMisvU05RWEVweXorK1hHekpsenIvMmlFQXRtL2hadWFaMTdyRE5BeEhNNWEwUDh1ZzN4cGJwOGtpcldpL05TNDZUNGRDRmlGRHV4WVdiOWNlZUdLcWhSdEhZL0hPZVFZRWJScS8wclYiLCJtYWMiOiI5Mzg2MTg3YTFiMjllMWM1ZmY5ZjM4ZWFkN2MzZTM0ZmQxZjA3Njc5OTAyOTExZTQzY2RiZWRkMjJmNDA2YjgxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:54:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-696087224\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-898823638 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"26 characters\">User successfully updated.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-898823638\", {\"maxDepth\":0})</script>\n"}}