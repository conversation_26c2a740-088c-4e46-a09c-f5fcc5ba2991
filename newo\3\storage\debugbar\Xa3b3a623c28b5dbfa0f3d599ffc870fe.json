{"__meta": {"id": "Xa3b3a623c28b5dbfa0f3d599ffc870fe", "datetime": "2025-06-16 15:21:25", "utime": **********.937371, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087284.168299, "end": **********.937405, "duration": 1.769106149673462, "duration_str": "1.77s", "measures": [{"label": "Booting", "start": 1750087284.168299, "relative_start": 0, "end": **********.78727, "relative_end": **********.78727, "duration": 1.6189711093902588, "duration_str": "1.62s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.787294, "relative_start": 1.618994951248169, "end": **********.937408, "relative_end": 2.86102294921875e-06, "duration": 0.1501140594482422, "duration_str": "150ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43506864, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.01434, "accumulated_duration_str": "14.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.893556, "duration": 0.01434, "duration_str": "14.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aXNlwC2u7eBVVmf76Srodlte78QD6PcvbxRIevxR", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1828290581 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1828290581\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-727458018 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-727458018\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1449107374 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"259 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=1x3i2fa%7C1750077280918%7C10%7C1%7Cq.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1449107374\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-705238713 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-705238713\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1641755745 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:21:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxEUDBvQ2JXVTlsekx2cGxuQzYvSXc9PSIsInZhbHVlIjoiM3JHcUdtQWlwUGlvOXVXV3RtRkhXcVJycWVoSTlxRWtTRWx6QUJYSUhPZkZzZFR2WUppbEY3Z1JBcHdIVVcraXBHUGVWbWEraXRHbWNvaTVpYXQxUE1YcThMZkYrT25ZOUhna0U1UEdjbXBxQkdwSjd1RFBjTGFLZFdlWmtnbkRhNU9wVU9OZGlaelpwQS9lVnJlQmlYaHZiSG5FZW0wMFkyMTB2c05vR0ZOS0gyeHlYMVRzTldibEU3RGZydjl2dEdnSFZVdGszVjlUU2sxdEJXSW5JZSt5NEFkKzJ1NjJwcmRnM3lzWHVQSGZ3dkZ6cHVCdTRUSm56OFgyeXdFUjF5aG1iN2prUlZpa3hCc2RsQ0tpYkRIbHRQN01HRGcyS1NUUWpXaC83Mmtva1hOL0FNUzllT056dk5kWm1aMkNhcnFDWUdQNmVpWUZiQ1dCU2xHdnlleWFjRWlsWjNVVTdVUDhuTmJGNHVXSmJSOVdkdXZNNmtVVGRYcXgrcVRMVUE4bnBNdDhpb2gxN051UTFla2VMZE1ZWGYxd3RjVXlyT2JRTzBoQ0F6WXQwVDNVOFBuRlpzbU5XZDNUVC9keWY0SHBwQkJVLzRiaVBOQkQ4WGZFLzNQMStKTk1WWm1OU0ZMeVVINDNhMFVnWlByM0ZXaFE1cE1sSEhBZVFXV1kiLCJtYWMiOiIyNTRhNjU5YzMzMDA2MWU2NDgxODg5Y2E5ZDg2ZTA0MGNhNWEzZDI0NzM1N2Q3MGRjODI4NGNjMDM4OTE0NzFlIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:21:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZDelpmUUJyU1REbnhMUmR5b2dwaEE9PSIsInZhbHVlIjoieUYwVVcrK1lOazVMdDliampaaFVUN08wbFBvTGVvZXJYL29jTDhnVDE3anM4ZVR6MjdaemcvNFUvaFRCVGxhWXZPMzRnWmJEM1BUTGQ4Z09MQ3BFUnhWNVRTZzVjbmM5OS9nQXMxSnhuS2ZhMmJVVjd0R3pZTm5WTnI3eUdDRzFwa01xcTNLVHcwTnhXaGV2TkR5aS8waXl0T2hxLytXNzZ6QUdRWkhhV2hZNUlYMTlIUjU3alVqSHpoMG9jeEwwTEQ1NTZYMlo1UzBBSCtkTUY1dkl2Sk1TSXB4S0VSZW1BZVJPY0VqZG1hTUk0VU9rUzJzZGF2SzJnWnhQSUhBRVM2TFJPL1pKZFVvRUszZW5LTjNEMTYrM290TEMrMFlHL2xIMkFBU3NheFRVc2dWL2x6NHQvZldnUHN3K0tkNnAyRzV1S0lCeWE2MzRKSVhEdnBBOCtnZTcvUTVGM09nNjNYZ0o4UFlRNlJNdFdQVjVjQzZWMExJSG54ZUVBeVZPT25qa3lSalJCKzZuaWU5NG9MTE9NQTJLYWlDUkZXMUhlakRlQ3NKOGR2K3JZZmNpZE9yWWg2OFh0dEV4WWJDUlc2eGk2U3JOYVRlblRoMDhKN3pkbk5wZ2hsbVp6eHZaTkM1RG4xTG0zYkZMbHEvWUdza2tTSWhBR25iVmYxd0MiLCJtYWMiOiI2MTg1N2FkZGJjNDU1NGYzYWRiMzdjNmNiNzI3MzhjNjJlYmIzNDg0NTE5NDJlZTYwMzM1ZjMzOWQwZjczZDNjIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:21:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxEUDBvQ2JXVTlsekx2cGxuQzYvSXc9PSIsInZhbHVlIjoiM3JHcUdtQWlwUGlvOXVXV3RtRkhXcVJycWVoSTlxRWtTRWx6QUJYSUhPZkZzZFR2WUppbEY3Z1JBcHdIVVcraXBHUGVWbWEraXRHbWNvaTVpYXQxUE1YcThMZkYrT25ZOUhna0U1UEdjbXBxQkdwSjd1RFBjTGFLZFdlWmtnbkRhNU9wVU9OZGlaelpwQS9lVnJlQmlYaHZiSG5FZW0wMFkyMTB2c05vR0ZOS0gyeHlYMVRzTldibEU3RGZydjl2dEdnSFZVdGszVjlUU2sxdEJXSW5JZSt5NEFkKzJ1NjJwcmRnM3lzWHVQSGZ3dkZ6cHVCdTRUSm56OFgyeXdFUjF5aG1iN2prUlZpa3hCc2RsQ0tpYkRIbHRQN01HRGcyS1NUUWpXaC83Mmtva1hOL0FNUzllT056dk5kWm1aMkNhcnFDWUdQNmVpWUZiQ1dCU2xHdnlleWFjRWlsWjNVVTdVUDhuTmJGNHVXSmJSOVdkdXZNNmtVVGRYcXgrcVRMVUE4bnBNdDhpb2gxN051UTFla2VMZE1ZWGYxd3RjVXlyT2JRTzBoQ0F6WXQwVDNVOFBuRlpzbU5XZDNUVC9keWY0SHBwQkJVLzRiaVBOQkQ4WGZFLzNQMStKTk1WWm1OU0ZMeVVINDNhMFVnWlByM0ZXaFE1cE1sSEhBZVFXV1kiLCJtYWMiOiIyNTRhNjU5YzMzMDA2MWU2NDgxODg5Y2E5ZDg2ZTA0MGNhNWEzZDI0NzM1N2Q3MGRjODI4NGNjMDM4OTE0NzFlIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:21:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZDelpmUUJyU1REbnhMUmR5b2dwaEE9PSIsInZhbHVlIjoieUYwVVcrK1lOazVMdDliampaaFVUN08wbFBvTGVvZXJYL29jTDhnVDE3anM4ZVR6MjdaemcvNFUvaFRCVGxhWXZPMzRnWmJEM1BUTGQ4Z09MQ3BFUnhWNVRTZzVjbmM5OS9nQXMxSnhuS2ZhMmJVVjd0R3pZTm5WTnI3eUdDRzFwa01xcTNLVHcwTnhXaGV2TkR5aS8waXl0T2hxLytXNzZ6QUdRWkhhV2hZNUlYMTlIUjU3alVqSHpoMG9jeEwwTEQ1NTZYMlo1UzBBSCtkTUY1dkl2Sk1TSXB4S0VSZW1BZVJPY0VqZG1hTUk0VU9rUzJzZGF2SzJnWnhQSUhBRVM2TFJPL1pKZFVvRUszZW5LTjNEMTYrM290TEMrMFlHL2xIMkFBU3NheFRVc2dWL2x6NHQvZldnUHN3K0tkNnAyRzV1S0lCeWE2MzRKSVhEdnBBOCtnZTcvUTVGM09nNjNYZ0o4UFlRNlJNdFdQVjVjQzZWMExJSG54ZUVBeVZPT25qa3lSalJCKzZuaWU5NG9MTE9NQTJLYWlDUkZXMUhlakRlQ3NKOGR2K3JZZmNpZE9yWWg2OFh0dEV4WWJDUlc2eGk2U3JOYVRlblRoMDhKN3pkbk5wZ2hsbVp6eHZaTkM1RG4xTG0zYkZMbHEvWUdza2tTSWhBR25iVmYxd0MiLCJtYWMiOiI2MTg1N2FkZGJjNDU1NGYzYWRiMzdjNmNiNzI3MzhjNjJlYmIzNDg0NTE5NDJlZTYwMzM1ZjMzOWQwZjczZDNjIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:21:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1641755745\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-152023543 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aXNlwC2u7eBVVmf76Srodlte78QD6PcvbxRIevxR</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-152023543\", {\"maxDepth\":0})</script>\n"}}