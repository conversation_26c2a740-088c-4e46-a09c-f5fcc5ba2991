{"__meta": {"id": "Xf53b00c075821b9a4c45a25363a41a1c", "datetime": "2025-06-17 05:42:13", "utime": 1750138933.011383, "method": "GET", "uri": "/roles/23/edit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750138931.120962, "end": 1750138933.011415, "duration": 1.8904531002044678, "duration_str": "1.89s", "measures": [{"label": "Booting", "start": 1750138931.120962, "relative_start": 0, "end": **********.324335, "relative_end": **********.324335, "duration": 1.2033731937408447, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.324356, "relative_start": 1.2033941745758057, "end": 1750138933.011418, "relative_end": 3.0994415283203125e-06, "duration": 0.6870620250701904, "duration_str": "687ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 55628176, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "1x role.edit", "param_count": null, "params": [], "start": **********.677527, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/role/edit.blade.phprole.edit", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Frole%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "role.edit"}, {"name": "1x components.required", "param_count": null, "params": [], "start": **********.783586, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.required"}]}, "route": {"uri": "GET roles/{role}/edit", "middleware": "web, verified, auth, XSS, revalidate", "as": "roles.edit", "controller": "App\\Http\\Controllers\\RoleController@edit", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=99\" onclick=\"\">app/Http/Controllers/RoleController.php:99-127</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.02905, "accumulated_duration_str": "29.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4333508, "duration": 0.01149, "duration_str": "11.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 39.552}, {"sql": "select * from `roles` where `id` = '23' limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 64}], "start": **********.459264, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "ty", "start_percent": 39.552, "width_percent": 3.614}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4899871, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 43.167, "width_percent": 4.716}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.544575, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 47.883, "width_percent": 5.852}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.551995, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 53.735, "width_percent": 4.578}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 2", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 114}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.567715, "duration": 0.008960000000000001, "duration_str": "8.96ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:114", "source": "app/Http/Controllers/RoleController.php:114", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=114", "ajax": false, "filename": "RoleController.php", "line": "114"}, "connection": "ty", "start_percent": 58.313, "width_percent": 30.843}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": "view", "name": "role.edit", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/role/edit.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.768524, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "ty", "start_percent": 89.157, "width_percent": 5.37}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 996}, {"index": 24, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 938}, {"index": 25, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 911}, {"index": 26, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 888}, {"index": 27, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 851}], "start": **********.787741, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "FormBuilder.php:996", "source": "vendor/konekt/html/src/FormBuilder.php:996", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fkonekt%2Fhtml%2Fsrc%2FFormBuilder.php&line=996", "ajax": false, "filename": "FormBuilder.php", "line": "996"}, "connection": "ty", "start_percent": 94.527, "width_percent": 5.473}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 544, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 548, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-484751355 data-indent-pad=\"  \"><span class=sf-dump-note>edit role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-484751355\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.565699, "xdebug_link": null}]}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/roles/23/edit", "status_code": "<pre class=sf-dump id=sf-dump-1910775690 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1910775690\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-310383286 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-310383286\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-31605129 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-31605129\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-575475415 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750138928337%7C13%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFTNGF3M2M2djlyNHRmalBQWmY5L0E9PSIsInZhbHVlIjoiOC9iYTZkRXZBQXpQbjJMcGNQTlN3d09BTllMODh3MGNPV0dzU1NSLzk0NTJoN0QrcG1pV2tEVFZ0NThqRzlCSlRPZCtKUnkrZmJvcFVwV0Q0TEFKMjZVVUFHU09rSS9nVDN3YWNPN0tLTG9hYnVuSXpsV1JIamJsT0dWYzZMT3IvNVAydEdkMVZTNzdkaFN6SVg1elNuMWEreWQ2N2krdFZLUzB6bkJXUVJ0QXB2aWljVi95M21KcDljcFBxeDZVRzZnMlZlQmljZ2hDT0FkTFBWOXJKdEdLdGJHQkNVckp2YVEvVEpORlV4dzIvelZTM2dhODVmK2FJaVZSdThJNmVUeW5DVGNTaVh5cTl5bEtuL25yR1JOcU5STXE5MUNYK1ltV2NhdWYyaWozaWhjNlBaZkJINDQ5dkU0NkJzZDRJZ1JuZnZYM0k2ajNlRlM2K0lFZHF0TGRRVDI4azM2YzhIT0VOM2hWdzFYQ1hUS21lT05nNnYzZHdHZ0dYV3lKWFExS2RPV2QxSHBOUUpiRHEzeGUrdk1RQUh2MS80Y1FJZWZOOFdZYVl6dndQa2NxNG9oVWIrbGhqWEtYdTFEU1k5T0dkN2NUeFFKZGZ4OG12KzdzSDNqdkxiT0xrSGdJQWV6SjV6aDZlNzBzbWJQZWh5a1dldkVWS1BqWXdCREYiLCJtYWMiOiI5MmFiNTU3OWQyOGIxYjcwMzAxYzA4YTQxNTQyNWQ0NjRhMDZmM2I0M2U2OTU4YjcwNDJhYmIzOWFmYWU0YzRiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImtPblg0a0RQRVE1QjB5MWZTdzhnZkE9PSIsInZhbHVlIjoiNzRaa2xML2FlL0JZeGNaWVp0WGpxajZ0YU9sZEdmN3JWeUV5SG9IR3dQTW1nV2p4QWU0ckpkRFpqMVBiMm40b1ZSS0l4UUhYbGdpb3ZMQ0phRDBTSmtuL3NFVVI3dXd4UmRSY2kxdHkzUmZOYUJRNUs3UGpkRU1LZmg5SWViWkpSZDloN2JuR0FjWXFMODROTHI4eXJrZllpUlpCMTBiem9xM2owS0xGdzRKQ0s4RDF4Qm1pamxSY0toK3ZVVU1zeDNoaW9QUEpuMXlWcVVCeW0rV0dDYWxJb3F0TWViQTN1QUhoRDJldnptOE5QcXdnWDZXbVFXUWpsN2w2b1M5OVlYd1hrR0pjTW8wYm5CbU5LNVdiLzFENERrdXJGMnZxSzlORllkYjh5aitFek1tTHY1OXJZNUQ0VDBpY1JJT0xzWExaQnNnakhkcjlIcmphNE5sTmxiOW5KWS9aOTdtQzNpRTJpVkxySC9pYmJSVndKN0tTREFLd0s2TVhTS0dQLzBTTWU0b2FYSkpJUW8wRVpXQS9QTWlDVXFYVCtsQSt3YWlFWjdPTWZwcTVEWHhtQ3EydDZkVEordkZIZzgxeTRUa0FvZXBSQ04vVWtZVWFpK2F3MVFicitKR0x6dDdla2NwcjBaclFOVkprZzMxQmpGdFd2eTQ0OTNSMEJxV00iLCJtYWMiOiI1NjVmODVjODA5NzA5ZjhjMTk5OTUxMzFjY2NlNTUxOWEyNDY2NjI3Zjc5MTM0YTNiYzQ4MjllMTQ5ZTkxMThlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-575475415\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-371644637 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-371644637\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-939816541 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:42:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IncwajRuaXNQSUMwTHpxTUFGNERjSXc9PSIsInZhbHVlIjoiV0MvNWdwS25tcm9NMkg3R05SdUtJUEJ3dVpZS3ZNSUs4dEEvWWlGU0xTV1NoMkNqSHlsOWFRV3Uvb2JCNUZLRk81QURNR0l0T0VTUk5OZlprdy9xTk5iSDVubVRuVVJpaFFXbXdReHhwOXV2MGVmcEtoRE9UMHJoek1TbkUvSnlsNG5NVDVaS1pPRk1sMGw1L0pWNUlOb2dJNjNpaG42R3N6M3VRTWp3L0pGV3pFV3ZiVUJuTEp1VlVrRE1temJwMG9nSHNoK3BpbXJEdkJYWDBlNVlaMlJ6d3dlcDBuUlJUaXpWajQ1S3RyVUp6RnVBRnVoeWRzUkV6SzFuOGpIaENzb2IreVRPOUxtd0g3S1IyazBUTEgrSERJTDdPR0lzckg0TXl4QldETlF6UzBKK2dEM2FmZGdnVTFqMm5PUk1sbm9UU1MzdGRXRklESVI5UHRqN0V6dGJCS3JQaDNOTytkUm1IZFA5TWhXaC93OHNQeWw1T0o1emYwTDUzVUdsNE1yVk5vTG9nWllIcFhEVTk2Vmw1eDFVdkRuNEtEaTR3cTliajBlMHVZUmMvbTFHa1RmVEVucVhPSXhqU3lYWmF4M3ozWUtaSVYrZlZsOU5hdk5PbVpiUGJzaXBkRXVnWmlZdG1qWDdaWWZqMjNDT2FZYnc3dlpCUUpTQ05NMmIiLCJtYWMiOiI2NzU4NTUwYzcwODdmNjE4MDVjNmVjMTdmMWRkZTNhYzNjMWE5MjRjOWE2MDM0MjM1YzBjZmZkNzg2NDBkODI3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:42:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Iml6ak5iUytaWUtDR2s5ekkzSXhtR2c9PSIsInZhbHVlIjoiN3hobC9kS1owZGlsUGMvblFLcnpKa0NWaGFuRUdXcGhvZFFjOVNYT2VzY3JoV01CUTB1N0dLbkJwMVB0cU1VUG82bUoxTHZQc2hoUGFYMUwwWi9CTlByWllqcjVGT1k1bjluT25JRk9BaHpmbEtRRFdLdEM4V2NlYTFBeEpEamZPREFpZGUwUmxhSld5WXdCdTlWVHhUVUhvRG9uRll5T0w3OW80b3ZjNjcwVVYybGI0RVVwYmdjM0lGUGJqUlRLQ1ByY2s2RUdVYlFZemw3b3VSTnYwOUhCRS8yQ0dXU3dqbU1jeVhyV0VWN1p2cGM5VW5iOUtlVk5QRytSVGRjdmE3VUwrU3FSaFAvTklSd1V2SVV3VlowaEltSFRBSGJGbkltdUp4V1RXZGdQa2dOOEViQTBWb1RMWnFNdEpneExGZFcvN0Q3N0g1dlkxQytLUm1FV3o2NTZmbm82cXRScHY2bUJtckZIcG1DVGcrTlFhNnducVRaZWlVd3pscXpMMmhJY2NDWDkzWDVkWEo5KzF3N2ZBcExGRWJyQjJMK09WeFRPOElML1NUWjVYNWhnb21uaFNRS2VEMnJOZ2g3OW1TZVJyaFA4UXNlenZVakRHVWx0MDJYQ2VwVXF5OU83cERuL28zVS9Za2NoNTZnMzk5YzNBV2xNR1MvK1lsZXYiLCJtYWMiOiJiNjQwN2M0NWRlNDgwODc0NjBlMjMyMmIwODU0NmY2ZjA1OTY3YjVjYWUyZDBiNmU3ZDhkNzAwMmI3NjU2YjZlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:42:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IncwajRuaXNQSUMwTHpxTUFGNERjSXc9PSIsInZhbHVlIjoiV0MvNWdwS25tcm9NMkg3R05SdUtJUEJ3dVpZS3ZNSUs4dEEvWWlGU0xTV1NoMkNqSHlsOWFRV3Uvb2JCNUZLRk81QURNR0l0T0VTUk5OZlprdy9xTk5iSDVubVRuVVJpaFFXbXdReHhwOXV2MGVmcEtoRE9UMHJoek1TbkUvSnlsNG5NVDVaS1pPRk1sMGw1L0pWNUlOb2dJNjNpaG42R3N6M3VRTWp3L0pGV3pFV3ZiVUJuTEp1VlVrRE1temJwMG9nSHNoK3BpbXJEdkJYWDBlNVlaMlJ6d3dlcDBuUlJUaXpWajQ1S3RyVUp6RnVBRnVoeWRzUkV6SzFuOGpIaENzb2IreVRPOUxtd0g3S1IyazBUTEgrSERJTDdPR0lzckg0TXl4QldETlF6UzBKK2dEM2FmZGdnVTFqMm5PUk1sbm9UU1MzdGRXRklESVI5UHRqN0V6dGJCS3JQaDNOTytkUm1IZFA5TWhXaC93OHNQeWw1T0o1emYwTDUzVUdsNE1yVk5vTG9nWllIcFhEVTk2Vmw1eDFVdkRuNEtEaTR3cTliajBlMHVZUmMvbTFHa1RmVEVucVhPSXhqU3lYWmF4M3ozWUtaSVYrZlZsOU5hdk5PbVpiUGJzaXBkRXVnWmlZdG1qWDdaWWZqMjNDT2FZYnc3dlpCUUpTQ05NMmIiLCJtYWMiOiI2NzU4NTUwYzcwODdmNjE4MDVjNmVjMTdmMWRkZTNhYzNjMWE5MjRjOWE2MDM0MjM1YzBjZmZkNzg2NDBkODI3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:42:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Iml6ak5iUytaWUtDR2s5ekkzSXhtR2c9PSIsInZhbHVlIjoiN3hobC9kS1owZGlsUGMvblFLcnpKa0NWaGFuRUdXcGhvZFFjOVNYT2VzY3JoV01CUTB1N0dLbkJwMVB0cU1VUG82bUoxTHZQc2hoUGFYMUwwWi9CTlByWllqcjVGT1k1bjluT25JRk9BaHpmbEtRRFdLdEM4V2NlYTFBeEpEamZPREFpZGUwUmxhSld5WXdCdTlWVHhUVUhvRG9uRll5T0w3OW80b3ZjNjcwVVYybGI0RVVwYmdjM0lGUGJqUlRLQ1ByY2s2RUdVYlFZemw3b3VSTnYwOUhCRS8yQ0dXU3dqbU1jeVhyV0VWN1p2cGM5VW5iOUtlVk5QRytSVGRjdmE3VUwrU3FSaFAvTklSd1V2SVV3VlowaEltSFRBSGJGbkltdUp4V1RXZGdQa2dOOEViQTBWb1RMWnFNdEpneExGZFcvN0Q3N0g1dlkxQytLUm1FV3o2NTZmbm82cXRScHY2bUJtckZIcG1DVGcrTlFhNnducVRaZWlVd3pscXpMMmhJY2NDWDkzWDVkWEo5KzF3N2ZBcExGRWJyQjJMK09WeFRPOElML1NUWjVYNWhnb21uaFNRS2VEMnJOZ2g3OW1TZVJyaFA4UXNlenZVakRHVWx0MDJYQ2VwVXF5OU83cERuL28zVS9Za2NoNTZnMzk5YzNBV2xNR1MvK1lsZXYiLCJtYWMiOiJiNjQwN2M0NWRlNDgwODc0NjBlMjMyMmIwODU0NmY2ZjA1OTY3YjVjYWUyZDBiNmU3ZDhkNzAwMmI3NjU2YjZlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:42:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-939816541\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-653077193 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-653077193\", {\"maxDepth\":0})</script>\n"}}