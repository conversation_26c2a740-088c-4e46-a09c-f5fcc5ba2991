{"__meta": {"id": "X2e0002b174e91c6d0c1e298c91a05d95", "datetime": "2025-06-17 06:28:11", "utime": **********.756982, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750141690.112883, "end": **********.757017, "duration": 1.6441338062286377, "duration_str": "1.64s", "measures": [{"label": "Booting", "start": 1750141690.112883, "relative_start": 0, "end": **********.570883, "relative_end": **********.570883, "duration": 1.4579999446868896, "duration_str": "1.46s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.570905, "relative_start": 1.458021879196167, "end": **********.757022, "relative_end": 5.0067901611328125e-06, "duration": 0.18611693382263184, "duration_str": "186ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45156040, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.009340000000000001, "accumulated_duration_str": "9.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.659201, "duration": 0.00705, "duration_str": "7.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 75.482}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.706052, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 75.482, "width_percent": 13.276}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7249131, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.758, "width_percent": 11.242}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-778511852 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-778511852\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1859812820 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1859812820\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-581689268 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-581689268\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-273174396 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1930 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; XSRF-TOKEN=eyJpdiI6ImVQWHVJakx4YkJqZzJnR2Y3VXp3Unc9PSIsInZhbHVlIjoiUzFjaGU3bDZLOTZaUXVrYXh4bUdsSi9NTmRMZXh3SHB4TGRUMmFOazd0bEhFczRUSkRuZ1RuMDFqQm04VWZRUytUdUVZL0FjbHF6Sm1ZV01nQ0kzbFE0RkpWRzhiSTlrZ08zMVFSeWwxbUhoN3F5MC85cXYxdmtMYTBac0xqSXFYRXZrdGlNaVB1dVdLNk5lMDRONmNITTFmQ2t1ZzkzbWVleXJNUTBQSVNOWTdJMWZCaS92eUxyRkVIcjJ5WFpYM09veC9rK0ViejF1byt4cS92Rk80eUpjY0R0dGxYU1dkanp6TzlnbkVNd1VvNUhFTWpnTjF6OTNsZTZNdWhvL25hdGZJQitjT0xyaUJUSG8zV2RUaWJ3d05rc2F4TUI4ejd1YW5FV0VJRmZtalFLUEFCaHJOOVVzbTFXZ2JPVHl3KzRLcmFXNUZrSlhtTmJlNHNmelhnWE5YakIvVHU3NkR4T1NmRkhjdnBuRENvNklyVHRrandrTHVsWHFtczFLQUE5NVpTLzFJYkcyQVlMTVQ4SE5qTGdJZFhQYWZySi9YYUhBUlYzMEJkbWFIRVRPYW1Wc1JPcnB4OGhVdVdORTN1eEpDaVVOQ1hvMjBEaFBYZlowZXduWElWMFB2QmE4WWZJQkFOQVBOdUVjMmpORWp4YXZFaUN3bUxXT3ZTUDUiLCJtYWMiOiI5Y2RjNjI4NGViY2UxMzE2OGRkZWEyYWM5YmEzNjVlNTFiOGM3OTJlNjE4NTQ0MzI4MzRhNWEwYmZlYjU4YmJhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkI4ellaYzZYRlBBa0gxZE93ZWxNMUE9PSIsInZhbHVlIjoiOVJFZCtISXB5M2ltNE91OTVVQ2dCbjgzQ1RVQjZyU3BBNGkzMVAzVjdkUUdlTUxEUkFITjhLYzAwaDNuVDhYeWNKZnAzQ21mWFowV3ByTFllM1hDNDBLMnNYQ2FsaWgzSHpPY1FOVmZDZFk2eElBM1Bja3BieVU1T3gvUFU2ek15V0pDYVJ1YklkdUNZS2cyNS9jT0RwSVFucTUwWHFoVlRlR2xYeWpUK1Uyb3ZZbHpLVmJKalQzQ2pKUE9KWHlGSE1BSEQrN2p3Y3FjbkFicXNteWJyWGFCQ1RHL2RRWDJDeGZGOWVsU3Q1WVU0clVpcUxTR2h6RXFjaXB5dHJPMUpkbC9NY2V1aHczVnVEb2wxc1U0dWk2bGJxRzBRd3JPUDRianFJcjAzUW9udEhKWVplNlR3ckdaTnpFL1ptcUZyRUNvTHE4aXp3WVRHaTlCZHVTNGZCMDAzNFFzdTFpQithNE52ODc1S2I3amRCVW04RUtSakl5YTJvbHM5cGtIM21DOXlaWXpxcEdWa0IycWVQSms0aDhqTWM0L0xINGluaDdycFp5V2lRTmlEc2JhckxQdENKNHlMRWdFRUJrNEJZZm41Z3JUWEo5emRJbFllRGhkWTBQM2lPNnE3bWNXUFp2UERwYmR3cnRCSXRxNjdkcDFQUjk2OE5sNnJGMHIiLCJtYWMiOiIzNTJkODY1MGQ1YjZlNGI4ZDY0YzQyZGFjMDEwNzBlZWJmMGViODFhODAzZDc3NTY1NTRhNjVmNjBkM2MzZWFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-273174396\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-124836169 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-124836169\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-909454918 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:28:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InB5RmlZbXhDOVhVKzZYQlJsZnUyNFE9PSIsInZhbHVlIjoiMHQ2OHhLWmpkWUVlWk4yRDBNMXYxTlgyU1BXM0YvcGw4bmFTRmowQnF4RHBDRjJJMERRbWhJVmZ0TXRjRGN5ZE1YTis0QnlCdkpNTENoTFc2Y3gxVlBQYjN1ZkxMWWpNTjloWTlFYzlzZFMzbkdqQ2VoS3h6c2RWUWpWSFIwU1c2UnpqcHNDMlFmYStHeHQxSjBncGkvM24xbm9PTGFtZlN1cmJYOXM3elBKd29CVEd2MGFEQUp5RkJLbk0rOVdwSUpqQXU2TTlERlRmUkpFS08zVFFRQStBVVJmYVpVVXJtbk1XTE9SdVh4Y0ZBK3JaNGFlY3Rwa0s1WUNqNEM1eHE3MGxtM3h3ZTFZMHZmN0FsTWVEbHV5L2ZJN2hVQUxXU1dPdDhrNmZhazc1bHBzWEEwODMxLytmRTY1MEhxbmNJeWY5ZVZKQ0RGUVRuanZpaVd4ci9uOUR0aERjV2hsekhMdW5IUDJlbkxzOWZuMDlSaHluR1JjQ0JnSS9WaXhxZThjMDRPRFM5b0pWY2tuY2IwN040b05pbVlCN2JNeGUzY0dReVI5MXdlL3QzQjlPUDN4bjRpZlRDVitTVmtvTHN0T1lFeVlLb09VcjBNUzF4SHU5SE83by9WRkZMeUhtcFRLUERwQThVanFZc1hLY29YTng1QmtsY21PaUFMb0siLCJtYWMiOiIyOGY2Y2U2MDY5OThhNDFhOWJjZWVlMTI2MzBkZGFkMDU1ZTYyMTg4MjQ2YzgwMDNlMGE0OTViYzcyZjdkY2E1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:28:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklkbUlZVXFjY0xvNUlYQnRRemtGbXc9PSIsInZhbHVlIjoianQrSDFCTkhpc0EzMmpEYmlGam1OZG1WeXpQY1lxVHV5UXRlWnVXVFNjYlFFWkZzbkg3cmVFQ0RHMXJ6L0s5QldTV2YzOGZ2OWVIQlpUcHRPWklCa2xzd0kyZHVrWFVyeVZpWFFDTzVQS2tkNGhwWXQ2eG9VdlRCTWY5c3hKeUJUd3p4Ry9rcGtwaHNuRFZtVTJ3SU1hYkhROVFlSWJhV1FXUzVCVXFZalpxQU5ZRkhON1pPSjJ6eTNkMzlQRVhjOEtKN2RQSmJjZUpxOGw0S3krK200Y3NsWmRsakRIZHpwc3NMaHRwVWhsYld4WTNwKytqNURZcmgvdm9zWmQ5Ny9FQkxBbDFtdVdVdTdyemhyMU5IbjE0cXFPd0pIaXNGQng2RDhYbEo0Zkttemp0V3ZjbmhOY2VZUUUrUTFMV3Fya0pSbGw0ZDFySEUvM3ltRnAxaUxBam1FRHFFOVhIc3oyREFmME9jblRFUys4dHlhTmpISUYvOEZKSkJEY0k2dW5kcjRxdHRWVmV1SHRwMlhBcTY2clVFMjdYUkRrVzhKakh5T0hKbkpkMHh0WVI0NkxraWJURnZuUWptQkppWTY0NXFNZmdjR0hKejlqclN4TWZWNDhyRTI2TGY3ZzFKN0d6YVJnd1RNZS9sbVpkT0VMTXRUWmRnYlYrVnVUZzciLCJtYWMiOiI2OGI2Yjg3ODg5NzNmZWMyMzNlMmU3YjA0YmNlMjI0NGYxYWExMTc1MzYwM2JhZDM1ZjdkMjhmOGUxM2MwYmYyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:28:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InB5RmlZbXhDOVhVKzZYQlJsZnUyNFE9PSIsInZhbHVlIjoiMHQ2OHhLWmpkWUVlWk4yRDBNMXYxTlgyU1BXM0YvcGw4bmFTRmowQnF4RHBDRjJJMERRbWhJVmZ0TXRjRGN5ZE1YTis0QnlCdkpNTENoTFc2Y3gxVlBQYjN1ZkxMWWpNTjloWTlFYzlzZFMzbkdqQ2VoS3h6c2RWUWpWSFIwU1c2UnpqcHNDMlFmYStHeHQxSjBncGkvM24xbm9PTGFtZlN1cmJYOXM3elBKd29CVEd2MGFEQUp5RkJLbk0rOVdwSUpqQXU2TTlERlRmUkpFS08zVFFRQStBVVJmYVpVVXJtbk1XTE9SdVh4Y0ZBK3JaNGFlY3Rwa0s1WUNqNEM1eHE3MGxtM3h3ZTFZMHZmN0FsTWVEbHV5L2ZJN2hVQUxXU1dPdDhrNmZhazc1bHBzWEEwODMxLytmRTY1MEhxbmNJeWY5ZVZKQ0RGUVRuanZpaVd4ci9uOUR0aERjV2hsekhMdW5IUDJlbkxzOWZuMDlSaHluR1JjQ0JnSS9WaXhxZThjMDRPRFM5b0pWY2tuY2IwN040b05pbVlCN2JNeGUzY0dReVI5MXdlL3QzQjlPUDN4bjRpZlRDVitTVmtvTHN0T1lFeVlLb09VcjBNUzF4SHU5SE83by9WRkZMeUhtcFRLUERwQThVanFZc1hLY29YTng1QmtsY21PaUFMb0siLCJtYWMiOiIyOGY2Y2U2MDY5OThhNDFhOWJjZWVlMTI2MzBkZGFkMDU1ZTYyMTg4MjQ2YzgwMDNlMGE0OTViYzcyZjdkY2E1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:28:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklkbUlZVXFjY0xvNUlYQnRRemtGbXc9PSIsInZhbHVlIjoianQrSDFCTkhpc0EzMmpEYmlGam1OZG1WeXpQY1lxVHV5UXRlWnVXVFNjYlFFWkZzbkg3cmVFQ0RHMXJ6L0s5QldTV2YzOGZ2OWVIQlpUcHRPWklCa2xzd0kyZHVrWFVyeVZpWFFDTzVQS2tkNGhwWXQ2eG9VdlRCTWY5c3hKeUJUd3p4Ry9rcGtwaHNuRFZtVTJ3SU1hYkhROVFlSWJhV1FXUzVCVXFZalpxQU5ZRkhON1pPSjJ6eTNkMzlQRVhjOEtKN2RQSmJjZUpxOGw0S3krK200Y3NsWmRsakRIZHpwc3NMaHRwVWhsYld4WTNwKytqNURZcmgvdm9zWmQ5Ny9FQkxBbDFtdVdVdTdyemhyMU5IbjE0cXFPd0pIaXNGQng2RDhYbEo0Zkttemp0V3ZjbmhOY2VZUUUrUTFMV3Fya0pSbGw0ZDFySEUvM3ltRnAxaUxBam1FRHFFOVhIc3oyREFmME9jblRFUys4dHlhTmpISUYvOEZKSkJEY0k2dW5kcjRxdHRWVmV1SHRwMlhBcTY2clVFMjdYUkRrVzhKakh5T0hKbkpkMHh0WVI0NkxraWJURnZuUWptQkppWTY0NXFNZmdjR0hKejlqclN4TWZWNDhyRTI2TGY3ZzFKN0d6YVJnd1RNZS9sbVpkT0VMTXRUWmRnYlYrVnVUZzciLCJtYWMiOiI2OGI2Yjg3ODg5NzNmZWMyMzNlMmU3YjA0YmNlMjI0NGYxYWExMTc1MzYwM2JhZDM1ZjdkMjhmOGUxM2MwYmYyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:28:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-909454918\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1477442612 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1477442612\", {\"maxDepth\":0})</script>\n"}}