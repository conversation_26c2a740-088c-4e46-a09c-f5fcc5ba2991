{"__meta": {"id": "X4eaeb3614eec11fc721d4e379f2cde31", "datetime": "2025-06-17 07:12:53", "utime": **********.724173, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750144372.983512, "end": **********.724198, "duration": 0.7406861782073975, "duration_str": "741ms", "measures": [{"label": "Booting", "start": 1750144372.983512, "relative_start": 0, "end": **********.622812, "relative_end": **********.622812, "duration": 0.6393001079559326, "duration_str": "639ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.62283, "relative_start": 0.6393179893493652, "end": **********.724201, "relative_end": 2.86102294921875e-06, "duration": 0.10137104988098145, "duration_str": "101ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45169128, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.006549999999999999, "accumulated_duration_str": "6.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6786819, "duration": 0.00462, "duration_str": "4.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.534}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.6994162, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.534, "width_percent": 14.809}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.709632, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 85.344, "width_percent": 14.656}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2062032255 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2062032255\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2042052203 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2042052203\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1135657159 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1135657159\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1028109198 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1874 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; XSRF-TOKEN=eyJpdiI6IklxZiswcFlVQTFXRW5oSUNZRnBEUEE9PSIsInZhbHVlIjoiR0h5WGZJbVczM2ZCdnhtUGFCbXBVNzZsL0hDQnF0K3lLWk00UEdGMzQySkZnM3U4V2JQZU8wUThXMlVLclg0SmZ3TGR5WXBzSkVXVWVPQldZMllyYVhpOHZwdW11V2FMSGdhTWxXbGhxTG40SGZhRVNiMzZ5VzIreUd5dk9MSXZsZkQ4bnQzVFBCUk40MHAzUnRhUVdwcTMzcEwwajI4Nm45MDVoOGRCZEJqZ3l2c2p4UVVzMWV3MU9la29aZkNTd3hRcnlwM0d3RllJMTEwMlpuTkFxQ0ZhaTIxRDVYSlBNOHpLMmgyaklKcS9iYzdMRWhWU1NFSGhidjY5MDdHcmppS0NvMWhFalRRNVdiaEhxb2pYdlFLd3h0UmVuS2NROU9QMm9LM2ZadU5hMWdic1JLM3o3clN3QlhwVzJURFdTZzVFdjVvb054WmtHK1FqMlExRWppVGhkTHpKcmhtdXFMaENYeCs0YU9INEtPK3hjRnBmSkNLSHB6WWZWVzVYMFJma1pYMCtNUTNjZDVkWk1uVDlOMkUxNmd0dTIwNGg5YWcrMDN5S2RKWlRnZnJEVW9aTnVoZmtPL3JOb0lIejdJL1BNZGFDdXJoRGxxNnV3Qk8yam4rWnBObkRQSmdSUnc0OHZDOUFBNGJQK25yK3VJdkFDczg2NUdWODBlekkiLCJtYWMiOiJjZjI3ZjgxMTQyYmNlNzdlZWVhZGU1ODhjMDBiODI0Y2VlZjNjYjExOGIwOTA1NjE1NzgwN2NiYzY2MTIxOTcwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InpJajl4eVU3cElZR0RGQW1hbmJjK2c9PSIsInZhbHVlIjoiRGQ3MTlyWmlOR0hiQVgzZVRIWXI1WGRiZ1VkdFdWbzlnVndoUkx6TXY3cmR3TkFJRDB4amc3NDRxZEtOVE9WaldQQ3Joajd5ZEtNdXh3RjUweUFZUnN3NVd3MmlIN2VLZDlyVmJ5VVBjalhwaEc4QTBhb2lDMXRZejJRUkx0OG8wUUErWmpwNmpUZU1sUy8xdVVRYmpLNmxoQXFLWFMzSXJUbWxkM0dOTm56ODJlTWplZVFSdmoxdzZQWkpSd2ZPd2JnZ0E3R003VWlRNWxFWHkyd2g3NmxjR0VjaDZWeU4vbFk1NXF4UDVlQXRTc0lLYWxRdVZxRDdIUUVzS2tLK3JnT2dkdWN0OXhDU2o1L1VwUTFwQVlMZlZXQU9GTTczU244S21raEpPd3orV2JqY09sWlJCbGN0MENBOEw4S2NqNGxxdFA3NXdiQisrdnFMVCs2WmVTU29tS3RlTlZpbkpLSWtmTHdMYk1lNW91U0RHbTNTbmcvSnZ6RFVaQWhMRDR1UEtNdVNucmhwSnM5MGxTdCt3MkNQTGVUUjdBMkUxOWdraDJzWHhJbitGYUtTVFFreFRsNS9hQlVaUHdJa0J6a1RrWERwZ1NhcnNQQTgrbDVtMk1LRUlEdjA2enM0dzZBc1lvR05iNnk5dEpqcnIrRmplbVA5NzJxdEd2ZmoiLCJtYWMiOiI4MTU4ZGQ3YTIyOGU4MjUwNzk3YWQ2Mzk1MjM5ZmM0ZWM4NDcxOTczMzkwMDZlNWU4ZTBjZGJhM2M4NTM3ZjRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1028109198\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1829559986 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDWN9h6R9gAIdzZaXBzXfq0WGpauVfrUj70kKWA2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1829559986\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1754402020 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:12:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJETDdCbzRGQU42YlpBTmR1MTdTRGc9PSIsInZhbHVlIjoiUG1YeGVaVHNjL2xvdkQ2eTlJU0hmVWs2VGZVTVgwZ09YMXROdHBUb3BvdXNhMTRCYUJNR3QvcmNWaUNqOWtKcUJMUW9UTmxuWVdwUG5mQVlvdVhmYkhORnVMQnphRktxTC9taW90TnBnTmNSVndjcWlzVGtqdTFpNG5hZjJvMER5QVZKTVhSekkrMXkyNFh5NXdNY3pTUkhpc2NmbVQzUVhCWU5lMS8reUdLTGZiL29Pd0p5SGx2eXVPSkxTN05jempvbmZQVWtYZDVMcXpiWjhZWlV1WXUvNnZvYWdmWDVvZ1dReThDZ2pwSHlTMG1pc2ZWbXJWOTRGcE85Q1IwWEc5bnN1VWJ0azRFcDF4V1V3M0wvdWdzOGpwdG84YVVza0tGd0gzQ01aSVJ5QTlZVmh1N0FaSHYvVm1JOUlTSGNtTnR4REtYdUY1VFZxRkdHbDF5YmNUL1lVWU1WUEdCSnZ1cXR0NU1RaHRCUzB6MGYxdmRoTzlUaDh3aktENDl6b0tvTWNUT1VqbTVmeU8wRFBBRTg3NkxOblV1MSt6VVpDZm1wdlRNWUhRcHJta0l2K2dyNGU1NFZxRDk4eURPb29iS3pMY3FpSHBHQkZucjJLWTFyT0Uzak1tKzgxUXdpamZ4R3JLTVFmcUtoTXN5Q1pXQ0x3OFVpODBlWEFPYUUiLCJtYWMiOiI0ZjcwYWEwNjI3MTY0ZjU4YWE1ZWUzNTA4ZGQzYjE0ZDJkOWRlMDUxYTI5ODQ5OTg1YzBjNWJhMDNkYzZiN2Q5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:12:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImFacTdVQkZoelgxZFVsUzA2SzQ3SWc9PSIsInZhbHVlIjoiVkVmcm43c2JWM3RuNStxS29oTkhQM2dTSDdYMDRLZk4vTkhpd05JQzF3V21JL1kzVWRYVnJ0cEJVQkJ3VStkaDdJc0Q1amRBeGU2b0prQytuUmxhWjQ4OFBPZ3dKb2RGS2xya0tmbytrRTRXYmFFeUwxd0VUelZEbTB2cm41WW9NWDJUU1VUWXFBaDF2MTUrSm1TSjNOMHViT1NaQWVKSXluYVJybWVRdUNvMWZKbmdqdTdURUswajd3S3VJME8weVpOUjlZZ0VtYlBWRnNvRUxqUE4yd3k2bWZDQUVpbVNyOXNpaitxSHRFY3A3ZGtVZzlSUDl4V0cydmFyRXIvYWJHNDV0Mncxdk1LQ0Jja21KNXRPV1hYR2ZLUmxUMC82QjlMMTVIdzRJb0NKeDBmYW94Qk1WUTVGc0JHb2RlaEpSWlV5a1lQZ043aTJ4cVZHWFdLOGFpdnpKQ01oT0JXbVh4UHpPUTBNN3BtUmdrNVlEZUJ4MGhpdEhOZ2ZrbzhsNmJzT20rT3NYdGpCc2wwdEZHMDh5T00zWlhTclpUeE8xNnVtck9iWnhXMmpTVjFreW5vTGFrcGp1Q3NxeG54Qm1GczdlL3cyb2w1RlBhQ0lDSDBwVTE4NGZlWHZMc3dqRXNhT05aUUNLMzJKVzNjdTdGY2tvWHRJWnREZUFQTmEiLCJtYWMiOiJmZWI3YjI1ZWUwYjI4N2NlMTk1YjI1MTg3NWVmM2Q3YzdlMWZjMGI1ZWQyZWEzZGY3NGU5OGQ3ZjQxNmZjYzc3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:12:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJETDdCbzRGQU42YlpBTmR1MTdTRGc9PSIsInZhbHVlIjoiUG1YeGVaVHNjL2xvdkQ2eTlJU0hmVWs2VGZVTVgwZ09YMXROdHBUb3BvdXNhMTRCYUJNR3QvcmNWaUNqOWtKcUJMUW9UTmxuWVdwUG5mQVlvdVhmYkhORnVMQnphRktxTC9taW90TnBnTmNSVndjcWlzVGtqdTFpNG5hZjJvMER5QVZKTVhSekkrMXkyNFh5NXdNY3pTUkhpc2NmbVQzUVhCWU5lMS8reUdLTGZiL29Pd0p5SGx2eXVPSkxTN05jempvbmZQVWtYZDVMcXpiWjhZWlV1WXUvNnZvYWdmWDVvZ1dReThDZ2pwSHlTMG1pc2ZWbXJWOTRGcE85Q1IwWEc5bnN1VWJ0azRFcDF4V1V3M0wvdWdzOGpwdG84YVVza0tGd0gzQ01aSVJ5QTlZVmh1N0FaSHYvVm1JOUlTSGNtTnR4REtYdUY1VFZxRkdHbDF5YmNUL1lVWU1WUEdCSnZ1cXR0NU1RaHRCUzB6MGYxdmRoTzlUaDh3aktENDl6b0tvTWNUT1VqbTVmeU8wRFBBRTg3NkxOblV1MSt6VVpDZm1wdlRNWUhRcHJta0l2K2dyNGU1NFZxRDk4eURPb29iS3pMY3FpSHBHQkZucjJLWTFyT0Uzak1tKzgxUXdpamZ4R3JLTVFmcUtoTXN5Q1pXQ0x3OFVpODBlWEFPYUUiLCJtYWMiOiI0ZjcwYWEwNjI3MTY0ZjU4YWE1ZWUzNTA4ZGQzYjE0ZDJkOWRlMDUxYTI5ODQ5OTg1YzBjNWJhMDNkYzZiN2Q5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:12:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImFacTdVQkZoelgxZFVsUzA2SzQ3SWc9PSIsInZhbHVlIjoiVkVmcm43c2JWM3RuNStxS29oTkhQM2dTSDdYMDRLZk4vTkhpd05JQzF3V21JL1kzVWRYVnJ0cEJVQkJ3VStkaDdJc0Q1amRBeGU2b0prQytuUmxhWjQ4OFBPZ3dKb2RGS2xya0tmbytrRTRXYmFFeUwxd0VUelZEbTB2cm41WW9NWDJUU1VUWXFBaDF2MTUrSm1TSjNOMHViT1NaQWVKSXluYVJybWVRdUNvMWZKbmdqdTdURUswajd3S3VJME8weVpOUjlZZ0VtYlBWRnNvRUxqUE4yd3k2bWZDQUVpbVNyOXNpaitxSHRFY3A3ZGtVZzlSUDl4V0cydmFyRXIvYWJHNDV0Mncxdk1LQ0Jja21KNXRPV1hYR2ZLUmxUMC82QjlMMTVIdzRJb0NKeDBmYW94Qk1WUTVGc0JHb2RlaEpSWlV5a1lQZ043aTJ4cVZHWFdLOGFpdnpKQ01oT0JXbVh4UHpPUTBNN3BtUmdrNVlEZUJ4MGhpdEhOZ2ZrbzhsNmJzT20rT3NYdGpCc2wwdEZHMDh5T00zWlhTclpUeE8xNnVtck9iWnhXMmpTVjFreW5vTGFrcGp1Q3NxeG54Qm1GczdlL3cyb2w1RlBhQ0lDSDBwVTE4NGZlWHZMc3dqRXNhT05aUUNLMzJKVzNjdTdGY2tvWHRJWnREZUFQTmEiLCJtYWMiOiJmZWI3YjI1ZWUwYjI4N2NlMTk1YjI1MTg3NWVmM2Q3YzdlMWZjMGI1ZWQyZWEzZGY3NGU5OGQ3ZjQxNmZjYzc3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:12:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1754402020\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1752146391 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TtKY2nS6X872fFvO1RFA4l1SNop1oP15UIzq14kT</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1752146391\", {\"maxDepth\":0})</script>\n"}}