{"__meta": {"id": "Xf798608c5fba7bf22bff097e05ae6066", "datetime": "2025-06-17 05:41:44", "utime": **********.638577, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750138902.889154, "end": **********.638618, "duration": 1.7494640350341797, "duration_str": "1.75s", "measures": [{"label": "Booting", "start": 1750138902.889154, "relative_start": 0, "end": **********.442571, "relative_end": **********.442571, "duration": 1.5534169673919678, "duration_str": "1.55s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.442594, "relative_start": 1.5534400939941406, "end": **********.638623, "relative_end": 5.0067901611328125e-06, "duration": 0.1960289478302002, "duration_str": "196ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45156232, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.015, "accumulated_duration_str": "15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.542871, "duration": 0.01202, "duration_str": "12.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.133}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.592388, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 80.133, "width_percent": 8.733}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6140642, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.867, "width_percent": 11.133}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-513634876 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-513634876\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1150021062 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1150021062\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-551336069 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-551336069\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-36298188 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750138884155%7C10%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InB6eTdxemtUVE1CdnNGMERqRU9UdVE9PSIsInZhbHVlIjoiclZuQWorZzliREYzbi9WU25rTGlnSjN1Qy9ETlpPMGdrVDIxdFYwWWZ0UjRyMWRKUkxQTXZnUlZNcDZaMzFnQnpUWDNQZjFNSFFFdkxtUm8rd3hmTmJaNzZYMnhORkd1Si9wVHFZcUZmYXlxaUdFQmJodlMrT2Y5cCs5SHRLL2pYOWd6QXBRTEl3U0tzcit5MlNVeUwvZ1lVb0JvN3BIV2R4STJCWm5BanpUVHJGWjRQbDdFRWJVbWtRVE90OU0xMDg4eUdFbTdnQjNuMWxOMG1UZ0VPR1E5TUN3VWJROFBDSTZXNkI2YnhoR2wrYi9ENUQrNWlLSjRzK1lHYnFlNXZhRmNNRENRNEdQZ1N5QVFvblJJemRreTJ4ZDNmT0FJS2ZCL2ErWlNaTkJDYnN6dElPWHFzT2RCdEFOcEJTL0hQUXJuSkR3ZG5nK2N0bDZXM3pkQ0dnU0tkYmJwSkdkNnpMdEJEYXRseU13dndsQlovQzFwdU12bnQvaDlqb3o2Z1l3YUxOWlRSSlpIa0VqT3VEOUhXVVhNakVBcCtrTS9qUit3dHZqL3hxMEZ0WmZKZ0x5Q3VWbGd5aUE4endsa3I4eGU0SFV6aDFxdlBWNG16VW9KU0JtSm9ZN2ZkNEhJbW01NDh1eE41S3g3MTJSWVlVQk1sV1hCeldVTTVibXkiLCJtYWMiOiI1MjM5YjQ3OGUzYzdhNzljYzIzM2I1NDE0M2U4N2Q1NDllMDhmNWFhZjU0ZjA2MjgzMzBhM2Y3ZDM0M2ZkMWYxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlJibmRKVk5xdFc3Vk9Wa0Nudk1SL0E9PSIsInZhbHVlIjoiUjhGV0d0SHQ1RzlyUm9NbC9hdkVFR05lOUxnMEltNHRKZmdra2hSZmhuV3JGdkpVU3Q1S3BwazNKM3N5NG1ubUtINGVGYm9wU01GQVdieXllWFRGV0xZZkZjVlR2dFAxNXJPM3JoUWRwbkpiV2Zmc0VWT2d6MFdZQVRjV2lzanlTamFtK0Z0TGV5cWg0cGwvQ2ljRk5YNjU2ZXBySkdHR0lsOVV1bm1Gc0pETGhjYm5jcXlpaTdlMU1LOG5UOEdRZ2NkSmF6M29kUXBhSnN0bjgwcEFKdjNiSEl3bndiNllNQU5vNHNLK0hQdFNIRG04MSsvVjgvNmV3RTFubGo2Ri9ORytDYnczNU5ld2k2cjNOQlVSZ1c4Z2VWTVZ2a1VYZkpmUTBZdCtqaWNHMzRRUXg5R21XUDNlZHdiNEZ6L083Ykkzd0Z3SENlZ09QRDJ5L3RRUWV5azJCVnpST2JpOWJJQldTY2JIRUJ4L1l1aGxFblFtakFCc1pTLzVqNFFPRHd6SGxMV3Y5TGI5OVZ1VW94UmVIdzgrN2pOSE4zQW9udGtJK1c0SWM2blVSV2hHRWNHRmFzTHJHekx2NDJVN2RqMktuWWNvNDlXR3VBYjQrUGNyR29ROTlLQlJLdFJNOER0NlVSNUx4SWswb0pCZWoxSlkyclBwSTdvazZrSEMiLCJtYWMiOiI0MWU5ODRmMTdjZDdhYzI3MjQwMmFiZTc0NjU5NTg0ZTYzNDY4YjIyMDhjNDg4MzgzNjZiMjU5OTBkZTY1NjlmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-36298188\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1322685197 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1322685197\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1639405606 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:41:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjY2YWxLeHlYS2xlUEd4QW1mTHJqVlE9PSIsInZhbHVlIjoiY3ptWFZ2RXVUMnJaUHVrYVNWcTFwQVk3OTE4SnAzd01vM1FnVmwxUEhLTElnZ0haYkhCM3RyTUdFa1phQVM4TFhCdmNhU3Nhdkw2ZDR2RVplbHZhZGw0L1QxeVFhemtTZUpSVGVLK2RZL3JTQ0Z0Tjh1SnEvbmV4VnIzOVVxNXNjOWpCMlI1TE1DYzV6TnF5ZHc0QldLWHhHWkszVDN0c2JtODljSnRhQ2hPYnJQRnNjOFA2NlNMcWNhUjBSWVdYY255dW5LaWtPb1BhVWd6KzY4S3kzamdaTm95OWluZ0FhNklPMWJPRG9tY2UveFRIRURYUzM5VTVyUEJKTTg0QWc5KzY4a2tBQzlleGxBWm1xNExhLzlOR09oTzlkR091K1pDN0ZXMjdyekFNY3RNV2QxaVRFUTVmbDFNNlRlOHl3WjVRTnlIcVZZdmtGZjhDbzBjSWc5UStFZmxINUdqQmxXYUJZbjJVSHhJOHRCeDFzWWxBODY2RmE4OGdOR3RtbzNIUjk5WW4zc2duaFNlSVUvTHpxNE5FcVczcXNtRlAwTDllc2FMcnE4WnFmaHhWeFlYSEo1UDRUNytXam5vU1V2RVJOTGZPMndqSFlYRDIxNTJmYXRnZUM1bmtMWGRRSlNiZUJDSXR3OW5iRXFPRUxCTkhrN1lXWlBSQjdVMXgiLCJtYWMiOiI4ZWRhMjhmYTZkYzVjZGY0MjQ2NjJmZjg2YTNkNWFjZDViNGU5Mjc5MThmNzYxYTFhZDc5YmE4NTVlMmZjMjc3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:41:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IllVdUtsc2prOFJDbHErYkpIWXZ5dVE9PSIsInZhbHVlIjoidVJNeUgyVTNEbmU0NGNrODNwZEpJcUs1T0NFZzM2QzJiTWQxdnROTzlTVHAycG5YT0ZId1lXczZXSjhYUXNkSGF2ekZPYnJqT1JPUCtmQ01vV2YvMFg4bDhtQjFiZ0pldzhSWFFjdnB1MGZ3Q2E1bW9HL0I2UTlObVY0eUpxQ1dWK3FHVE8ySTJYTmdBUjRiQmJTN3Z4N2VzcHovZlplcERkVFBnWE5aNVdORGlNMjVwcCtYdHZvV08yaENBQkJOVlJtdVFYQUJRK0VSRDhtWExVOVZyTTk4MThNYmN5T0RVd1VTUURSZUlLQzVpaVBKUWovblhobjV3UXQzYytuNHZiTEdUNUFJUFYvZ08xeFJkdlNhb21QMWRvMXVhdFNmUk5vSVZMZWUySHppdXF0OUlZUWhISnZkdTBKeWgzUlAzaFZrMjVWcFFCQ2x3TktLU0tqSkRVUkRtNmpjRnFTdzVOY1pyWkZxYUdJZ0hXTC9QQjc5bXdtYjVZckdWbjBENWZ0MFRoZEE2UkFYTmVlNVFXWEJEQUttVEtPNW9xeW1rdWNSU012MDFIeW4rZUt2WW9XMkozQjdzOUVPUFNMZUJPclE2R1VjTVU3NU5saVNyOGltSG8ybmVLTHRBZ2E5YUE4WGhFREFQYUVwVHVCSWdzUGhxcEhXd3dIb0w5ZXEiLCJtYWMiOiI3NTlhNTY4NjQxZjdjNDBhMjUzZDkwN2IzNjE2MzQ3M2JmNTAxMGZjNzdiNTExZWIwOGZmOTZmZWFkOWEyZTFlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:41:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjY2YWxLeHlYS2xlUEd4QW1mTHJqVlE9PSIsInZhbHVlIjoiY3ptWFZ2RXVUMnJaUHVrYVNWcTFwQVk3OTE4SnAzd01vM1FnVmwxUEhLTElnZ0haYkhCM3RyTUdFa1phQVM4TFhCdmNhU3Nhdkw2ZDR2RVplbHZhZGw0L1QxeVFhemtTZUpSVGVLK2RZL3JTQ0Z0Tjh1SnEvbmV4VnIzOVVxNXNjOWpCMlI1TE1DYzV6TnF5ZHc0QldLWHhHWkszVDN0c2JtODljSnRhQ2hPYnJQRnNjOFA2NlNMcWNhUjBSWVdYY255dW5LaWtPb1BhVWd6KzY4S3kzamdaTm95OWluZ0FhNklPMWJPRG9tY2UveFRIRURYUzM5VTVyUEJKTTg0QWc5KzY4a2tBQzlleGxBWm1xNExhLzlOR09oTzlkR091K1pDN0ZXMjdyekFNY3RNV2QxaVRFUTVmbDFNNlRlOHl3WjVRTnlIcVZZdmtGZjhDbzBjSWc5UStFZmxINUdqQmxXYUJZbjJVSHhJOHRCeDFzWWxBODY2RmE4OGdOR3RtbzNIUjk5WW4zc2duaFNlSVUvTHpxNE5FcVczcXNtRlAwTDllc2FMcnE4WnFmaHhWeFlYSEo1UDRUNytXam5vU1V2RVJOTGZPMndqSFlYRDIxNTJmYXRnZUM1bmtMWGRRSlNiZUJDSXR3OW5iRXFPRUxCTkhrN1lXWlBSQjdVMXgiLCJtYWMiOiI4ZWRhMjhmYTZkYzVjZGY0MjQ2NjJmZjg2YTNkNWFjZDViNGU5Mjc5MThmNzYxYTFhZDc5YmE4NTVlMmZjMjc3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:41:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IllVdUtsc2prOFJDbHErYkpIWXZ5dVE9PSIsInZhbHVlIjoidVJNeUgyVTNEbmU0NGNrODNwZEpJcUs1T0NFZzM2QzJiTWQxdnROTzlTVHAycG5YT0ZId1lXczZXSjhYUXNkSGF2ekZPYnJqT1JPUCtmQ01vV2YvMFg4bDhtQjFiZ0pldzhSWFFjdnB1MGZ3Q2E1bW9HL0I2UTlObVY0eUpxQ1dWK3FHVE8ySTJYTmdBUjRiQmJTN3Z4N2VzcHovZlplcERkVFBnWE5aNVdORGlNMjVwcCtYdHZvV08yaENBQkJOVlJtdVFYQUJRK0VSRDhtWExVOVZyTTk4MThNYmN5T0RVd1VTUURSZUlLQzVpaVBKUWovblhobjV3UXQzYytuNHZiTEdUNUFJUFYvZ08xeFJkdlNhb21QMWRvMXVhdFNmUk5vSVZMZWUySHppdXF0OUlZUWhISnZkdTBKeWgzUlAzaFZrMjVWcFFCQ2x3TktLU0tqSkRVUkRtNmpjRnFTdzVOY1pyWkZxYUdJZ0hXTC9QQjc5bXdtYjVZckdWbjBENWZ0MFRoZEE2UkFYTmVlNVFXWEJEQUttVEtPNW9xeW1rdWNSU012MDFIeW4rZUt2WW9XMkozQjdzOUVPUFNMZUJPclE2R1VjTVU3NU5saVNyOGltSG8ybmVLTHRBZ2E5YUE4WGhFREFQYUVwVHVCSWdzUGhxcEhXd3dIb0w5ZXEiLCJtYWMiOiI3NTlhNTY4NjQxZjdjNDBhMjUzZDkwN2IzNjE2MzQ3M2JmNTAxMGZjNzdiNTExZWIwOGZmOTZmZWFkOWEyZTFlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:41:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1639405606\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1819973885 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1819973885\", {\"maxDepth\":0})</script>\n"}}