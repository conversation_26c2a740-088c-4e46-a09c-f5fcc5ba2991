{"__meta": {"id": "Xa43e8f9d7cbbb4aeefc8aa258dfc4c3c", "datetime": "2025-06-17 07:02:39", "utime": **********.550426, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143758.78343, "end": **********.550453, "duration": 0.7670228481292725, "duration_str": "767ms", "measures": [{"label": "Booting", "start": 1750143758.78343, "relative_start": 0, "end": **********.44527, "relative_end": **********.44527, "duration": 0.6618399620056152, "duration_str": "662ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.445295, "relative_start": 0.6618649959564209, "end": **********.550456, "relative_end": 3.0994415283203125e-06, "duration": 0.10516095161437988, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45154072, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.023799999999999998, "accumulated_duration_str": "23.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.49053, "duration": 0.02187, "duration_str": "21.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.891}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5252628, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.891, "width_percent": 3.319}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5363138, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.21, "width_percent": 4.79}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-610069135 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-610069135\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1626894731 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1626894731\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1020467567 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1020467567\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2032033785 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1930 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; XSRF-TOKEN=eyJpdiI6Im82UEVHUjZIc1hUOXM2R29FTjRNaHc9PSIsInZhbHVlIjoibzJqbm5YbWhoL3AvY2tQS3FkTkdHVisyTzFaL3VrS0dPL1JDQ29YeDFLRFVzM0hxYzJocGhzdlhyaEVxZnVmNDc3TDZ4MWxsZUtTNUZ4U1VFa0ZXVnd1YzhPaUNDbkRwRkZ0QndjWEZvY1ZaSXc0WmFZM2MydGJ4NVJkdVFqVm42ZWNYbml4S0V1aDdqcXZPditrQnlWTHNXaXZtc2ZWQ3dmRDFmV3F2M1VWVjBWeTVTSzA5MHFyWWh4bHZuMDlYd2Z3YVUrTXNGL1BFQXB6elVQcDlZbHFCUS9WckhFckFTSEhxOUNmbG5zVEhYMEo3bHNDN2k2U2tLOTNHMUMyVmhMdTlRclRWZVNqLzBLcjhvazkxR0NmcHFqYjdvcXZGaE4xcUp6Y0JIYjZmQTJwZW0vOW0ydTBGN0QwSjlSTEpnbkxQSWNybFh4OTB5Q0hySWRxTVFFVTUvZ0FuUGxNQkhQMTB2RWNTWXZ1Wm4ybkdKZkhsV3lFNkZUajhsVWE2TzRtaVZkcXVzeDZuTTcwS2F3OFY4Sjd3dVlnRGJ0bjNHaXpEc3NjTFFKM1ZjcHpyQkVWcUJsR25sTWNlOWFaMDlCLzdUS3ErSkV5Q3Bic1d6L1p5N1dwRlNNcUhDTWF2U21pN2lFdTRFbHdaOWlKZ3JXK2x0bjdHYzV6ZmRORnMiLCJtYWMiOiI4YjYxZjAwMjA1ZWYwY2U2MzY4OGM5OGNiMjk4Zjk1YTdmNjI5Yzk1ODE3ZjQzZDI1N2M5MDkyYjE1NDY2ZDU3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ilg1cVQvcm9rdUZ3T0Q2RGRxRTgybEE9PSIsInZhbHVlIjoiRVByQXBlbTIvZVBWVE1ZV3YzbUppbVpqb3k5STJzVERXOGl0MTlrZUMvcnVac1BjSUJXQ3NvMTlmMDBBUmFLdjk4cmFqRVVRMVh2bGE2Y0FqeUNoaXphRk9udGU1c3YwdEY3ZWViSzR3QTQ0TWtlTG5vM2t4ZURXNWhyVVRMQzZXTUdkVnFjZzlKeSs5OEpnaGdHTU1WelYwdU9tRzd5azRZeFVoNitSdk1qUUtwVmRRaXNNamlTZzFPSmZwZG50L2ROY3BqMTNNdmIrbFVibnhXNG1HcXVsY3YzU1BPdW9ZVTNxeEJFL3hnRWFSMDAvYkNSVjVtSFhQMEwycWFKeDBGam5kZm1SY2RGVFJqNER0WVd6d21TMjRlWlhnaGs1QThjVWtadDBKd3V5ak9LS2dLa0VPejhCc2ZTcnI2dG9mMGNaQzFhbHk3VE43RHFETFg0bEFlLytBdjZuNWhRYUsxTWVCOHlORzRPajBwU2ZRamJ0Y2NYWGZPYlpiTW1UTDVUUHpQa1UwWm9vNDhLWFhpdTAzYXNwQklYVDNGY1BjVW9nVDhHbVU2aFo2UVNML2Jzc0N2V0c4eTdTMDlxaEx4blhhQ1JERTVxMGJQUGllYWNnaWRmZjJDSk45bm1nSmRuYm5KaTltL3B6c25rQWUzYnMzWmNDd252ZVZNV1QiLCJtYWMiOiIyN2Y0ZWU4Njk2MDg3YWY3NDFkOGEyNzA3Nzk5YTZhZjM2OTFhMGRiZTJhN2I3ZWU2YWE0NGNhMjkzMDc2YzJhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2032033785\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1259806670 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1259806670\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-321557989 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:02:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InptYy9wQXBDeTAwMTZDWnFJQ05nWVE9PSIsInZhbHVlIjoiRVJDemdnTXNIVVM5bDhadGY2L0ZpWWg3Wm5Oays0RVlnZWlNd1djaG5ycjlCQjF3d2VUdFhNV0ZhNVV3NW5mOXZ4UUU4b0VzTGhLL2lZdHM2ejhOWm1RSFNtQUFxZVVNb2ZSOHRuRXJaejJhbUtWOEU4SzJJVzMwVmd0aCthN25TL3J6b3JKWUhqdk5UdnBkVXpPZjlPc3U5S1ZzNTdreW1CZlk5YzMrNmdlc21IaWw1QURvWDZmU00wTlNWcWhXTjBEbDJlNXF2clE1M28vVllGTCtBdFkvMUJuNnJGU3lIS21ETjhlNi9BM1JtdFZGVWdrZGRUUWpDQzhraWE4YyszbmFIOWVnTzFDLzZXV1lRWU9aZjNqYzF2MUdjZUFmbHpSYndmQWJ0YU1oQ1lQdXVrRDBoNUhEcS8zYzh2L1cwMDl1TCs2UDZ5YWc3R1F6TGVGQWpzbEtqeVVqV3dmUFkxWEovSDVjWGVnM090TVA5cDBSaVJVNWhUNENQYWJiRm9GcXd4MUYxVlV4TVRNUGFNaDBGUGl2VllsWnJHcVNlTzA0NUg1SldiRDg4V0tQWU5UQ1pHOWQrLzk5WTJ1dnNraDYwaCtDL3BnK0JqUFEvWUVXNzBtQXB5ZWc0Qkd1TE05dnc5WFBZbDZzMTBsUlU4K0wzRUJFRUUwSEVBUm8iLCJtYWMiOiIyYzNjOGYwMWM0YjBlMWMzMDA3OGE0NWI1MjlhNDk3N2M2ZDg5YmE0ZjYyYmYzNGFmMjIwYmFiMjk2YzM5MjAxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:02:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Inh5c2UvR1dzejRnRUVDTElmQzV1alE9PSIsInZhbHVlIjoicy9ha1RUL2xSOTBzajNlRmJIY3ZZWTN3QVhuQkdqVitoU2hxWnIwdFE5VWxXKzg2OWlxV3ZiSjBHTFMzUDFrbVB6b2k3aml4enZCY24yRDdWb1B0dG5QWU9VOVMwd3I2aEdRRXFpOUUvV25ESWxOelhKanNOWkcrNmhLTlYrVHVqVytaZHRzNVNoOFdxd2pKRkNVYml5aEJQTnRLczQ0ZnpYWnRiK1J1UjRXL0FqQzEwSzN1UmMzdzZ4U2lxYWZFSEladTRmSVBSQ0NrOS9oOG5zRlc0QjRPQ0VlSkczVWJZc3RJc2lvRE1KdmtRZy9iOUpaMkgyMEdTeWZhbWxlVlQwN3U5U3F2V3FGNzNsdHl3Tjl3d3JPV2JjdlVDK3hjLzg5VWVVUU9UV1JBVnJjNE12Q0dwM0ovM1ZnaTlENlZhQjMzZFNCcWpRaGFESTg0ZEJ6ZU4vMVhLZGZ5VE5rSHgyQTBxeFBGdnQ5U2xBMlNkcW9uRzBLbm8wbUFQMTQ3N1gyaEE2K3FKaktlTWI5b000WjRwLysxTkpQbDlvYlBuN050N3YrbzdOdDRtNVExMTRSOFZWbGZPOGdwcEVqeDhpSENvc0hqSEhMWjlCdGEyb2NhMHFHOE0vWTBkMzVpb1F0aHJXZWMxcDZtNTlsNGJQcGxmL3JRdXJzNDJrYkUiLCJtYWMiOiIxZDYxNWFlNWM0OTJhODc5OGFmNTEzZmI2MTYwYjdlN2I1ZjJkZGRhNDQ2ODE5ZDQxZjQ4MDFmNjhhYWUxZWVhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:02:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InptYy9wQXBDeTAwMTZDWnFJQ05nWVE9PSIsInZhbHVlIjoiRVJDemdnTXNIVVM5bDhadGY2L0ZpWWg3Wm5Oays0RVlnZWlNd1djaG5ycjlCQjF3d2VUdFhNV0ZhNVV3NW5mOXZ4UUU4b0VzTGhLL2lZdHM2ejhOWm1RSFNtQUFxZVVNb2ZSOHRuRXJaejJhbUtWOEU4SzJJVzMwVmd0aCthN25TL3J6b3JKWUhqdk5UdnBkVXpPZjlPc3U5S1ZzNTdreW1CZlk5YzMrNmdlc21IaWw1QURvWDZmU00wTlNWcWhXTjBEbDJlNXF2clE1M28vVllGTCtBdFkvMUJuNnJGU3lIS21ETjhlNi9BM1JtdFZGVWdrZGRUUWpDQzhraWE4YyszbmFIOWVnTzFDLzZXV1lRWU9aZjNqYzF2MUdjZUFmbHpSYndmQWJ0YU1oQ1lQdXVrRDBoNUhEcS8zYzh2L1cwMDl1TCs2UDZ5YWc3R1F6TGVGQWpzbEtqeVVqV3dmUFkxWEovSDVjWGVnM090TVA5cDBSaVJVNWhUNENQYWJiRm9GcXd4MUYxVlV4TVRNUGFNaDBGUGl2VllsWnJHcVNlTzA0NUg1SldiRDg4V0tQWU5UQ1pHOWQrLzk5WTJ1dnNraDYwaCtDL3BnK0JqUFEvWUVXNzBtQXB5ZWc0Qkd1TE05dnc5WFBZbDZzMTBsUlU4K0wzRUJFRUUwSEVBUm8iLCJtYWMiOiIyYzNjOGYwMWM0YjBlMWMzMDA3OGE0NWI1MjlhNDk3N2M2ZDg5YmE0ZjYyYmYzNGFmMjIwYmFiMjk2YzM5MjAxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:02:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Inh5c2UvR1dzejRnRUVDTElmQzV1alE9PSIsInZhbHVlIjoicy9ha1RUL2xSOTBzajNlRmJIY3ZZWTN3QVhuQkdqVitoU2hxWnIwdFE5VWxXKzg2OWlxV3ZiSjBHTFMzUDFrbVB6b2k3aml4enZCY24yRDdWb1B0dG5QWU9VOVMwd3I2aEdRRXFpOUUvV25ESWxOelhKanNOWkcrNmhLTlYrVHVqVytaZHRzNVNoOFdxd2pKRkNVYml5aEJQTnRLczQ0ZnpYWnRiK1J1UjRXL0FqQzEwSzN1UmMzdzZ4U2lxYWZFSEladTRmSVBSQ0NrOS9oOG5zRlc0QjRPQ0VlSkczVWJZc3RJc2lvRE1KdmtRZy9iOUpaMkgyMEdTeWZhbWxlVlQwN3U5U3F2V3FGNzNsdHl3Tjl3d3JPV2JjdlVDK3hjLzg5VWVVUU9UV1JBVnJjNE12Q0dwM0ovM1ZnaTlENlZhQjMzZFNCcWpRaGFESTg0ZEJ6ZU4vMVhLZGZ5VE5rSHgyQTBxeFBGdnQ5U2xBMlNkcW9uRzBLbm8wbUFQMTQ3N1gyaEE2K3FKaktlTWI5b000WjRwLysxTkpQbDlvYlBuN050N3YrbzdOdDRtNVExMTRSOFZWbGZPOGdwcEVqeDhpSENvc0hqSEhMWjlCdGEyb2NhMHFHOE0vWTBkMzVpb1F0aHJXZWMxcDZtNTlsNGJQcGxmL3JRdXJzNDJrYkUiLCJtYWMiOiIxZDYxNWFlNWM0OTJhODc5OGFmNTEzZmI2MTYwYjdlN2I1ZjJkZGRhNDQ2ODE5ZDQxZjQ4MDFmNjhhYWUxZWVhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:02:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-321557989\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-190722679 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190722679\", {\"maxDepth\":0})</script>\n"}}