{"__meta": {"id": "Xd9ea32b152a411aebddcf0f9717880d3", "datetime": "2025-06-17 05:48:28", "utime": **********.837214, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750139307.283926, "end": **********.837243, "duration": 1.5533170700073242, "duration_str": "1.55s", "measures": [{"label": "Booting", "start": 1750139307.283926, "relative_start": 0, "end": **********.534401, "relative_end": **********.534401, "duration": 1.2504749298095703, "duration_str": "1.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.534423, "relative_start": 1.2504971027374268, "end": **********.837246, "relative_end": 2.86102294921875e-06, "duration": 0.3028228282928467, "duration_str": "303ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46095312, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.723239, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.742968, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.806847, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.817697, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.050960000000000005, "accumulated_duration_str": "50.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.632763, "duration": 0.03077, "duration_str": "30.77ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 60.381}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6729121, "duration": 0.01063, "duration_str": "10.63ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 60.381, "width_percent": 20.859}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6916978, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 81.24, "width_percent": 1.864}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.725039, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 83.104, "width_percent": 3.179}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.74598, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 86.283, "width_percent": 2.59}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.776903, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 88.874, "width_percent": 2.453}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.786849, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 91.327, "width_percent": 2.355}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.794005, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 93.681, "width_percent": 3.12}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.8100219, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 96.801, "width_percent": 3.199}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XTqfILO20ESOyUrbO2PEByvY2Oxsmxq0H0QyDSRn", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1028223147 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1028223147\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1880381603 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1880381603\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2106950552 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2106950552\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1pubyvl%7C2%7Cfwt%7C0%7C1993; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1s0mt6n%7C1750063901378%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNsOXl3WXAwK3YyRjd2UTEzVDdJNEE9PSIsInZhbHVlIjoibVJFdHBNUFd6NkZxekZwdDJ6SE5Sa0Q5MTFPSnRzVjAvcW0wSXllaU9iVWp2MmRVanJldDd2dVFnZXlha3kxK1lkQjEvZEJndytOY0JDRjA0NWkrTlBOYkgwcXd1Yk5RZmlPWllSSXNSUklXYWQxZm1tR2JFTkUyOENFYkFKdDhIbzZzWUJheTF0d0xreThJOGJ3V0VacldRSHAvQ0xNTzRPRjRTdUNXWDRka0lzV245eGIxZzNhUGd5Rm9lZ29idVFQS0xGVWtGVnNPRVJlN3JUTGpzSGxsaGt2aUZCaFB1Q0ozQWt6ZFEzQmdtTzlSQ1ZtZnFmZU5ucURhaEhtSi9KTS9KSGpOV0JtMDZGZlJtM3JYT1A4b2dhZDVMajY0QThpbWp0SlZsblFGMkZpbU1zWlBCNFdYUDFaYkd4WXRyNmo5NXlrK1hZbTl3eUhOdTZuSU1aSFlvNnRhOTMwRzcvbS9tdHdtU0xQQklGSmZsR28zd0Zjc3pKOCtHN016NlpYY2dGMWhvdDF6MmFDZjlBMjdKKzBQcFYySXlKWFIxZGRrNGhPU21EK1JtamxpZ0NhZUFrYzJBck1PRjg1NlRyUmxVNUdxK0syZDFhYnpuSFRIcFl3c0RaSEQwZElod2JtSDdCZWJDRUdUQ3lhNkY2UGRqbmdUVDJEREFnQS8iLCJtYWMiOiI1NmQ5MjQ3NThjOWMxYTNmZGU2OTQ2ZTg0OGVhMWFkMDliMTg1ODljNzZkNTk0YTFmZjM1NzY2MjFjZmZlYWRlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkJWT3JodjZiUHNoM1k4ZmVsQm8vNFE9PSIsInZhbHVlIjoiWUU4QWhaWmorL3BabnBJU2xZZzNGdUxDclpjSjFLR0dHZkN5b2hDb1hTTUhKT0xJVkV1NERPSDN4ZE55SGlsWWdYM3M4cFNhNGx1b3ZwTE9NRTBsZEx5SUFHY2FtNlVLamRrcVBLTGZuTjFpd3RuVWlBR1lFdVJqcWVZOFFoQlRuZ1FVd3ZoanhCaUt2MEVCOTliY2psYXNKd0lJZXpjT3BQZGZhSUFlWXpWTlZ0eFJhUHN1dWFUQlJWeVF1QkZENXdmQUhpa2JSR2dLUnNKcUFTWkM3NWF3TjJqSnVnU3BmSDE2R1pZVHBtZ0t1ZzNPN0ZSWldSM3E5K2xCNmNLTHJHajNuTDhiVklXQVVMZkpYZTRnc2dNeFU4cUVBcWE0T2gwRDVlMVMzalU2Y05QMURBeXRKR3FHd01QRXk1VlpKYnBoNHdvNGZsa2VxdkJqNWRDaW1kVHRaVS8wWE1Ycmh6WkkwSWR0eGduczBjSVRJZXorMWl6WTMxeFg4WEF3YkNTU2dPLytwdUVZSW8rVXc3NEQvVCttK0JTUitiQ1RqSWNPelA5WUhOUWo1UlpHVVJWMEhNK00wVW5ZMzZ0b21iYmVvY2dsbDFuWUc3ZjY5VVFzTFBLRDhlTmR3OEtsYUhEQkNvVU5VcVhvb2dubFR2MTdWaWRWeDJrMzYvQUoiLCJtYWMiOiIzYWM2YWNkMjQ2M2FhNTA3ZTg2OWEyZWU3YWFiMWFmZWQ2NTIzNjg5NDg1Yzk1OWU0ZjU3YTY4NjcwMzNiNzU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-581242769 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XTqfILO20ESOyUrbO2PEByvY2Oxsmxq0H0QyDSRn</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8dDZ2YVZ5BNkdHQRuaGlAK2aCC8rYkxUsxd4FSmV</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-581242769\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2098919969 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:48:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlPeTV6VXd0OUkzOGJXczR1cm5kamc9PSIsInZhbHVlIjoieS9JNmFnWTVRc1VLWkRhMUJCMkQ4U3pQaVJYSVJyS3pDTGRoZWgzTDJhRWpwbFB5UGRDeE1XMnhneHRVdUZhMUdKUmJEQ0VjSGZ5NFlSS01keVArbWhhYlFlZjlNMUtJdGhYamZuN1BEK3pvc0tkWk9rRUhQNG9nVStpTWE5SGd3MFc1ZzgyeG8rQTRGTzRDZFJ1U3FzaWx5Z25LT3l4ZTdRRUNSa1R4Nmo1MnRqMSt6RDJTd1VOM3NMTXZJdUJSaFMvT2dkenV1bWdsUVJ4NjZmeTFrbHBzN3FjMWIvcGlpLzlJVk80bFh3aUpUeVpqSFZoR3YrbmFFMEs4eVVEVmNrejRZT3h4eVpmamI4blI2N0hvZzB2K3FNUks1V2F6akFuTm0yR1d5bDhpRWRXbWovV0srZTE1N0hjcmhtc1JkeXdINGRVS3hLZi9FSG1BNWVhQ2UzVldpdmt3d3BUU09ibnlUdlJoQWc1Ty9uUVY1MWd6MENaaHpBUktTWW9aZHRNUjZqSzlZV1VIREhKMVFvMDZZZzh0YUdLc3VGOVJLMWpLbHo3VVlmbFFqZktwWGg0V2xRaUtEUFFQL0ZWWnd0bUFvaUhMRnplL0lmQ3RMVHZ2eTZDVlZxNHUrM2dKMTV0NktMNkdjdTJ6VTBmWTJXbXR3T1R0aGhhKzlqRWoiLCJtYWMiOiIzNDcwMDhiZjY4OTNmMDIxMjUzODMyNzk4MTg4ZmUzMmZmYzQ0MzEyOTJjOWRhMThkNjNkMjYyNzk0MWY0YTdkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:48:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZCSmw5d1Ewd2I2OWlEZmNGd29kSXc9PSIsInZhbHVlIjoiZVRtaVpaL1hNcU9VWHhiR3hDRjViaUE1VFBsWDR3Y1hVUG9Cb3haK0p4ZVk3cDFid3ltdlpOVXNRUm01a0dmQkNPZTZhR0R4YUsvRlV1NmN1eVVzMEpzVHVLN0VFOGg2MlVybVF1MVdDWjl6TjdrMklmejkwdHdDcjMwaWpVQlhSclZGZlVXSVdzZ0QyRjFKeFpBcmw5UForZFJaSVo1QVFneFFkZGVBZXl3WUhjTG0yRkFiakFmcTRPellCS3ZoaXl0N05qTERRNHMyVnBBMmJzbkRGeGVwRERjMURkVXpQS0x6d2FFdHZzZkx2MzJGQkgrRzJxZVRmeExFaENYRDV1eG15RG9tSjlFSm5yQ1pYUXlQSlJOVmJWQjlycC82MnZHYWxZTVdUczQ1eU15bHF2TWttU0FEZDBKeHZKckUrMi94K2RtQ1M3Z0xyUGVKTzRJcTB5K3VHZ1YvUU5kMkRKYzYzUWpnSFFyQ1UwZ2tjcVVDYlRibmpnUmw4UE1TMGY0WDJsenF4VmlMb01Mcm10aFFYRUJaSHU2TmZYQ0ZJQndnZVdScXQ5TGt5NjZNUmorRmhsVkZQeXlYb2Q1NngyY0JpNURybHg2RnhXclNiMDA3M2tTNkUxUHZPaW9Rb2pyNEM0Yk9jMjVJMUZsdlBJWkFKQU5GVDlYT0pDSUIiLCJtYWMiOiI5YTQwMzkyYTkyMDJlMDI2NzgyNGZmZDY5MDJkNGIyMDM2YjA1ZTlhNGQwNjBjNDYzMzM5YzFhOTEyZmM3MDU3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:48:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlPeTV6VXd0OUkzOGJXczR1cm5kamc9PSIsInZhbHVlIjoieS9JNmFnWTVRc1VLWkRhMUJCMkQ4U3pQaVJYSVJyS3pDTGRoZWgzTDJhRWpwbFB5UGRDeE1XMnhneHRVdUZhMUdKUmJEQ0VjSGZ5NFlSS01keVArbWhhYlFlZjlNMUtJdGhYamZuN1BEK3pvc0tkWk9rRUhQNG9nVStpTWE5SGd3MFc1ZzgyeG8rQTRGTzRDZFJ1U3FzaWx5Z25LT3l4ZTdRRUNSa1R4Nmo1MnRqMSt6RDJTd1VOM3NMTXZJdUJSaFMvT2dkenV1bWdsUVJ4NjZmeTFrbHBzN3FjMWIvcGlpLzlJVk80bFh3aUpUeVpqSFZoR3YrbmFFMEs4eVVEVmNrejRZT3h4eVpmamI4blI2N0hvZzB2K3FNUks1V2F6akFuTm0yR1d5bDhpRWRXbWovV0srZTE1N0hjcmhtc1JkeXdINGRVS3hLZi9FSG1BNWVhQ2UzVldpdmt3d3BUU09ibnlUdlJoQWc1Ty9uUVY1MWd6MENaaHpBUktTWW9aZHRNUjZqSzlZV1VIREhKMVFvMDZZZzh0YUdLc3VGOVJLMWpLbHo3VVlmbFFqZktwWGg0V2xRaUtEUFFQL0ZWWnd0bUFvaUhMRnplL0lmQ3RMVHZ2eTZDVlZxNHUrM2dKMTV0NktMNkdjdTJ6VTBmWTJXbXR3T1R0aGhhKzlqRWoiLCJtYWMiOiIzNDcwMDhiZjY4OTNmMDIxMjUzODMyNzk4MTg4ZmUzMmZmYzQ0MzEyOTJjOWRhMThkNjNkMjYyNzk0MWY0YTdkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:48:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZCSmw5d1Ewd2I2OWlEZmNGd29kSXc9PSIsInZhbHVlIjoiZVRtaVpaL1hNcU9VWHhiR3hDRjViaUE1VFBsWDR3Y1hVUG9Cb3haK0p4ZVk3cDFid3ltdlpOVXNRUm01a0dmQkNPZTZhR0R4YUsvRlV1NmN1eVVzMEpzVHVLN0VFOGg2MlVybVF1MVdDWjl6TjdrMklmejkwdHdDcjMwaWpVQlhSclZGZlVXSVdzZ0QyRjFKeFpBcmw5UForZFJaSVo1QVFneFFkZGVBZXl3WUhjTG0yRkFiakFmcTRPellCS3ZoaXl0N05qTERRNHMyVnBBMmJzbkRGeGVwRERjMURkVXpQS0x6d2FFdHZzZkx2MzJGQkgrRzJxZVRmeExFaENYRDV1eG15RG9tSjlFSm5yQ1pYUXlQSlJOVmJWQjlycC82MnZHYWxZTVdUczQ1eU15bHF2TWttU0FEZDBKeHZKckUrMi94K2RtQ1M3Z0xyUGVKTzRJcTB5K3VHZ1YvUU5kMkRKYzYzUWpnSFFyQ1UwZ2tjcVVDYlRibmpnUmw4UE1TMGY0WDJsenF4VmlMb01Mcm10aFFYRUJaSHU2TmZYQ0ZJQndnZVdScXQ5TGt5NjZNUmorRmhsVkZQeXlYb2Q1NngyY0JpNURybHg2RnhXclNiMDA3M2tTNkUxUHZPaW9Rb2pyNEM0Yk9jMjVJMUZsdlBJWkFKQU5GVDlYT0pDSUIiLCJtYWMiOiI5YTQwMzkyYTkyMDJlMDI2NzgyNGZmZDY5MDJkNGIyMDM2YjA1ZTlhNGQwNjBjNDYzMzM5YzFhOTEyZmM3MDU3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:48:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2098919969\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1061711489 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XTqfILO20ESOyUrbO2PEByvY2Oxsmxq0H0QyDSRn</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1061711489\", {\"maxDepth\":0})</script>\n"}}