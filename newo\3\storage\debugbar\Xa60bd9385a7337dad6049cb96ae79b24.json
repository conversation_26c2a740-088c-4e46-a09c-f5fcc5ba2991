{"__meta": {"id": "Xa60bd9385a7337dad6049cb96ae79b24", "datetime": "2025-06-16 15:21:46", "utime": **********.731547, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.865573, "end": **********.731589, "duration": 1.***************, "duration_str": "1.87s", "measures": [{"label": "Booting", "start": **********.865573, "relative_start": 0, "end": **********.432137, "relative_end": **********.432137, "duration": 1.****************, "duration_str": "1.57s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.43216, "relative_start": 1.***************, "end": **********.731593, "relative_end": 3.814697265625e-06, "duration": 0.*****************, "duration_str": "299ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02574, "accumulated_duration_str": "25.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.579916, "duration": 0.02166, "duration_str": "21.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.149}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6432128, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.149, "width_percent": 5.711}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.702665, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 89.86, "width_percent": 10.14}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087303446%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlA3QmxmVXVKaEhTL3Jsa2dsK09ET3c9PSIsInZhbHVlIjoiQ1dza3UvMDhXN1o2Q1FOTHVna1JPMGJiL1hLamJ4c01oTk1LYjhUQk9CK3JaeFpDREpubGpmU2NRdS9Dam1rVkpqVi8zdDJJbE1KUkZ5WWp6dVIvMEtCUCtwWjFpV2JDQytCL0VKS2wyS3kzekJqMWRMbUp2VFFHNzR5amV1MWc3a3VNbXBubXJrc1JITjlBNEZvcEJRUE9sVXNTSVoxN09GMW9pU3VnMTNPK2w5QVErb1ZRUFB4VkxnNUwycHh5Y3pQMlVTT01CY0cyVjB3Sll0Z29iakx5NTdTcFBmRmJ0OC9xNktmS3ZoS2FTRmN4T1VJUW93cFdPNlczbTNkMnBWR2xERzdDczkxelZzcmRKQ1ZibVNVYlljV1pLcGxsQWNSdFdPZ0k5ZmhlQ2tlUk4zWlNOcWJvNGZZYi9kN3hRSG50UjRnbVV3NVBLL0FRRU9DbEoyczgrZjJXSmxkWVRnUjI5Y084K3Rnd1NQYnFqR1J2clB1UEE3bFJKcHhnbkJEN0NLL2pucVhDNTdZbGNpM1VJTlREUmhPMDE1b1A1SlNENzFQZXNwYkdETTBlTWN6WmQ4TGRwSjlzSDcxN0xiTXFlTDB0L3BmRkM0d3JZMTBmVU4rSUpEOC9MLzdEUlloTGlmTmVjSjkzQ0x6YmZTQys5U3ZiODBKa0t3RGkiLCJtYWMiOiI5MzVlMTliZGFlYmJiNjE4OGUxZmQ3OTFhMjY0NjU5NTg3YjgzM2RmODAzMWQ4Y2Q5NDNiZjZlYTkwNDViZDczIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImdvRFljZ3V0QzN3VUNLRGZPd2VWU1E9PSIsInZhbHVlIjoiQWlPUm9RVWwrR3pSNjFmTE85cVdpQ0R2RkJMYlRFelZFVlAvV3VnZWF2TVZSRU1BQlRsOWpUbjIvc0Z2dXNtVlVJMzRnOWNaWnpzRUZGbnlYMDh0Rk1rVnRoNzdQVnZYZnp4UGZKUlFUOUZkRDVodTFmbC9yN2VHVnpTOUhIR1c2ekpBZENHS2lESS92YUlENWNPY1pheU50d04yRkRncVZ6cmRHVkZHa1RWSFZQVE85WHdyK04zbHFtL0t3c1Vqb2NYTThvUDFTeGJvREg1SDZMRkthUXBZWGRmand6QXlSZWdEMzdqSWNyNldMN2JsdE5jcFB5cEtOVFJneDdVb1N1czZnM0pEM0IyK3R2UGN4aEs4OUdMeklrbThhcDVSanRtTHAvSFQwVFFJOS82Tm5GRmU3ZUpwM0VRaGlXTEl2VnJsS2pQN21sRENCTzBVcXdnQk9waGpOc3RhN2ZBd3JLU1V6Wkc5NDE5V3F0c2MvSmErUko4cFFVVFAvMVBBenZ4VDR5d1ZUM0dkeHlQRUhWemRmdXJnWk5QT2NJdW9iTGhMaFNOR3d2MDBPQ29jaTJKS2djdWJkQTdwOWVwTTcxYTdtdnRrSHo3dEVIdWIzU1VNOGJrUTVtdXVKSVVjMkdLWjB1dUFMNldEZ3ltblYwUWVoM1h4c0FYMkp0RnUiLCJtYWMiOiJmZDhlMzg5ZjBjMzMzNTUzYjk5OTU4YzE5OTUyMDJlNjBkYWFlZjdhNzc1NDQzODRiYjFiNzAwZGE3MjRjMjEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WjiNcn9cZJfMB3HvNRV2ADeuSdnp1JlxvgqAKjT1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-6984543 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:21:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IncrdVAxVzJtQllubkQ4TGgyNStCMkE9PSIsInZhbHVlIjoiRFJtNEdmMHNTZkhOUGxzMzk3d09VeUZZNzBoTHZHYkNrcDZiSDFMZzZlQldDaGNvK3dKYXBXaEwvWTI3OWRYVWRRMGxWWnptVG4wSnQ1aTY3ZFBTWGdqdHA5NHNwdzhmUWFRZ0dVdUs4c3JwQVNrdDllU3NDWVhGbmN3VnpUWWsrRCtJVnd4blBuZUREeC9iL3FYY2xvT3ltdWd3K0NXU0VyYlJzeHZhZXczMG55VnhaY1NqR092NGdIQ3VMMGkzaTdOSVFaRGpxdUJmNTZQaUIxWXlURmdjWDM2WEdHSU5SZVNub0xnOGZWdCtSS2hudGQyWlhNNmowbkVlMmp6Y1ZHdFpsdUZFRmg3bHlqVXFLVFA3dWNEb0lJWFV4NFRpSVJiUHdOd2NPbUFMUVkxNjI3TDMvMElzV051eERPZHA3TjA1dVhtSklRMXArbFRwbkFVTENJUW9meUhFMFVtalZyZHdrcmdLaGVyMUszTTBJWm5RR1A3Y1k4eDZQYjgvL0lLU1BkdWQvTXBkK1RjZHZsNGJoT0Fvc2ExTEFvV0pRRG5WOXN2K21ZaHJVbndWNnlKclY3cWVSSlhnUXJQdmt6YXFVY1RYa3QzNTdEQ1BXTFFaTUpCQklObThCRllaLzdtOEZXVkJxNXh1YmlLZjE3eTlPbTJEaEpJaDYyemYiLCJtYWMiOiJkNWZhOGYzYmQ1YzgyN2I4NTVlN2YzYjczYWUzYzg0MTVjNmNiYmViZjE0ZTA2YTQ5ZDkyYmVkZmIwNWI3YjBiIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:21:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InNrOGpFYXdRejRBdEpDNzI2UjVsSlE9PSIsInZhbHVlIjoiVmkzS3JndVhhR2JFWm1aVG0rSlRsVGZhSVhuRXRrQU0zTytVenBtdUpXYWsvVjFWc2ZOVWlpTzBsTE55UUZLeXMrUmJEVXNiNy9QWEFwaG9WMVdOMk5odUw4cFkzV0oveS9iQ1RTK0kxcHN1NEtzN3hEQnRXRHlQKzRkbmZHT29LMWphZ3FSdXF0M05HZ2tZS2ZGM3lkZ25PSUtsT1Q1UVZUNE50S21WMnVwc3FNKzgxRXVTN2NHSDJqYWJyMUcyUERZUHhLZTE3S3hOTXdWaGNRRFNJUk01OHZreGtQWUQwMzlsQVBqUmVFUm1qSGNWN2p3RVRjMG1uc2xWbnVMVTd2bTRwbjR4bHd5aXJCYkYzVytHaHQyN3lBRWFFWE43bWhBc1NuYnl3a2tldUpyckwxQ3pLK29CblhEVUZ6dlpRSHJQZ0NpeW8wSTByODZWQmJMNG84MXRYdzhJYm5tNEtYTGxYaWZmZmdxQUFjSU81dTBhWTB2NTlZOTNQUGJVcHlpOTIyTCtRTjdFdFZsRWZwUGtERjEwZDRvWmRGSytLdTVWeW82OTI4a0MzVE15RnlOS24yaW5leWw2TFdxb3BnNlpkQzFXYVdINXVIUmh1RFpIWXRnWlVNazZCa0JNSHpCMHNlRUw1NEk4M2lneE1nQ3dwY2tzV3duc1VvdWgiLCJtYWMiOiJiY2YzZGZlMmQ4MzU4MmNlMGNkMDM1ZjhjYmQ3Yzg0MGJjNzUxNDE0NzZlZjdhZDg0MTIwMTMyYzg4MzI4ZTY0IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:21:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IncrdVAxVzJtQllubkQ4TGgyNStCMkE9PSIsInZhbHVlIjoiRFJtNEdmMHNTZkhOUGxzMzk3d09VeUZZNzBoTHZHYkNrcDZiSDFMZzZlQldDaGNvK3dKYXBXaEwvWTI3OWRYVWRRMGxWWnptVG4wSnQ1aTY3ZFBTWGdqdHA5NHNwdzhmUWFRZ0dVdUs4c3JwQVNrdDllU3NDWVhGbmN3VnpUWWsrRCtJVnd4blBuZUREeC9iL3FYY2xvT3ltdWd3K0NXU0VyYlJzeHZhZXczMG55VnhaY1NqR092NGdIQ3VMMGkzaTdOSVFaRGpxdUJmNTZQaUIxWXlURmdjWDM2WEdHSU5SZVNub0xnOGZWdCtSS2hudGQyWlhNNmowbkVlMmp6Y1ZHdFpsdUZFRmg3bHlqVXFLVFA3dWNEb0lJWFV4NFRpSVJiUHdOd2NPbUFMUVkxNjI3TDMvMElzV051eERPZHA3TjA1dVhtSklRMXArbFRwbkFVTENJUW9meUhFMFVtalZyZHdrcmdLaGVyMUszTTBJWm5RR1A3Y1k4eDZQYjgvL0lLU1BkdWQvTXBkK1RjZHZsNGJoT0Fvc2ExTEFvV0pRRG5WOXN2K21ZaHJVbndWNnlKclY3cWVSSlhnUXJQdmt6YXFVY1RYa3QzNTdEQ1BXTFFaTUpCQklObThCRllaLzdtOEZXVkJxNXh1YmlLZjE3eTlPbTJEaEpJaDYyemYiLCJtYWMiOiJkNWZhOGYzYmQ1YzgyN2I4NTVlN2YzYjczYWUzYzg0MTVjNmNiYmViZjE0ZTA2YTQ5ZDkyYmVkZmIwNWI3YjBiIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:21:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InNrOGpFYXdRejRBdEpDNzI2UjVsSlE9PSIsInZhbHVlIjoiVmkzS3JndVhhR2JFWm1aVG0rSlRsVGZhSVhuRXRrQU0zTytVenBtdUpXYWsvVjFWc2ZOVWlpTzBsTE55UUZLeXMrUmJEVXNiNy9QWEFwaG9WMVdOMk5odUw4cFkzV0oveS9iQ1RTK0kxcHN1NEtzN3hEQnRXRHlQKzRkbmZHT29LMWphZ3FSdXF0M05HZ2tZS2ZGM3lkZ25PSUtsT1Q1UVZUNE50S21WMnVwc3FNKzgxRXVTN2NHSDJqYWJyMUcyUERZUHhLZTE3S3hOTXdWaGNRRFNJUk01OHZreGtQWUQwMzlsQVBqUmVFUm1qSGNWN2p3RVRjMG1uc2xWbnVMVTd2bTRwbjR4bHd5aXJCYkYzVytHaHQyN3lBRWFFWE43bWhBc1NuYnl3a2tldUpyckwxQ3pLK29CblhEVUZ6dlpRSHJQZ0NpeW8wSTByODZWQmJMNG84MXRYdzhJYm5tNEtYTGxYaWZmZmdxQUFjSU81dTBhWTB2NTlZOTNQUGJVcHlpOTIyTCtRTjdFdFZsRWZwUGtERjEwZDRvWmRGSytLdTVWeW82OTI4a0MzVE15RnlOS24yaW5leWw2TFdxb3BnNlpkQzFXYVdINXVIUmh1RFpIWXRnWlVNazZCa0JNSHpCMHNlRUw1NEk4M2lneE1nQ3dwY2tzV3duc1VvdWgiLCJtYWMiOiJiY2YzZGZlMmQ4MzU4MmNlMGNkMDM1ZjhjYmQ3Yzg0MGJjNzUxNDE0NzZlZjdhZDg0MTIwMTMyYzg4MzI4ZTY0IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:21:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-6984543\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-332300097 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-332300097\", {\"maxDepth\":0})</script>\n"}}