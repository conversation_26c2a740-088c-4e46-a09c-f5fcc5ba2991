{"__meta": {"id": "X826c1481d7800e9c343720a95ba1c296", "datetime": "2025-06-16 15:21:48", "utime": **********.324039, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.741331, "end": **********.324078, "duration": 1.***************, "duration_str": "1.58s", "measures": [{"label": "Booting", "start": **********.741331, "relative_start": 0, "end": **********.088386, "relative_end": **********.088386, "duration": 1.****************, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.088408, "relative_start": 1.****************, "end": **********.324083, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "236ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00929, "accumulated_duration_str": "9.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.188979, "duration": 0.00597, "duration_str": "5.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.263}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.225929, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.263, "width_percent": 13.025}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.290175, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 77.287, "width_percent": 22.713}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087303446%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRmMlNaTGxTeTVPSFFWOXZ6alBvcFE9PSIsInZhbHVlIjoiRGQ0Z2dsdTVKNzVMOG5rQm5QNUo4YkFiSDIxRnFGK1BZWkw3b1hYQVpNOE1ENmdLbFd2WnBrSzgxYm0vdHdoY3ZBc1pRS0NtSmVSSnJZMklmSjQrWlNqZWQ2NXBFcllLMUJuQkUrRldkUlJRRS9ET0RtbmxoNXNnSVlLQ0FQN0NsSFRKMnBqNVJZM3JzKzFkbktnUnFob2RLZ3BxT2RNUzNoay9mczJpMWVxdlBSVGdVY3kyUTE4b0RtVXZlOUtBZHBtQWR5MklCa1lVYzJJZFBGbHViRTU1Y0dlbUxEYlVqbUF2OEl5UkJncWtpOXE0SE1DL2d6UXVBcFhlc2F5eUJvU25QMGRUTHVDUkdOVktXcXpnYzh4UDJEeUhaclBRcmdiNHFpYkZOeXpxN3NuTWRPelczQS9raUxTd0dpcDgwMHd2M0FRdHZkbXhXcStHaTdFckU5c3Bwa2M5Q1FCQ2hRNS9Ga0J0aURKakJaVUd3eWYrNDFTOUJpR2xKREU5M2RMcjE4bUhnSG9LNlIyZFI2R05jaVZuTmUwelUxRDQ5SC9kTUZheXhoNThPYUxFdFJaMmZaUnZsdkhVYVJFYU9QbkVVajRtMHNEUVF0cktxeHZ3a2oxVXM1V0N4dWpFN0hPTkFodWdSWEplVHpiUFErRnJsVkFKbngwRUFHeHIiLCJtYWMiOiJkNWIyNDYyNDNkNTM3NDQ2MWJhNTk0ZGIwMjU4NDU5Mjc2MmY3N2UwMzE5YWNiOGNhZGNmZWQyMzM4YTg1Y2NkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkV3b0VhZ2l5bm0rTmhJZnRyR2haMUE9PSIsInZhbHVlIjoiWWI2RXB3amdMcnBTZnlZVFF4NkZKbjBlaUxWQzFYZDF6Q1drZDc3aXFwQUkxa0xuR3Nsait5K0tMc0VFMmpMdGNNalFGVXRlVFhwS2gvNEk4eExVUlNIS1huMlZ0YmxKQ2pjLzE1ajJMalpxWlByZnlxcGE5WCtxY2FHN0Z0S0Z2NkN4T3E3cVhQQyt2RHhhdUplaUZTS2lzNDBvTlVwYmdmOWZNbWxEY0NoYldRczlPUmMwZFZWMXBiZ05nU1NmZ05wUjZYMG10Qm1RTzNVMmdDSkVOV1JnQnk5cVdsVGVpOGowclNDOTluenJsVldNaUVhTU9WR3BqcEE1SmtLRy9kVWJFZjgzOUwzblFSSmJES29ST2ZNUWdoei9lMDBYT2FibTJhTHJ4R1ZiYmFzV09XQTNwZ3ZXb0hWNHhoZmZQbVBRUHJTb1M2aVNndlRqTGRDS2FTVnovQVgvWndDd1BvT3IrWFNxSlRUelQrK2pzRUFwRTAyY2JyTUN1bGZrcjIxbndEcnlGK0hwanZIVmh2bmZZSzBqL0NWNEN2SW9MaTRuek1LVlZ5SERTbmxENm80Y0xiQUs2eVlSMDllK01jM3BGY1hRVVRXWDlEaFNMUjUwRkV1LzhJbGxaRGFVRzExZVZjMmRLUS9oS2lJZzJKV2ZaM0E4cFpyRFBFNlkiLCJtYWMiOiI2MmQyM2VkOGRiZGM5YjUzNWU2NzY3MmUxZmM0NWViMzYyNjQwMjQ3MDMwOWRhNjUxMzAwZTdlZTg4MzU2MTk1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-889246453 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WjiNcn9cZJfMB3HvNRV2ADeuSdnp1JlxvgqAKjT1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-889246453\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-737802431 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:21:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpDQllrcWc0akMwb1dkU3J1NXhoL1E9PSIsInZhbHVlIjoiNHlkRzhrMm1RSFBFZTIzc2xoNmdMWTJMMGVTTitOMWxtcTg2ZkF5QktVVjBiRENxWkpQT0Mxc3NCemlzSmd3L3dOWU5xT2t4MUVqaWkzWjRuajA1Q1NyRlFhYmF0M01HMXhQVlBnRy8wV1pNZmc5OTV6eng3SkFubmlEQmJmSUpnaFcxeTlSd3lNdVlOZy9maXZlZkFXQ2I3T3BZdExSVVRuMXVvbXMvUDhnaXNrM0NuaklFQ29WSjl1YWRrMHVyKzlCWlF4dW5xZk82cTd6dGZjbkF5dHgwWmdZd3lMNkY2QnFxRDduVUVGRDRIb1UrSUNRSHdSMU9GVXV5ZUZqSmhLT0ZPZ25WU2E4a3h3ODdaQlB3ZHpaL0g5QnpJb09rYzZSam81QVJ6TDhrMzlJdE1yZ1d5dE1CRjVycFV0YlF2QzNJZWlYQTFtQWRndjEwM2ExYlFvSElrTmk0ejNlZWlvaWJ0VE9SSndxeW5kY0RnakZhZUJwRnZQZWVYN3NsZ2Facko0eTYxWXdKNHlyODNCVEFBTVg4VFhmblVQeE5iNnpkUTZ0Y0h3Qi9DSkdHYXNlVDZIRndZemcxWG42bk5ZeDZneGF3TTBsSll1aTEvYmt2M21PNFVmeTFsREwrMFp6V0ZjWG93dHZpTlRsLzF5NUQwQWNTOTBIbVc5TDUiLCJtYWMiOiIxOWIyZTk5NTk5ZTA1OWQwMzMyODkxZGFkODA5ZWE1ZGU2NTBmNDU0NzgzMTNlZjI2NjNjOGQyNjhjN2Q3MmNlIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:21:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InNvWlR5cDFhc3hHUzFWZXpmZUZvOVE9PSIsInZhbHVlIjoiTlpFcTVEM25MTlAyd3FZdFBqQ3hWK2N2RDVzZUNXNGxySmhkYzJ6TzA0YnV1blNNUzJkWWRCYW52eU0wOGZBb1NXdWUzTGZ4UERUTnE3QUNTMHdraEtoUitzRmNLSlg4NWZibzUvYUtmN21qcEI1bHdNUUFNQ0QxOVVmZWFSOEJNdk85SVZkNTRaWHlML1RaZlRpTWZMZEtmMHRNbnF0ckJDSVhQNng3S25pRDI2WlJMSm9OajlDMlA1RDBnMVkvc0ZTMG13cG1MTmR6dENTL3pxS25OWlpWekROS2VRaXpwQWRXWU8wcFJpSU53eFI1SHAxRVROTXJGNU1UNGlpYjVsdm9XOXI4Q25QQWlFcGxNTmNMbE1VUHlKL0JtaE5VRVBkMkNqcVc2MEtIbFJKVkxPSGx4dFRta0hoazdRNE1VZjl2NTV3UmpLd2VadDFRVGF6eFJwWjE2UUxldi92YmVQVk84TG9YQVBtbFltNjI2VElQOWl1K3pzK01SRTdtc2JiR2VPeWdUUUlua2s5RnVKajJpd1RjN1FoTjgvZldpYnI3OHlYdS84aHE3c3o2T1NsTHJVVlVLRlBGMTVxVWF1Mmw0Vk9lOVo5a1FIem1oYTZGUmthRis0ZVZoRmZzZFlZUDIvaUJ0dm1PdUZ4RVAwWStLWVNNZ1Y3dzZPaEciLCJtYWMiOiJhNGE1YWE1MDI5OTA1ZWMzYTAyZTI2MzRlZjBiOWVkOTNkNjRiNGFmM2FmNzAzODc2N2IzMWJlMGYzYWE5YjkxIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:21:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpDQllrcWc0akMwb1dkU3J1NXhoL1E9PSIsInZhbHVlIjoiNHlkRzhrMm1RSFBFZTIzc2xoNmdMWTJMMGVTTitOMWxtcTg2ZkF5QktVVjBiRENxWkpQT0Mxc3NCemlzSmd3L3dOWU5xT2t4MUVqaWkzWjRuajA1Q1NyRlFhYmF0M01HMXhQVlBnRy8wV1pNZmc5OTV6eng3SkFubmlEQmJmSUpnaFcxeTlSd3lNdVlOZy9maXZlZkFXQ2I3T3BZdExSVVRuMXVvbXMvUDhnaXNrM0NuaklFQ29WSjl1YWRrMHVyKzlCWlF4dW5xZk82cTd6dGZjbkF5dHgwWmdZd3lMNkY2QnFxRDduVUVGRDRIb1UrSUNRSHdSMU9GVXV5ZUZqSmhLT0ZPZ25WU2E4a3h3ODdaQlB3ZHpaL0g5QnpJb09rYzZSam81QVJ6TDhrMzlJdE1yZ1d5dE1CRjVycFV0YlF2QzNJZWlYQTFtQWRndjEwM2ExYlFvSElrTmk0ejNlZWlvaWJ0VE9SSndxeW5kY0RnakZhZUJwRnZQZWVYN3NsZ2Facko0eTYxWXdKNHlyODNCVEFBTVg4VFhmblVQeE5iNnpkUTZ0Y0h3Qi9DSkdHYXNlVDZIRndZemcxWG42bk5ZeDZneGF3TTBsSll1aTEvYmt2M21PNFVmeTFsREwrMFp6V0ZjWG93dHZpTlRsLzF5NUQwQWNTOTBIbVc5TDUiLCJtYWMiOiIxOWIyZTk5NTk5ZTA1OWQwMzMyODkxZGFkODA5ZWE1ZGU2NTBmNDU0NzgzMTNlZjI2NjNjOGQyNjhjN2Q3MmNlIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:21:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InNvWlR5cDFhc3hHUzFWZXpmZUZvOVE9PSIsInZhbHVlIjoiTlpFcTVEM25MTlAyd3FZdFBqQ3hWK2N2RDVzZUNXNGxySmhkYzJ6TzA0YnV1blNNUzJkWWRCYW52eU0wOGZBb1NXdWUzTGZ4UERUTnE3QUNTMHdraEtoUitzRmNLSlg4NWZibzUvYUtmN21qcEI1bHdNUUFNQ0QxOVVmZWFSOEJNdk85SVZkNTRaWHlML1RaZlRpTWZMZEtmMHRNbnF0ckJDSVhQNng3S25pRDI2WlJMSm9OajlDMlA1RDBnMVkvc0ZTMG13cG1MTmR6dENTL3pxS25OWlpWekROS2VRaXpwQWRXWU8wcFJpSU53eFI1SHAxRVROTXJGNU1UNGlpYjVsdm9XOXI4Q25QQWlFcGxNTmNMbE1VUHlKL0JtaE5VRVBkMkNqcVc2MEtIbFJKVkxPSGx4dFRta0hoazdRNE1VZjl2NTV3UmpLd2VadDFRVGF6eFJwWjE2UUxldi92YmVQVk84TG9YQVBtbFltNjI2VElQOWl1K3pzK01SRTdtc2JiR2VPeWdUUUlua2s5RnVKajJpd1RjN1FoTjgvZldpYnI3OHlYdS84aHE3c3o2T1NsTHJVVlVLRlBGMTVxVWF1Mmw0Vk9lOVo5a1FIem1oYTZGUmthRis0ZVZoRmZzZFlZUDIvaUJ0dm1PdUZ4RVAwWStLWVNNZ1Y3dzZPaEciLCJtYWMiOiJhNGE1YWE1MDI5OTA1ZWMzYTAyZTI2MzRlZjBiOWVkOTNkNjRiNGFmM2FmNzAzODc2N2IzMWJlMGYzYWE5YjkxIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:21:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-737802431\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-318648914 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-318648914\", {\"maxDepth\":0})</script>\n"}}