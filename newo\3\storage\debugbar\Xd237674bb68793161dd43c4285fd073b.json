{"__meta": {"id": "Xd237674bb68793161dd43c4285fd073b", "datetime": "2025-06-17 07:08:51", "utime": **********.867181, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.14203, "end": **********.86721, "duration": 0.72517991065979, "duration_str": "725ms", "measures": [{"label": "Booting", "start": **********.14203, "relative_start": 0, "end": **********.766797, "relative_end": **********.766797, "duration": 0.6247670650482178, "duration_str": "625ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.766808, "relative_start": 0.6247780323028564, "end": **********.867215, "relative_end": 5.0067901611328125e-06, "duration": 0.10040688514709473, "duration_str": "100ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45169128, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01734, "accumulated_duration_str": "17.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.809884, "duration": 0.015470000000000001, "duration_str": "15.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.216}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8421361, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.216, "width_percent": 5.882}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.853558, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.098, "width_percent": 4.902}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pricing/products\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1562045522 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1562045522\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-408515075 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-408515075\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1056173836 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1056173836\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1293407831 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=jfj0s4%7C1750143922573%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imdmd1ZLbXJsb1JBdHJuUGJFZ0VSQlE9PSIsInZhbHVlIjoicUc0WUJHV0QvUkRHd2gxeERHdm5ZUzh1V3J5RmRiSHlLY2N5U1owaHBQVXhlZmpCVGluT2VCZVd2aEFPMXlRWndpeGdXRDFINmFrc3dSWXJWSTB5RDEwMkdjYnlkOGFYOG9lK3VYTmlMTW5aclpnbitLQ1Z0TU14NnlKd0RUWE9kRjZMbERnNkgwNzlqWEFDRmlXMjVac0hyUXFTNXRCYzh2ckJ5dks5ditNVTFkTE5vSjdkYUdaaUttdHlacXVmeWg4OXMxRlRNRVlBL3E1S2V1b2p1ZmhPZjl5N3drTThiYytkTUk2NWhMRm85RVdKRGhCeWIzNzBrRUZ1aHgyQ2VFRzYxVnJCZDFVcWVpTE04d2R6blNxdm82R2FEbXhHUklFOEp0cFRKYlRQT1VhYXRwc3BsWnZ1cU1QUStYSXhVNnVveVJBWE5IWnluNmI4T2pPODd0cFhIOGp4d3MxQXRuempqMi9MQjVSOHNGbUZIZ3N0TTBGM3ZleXdoOGtqQnA0cTRxUlNad0pBT0dJWHp0T3VJWVF0RnJTVFpJeXZWVVZCNUhhNGgzd0hIRFo5SVdGRjhob2lhL1ZwSW9LU083aTNDdkNkakFWbXlRbVJmaldkTVl0LzIzTm1WdTNnV0IrRXlPRWZXM3p3Zm5jSmRORVZDYmYyM1BPY2dCZ2wiLCJtYWMiOiJlMjEyMDY1OGYxOTU3ZjBmMGJiMDY0NGZhNmQxODE3MDQ4NDQ1YTA1ZmJmODM0ZTk3NWIxZWJhMWRjYmI0Yjk0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkxZclFqTzhwUUpwWkkzOW5LNG1nZEE9PSIsInZhbHVlIjoiekpXWTVSb0VrSktKbHRFVWttTHJzRWNIamFncmRlQ29NaE1OK1dEdklWRnZ3TER5NnN2ZkMxTzZOdUg5bGF2ZzBicUoxd0QrVkVwN1hsQXZCd1VZRUwwUTF6TUllQzlKay9yTitYUjNRTHI3TVNJc0E4ZHczb0ExVDZkT3VCc0o1dEtOYnpsSkhvTzREQXJWU0FUdEZnSHZaTGdSNElKMVNTaDg1aGVVTkwvY0s0YjR1bnk0YTYrcDhkMHFjUVVUSDgyQXZDaVNzUTVTL1IzKzFGVldlbjJuRjJPZ1gyS3d3MmxSWWF3NFVVanhMU3BhWlJCOStiNmRQRnY4MlhQLzVlaG5ySE5hcjQzWXNHRnhXSXJUWEZOTWU0bFgyb2VheTlrUkc3U1F4VVlZNXZGb2ZDckZBNGFhQWJTUXhHTXNhU2t0c3hRZWhWcllZSm1kdkljeXVydmcxbS9maTNIOVlVVFQ5UUdWUTFlUGt6YzFBV0RpNkhIYnNIb2hpUGhFTHJxQk1HSVZnMTdGcldkaFBNSEQ4MkdNOXpDK2dKSGtFSE9VcXdTQW9OZGxRQVZScnZUS1lBWEFmSC9wKzBwYjhlV21TdHgyY3ZsMlh6Z1FqYUwrdU96dDVKUGxrdFNKeWR2QlIrU0ZuK0ZNU3dXb21ISURjaGNtT0NIczVJaGYiLCJtYWMiOiI5ZDY4MTA1NDJlZjQzMTcyNDBkZDdiNzc5ZWU3NDFkNzA3NDQ5YTQ2MmFjZWFhYjdlOWZlZjQxOGI2YzI3OWRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1293407831\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1213393051 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1213393051\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-218613688 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 07:08:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJOTURoR0lCS2xYeTMzcm1BNFczUHc9PSIsInZhbHVlIjoiRE8zZE5nbm5UcEtBMUJFdURiemw3SFFxV2x5blc5bnBwV21UK3d0cWNaWCtjZXhQbytyN2lCZWExTk04ZmpuT3VhVlZHOVgydnNRdWtVSWk4ZmJBUkp3Nld4NG4zRGRpQ09xMktoMlgxSXk0dThCaEd5ZC9scnUvVElOcXVOSVVDRDY3VVBuR1UwdGFZREhSUHpuQmNLNTd2anFtaDBMSVFzdmpYL1VPNmg0MmRzRDc0bUlOZzlybHMvQkUxTUJzVlo3bllKNDhGcXIrd2srTkd4dTZmSFIxTXZvaHRUU2lMamd0dStUcU9sbkFxanIreFR4TnlCSXQ0T0xVRGxjZExQc3RHalhmQXIvL3RSM2hUc3luZHlQL3pnSVhaZ3hDdU0rZEw5cWtTZTNqbGlJVy9nNmFrd2JPRC91NmhRT2dROFZVYlV0RFJ5endtNVE0STVaSGs4cE5hWGdUZmdndzhFZDF2N1hxblVjQUFNcUNmVzlJdnJ3enRRR3ZUN0RBT3I4bDAxck1Yd1JoMzE4L1F0K2JrWDB2Y0I2QUVPK0JXdlRXTTNUT0RKaTY4Z0k5allnSnkwamI1SFZMUlRWY3IvOGR0RXhqR01VanBwVE1rV0Vkc2UweldRK3NhdWV4Zm1ZTzBRenUyeWdweHNoKzlKdVVxcFAzbHZydW42WU8iLCJtYWMiOiIzMjZiNzljMDRlOGJkZTY5OTM3MGYwZWYyNTVkZjE1MTIyODA0N2Q5NmZhM2JhZjJhOWQzNDc1Njc1ZTU3NmIyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:08:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkRBclhPb1NHZWJ0K0lBWTI3RGJqdWc9PSIsInZhbHVlIjoicjdHTlFhNzkwK09jS1laMmh5OE5RRHcrbklHTEdKT3N3VjVUTlUwUExoS1pjS2FUQ1kycnZsZWM3Um1PNzZqanY1SE9IOGtoSjRsOW9MMHk4c1d2OEtrZVBMOFkzQWJNWTJwME1WT203K2VpWUptTmpBeWtDT0I0d3pTcCtQT3NXaHdFOURxWEQzcEpMOVRZRXlJWm4rNGNHR3ZsM2xLNElMc1Z0bGM4UExkcGk3K3FFTmlYNklrZlFqZkJqazQ1TVdERDhUMENXR2d2NXIyMk1VbG9lMWRHeVZuR2NydkZpNWM1ejIzRjZ6WlRoNWR3OS9kc2JzMC9MdFB5TDZYTC9URkx2TWhSVVRabHBtZzdpbG03YzcyS1d0MEVPd1BzZE9GZWt5dlYrRy9nZUthTFpidUpRMWNTaTZWWWZYSjBlQk80M2FuRHA4N0RaZFByNHh4NU8wVExRSU01VnhzdmRjUTJmREVaN3lSK0tYSUJLQnNzdlV6NjFLdHdCVDBrdm9uODdudFMwSlArM2xpMFBBTWVIdXVIODZvQzhmNWQwRGgwOXFPQXFIR25HQWJCeVdWM1JSMC9ySUc2Qm1RZ294aEl2eExYK2t3MCtVamRsMGpIQko1ZGJKQWxReDdPWVZGMVE5VkhUelQ1VG8weTJ6TEhvc2dDN0NTS2tyOUoiLCJtYWMiOiIyZTFkNWYyMTc2NTYxOWI1ZjA2OWJmNWVlNTU1ZmJiZGE1MDViYTY1Nzc0MDg5Y2U1ZWJhMWIzNDk0OTY5YWUwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 09:08:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJOTURoR0lCS2xYeTMzcm1BNFczUHc9PSIsInZhbHVlIjoiRE8zZE5nbm5UcEtBMUJFdURiemw3SFFxV2x5blc5bnBwV21UK3d0cWNaWCtjZXhQbytyN2lCZWExTk04ZmpuT3VhVlZHOVgydnNRdWtVSWk4ZmJBUkp3Nld4NG4zRGRpQ09xMktoMlgxSXk0dThCaEd5ZC9scnUvVElOcXVOSVVDRDY3VVBuR1UwdGFZREhSUHpuQmNLNTd2anFtaDBMSVFzdmpYL1VPNmg0MmRzRDc0bUlOZzlybHMvQkUxTUJzVlo3bllKNDhGcXIrd2srTkd4dTZmSFIxTXZvaHRUU2lMamd0dStUcU9sbkFxanIreFR4TnlCSXQ0T0xVRGxjZExQc3RHalhmQXIvL3RSM2hUc3luZHlQL3pnSVhaZ3hDdU0rZEw5cWtTZTNqbGlJVy9nNmFrd2JPRC91NmhRT2dROFZVYlV0RFJ5endtNVE0STVaSGs4cE5hWGdUZmdndzhFZDF2N1hxblVjQUFNcUNmVzlJdnJ3enRRR3ZUN0RBT3I4bDAxck1Yd1JoMzE4L1F0K2JrWDB2Y0I2QUVPK0JXdlRXTTNUT0RKaTY4Z0k5allnSnkwamI1SFZMUlRWY3IvOGR0RXhqR01VanBwVE1rV0Vkc2UweldRK3NhdWV4Zm1ZTzBRenUyeWdweHNoKzlKdVVxcFAzbHZydW42WU8iLCJtYWMiOiIzMjZiNzljMDRlOGJkZTY5OTM3MGYwZWYyNTVkZjE1MTIyODA0N2Q5NmZhM2JhZjJhOWQzNDc1Njc1ZTU3NmIyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:08:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkRBclhPb1NHZWJ0K0lBWTI3RGJqdWc9PSIsInZhbHVlIjoicjdHTlFhNzkwK09jS1laMmh5OE5RRHcrbklHTEdKT3N3VjVUTlUwUExoS1pjS2FUQ1kycnZsZWM3Um1PNzZqanY1SE9IOGtoSjRsOW9MMHk4c1d2OEtrZVBMOFkzQWJNWTJwME1WT203K2VpWUptTmpBeWtDT0I0d3pTcCtQT3NXaHdFOURxWEQzcEpMOVRZRXlJWm4rNGNHR3ZsM2xLNElMc1Z0bGM4UExkcGk3K3FFTmlYNklrZlFqZkJqazQ1TVdERDhUMENXR2d2NXIyMk1VbG9lMWRHeVZuR2NydkZpNWM1ejIzRjZ6WlRoNWR3OS9kc2JzMC9MdFB5TDZYTC9URkx2TWhSVVRabHBtZzdpbG03YzcyS1d0MEVPd1BzZE9GZWt5dlYrRy9nZUthTFpidUpRMWNTaTZWWWZYSjBlQk80M2FuRHA4N0RaZFByNHh4NU8wVExRSU01VnhzdmRjUTJmREVaN3lSK0tYSUJLQnNzdlV6NjFLdHdCVDBrdm9uODdudFMwSlArM2xpMFBBTWVIdXVIODZvQzhmNWQwRGgwOXFPQXFIR25HQWJCeVdWM1JSMC9ySUc2Qm1RZ294aEl2eExYK2t3MCtVamRsMGpIQko1ZGJKQWxReDdPWVZGMVE5VkhUelQ1VG8weTJ6TEhvc2dDN0NTS2tyOUoiLCJtYWMiOiIyZTFkNWYyMTc2NTYxOWI1ZjA2OWJmNWVlNTU1ZmJiZGE1MDViYTY1Nzc0MDg5Y2U1ZWJhMWIzNDk0OTY5YWUwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 09:08:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-218613688\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-573585668 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/pricing/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-573585668\", {\"maxDepth\":0})</script>\n"}}