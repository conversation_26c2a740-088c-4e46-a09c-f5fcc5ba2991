{"__meta": {"id": "Xfbed0c438295e6815b658582dfc9daa7", "datetime": "2025-06-16 15:22:01", "utime": **********.749816, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087319.926735, "end": **********.749854, "duration": 1.8231191635131836, "duration_str": "1.82s", "measures": [{"label": "Booting", "start": 1750087319.926735, "relative_start": 0, "end": **********.464742, "relative_end": **********.464742, "duration": 1.5380070209503174, "duration_str": "1.54s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.464767, "relative_start": 1.538032054901123, "end": **********.749858, "relative_end": 3.814697265625e-06, "duration": 0.28509092330932617, "duration_str": "285ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45215264, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02325, "accumulated_duration_str": "23.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.56341, "duration": 0.01725, "duration_str": "17.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 74.194}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.623804, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 74.194, "width_percent": 5.978}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.694162, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 80.172, "width_percent": 10.71}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.720457, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 90.882, "width_percent": 9.118}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-280145696 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-280145696\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-73121246 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-73121246\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2003814831 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003814831\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1579688620 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087303446%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVCNDh6dzRMSHg1RWRtVngvQy85S0E9PSIsInZhbHVlIjoiM00vVXoyVTJqRWFVbDVaTDZueWU2WCtzdVVxVlc2K3NxUG93Q2pSN3BoQmF5VGpDdmErdFhIaXV1UVJtZ000bld5d05IWHZsY1RUVzFUSEV1MmY4S3Y3RDdUdHFYblROQ24rSVRrMUV6ODJOQ2loWjdvemxiY2hkQkhjNmlVL09DWVhjUGxsU0lyUGx2OWw1dEdGMWJOdHFJTzhUWmZUT0ZsUG45U1pOcTlwckRjMzRPb1czYjEweW94dzRwVktCdXB6a1JXVExDdHZWd3FMWDBOamNGRDFqMERKTmFVeS9EOWtVb2l2SEV5OW9QMTlJZ2taZ1kvUGZabFVsRUhRR3JmZ3ZSeUdQTnZjTmVPbEtvTnpWNzVUa2d3VmRrclZxaEw1eVZZenAvSHZhVUJnOXIvNlZnUmtTYlFERHJpUTVkNXh6ZHpNL25jUmpud3lZbEptUExjejhmUXZoY3Y3OUI4VXIwZ3kzai9BVnU5Z3ZpRlBtL2NtOU1sTGU3UlFrMnorRlE3OVRHa01tdkQ3SmRWRmgyN3UwcnpDWXhMWUIyR2ppT2M5ZThYd2I2Y040aW1uR3RFYjhmSnZvNDBPMTNpazNxU2lFUUdWSitJZ3RESXcvUUFEVktzZmlZNG1LM2tKYmN5dldRMEhBdXM5clRxVTVnbVpBSlhibU44NFkiLCJtYWMiOiI0OTJhM2ZkN2QzZDczYzVlMjg1M2RiODBmNjYzMDE5NTViODkyMTJkYjVmNDg5ZDI5MjY3OWU1N2RlY2I1MWRkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjEvRE4vVG9zalVGZHRGKzgvc0lEK2c9PSIsInZhbHVlIjoiTngrYnhVQ0ZvaWxncHpaZUdKUW5rL092VEZGZ1BSdjBBTGNmZitMUGlhdGoyYnEvRjRLNkhLaVk4U1ZJSVNCTDVSOUJ6Z0ZGaXN1OEs1MDd6Tk90ZkxaN3JWZXl5TW5sTWhHUUJsN0tOYkhKL0dvQy9TMGhHUSthbmh3VHk5S2trRmxlWCs1OG5TTkRRY3NWSXlocW9lSGsrTEorUmhBSXhXUWtEMmVNRm9UUzA5S0JPQzFXMjAwejVuQ09zVExHN0p0WXVKYkovcTY4dmcrcFlhbjBUOHJoR2ttYWlaRjVpNC9Fa2FXWHExa1JSdjh1ZWNOUS80WWlGeEYwNTMvblI4d2ZpWmMzN2dnUXo2QWE0R1B5Tno2WnVyakhBcmdhZnZIeHN1Y2hFY2xmQU5ONytVN25EN3c3djhHaDY3S1RlL2dGbjRQQlB5UkcyZ3JWNWFRQ1RBS2NhY0pvWnVSaUZobE1zS2x1cDU3N25wN3lRRGZjMGxFMzFGajNISU1naVpiV1BHdkFqMkJjdlBmbTFlYnQ5RHJOTDBCME5PRGk0K2ZzblB4Z1ArdEdHUllnMmVtSEdQM3UwV2dYQlk4WWNsVFFrSzFGZGlmM21kOVpyUEFUZU5yMVdmYVVFSUNOZ1l4NXczeVRWWVNQVFlCc29DUy9qQTRIZU9DUmRqVmwiLCJtYWMiOiIxZGIwNmRmMGYxNWMxYjAzNjMwYzU4ZTE1YzYxOTEzN2Y4NDBlMTg4ZmM0MDBjMTY2OGJmNTVjZjgyYTRjMmFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1579688620\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1983690704 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WjiNcn9cZJfMB3HvNRV2ADeuSdnp1JlxvgqAKjT1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1983690704\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1908505208 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:22:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNkZnB4TlZEZU9pOUV4NFZiYWNVNUE9PSIsInZhbHVlIjoiSkFlU0F3eE1BRGF4RzFHeXQxUE9rd3VwbXVEUmdGMWx1UldtK3dQR0dzK2ZUOUxoRkh5U2U4SGlFVXlKck5lZ25taVdrWnF1Q1lVakM2djRTUkxEVzAyK3lsM25kSUZYS3BNaXlmZmdZZTBIbXEzMHVleVVraGJiTHNrZXZ6cCtTc1FBME42blFERWUyL0ZSRy9yZDZ1U1loa1hMSlB2ODRNYUwzdXZiazZZNXlvNVlCOWY1QXlsUVNlQlJKTG1SejFlLzllWGVGZUYyMW10cTY2Zk5rWXpKOFFXMXcyWVpueTZycGtqYmpoYTJsbmowb1RuelIvSng0UGpMVFFaeWVKYjY3Wnlab016NUM3OXIydU1hODU0TWhZei9mRlhGTnhwREtFNGNoRTg3MHQ1M3o1aUp6ZXlFckpESWVaYzhOSjRXbk9QT1hLbzhJbnIzdWtIdFRuZ0o5dnFBeGNvb21qNVFsdm9LS21EWThVckx6cmkwTW9KWnF0Rzl3NW94YWpzcmtzMmd3eTBiU2dyQnIxRUFCUGhjYzlxMnkrL2EyYmVYOWVZcE5DNVRWQ3pNWC8ybEN5MmwyRitLTGJhLyt2enVKNU50K0pXT09EYjZyamU5U0tpenpQRzJUUW52STlHUDBLWDh5Z05HdkppYUlEdEpFdVdmSTY1RzloUGUiLCJtYWMiOiI1ZjM2MGEwZjkwZGVmMDA3MTY1ZmE0MTI3OTY4NjM4YjAwYTA5NTgyMWUwMmIwZTlkZTMwZmQxN2E5ZjhiNWFmIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:22:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InplWGdQWnQ3eGIyRnlHUGZ0YVpJZVE9PSIsInZhbHVlIjoiQmxoMURqcy9zNzBDZUl2N0hWRnEyL1I0NnZER3c1U0tTZUhkc3lsWW9RM2NVc2V1T2JGanBqRnNzM0U1YlQ4U3ZxNVhOMldNaUduUlMrZGh5NWNBaEVyK0VPcm01TEsxdzV5YjNrSlVOZEREclQ1L1I2S3RheVdLQTB3RkhCSGh6SlNCTXdSRFpkSFlvUmhCTllDR3hxQlJiaUMwT2NjODI1ZFN4OHBaeG8rTS90VlJzbVJEb3J5Nmhpa2tWTkRmYlE1UDV6ZUZhRnVBb0xMNURsT29NT20zSm83RmxQcWxzMXBPeXpMWDhkb21sM3hkcXlCY2VwUG1HZ1Nub0h4VzlhU3N3aWtTSk8xNjdsTWlXcUhrb05uTW5lTis0R0JXVFd6Rk1QOXMzcFhTUXlkcUsrWlEzSWlBU0tqSEZpVS83czZBazA1TGNUb0JmaHlwdFR0VS8yK3VrRWltSWVSdnJ0NDhjd3psT3FMck9PRXFvWWRaQXlEaVNuN0dFd1hDRk9rbVlsWlBCU2doY2VJWmNYQVdLVWtFK2ZQdDNkeTNFUGNvTTJWU00xcUFrTmx1bjl2TGlaSkpGdmFPdGw2N1FEeWgyK0pTOWVubW44bUl4NUNXeUN2Nm92TEZ1NjQ4RG1sTlE3SnYvdEhORldMY2dFQ20vbnpWSU9BTEJxOG8iLCJtYWMiOiIwZTQ0ZWZhNTY1ZmI2YmY2ZDAzOTQyNDJkYzIyM2E1NzJiNDg3MDZlNzkzNzE2NzM3ODlmZThkZTFlN2UwOGUyIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:22:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNkZnB4TlZEZU9pOUV4NFZiYWNVNUE9PSIsInZhbHVlIjoiSkFlU0F3eE1BRGF4RzFHeXQxUE9rd3VwbXVEUmdGMWx1UldtK3dQR0dzK2ZUOUxoRkh5U2U4SGlFVXlKck5lZ25taVdrWnF1Q1lVakM2djRTUkxEVzAyK3lsM25kSUZYS3BNaXlmZmdZZTBIbXEzMHVleVVraGJiTHNrZXZ6cCtTc1FBME42blFERWUyL0ZSRy9yZDZ1U1loa1hMSlB2ODRNYUwzdXZiazZZNXlvNVlCOWY1QXlsUVNlQlJKTG1SejFlLzllWGVGZUYyMW10cTY2Zk5rWXpKOFFXMXcyWVpueTZycGtqYmpoYTJsbmowb1RuelIvSng0UGpMVFFaeWVKYjY3Wnlab016NUM3OXIydU1hODU0TWhZei9mRlhGTnhwREtFNGNoRTg3MHQ1M3o1aUp6ZXlFckpESWVaYzhOSjRXbk9QT1hLbzhJbnIzdWtIdFRuZ0o5dnFBeGNvb21qNVFsdm9LS21EWThVckx6cmkwTW9KWnF0Rzl3NW94YWpzcmtzMmd3eTBiU2dyQnIxRUFCUGhjYzlxMnkrL2EyYmVYOWVZcE5DNVRWQ3pNWC8ybEN5MmwyRitLTGJhLyt2enVKNU50K0pXT09EYjZyamU5U0tpenpQRzJUUW52STlHUDBLWDh5Z05HdkppYUlEdEpFdVdmSTY1RzloUGUiLCJtYWMiOiI1ZjM2MGEwZjkwZGVmMDA3MTY1ZmE0MTI3OTY4NjM4YjAwYTA5NTgyMWUwMmIwZTlkZTMwZmQxN2E5ZjhiNWFmIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:22:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InplWGdQWnQ3eGIyRnlHUGZ0YVpJZVE9PSIsInZhbHVlIjoiQmxoMURqcy9zNzBDZUl2N0hWRnEyL1I0NnZER3c1U0tTZUhkc3lsWW9RM2NVc2V1T2JGanBqRnNzM0U1YlQ4U3ZxNVhOMldNaUduUlMrZGh5NWNBaEVyK0VPcm01TEsxdzV5YjNrSlVOZEREclQ1L1I2S3RheVdLQTB3RkhCSGh6SlNCTXdSRFpkSFlvUmhCTllDR3hxQlJiaUMwT2NjODI1ZFN4OHBaeG8rTS90VlJzbVJEb3J5Nmhpa2tWTkRmYlE1UDV6ZUZhRnVBb0xMNURsT29NT20zSm83RmxQcWxzMXBPeXpMWDhkb21sM3hkcXlCY2VwUG1HZ1Nub0h4VzlhU3N3aWtTSk8xNjdsTWlXcUhrb05uTW5lTis0R0JXVFd6Rk1QOXMzcFhTUXlkcUsrWlEzSWlBU0tqSEZpVS83czZBazA1TGNUb0JmaHlwdFR0VS8yK3VrRWltSWVSdnJ0NDhjd3psT3FMck9PRXFvWWRaQXlEaVNuN0dFd1hDRk9rbVlsWlBCU2doY2VJWmNYQVdLVWtFK2ZQdDNkeTNFUGNvTTJWU00xcUFrTmx1bjl2TGlaSkpGdmFPdGw2N1FEeWgyK0pTOWVubW44bUl4NUNXeUN2Nm92TEZ1NjQ4RG1sTlE3SnYvdEhORldMY2dFQ20vbnpWSU9BTEJxOG8iLCJtYWMiOiIwZTQ0ZWZhNTY1ZmI2YmY2ZDAzOTQyNDJkYzIyM2E1NzJiNDg3MDZlNzkzNzE2NzM3ODlmZThkZTFlN2UwOGUyIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:22:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908505208\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-314554277 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-314554277\", {\"maxDepth\":0})</script>\n"}}