{"__meta": {"id": "Xb9290538a68cfdac0dc139efa89d0da4", "datetime": "2025-06-16 15:22:11", "utime": **********.706765, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750087330.02352, "end": **********.706802, "duration": 1.6832818984985352, "duration_str": "1.68s", "measures": [{"label": "Booting", "start": 1750087330.02352, "relative_start": 0, "end": **********.497044, "relative_end": **********.497044, "duration": 1.4735240936279297, "duration_str": "1.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.497064, "relative_start": 1.4735441207885742, "end": **********.706807, "relative_end": 5.0067901611328125e-06, "duration": 0.20974278450012207, "duration_str": "210ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45169568, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01918, "accumulated_duration_str": "19.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.605453, "duration": 0.01554, "duration_str": "15.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 81.022}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.6549292, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 81.022, "width_percent": 6.1}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.680444, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.122, "width_percent": 12.878}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=8f5bqk%7C1750087320647%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFsYiswQ09QSjlyNC9zVDUyOHBmbEE9PSIsInZhbHVlIjoiYi9uRGhZc0NHRUZBZ1JkeXdLSFk1Qisvd2xjUW51MXA2aHZxRXh6T29ZRzZ5UzNMNUU1Rk9MUFRLdHNSZFJ4b01QckU0Y3AvaDhoRHRBY3h3UnV1KytIS3RSSWFvaDZWcWF2SGdud0hSYjN5V2tIbkxvWGZqSjlxeTZ1ZHZMSWVoYTcyRkt2Q0paYWEyR3dSaUhOakJkcjRSeGNvWXRTeWFsak5zMGhZS1ZuVE1FcHp6R1dhek9SNEJPVTFwUkRraGQ2c1ExNFlwY3lhem5WdnVaQkhxQXFLU2JiV2NJczNLWDFsWlpnbEwxNHVlRXN6ZWpTWHBuaXA2R1hmZ1NTcWRFMEVZWkVtYmVoVFBTaHNDT0ppUXFvNEZTbU9sSGNrek40b09OMS80a0cvNkpyRXlPemtjR3E2Z3NTdXVJbXhpSXpqRDJNKzUzQS9FNkMrSTdJNDFKQ1hhczJCSEtVNGJxY0prZGFKMUduZGlwd0poRjJOOG1mV2VjcVJZK3dNWFhEczZWd3N4dldEajlMZk1FbTVyVGRKQ2YxWEliSHY4WWFCQ0ZTa0JyZXpxaElzellkSlBBSjd4YkVGZ25YRUEzT085SnU5ekpCQ2ZZWXlhdzVXeE80RnFPejQ0QVYrN0NvaFl2V05LTHdGUmpJaEtHeDBpYXY4RTJGUWUrRXIiLCJtYWMiOiJhZjdlYWRmYWYzNjM2YmRkMTNiMmZhNmU4YmZkYmJhYTZkMWEwMzY1MDUyYTZiMTM5YmQ3YTMwODA2Njc2ODFjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjhPMm5lSWFsODdpVnpaRmE3R1JiUVE9PSIsInZhbHVlIjoiS09ySG9ZNVMxdjR3Z05WWmQzb2J5QXNTVXkwQ1FDVmxNQ2VzZFhvZjVOSDZoRDV2OEgwSm9JeVJKTXMrSmhBaGcvVUxGK0s2NHRLOVVibGRhbEJuWnQ3MnpwZ3d3VGRzckxBWUVXQlZ6U1BVNFhBcmhQek0zOEZ0Ym1HMURJVEg3bnpEZmR2YU9iYWFCTEUyaVRNb2RXaStIcFR1azVrTXNvcVNaNGJ6ZVYwby8zdVg3YllMY3NSRmtqNEhkVXgrdG9CVUF4WS8yYVZiOUJDb0Q1VGNQbEkzckM3ZllUbVZNZWdqbHdSelVxSTJRUzgzK2dJMVZzaUZJNkV6WmJ1QUdPZCtYZ0IxTnJSUU1UcWdGbXk4Ny9kZTVpQytzTGpFL3U0OC9YTnNkNHF5bGYrT2kyblpvbnlnSkFQUTBHYlZXclhtM0IxbldCLzlyUDZaWk5LMkRMWGZQQkJHWmZvalN1b2x3d2dKYU9SaGtnVU9oRlhTV25wcXZPWlpSU05sZkNUREUvTzFqRnJSR21IV3draGphUmk0NGNXVWtmM292STFGMGRIbkhTVkNmdmQ0WGlKbHdkajhxcDVSdVpHN2xoUkdzUFRBYklFenZzOHRBRURQa0JWT1BZajNyU041TTRpVWRPNWJIZ2JMR1M0QmNjTFdVbWlHK2taTjBqNEMiLCJtYWMiOiI2ZTQ2OGE4OGQ0MWY4MWYyNzllZTdjMWMwNWQxZjgyZTcwNjI2MTAzNmIwMjU1YTlhNWVjNGY4MGVhZDg4MDcxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-491195457 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzcvmOKIgVMCCvEHD9HLB5trqF9yJ9GcIs8Pndo9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-491195457\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2120625319 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 15:22:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImY5a2wyVzZaRnRIWFc1MngwOE9FVVE9PSIsInZhbHVlIjoiNnZMTHV0Tk9ER3p2dFljL1VQZ2JIZi93aGJUZWw0WDFMRVF1K0JrTDJtMy9xUjJIaUduQVFNWDdqUjhBZXBJZ1JSc1g2RzNmWGwyWmFWdzVVcVVwQmVPZXBGckxFYU1MZ0pCR1I2T0JpVUszeDM4YlBOUFNkOTlVSnJNWHZqakY5YmtoeFFwL3Zqb1dRTGM2QVk0M1h2YXJKbGtKK1JrMkZUaXQzc1JPLzFjbGZPV3lZOUt1RVV2aktPbkVtdEhTaXFlaXBRbCsrQXkxaW0zejZQUVg3RkFjbXZabE9rVHExL000cVIzNlJsM2N4cGoydTlnZHhzTEwzVk1pUCt6elAxT2poSDR0U2l6d0hJNTZlcnRuT2NhcjBKcVZqdzB6c2lVSEpsZ0lUcUE5STliQVVhK3J2bTQ4TEZxdldSZENERkNTd05CTU5XWE9VaUdVd1d0WGFVTUlESHVOd1FEd3RYZndwS2IyQkM3ZmFENENrN3J4aG44QkZhZUxId0FtbnFSZTZRZW4zOXFJQVhIS0lFb0xxMHVYM0haaE1EMFNOWXpSeFc1R2NQU3lTbHp2am9IUjcxOHg0MDEwMDJrOHhUZXBtNy80VEZqZ3NxWkxnbmJjRzFjMXpDMWpaZkZiSWlBMGpKb1g5eVVpMnI4R2FhdnhrRnEreFpBcWNpb3YiLCJtYWMiOiI3OTI1YTQ2OTFmNTVlNzE3NTI3YTc3NDZmODljNWM4MzdkZWZlZTYzZTQ2Yzk0ZTVhYmQ0ZmUwY2Y4ODIzYjBkIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:22:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imc4dk93a3pNTTZIOWgrRVVmN3dkUHc9PSIsInZhbHVlIjoiMXU5aGZtSnA4Z3hZdytEVDJ5VTlWN0xOUjk2Q3hCaEo3UmovWUloNnRZc0lPVTZZMkNkQ1Z3dU01VC94VGhVSHZycVBzVVJkV3hNYWJzemQ3QUFyQ0hpVG1KQjFNR1MrU2tRZUdDR3g4cy96MmZBTzZyOXEzYm1SNkxjWFBRQ05OZ2lENjJrM1Zaa0NUR3cwUGU2eVI5NWZhMUhxSHJ0SGNjTVVWN0FXbDJueE9FWUJTMjFyMVJtdzh1MEU1V0srQVZ2YXZHZS94VjhpUGdXZVhERkZyTEpCczVKTXBnVDZBalVjRVdRRVBsb01WdWdKRGZNWkxmQ2NiMHJZTm44Wmhna1BOVHpXZjJTMnliWVl5cGxkTmwySGxKMFg5QUtLUHNNTmI2KzVxSWkvUWVoMGFBYmNWTk1UUi9yM3VkUnVZQVJXVzN1L0VaanlSa3U3RXpmUVN3SS95Yk5Cb0JEWnJXWDFwUDNqL1d3czZ0TE9nNmFtSHROVWpZQmxJTi9YRWMrZXRxcndjMm1vdDJCRHVmSEl3dVdQYTl2YjgvYVFvL2Ryai8wV0Q1TWlpVGVWVzhjZlZXUlJaSGpadzlVdU10RFpLK1V6cGRkb3MzdkpvMy9ibzdRRnRPTWYvdTdwamc0Qi9RbHRZbW5RWlhyUmdaZ3ZKNGNUaHl0RHZmV1kiLCJtYWMiOiJjZGM4Y2UwNDY5ZDc3MjEwMTMzNjM4ZGM0ZDJmYjdiYzVhNTc1MTg0ZTc2OGJkY2Y3NjM0MzMzNmFiZDA5ZmFjIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 17:22:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImY5a2wyVzZaRnRIWFc1MngwOE9FVVE9PSIsInZhbHVlIjoiNnZMTHV0Tk9ER3p2dFljL1VQZ2JIZi93aGJUZWw0WDFMRVF1K0JrTDJtMy9xUjJIaUduQVFNWDdqUjhBZXBJZ1JSc1g2RzNmWGwyWmFWdzVVcVVwQmVPZXBGckxFYU1MZ0pCR1I2T0JpVUszeDM4YlBOUFNkOTlVSnJNWHZqakY5YmtoeFFwL3Zqb1dRTGM2QVk0M1h2YXJKbGtKK1JrMkZUaXQzc1JPLzFjbGZPV3lZOUt1RVV2aktPbkVtdEhTaXFlaXBRbCsrQXkxaW0zejZQUVg3RkFjbXZabE9rVHExL000cVIzNlJsM2N4cGoydTlnZHhzTEwzVk1pUCt6elAxT2poSDR0U2l6d0hJNTZlcnRuT2NhcjBKcVZqdzB6c2lVSEpsZ0lUcUE5STliQVVhK3J2bTQ4TEZxdldSZENERkNTd05CTU5XWE9VaUdVd1d0WGFVTUlESHVOd1FEd3RYZndwS2IyQkM3ZmFENENrN3J4aG44QkZhZUxId0FtbnFSZTZRZW4zOXFJQVhIS0lFb0xxMHVYM0haaE1EMFNOWXpSeFc1R2NQU3lTbHp2am9IUjcxOHg0MDEwMDJrOHhUZXBtNy80VEZqZ3NxWkxnbmJjRzFjMXpDMWpaZkZiSWlBMGpKb1g5eVVpMnI4R2FhdnhrRnEreFpBcWNpb3YiLCJtYWMiOiI3OTI1YTQ2OTFmNTVlNzE3NTI3YTc3NDZmODljNWM4MzdkZWZlZTYzZTQ2Yzk0ZTVhYmQ0ZmUwY2Y4ODIzYjBkIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:22:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imc4dk93a3pNTTZIOWgrRVVmN3dkUHc9PSIsInZhbHVlIjoiMXU5aGZtSnA4Z3hZdytEVDJ5VTlWN0xOUjk2Q3hCaEo3UmovWUloNnRZc0lPVTZZMkNkQ1Z3dU01VC94VGhVSHZycVBzVVJkV3hNYWJzemQ3QUFyQ0hpVG1KQjFNR1MrU2tRZUdDR3g4cy96MmZBTzZyOXEzYm1SNkxjWFBRQ05OZ2lENjJrM1Zaa0NUR3cwUGU2eVI5NWZhMUhxSHJ0SGNjTVVWN0FXbDJueE9FWUJTMjFyMVJtdzh1MEU1V0srQVZ2YXZHZS94VjhpUGdXZVhERkZyTEpCczVKTXBnVDZBalVjRVdRRVBsb01WdWdKRGZNWkxmQ2NiMHJZTm44Wmhna1BOVHpXZjJTMnliWVl5cGxkTmwySGxKMFg5QUtLUHNNTmI2KzVxSWkvUWVoMGFBYmNWTk1UUi9yM3VkUnVZQVJXVzN1L0VaanlSa3U3RXpmUVN3SS95Yk5Cb0JEWnJXWDFwUDNqL1d3czZ0TE9nNmFtSHROVWpZQmxJTi9YRWMrZXRxcndjMm1vdDJCRHVmSEl3dVdQYTl2YjgvYVFvL2Ryai8wV0Q1TWlpVGVWVzhjZlZXUlJaSGpadzlVdU10RFpLK1V6cGRkb3MzdkpvMy9ibzdRRnRPTWYvdTdwamc0Qi9RbHRZbW5RWlhyUmdaZ3ZKNGNUaHl0RHZmV1kiLCJtYWMiOiJjZGM4Y2UwNDY5ZDc3MjEwMTMzNjM4ZGM0ZDJmYjdiYzVhNTc1MTg0ZTc2OGJkY2Y3NjM0MzMzNmFiZDA5ZmFjIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 17:22:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2120625319\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nRKpbN5LpywXyCQRSJtszgbxLs5FOetexjTyneoy</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}