{"__meta": {"id": "X8790e7255abfebbf1a85727b588882d1", "datetime": "2025-06-17 05:42:09", "utime": 1750138929.030209, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750138927.518226, "end": 1750138929.030244, "duration": 1.5120182037353516, "duration_str": "1.51s", "measures": [{"label": "Booting", "start": 1750138927.518226, "relative_start": 0, "end": **********.828412, "relative_end": **********.828412, "duration": 1.3101861476898193, "duration_str": "1.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.828436, "relative_start": 1.3102099895477295, "end": 1750138929.030249, "relative_end": 5.0067901611328125e-06, "duration": 0.2018132209777832, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45154216, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02023, "accumulated_duration_str": "20.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.924389, "duration": 0.01779, "duration_str": "17.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.939}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.979925, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.939, "width_percent": 6.13}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750138929.005867, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.068, "width_percent": 5.932}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1031369489 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1031369489\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1111957178 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1111957178\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-634113820 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-634113820\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1428006954 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=1jp2hip%7C1750138924133%7C12%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImYrbThkbnZSd09ZZVBielloeEV6T0E9PSIsInZhbHVlIjoiU1hKVFNzbnUzcnlMeXpOaFkrT3FnYUowSmx0NmxaTzNoTWVRUC9yZmNBYW1lOTFNYlQ4QTE3YUtkS1JJVE5wSjQ5eVh6c3F3Vi9hVmFoZUNlT3BKYnNYMS92cmc2NjV0Qms4cG9FbWhOeEZZM2RieTdYNW9VNUdRc21lVytOQWUxS2sxK1VhVThzNTFyN2lJMGM5YUpscFFGMHNrbHA1dzdkNWJBT1loVEgvQVNoeXFrbHd0TnBHeEx5YVVkS0hUUGwwWXJDNVlXaVpOUVdTWnRiQVlOWHpsc2l6UHZrOEs2VXNWUUsrMlJVSnNtWXhXamNjZ1FyWVltaitNL0dobEp1YXBwSURjUHdGSUpnMmt4bThCOC9pbVhtek00d2RxNHp0SXRTTkVCL0JPWVpiK2paaXNheG9talZyQXkySlAvUk1UQWNPNkxRbGg4TU1DZkU0R1ZmaktWKzhFUDZqUDJWWi9ZbFBMbmt2UWdOY3pIT2V2ZEJNcG1jZzRlaGZDL2pIVk1QY2JKK2dsRXRITXBRay81d3VTazFwNkkrOWQ5bWtFT0tFaWVwZXdmOWFWUVR1RC9FTXA5Mm52YmV1SXJzVU5zQnVTMTAzazZ5UkxIaGhsT2dDdCt6bUVvMGJCSjdQVWNpRjFTVnl5MVBOVjJaOHR0bEt2aktKSFJuRTMiLCJtYWMiOiJiN2QyM2U4MGJiMjBjOGRjNzVkYmI3NjNiNmEyZTZkMWE1OGVhNWJkODI4OWUxODYzZTM3MjNmMDRhZjE2ZDI1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkFrYWxPaHdPMGNXR2N3eW03VzNqRGc9PSIsInZhbHVlIjoiWWtpSjhrSFlxNmhGRUx6M2xqOHo1UVdJN29xa0FrTVNYMWVhYmVwN2JYbllqNVVoQkNaM2c1U3NqUDR2WllWSVVlTUIxRnR5aytOdVpZOVkrZXFFbnJSNTVabUF1NENySUlIOFh1bStHanBXRFpTMnhlTjBZc2VZckNXL2pkaFJ2ZVkzMndBUi9sRUpISGZOVnhybFRrMU5RL1ErUEJJR292TG4xL05yenFrRFZhZXFPcmhtYm1VelNiMVRvK0hEMnEvL0wvdnNXQmFpaGRaRVp2SzJaeHZibnB1cEMxY0xoSktCUWI3anlqWkthdE05UHY5TVJBZ0x6aTFsTFNYbDRaOVowb1Q5M1FOTFJGdHl1cFhkdXRYR2VKUVhjRnBySG9SdkRFLzQ0K3F6R1laNXlYN0U5ZlY1Njg4VktrRmFzdXNGL05XNFlzQUFDdVZVcW5RQ1duZkVKTTJtNTF4cE9HNEEvUlU1OFlpQ3ROWTdHMHMxRlZTVkgvcXlQUmJFQkJncStjNkUzWm9PaGUzUVhSRStQNVhrSWhCYlJMRWlhekJhRFQ1TjVKWlMzWHArRm1SWldTU1I1TXp4NU5sWENzcVNYYzRiRXFYTzBmMkdZTi9vQ1h2UWVsbm5wLzVYZHl6eUFpbThmOHd1RFVDOVEyNEZ5SloyWWhyNGRaUWMiLCJtYWMiOiJjNTcyYTRiNzZjNjdkMjJkNTdiOGU4MGQwYTRiZGZlY2VhNzE4NzE4MDEwODRkZTRhZWIxZGY0NDRlYTZkYWMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1428006954\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1062803980 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5koYdmUwe7MsYgJhhuLKclDhRBGbboW56xZkfrLu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1062803980\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1977139479 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 05:42:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhQM1NVNE12cC9MMndjUVEwWlBBZkE9PSIsInZhbHVlIjoiL0JGTVR3ZG9xekZ3N09vODFpZm51cXRYVklReEh0T2NhOTc3c3NOeFhjZlgzOUlOVDRvd3cveGlWdk54d1lxODQzSEhFV3FPSjZweW1CbmM3cGZZTnB3NW1ZTW0yL1BGQkhjdDhWRGFLWVUrVEo4Y2dvRUJwNk5mNzJmcm94V1BRS0ttSXhmWktzUkEzb1dGNTltNTh3UkpOQzZVZnhkbjlxMWRRVTRHUWpUc1RXdFFleUxpWTNLSHBlUkNIdDQ0MmFMNXJZOHZ3dDBOU2ZKZ2RlZ3h2TWVwcnU1OUFHR3UyVFIxN253U1N3MUNXejg5Q2tmQ1VaeEdIQ21HSXZvTUVEa1dHdS9ONkVQSnlqOENiVGdzNmQvRlZQWTQzZ3FKVlRvRU1ZKzdneStaWUpxNEFwZnZUTDNjWElTMWttRFg2WEZHcU5IZUlQRDhtRG84akhVYXVzTjFBMXRrWUhtNkpLaHdLdFRmUXAyWi9Fc0g2dTNJM0JUUXFjanZYZ09aTnczZnVZdU5zQ0YxdkhDSlRzanR1VytPSUlqSXducHVpSGtuSVJkVU9WWmVybUhaV0djbUdXYjJDN25HZ0JpYVdFbWJHU1hkblBHZytYQ2lET3Q1YzdnVGpvSVBiOFVQQUo4MWtDUUNSRVZSVlBZTWFFa0lybDRCcDJNRUt5NEoiLCJtYWMiOiJhM2ExZjU3OTAwMTM0NTQ0MDc0NDc1MTY1NjE2OTUzOTVkNWIyMmE5YzZjYzA4MjgyZDdlZGI1OGM0ZWFkMzAwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:42:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlhrSWlxTzljMm9DUU05WEppVXVYN3c9PSIsInZhbHVlIjoiVUtJZHBhbWxVaGo5YUxjcnAvWlJyY0ovb005NW9BUms5SU81UDl5ei9KSmFpUzhrU0poTTcyTlRnWnNYL21aQmhBdDNFbkpxbzVsOWhUb2V5d3FlY1MwbFJkWTc3TXc5dVZHZ2luN2ZFbURlQ0toeHFZUWQ2K2xVS0J0ck1ZWkFTM0xGNHFaUG1qL3N6T3gxRTRvVHBURG1KUGhmaXNOWU5nOHkwL1RqbEdyaHF6UDJkZzFXWnZheVNsbjJtUGhiNjZtcHZCQXFrTkF3dzJiVGdqYTJkKy85WGZpeWtsTzJ1STVnd2JxSmJSQ2g0eCtySVROMTZUdllMS05EQzcwaXFsMVJEcXVGa3pCQWgvOVhWenJnV3hxQ2hNWGZkejl0VUFBc2pudjNFS1NiV3F4ZUlkQnJWOVpBZ2pERnFkNUFrMUJkU1VoS2l3cHoxd0JmUjIxZ0VkYjRYN0xSRWh0M2ZKU1lxSTBIcy9HVkRGbjFEcDIyVmQra0tIQnp1Rm5DdEpxMGdmMDBncWtoUm95NHJqNng3YkZTYjl1cTlGSnRJdkJsajA3SFBkZE43Rmx5dTVia3FRQ281aE1JUmRsQ25ZdjhQNnFiNDUvb1dpUC9JcmZQNldObFBzclZSbXlVMkRwSkFZbkRBTVIzT1JoT1EzaGlyRHpKaHlLRXhFMEYiLCJtYWMiOiJjMWYxMDBhZjk5MGYwMjExMmI3NmU3YzZjOWQ5NjI1NGZjNDVjMTkyYjU4YjM4NmVhNDdjYjVmZDczZjFmMmI2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 07:42:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhQM1NVNE12cC9MMndjUVEwWlBBZkE9PSIsInZhbHVlIjoiL0JGTVR3ZG9xekZ3N09vODFpZm51cXRYVklReEh0T2NhOTc3c3NOeFhjZlgzOUlOVDRvd3cveGlWdk54d1lxODQzSEhFV3FPSjZweW1CbmM3cGZZTnB3NW1ZTW0yL1BGQkhjdDhWRGFLWVUrVEo4Y2dvRUJwNk5mNzJmcm94V1BRS0ttSXhmWktzUkEzb1dGNTltNTh3UkpOQzZVZnhkbjlxMWRRVTRHUWpUc1RXdFFleUxpWTNLSHBlUkNIdDQ0MmFMNXJZOHZ3dDBOU2ZKZ2RlZ3h2TWVwcnU1OUFHR3UyVFIxN253U1N3MUNXejg5Q2tmQ1VaeEdIQ21HSXZvTUVEa1dHdS9ONkVQSnlqOENiVGdzNmQvRlZQWTQzZ3FKVlRvRU1ZKzdneStaWUpxNEFwZnZUTDNjWElTMWttRFg2WEZHcU5IZUlQRDhtRG84akhVYXVzTjFBMXRrWUhtNkpLaHdLdFRmUXAyWi9Fc0g2dTNJM0JUUXFjanZYZ09aTnczZnVZdU5zQ0YxdkhDSlRzanR1VytPSUlqSXducHVpSGtuSVJkVU9WWmVybUhaV0djbUdXYjJDN25HZ0JpYVdFbWJHU1hkblBHZytYQ2lET3Q1YzdnVGpvSVBiOFVQQUo4MWtDUUNSRVZSVlBZTWFFa0lybDRCcDJNRUt5NEoiLCJtYWMiOiJhM2ExZjU3OTAwMTM0NTQ0MDc0NDc1MTY1NjE2OTUzOTVkNWIyMmE5YzZjYzA4MjgyZDdlZGI1OGM0ZWFkMzAwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:42:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlhrSWlxTzljMm9DUU05WEppVXVYN3c9PSIsInZhbHVlIjoiVUtJZHBhbWxVaGo5YUxjcnAvWlJyY0ovb005NW9BUms5SU81UDl5ei9KSmFpUzhrU0poTTcyTlRnWnNYL21aQmhBdDNFbkpxbzVsOWhUb2V5d3FlY1MwbFJkWTc3TXc5dVZHZ2luN2ZFbURlQ0toeHFZUWQ2K2xVS0J0ck1ZWkFTM0xGNHFaUG1qL3N6T3gxRTRvVHBURG1KUGhmaXNOWU5nOHkwL1RqbEdyaHF6UDJkZzFXWnZheVNsbjJtUGhiNjZtcHZCQXFrTkF3dzJiVGdqYTJkKy85WGZpeWtsTzJ1STVnd2JxSmJSQ2g0eCtySVROMTZUdllMS05EQzcwaXFsMVJEcXVGa3pCQWgvOVhWenJnV3hxQ2hNWGZkejl0VUFBc2pudjNFS1NiV3F4ZUlkQnJWOVpBZ2pERnFkNUFrMUJkU1VoS2l3cHoxd0JmUjIxZ0VkYjRYN0xSRWh0M2ZKU1lxSTBIcy9HVkRGbjFEcDIyVmQra0tIQnp1Rm5DdEpxMGdmMDBncWtoUm95NHJqNng3YkZTYjl1cTlGSnRJdkJsajA3SFBkZE43Rmx5dTVia3FRQ281aE1JUmRsQ25ZdjhQNnFiNDUvb1dpUC9JcmZQNldObFBzclZSbXlVMkRwSkFZbkRBTVIzT1JoT1EzaGlyRHpKaHlLRXhFMEYiLCJtYWMiOiJjMWYxMDBhZjk5MGYwMjExMmI3NmU3YzZjOWQ5NjI1NGZjNDVjMTkyYjU4YjM4NmVhNDdjYjVmZDczZjFmMmI2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 07:42:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1977139479\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1784381022 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MhB3ylaPm6nYWvBOxPIGtW24phtvV2TuzrLzCUDw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1784381022\", {\"maxDepth\":0})</script>\n"}}