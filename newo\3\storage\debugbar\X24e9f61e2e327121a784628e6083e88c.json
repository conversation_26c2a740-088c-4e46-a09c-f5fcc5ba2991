{"__meta": {"id": "X24e9f61e2e327121a784628e6083e88c", "datetime": "2025-06-17 06:54:07", "utime": **********.776351, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750143246.286438, "end": **********.77638, "duration": 1.4899420738220215, "duration_str": "1.49s", "measures": [{"label": "Booting", "start": 1750143246.286438, "relative_start": 0, "end": **********.60615, "relative_end": **********.60615, "duration": 1.3197119235992432, "duration_str": "1.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.606169, "relative_start": 1.3197309970855713, "end": **********.776382, "relative_end": 1.9073486328125e-06, "duration": 0.170212984085083, "duration_str": "170ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43526216, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.02513, "accumulated_duration_str": "25.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.727032, "duration": 0.02513, "duration_str": "25.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XTqfILO20ESOyUrbO2PEByvY2Oxsmxq0H0QyDSRn", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-854529435 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-854529435\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1611897676 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1611897676\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-120451764 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjlPeTV6VXd0OUkzOGJXczR1cm5kamc9PSIsInZhbHVlIjoieS9JNmFnWTVRc1VLWkRhMUJCMkQ4U3pQaVJYSVJyS3pDTGRoZWgzTDJhRWpwbFB5UGRDeE1XMnhneHRVdUZhMUdKUmJEQ0VjSGZ5NFlSS01keVArbWhhYlFlZjlNMUtJdGhYamZuN1BEK3pvc0tkWk9rRUhQNG9nVStpTWE5SGd3MFc1ZzgyeG8rQTRGTzRDZFJ1U3FzaWx5Z25LT3l4ZTdRRUNSa1R4Nmo1MnRqMSt6RDJTd1VOM3NMTXZJdUJSaFMvT2dkenV1bWdsUVJ4NjZmeTFrbHBzN3FjMWIvcGlpLzlJVk80bFh3aUpUeVpqSFZoR3YrbmFFMEs4eVVEVmNrejRZT3h4eVpmamI4blI2N0hvZzB2K3FNUks1V2F6akFuTm0yR1d5bDhpRWRXbWovV0srZTE1N0hjcmhtc1JkeXdINGRVS3hLZi9FSG1BNWVhQ2UzVldpdmt3d3BUU09ibnlUdlJoQWc1Ty9uUVY1MWd6MENaaHpBUktTWW9aZHRNUjZqSzlZV1VIREhKMVFvMDZZZzh0YUdLc3VGOVJLMWpLbHo3VVlmbFFqZktwWGg0V2xRaUtEUFFQL0ZWWnd0bUFvaUhMRnplL0lmQ3RMVHZ2eTZDVlZxNHUrM2dKMTV0NktMNkdjdTJ6VTBmWTJXbXR3T1R0aGhhKzlqRWoiLCJtYWMiOiIzNDcwMDhiZjY4OTNmMDIxMjUzODMyNzk4MTg4ZmUzMmZmYzQ0MzEyOTJjOWRhMThkNjNkMjYyNzk0MWY0YTdkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZCSmw5d1Ewd2I2OWlEZmNGd29kSXc9PSIsInZhbHVlIjoiZVRtaVpaL1hNcU9VWHhiR3hDRjViaUE1VFBsWDR3Y1hVUG9Cb3haK0p4ZVk3cDFid3ltdlpOVXNRUm01a0dmQkNPZTZhR0R4YUsvRlV1NmN1eVVzMEpzVHVLN0VFOGg2MlVybVF1MVdDWjl6TjdrMklmejkwdHdDcjMwaWpVQlhSclZGZlVXSVdzZ0QyRjFKeFpBcmw5UForZFJaSVo1QVFneFFkZGVBZXl3WUhjTG0yRkFiakFmcTRPellCS3ZoaXl0N05qTERRNHMyVnBBMmJzbkRGeGVwRERjMURkVXpQS0x6d2FFdHZzZkx2MzJGQkgrRzJxZVRmeExFaENYRDV1eG15RG9tSjlFSm5yQ1pYUXlQSlJOVmJWQjlycC82MnZHYWxZTVdUczQ1eU15bHF2TWttU0FEZDBKeHZKckUrMi94K2RtQ1M3Z0xyUGVKTzRJcTB5K3VHZ1YvUU5kMkRKYzYzUWpnSFFyQ1UwZ2tjcVVDYlRibmpnUmw4UE1TMGY0WDJsenF4VmlMb01Mcm10aFFYRUJaSHU2TmZYQ0ZJQndnZVdScXQ5TGt5NjZNUmorRmhsVkZQeXlYb2Q1NngyY0JpNURybHg2RnhXclNiMDA3M2tTNkUxUHZPaW9Rb2pyNEM0Yk9jMjVJMUZsdlBJWkFKQU5GVDlYT0pDSUIiLCJtYWMiOiI5YTQwMzkyYTkyMDJlMDI2NzgyNGZmZDY5MDJkNGIyMDM2YjA1ZTlhNGQwNjBjNDYzMzM5YzFhOTEyZmM3MDU3IiwidGFnIjoiIn0%3D; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=lxd95%7C1750140707036%7C2%7C1%7Cl.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-120451764\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1518620978 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XTqfILO20ESOyUrbO2PEByvY2Oxsmxq0H0QyDSRn</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8dDZ2YVZ5BNkdHQRuaGlAK2aCC8rYkxUsxd4FSmV</span>\"\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1518620978\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1585775628 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 06:54:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ind0d1FJKzJJVmVDMnc1M3BIbVZXaGc9PSIsInZhbHVlIjoiSXY2WUloL3QrdXJISzM2UmJkb0hKV1BZV0dxVHBaMzVDdmFRcDhVODhLK3daMDdtTENEK1oxbEV3Z00vSlZNaG91UE5ZY1JNa0JCdk44aXRoVVJlTnlFU1JZOXhqcS9ralFkL0FyUmNBV2hRWVE1ZHNOYi9SSk40OHJCMTF2RnpNeWExaVBJbDJrTlJRTW13cUlIeVl2eTRmbHdVTUYzN3VZeEtFL0h2MU5GbDNIZENwdStXUXFvb1RUZFZubEZoOVdYQzlFN3RWOGxMMjRBU3R2REsrNzZJN3ZsRWRNY0ZDcmNtU1RoTFJCdGR5QUZUbnUzYW9kUXJrbHJ4VENyOVZwWlg4ZDdneHdDQ0VwWGNSazIzeHYzbEM4ck9EVDh4a1lMRndsZGpKMjIrZ2wyeHdhRXJ2R3dWeTEvaDhRem5rZFR3eWFlSnpDUjFHdmRUNHNsak05cUJ3dGhyWU4zdG02a1BySGx4cDc0Tm9YYzRhN2FrbTNSVWt4K2pKSW1zdEcyY0ZuSzAyUFdUQ094Nk1DVno1Rmw0TkN4Y0d6OFBpSUR6VCtzQnVzaXJiVHVkVE0rYlRMSGxvRXI4SkFxOGcvN08yQ0FBSGZBWmdxdzY2ajJwSzRKcS94TDV2WVVMZVdmMitnSUdvdDN1YTd2bWc3WW9NeFRua2VSdEJ6cDEiLCJtYWMiOiJmY2U2Y2VmYzZjMmZhMjllZjA2ZWJiNzNmZDRlYWZjOGViZjViMjRjODNlMjVmNGJlNDY2NzM1ODZiMmU5ZjIwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:54:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ii9EOXdSdzhZVmZyVkNxUGltWE9vd0E9PSIsInZhbHVlIjoicjhFQWNscXR0NmFNYlArNG5zOEliMGJqVjg0YzNNcEVGRmRURWVzdG93ekhBUXhLNVFoRFFHU3NGcmhJNWpKdStrTXlhbW1BTUFPeWN6bm1aelllcEdMb2Z4MjVEZFRjT2hHK1NlNDFvVVpvcWNUeENLRDdkenp1c3ZRd2dPaXJ5WUdqTDZMckhWbzk3WGdWei9kOXVHUzZ2eXZyQ3c1OGx4REg0eTBuNnMyU3NvRy9lKytJeDg2dzZ1WDkrNUZzWXhhMTBMdkNnYmdCVkwwZU80VWM3NVN2Mlp5SW16MG8rRHN4LzZDSjc3VWJWWGRxemRtZ29IVm5OQ0FydXpaUWNxbnY3ZXpyQ0Q1MjZ2dTNTdEcrVU9IQW1XNUZRVHo3KzRYdGNvQzJUdG1aK2hxbTRwU1FVNXc5bTBFME0rVnA4U0ZzRTE5T0FaYmttWHMzSHlrb2t3S2drcmlGUFdkWjluUWpieCtFZE1SbUpuZlNMdnl3cWwxQmxQalh4d2xPUldTQlE0V2JZZWkrSEZNTDlnc2ZBKzBwSlR5dm5Qb05PNnI0TnZSZmZLOXpnaC8xR2RGQzBBdytZaEpldTBPRXRFNEFJSk5RN0NyUWh2dHl6NW0wZ2NLaGl5clFXTXhqRHlMLzcvbE1Ha3NneGJhVjdacnFDSXZNc3VIQ3dQSkIiLCJtYWMiOiJiYjI1NDJiMTY2M2VkOTBjMGE4OGRjOGMyMDBmZDQzZDVhYWZmZTI2ZWJmYTk0ZTc3YzkxYzJiNjVkZTQ5YWE3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 08:54:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ind0d1FJKzJJVmVDMnc1M3BIbVZXaGc9PSIsInZhbHVlIjoiSXY2WUloL3QrdXJISzM2UmJkb0hKV1BZV0dxVHBaMzVDdmFRcDhVODhLK3daMDdtTENEK1oxbEV3Z00vSlZNaG91UE5ZY1JNa0JCdk44aXRoVVJlTnlFU1JZOXhqcS9ralFkL0FyUmNBV2hRWVE1ZHNOYi9SSk40OHJCMTF2RnpNeWExaVBJbDJrTlJRTW13cUlIeVl2eTRmbHdVTUYzN3VZeEtFL0h2MU5GbDNIZENwdStXUXFvb1RUZFZubEZoOVdYQzlFN3RWOGxMMjRBU3R2REsrNzZJN3ZsRWRNY0ZDcmNtU1RoTFJCdGR5QUZUbnUzYW9kUXJrbHJ4VENyOVZwWlg4ZDdneHdDQ0VwWGNSazIzeHYzbEM4ck9EVDh4a1lMRndsZGpKMjIrZ2wyeHdhRXJ2R3dWeTEvaDhRem5rZFR3eWFlSnpDUjFHdmRUNHNsak05cUJ3dGhyWU4zdG02a1BySGx4cDc0Tm9YYzRhN2FrbTNSVWt4K2pKSW1zdEcyY0ZuSzAyUFdUQ094Nk1DVno1Rmw0TkN4Y0d6OFBpSUR6VCtzQnVzaXJiVHVkVE0rYlRMSGxvRXI4SkFxOGcvN08yQ0FBSGZBWmdxdzY2ajJwSzRKcS94TDV2WVVMZVdmMitnSUdvdDN1YTd2bWc3WW9NeFRua2VSdEJ6cDEiLCJtYWMiOiJmY2U2Y2VmYzZjMmZhMjllZjA2ZWJiNzNmZDRlYWZjOGViZjViMjRjODNlMjVmNGJlNDY2NzM1ODZiMmU5ZjIwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:54:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ii9EOXdSdzhZVmZyVkNxUGltWE9vd0E9PSIsInZhbHVlIjoicjhFQWNscXR0NmFNYlArNG5zOEliMGJqVjg0YzNNcEVGRmRURWVzdG93ekhBUXhLNVFoRFFHU3NGcmhJNWpKdStrTXlhbW1BTUFPeWN6bm1aelllcEdMb2Z4MjVEZFRjT2hHK1NlNDFvVVpvcWNUeENLRDdkenp1c3ZRd2dPaXJ5WUdqTDZMckhWbzk3WGdWei9kOXVHUzZ2eXZyQ3c1OGx4REg0eTBuNnMyU3NvRy9lKytJeDg2dzZ1WDkrNUZzWXhhMTBMdkNnYmdCVkwwZU80VWM3NVN2Mlp5SW16MG8rRHN4LzZDSjc3VWJWWGRxemRtZ29IVm5OQ0FydXpaUWNxbnY3ZXpyQ0Q1MjZ2dTNTdEcrVU9IQW1XNUZRVHo3KzRYdGNvQzJUdG1aK2hxbTRwU1FVNXc5bTBFME0rVnA4U0ZzRTE5T0FaYmttWHMzSHlrb2t3S2drcmlGUFdkWjluUWpieCtFZE1SbUpuZlNMdnl3cWwxQmxQalh4d2xPUldTQlE0V2JZZWkrSEZNTDlnc2ZBKzBwSlR5dm5Qb05PNnI0TnZSZmZLOXpnaC8xR2RGQzBBdytZaEpldTBPRXRFNEFJSk5RN0NyUWh2dHl6NW0wZ2NLaGl5clFXTXhqRHlMLzcvbE1Ha3NneGJhVjdacnFDSXZNc3VIQ3dQSkIiLCJtYWMiOiJiYjI1NDJiMTY2M2VkOTBjMGE4OGRjOGMyMDBmZDQzZDVhYWZmZTI2ZWJmYTk0ZTc3YzkxYzJiNjVkZTQ5YWE3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 08:54:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1585775628\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XTqfILO20ESOyUrbO2PEByvY2Oxsmxq0H0QyDSRn</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}